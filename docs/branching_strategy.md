# Power Up - Git Branching Strategy

This document outlines the branching strategy for the Power Up mobile application. We'll follow the GitFlow workflow with some adaptations for our mobile development process.

## Main Branches

- `main`: Production-ready code that has been released.
- `develop`: Integration branch for ongoing development work.

## Supporting Branches

- `feature/*`: For new features or enhancements (e.g., `feature/habit-tracking`)
- `bugfix/*`: For bug fixes (e.g., `bugfix/login-crash`)
- `release/*`: For release preparation (e.g., `release/v1.0.0`)
- `hotfix/*`: For urgent production fixes (e.g., `hotfix/critical-auth-issue`)

## Workflow

1. **Feature Development**:
   - Branch off from: `develop`
   - Naming convention: `feature/{feature-name}`
   - Merge back into: `develop`
   - Delete after: Merge completion

2. **Bug Fixes**:
   - Branch off from: `develop`
   - Naming convention: `bugfix/{bug-description}`
   - Merge back into: `develop`
   - Delete after: Merge completion

3. **Release Preparation**:
   - Branch off from: `develop`
   - Naming convention: `release/v{version-number}`
   - Merge back into: `main` AND `develop`
   - Delete after: Merge completion

4. **Hotfixes**:
   - Branch off from: `main`
   - Naming convention: `hotfix/{issue-description}`
   - Merge back into: `main` AND `develop`
   - Delete after: Merge completion

## Branch Protection Rules

The following protection rules should be applied in GitHub:

1. **`main` branch**:
   - Require pull request reviews before merging
   - Require status checks to pass before merging
   - Restrict who can push to the branch (admins only)

2. **`develop` branch**:
   - Require pull request reviews before merging
   - Require status checks to pass before merging

3. **All branches**:
   - Signed commits recommended

## Commit Message Format

```
[type]: Short description (50 chars max)

Longer description if needed, explaining the context and providing
additional information about the changes.

Closes #123 (reference to issue if applicable)
```

Types:
- feat: A new feature
- fix: A bug fix
- docs: Documentation changes
- style: Code style changes (formatting, etc.)
- refactor: Code changes that neither fix bugs nor add features
- perf: Performance improvements
- test: Adding or modifying tests
- chore: Changes to build process or auxiliary tools
