# GitHub Branch Protection Rules Setup

This document outlines the steps to set up branch protection rules in GitHub for the Power Up project.

## Setting Up Branch Protection Rules

1. Go to the GitHub repository for Power Up
2. Click on "Settings" tab
3. Select "Branches" in the left sidebar
4. Under "Branch protection rules", click "Add rule"

## Main Branch Protection

1. In "Branch name pattern", enter `main`
2. Check the following options:
   - ✅ Require a pull request before merging
   - ✅ Require approvals (set to 1)
   - ✅ Dismiss stale pull request approvals when new commits are pushed
   - ✅ Require status checks to pass before merging
     - Add "flutter analyze" as a required check
     - Add "flutter test" as a required check
   - ✅ Require branches to be up to date before merging
   - ✅ Include administrators
3. Click "Create"

## Develop Branch Protection

1. Click "Add rule" again
2. In "Branch name pattern", enter `develop`
3. Check the following options:
   - ✅ Require a pull request before merging
   - ✅ Require approvals (set to 1)
   - ✅ Require status checks to pass before merging
     - Add "flutter analyze" as a required check
     - Add "flutter test" as a required check
   - ✅ Require branches to be up to date before merging
4. Click "Create"

## Protecting Release Branches

1. Click "Add rule" again
2. In "Branch name pattern", enter `release/*`
3. Check the following options:
   - ✅ Require a pull request before merging
   - ✅ Require approvals (set to 1)
   - ✅ Require status checks to pass before merging
4. Click "Create"
