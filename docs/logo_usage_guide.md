# Power Up App Logo Usage Guide

This document outlines how to use the application logo consistently throughout the Power Up app.

## Logo Files

The logo is located at `assets/images/logo.png` and is configured in the `pubspec.yaml` file under the assets section.

## Available Logo Widgets

### AppLogo Widget

The `AppLogo` widget is a reusable component located at `lib/features/core/presentation/widgets/app_logo.dart` that provides consistent logo usage throughout the app.

#### Basic Usage

```dart
import 'package:power_up/features/core/presentation/widgets/app_logo.dart';

// Basic logo with custom dimensions
AppLogo(
  width: 100,
  height: 100,
)
```

#### Predefined Variants

```dart
// Splash screen logo (120x120)
AppLogo.splash()

// App bar logo (height: 32)
AppLogo.appBar()

// Drawer logo (64x64)
AppLogo.drawer()
```

#### Advanced Usage

```dart
// With color filter for themed variations
AppLogo(
  width: 80,
  height: 80,
  colorFilter: ColorFilter.mode(
    Theme.of(context).colorScheme.primary,
    BlendMode.srcIn,
  ),
)
```

## Current Implementation

### 1. Splash Screen

- Location: `lib/features/core/presentation/screens/splash_screen.dart`
- Uses: `AppLogo.splash()` - 120x120 pixels
- Context: First screen users see when opening the app

### 2. Authentication Screens

- **Login Screen**: `lib/features/authentication/presentation/screens/login_screen.dart`

  - Uses: `AppLogo(width: 80, height: 80)`
  - Context: Above the login form

- **Register Screen**: `lib/features/authentication/presentation/screens/register_screen.dart`
  - Uses: `AppLogo(width: 64, height: 64)`
  - Context: Above the registration form

### 3. Main App Navigation

- **App Bar**: `lib/features/core/presentation/screens/main_layout_screen.dart`

  - Uses: `AppLogo.appBar()` (height: 32) on home screen only
  - Context: Next to app name in the top navigation

- **3D Drawer**: `lib/features/core/presentation/widgets/custom_3d_drawer.dart`
  - Uses: `AppLogo.drawer()` (64x64 pixels)
  - Context: Branding section at the top of the navigation drawer

## App Icons

### Configuration

The logo is configured to generate app icons using `flutter_launcher_icons` package:

```yaml
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  web:
    generate: true
    image_path: "assets/images/logo.png"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
  macos:
    generate: true
    image_path: "assets/images/logo.png"
```

### Generating Icons

To generate app icons, run:

```bash
flutter pub get
flutter pub run flutter_launcher_icons
```

## Constants

### App Constants

Logo-related constants are defined in `lib/core/constants/app_constants.dart`:

```dart
// Asset Paths
static const String logoPath = 'assets/images/logo.png';

// Logo Dimensions
static const double splashLogoSize = 120.0;
static const double appBarLogoHeight = 32.0;
static const double drawerLogoSize = 64.0;
```

## Best Practices

1. **Consistency**: Always use the `AppLogo` widget instead of directly using `Image.asset()`
2. **Accessibility**: The widget includes semantic labels for screen readers
3. **Error Handling**: Built-in error handling displays a placeholder if the logo fails to load
4. **Performance**: Uses efficient image loading with proper sizing
5. **Theming**: Supports color filters for theme-based variations

## Design Guidelines

- **Minimum Size**: 24x24 pixels for icon usage
- **Maximum Size**: 120x120 pixels for splash screens
- **Aspect Ratio**: Maintain original aspect ratio
- **Background**: Logo should work on both light and dark backgrounds
- **Spacing**: Provide adequate padding around the logo (minimum 8dp)

## Future Enhancements

Consider implementing these features:

1. **Animated Logo**: Add subtle animations for splash screen
2. **Adaptive Icons**: Support for Android adaptive icons
3. **SVG Support**: Convert to SVG for better scalability
4. **Dark Mode Variants**: Specific logo versions for dark themes
5. **Brand Colors**: Extract and use logo colors in app theme
