# Power Up Mobile App

A productivity and self-improvement app with AI coaching built with Flutter.

## Project Overview

Power Up is a mobile application designed to help users improve their productivity, track habits, and receive personalized AI coaching. The app combines task management, habit tracking, AI-powered insights, and community features to create a comprehensive self-improvement platform.

## Architecture

This project follows Clean Architecture principles with the following layers:

- **Domain Layer**: Contains business logic with entities, use cases, and repository interfaces
- **Data Layer**: Implements repositories, manages data sources (API and local storage)
- **Presentation Layer**: Uses GetX for state management, routing, and dependency injection

## Project Structure

```
lib/
├── core/               # Core functionality and utilities
│   ├── error/          # Error handling and failures
│   ├── network/        # Network related code (API client, etc.)
│   ├── usecases/       # Base usecase classes
│   └── util/           # Utility functions
│
├── features/           # App features
│   └── authentication/ # Example feature
│       ├── data/       # Data layer (repositories, data sources)
│       ├── domain/     # Domain layer (entities, use cases)
│       └── presentation/ # UI layer (controllers, pages, widgets)
│
└── main.dart           # Application entry point
```

## Getting Started

### Prerequisites

- Flutter SDK 3.10.0 or higher
- Dart SDK 3.0.0 or higher

### Setup

1. Clone this repository
2. Run the setup script to install dependencies and create the project structure:
   ```bash
   ./scripts/setup.sh
   ```

3. Run the app:
   ```bash
   flutter run
   ```

### Branching Strategy

This project follows the GitFlow branching strategy with main branches:
- `main`: Production-ready code
- `develop`: Integration branch for ongoing development

For details, see [branching_strategy.md](docs/branching_strategy.md).

## Testing

The project follows a Test-Driven Development (TDD) approach:

```bash
# Run unit and widget tests
flutter test

# Run integration tests
flutter test integration_test
```

## Dependencies

- **State Management**: GetX
- **Functional Programming**: Dartz
- **HTTP Client**: Dio
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Notifications**: Firebase Messaging, Flutter Local Notifications
- **Audio**: Just Audio
- **In-App Purchases**: RevenueCat (via purchases_flutter)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
