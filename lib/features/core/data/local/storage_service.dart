import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';

/// Service for handling local storage operations
class StorageService {
  final GetStorage _storage;

  // Private constructor
  StorageService._(this._storage);

  /// Singleton instance
  static StorageService? _instance;

  /// Get singleton instance
  static Future<StorageService> getInstance() async {
    if (_instance == null) {
      await GetStorage.init();
      final storage = GetStorage();
      _instance = StorageService._(storage);
    }
    return _instance!;
  }

  /// Save data to local storage
  Future<bool> setData<T>(String key, T value) async {
    try {
      _storage.write(key, value);
      return true;
    } catch (e) {
      if (kDebugMode) print('StorageService setData error: $e');
      return false;
    }
  }

  /// Get data from local storage
  T? getData<T>(String key) {
    try {
      return _storage.read<T>(key);
    } catch (e) {
      if (kDebugMode) print('StorageService getData error: $e');
      return null;
    }
  }

  /// Remove data from local storage
  Future<bool> removeData(String key) async {
    try {
      _storage.remove(key);
      return true;
    } catch (e) {
      if (kDebugMode) print('StorageService removeData error: $e');
      return false;
    }
  }

  /// Clear all data from local storage
  Future<bool> clearAll() async {
    try {
      _storage.erase();
      return true;
    } catch (e) {
      if (kDebugMode) print('StorageService clearAll error: $e');
      return false;
    }
  }
}
