import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;
import 'package:flutter/foundation.dart';

import '../../local/storage_service.dart';
import '../../../../../../core/util/app_constants.dart';

/// Interceptor for adding authentication tokens to requests
class AuthInterceptor extends Interceptor {
  final StorageService storageService;

  AuthInterceptor({required this.storageService});

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // Skip authentication for login/register endpoints
    final skipAuthEndpoints = [
      '/users/register',
      '/users/login',
      '/users/social-login',
    ];

    final shouldSkipAuth = skipAuthEndpoints.any(
      (endpoint) => options.path.contains(endpoint),
    );

    if (!shouldSkipAuth) {
      // Get stored access token
      final token = await storageService.getData(AppConstants.storageUserToken);

      if (token != null && token.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Handle 401 Unauthorized - token expired or invalid
    if (err.response?.statusCode == 401) {
      // Skip auto-redirect for login/register endpoints where 401 is expected for invalid credentials
      final skipRedirectEndpoints = [
        '/auth/login',
        '/users/login',
        '/auth/register',
        '/users/register',
        '/auth/social-login',
        '/users/social-login',
      ];

      final shouldSkipRedirect = skipRedirectEndpoints.any(
        (endpoint) => err.requestOptions.path.contains(endpoint),
      );

      if (!shouldSkipRedirect) {
        // Clear stored auth data only for authenticated requests that failed
        _clearAuthData();

        // Navigate to login screen only if not already on login/register screens
        try {
          final currentRoute = getx.Get.currentRoute;
          if (currentRoute != '/login' && currentRoute != '/register') {
            getx.Get.offAllNamed('/login');
          }
        } catch (e) {
          // If navigation fails, just log the error
          if (kDebugMode) {
            debugPrint('AuthInterceptor: Failed to navigate to login: $e');
          }
        }
      }
    }

    handler.next(err);
  }

  /// Clear all stored authentication data
  Future<void> _clearAuthData() async {
    await Future.wait([
      storageService.removeData(AppConstants.storageUserToken),
      storageService.removeData(AppConstants.storageUserId),
      storageService.removeData(AppConstants.storageUserEmail),
      storageService.removeData(AppConstants.storageUserProfile),
    ]);
  }
}
