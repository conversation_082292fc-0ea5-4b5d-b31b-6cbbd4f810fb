import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../../../../../core/error/exceptions.dart';

/// Interceptor for consistent error handling and Failure mapping
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Convert DioException to custom exceptions
    final customException = _mapDioExceptionToCustomException(err);

    // Log error in debug mode
    if (kDebugMode) {
      print('ErrorInterceptor - ${err.type}: ${err.message}');
      if (err.response != null) {
        print('ErrorInterceptor - Status: ${err.response!.statusCode}');
        print('ErrorInterceptor - Data: ${err.response!.data}');
      }
    }

    // Create a new DioException with our custom exception message
    final modifiedException = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: customException,
      message: customException.message,
    );

    handler.next(modifiedException);
  }

  /// Maps DioException to custom app exceptions
  AppException _mapDioExceptionToCustomException(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException(
          message:
              'Connection timed out. Please check your internet connection.',
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(error);

      case DioExceptionType.cancel:
        return ServerException(message: 'Request was cancelled');

      case DioExceptionType.connectionError:
        return NetworkException(
          message:
              'No internet connection. Please check your network settings.',
        );

      case DioExceptionType.badCertificate:
        return ServerException(message: 'Security certificate error');

      case DioExceptionType.unknown:
        if (error.message?.contains('SocketException') ?? false) {
          return NetworkException(message: 'Network connection failed');
        }
        return ServerException(message: 'An unexpected error occurred');
    }
  }

  /// Handle bad response errors (4xx, 5xx status codes)
  AppException _handleBadResponse(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;
    final errorMessage = _extractErrorMessage(responseData);

    switch (statusCode) {
      case 400:
        return ValidationException(
          message: errorMessage ?? 'Invalid request data',
          statusCode: statusCode,
        );

      case 401:
        return AuthException(
          message:
              errorMessage ?? 'Authentication failed. Please log in again.',
          statusCode: statusCode,
        );

      case 403:
        return AuthException(
          message:
              errorMessage ??
              'Access denied. You don\'t have permission to perform this action.',
          statusCode: statusCode,
        );

      case 404:
        return ServerException(
          message: errorMessage ?? 'The requested resource was not found',
          statusCode: statusCode,
        );

      case 409:
        return ValidationException(
          message: errorMessage ?? 'Data conflict occurred',
          statusCode: statusCode,
        );

      case 422:
        return ValidationException(
          message: errorMessage ?? 'Invalid data provided',
          statusCode: statusCode,
        );

      case 429:
        return ServerException(
          message: errorMessage ?? 'Too many requests. Please try again later.',
          statusCode: statusCode,
        );

      case 500:
        return ServerException(
          message:
              errorMessage ?? 'Internal server error. Please try again later.',
          statusCode: statusCode,
        );

      case 502:
      case 503:
      case 504:
        return ServerException(
          message:
              errorMessage ??
              'Server is temporarily unavailable. Please try again later.',
          statusCode: statusCode,
        );

      default:
        if (statusCode != null && statusCode >= 400 && statusCode < 500) {
          return ValidationException(
            message: errorMessage ?? 'Client error occurred',
            statusCode: statusCode,
          );
        } else if (statusCode != null && statusCode >= 500) {
          return ServerException(
            message: errorMessage ?? 'Server error occurred',
            statusCode: statusCode,
          );
        } else {
          return ServerException(
            message: errorMessage ?? 'Unknown error occurred',
            statusCode: statusCode,
          );
        }
    }
  }

  /// Extract error message from response data
  String? _extractErrorMessage(dynamic responseData) {
    if (responseData is Map<String, dynamic>) {
      // Try common API error message formats
      return responseData['message'] ??
          responseData['error']?.toString() ??
          responseData['error_message']?.toString() ??
          responseData['errors']?.toString();
    } else if (responseData is String) {
      return responseData;
    }
    return null;
  }
}
