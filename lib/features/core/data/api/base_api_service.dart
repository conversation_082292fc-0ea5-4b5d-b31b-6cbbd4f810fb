import 'package:dio/dio.dart';

/// Base abstract class for API services with generic HTTP methods
abstract class BaseApiService {
  final Dio dio;

  BaseApiService(this.dio);

  /// Generic GET request
  Future<T> get<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    final response = await dio.get(
      endpoint,
      queryParameters: queryParameters,
      options: Options(headers: headers),
    );
    return fromData(response.data);
  }

  /// Generic POST request
  Future<T> post<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    final response = await dio.post(
      endpoint,
      data: data,
      queryParameters: queryParameters,
      options: Options(headers: headers),
    );
    return fromData(response.data);
  }

  /// Generic PUT request
  Future<T> put<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    final response = await dio.put(
      endpoint,
      data: data,
      queryParameters: queryParameters,
      options: Options(headers: headers),
    );
    return fromData(response.data);
  }

  /// Generic DELETE request
  Future<T> delete<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    final response = await dio.delete(
      endpoint,
      queryParameters: queryParameters,
      options: Options(headers: headers),
    );
    return fromData(response.data);
  }

  /// Generic PATCH request
  Future<T> patch<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    final response = await dio.patch(
      endpoint,
      data: data,
      queryParameters: queryParameters,
      options: Options(headers: headers),
    );
    return fromData(response.data);
  }
}
