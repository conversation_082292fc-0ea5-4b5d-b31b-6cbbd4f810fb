import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/error/exceptions.dart';

/// API Client for handling REST API requests
class ApiClient {
  final Dio _dio;

  ApiClient(this._dio);

  /// Performs a GET request
  Future<T> get<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      return fromData(response.data);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      if (kDebugMode) print('ApiClient GET error: $e');
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  /// Performs a POST request
  Future<T> post<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      return fromData(response.data);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      if (kDebugMode) print('ApiClient POST error: $e');
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  /// Performs a PUT request
  Future<T> put<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      return fromData(response.data);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      if (kDebugMode) print('ApiClient PUT error: $e');
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  /// Performs a DELETE request
  Future<T> delete<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      return fromData(response.data);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      if (kDebugMode) print('ApiClient DELETE error: $e');
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  /// Performs a PATCH request
  Future<T> patch<T>({
    required String endpoint,
    required T Function(dynamic data) fromData,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.patch(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );

      return fromData(response.data);
    } on DioException catch (e) {
      _handleDioError(e);
      rethrow;
    } catch (e) {
      if (kDebugMode) print('ApiClient PATCH error: $e');
      throw ServerException(message: 'Unexpected error occurred');
    }
  }

  /// Handles Dio Exceptions and converts them to AppExceptions
  void _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        throw NetworkException(message: 'Connection timed out');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final responseData = error.response?.data;

        if (statusCode == 401 || statusCode == 403) {
          throw AuthException(
            message:
                _extractErrorMessage(responseData) ?? 'Authentication error',
            statusCode: statusCode,
          );
        } else if (statusCode == 404) {
          throw ServerException(
            message: _extractErrorMessage(responseData) ?? 'Resource not found',
            statusCode: statusCode,
          );
        } else if (statusCode != null && statusCode >= 500) {
          throw ServerException(
            message: _extractErrorMessage(responseData) ?? 'Server error',
            statusCode: statusCode,
          );
        } else {
          throw ServerException(
            message: _extractErrorMessage(responseData) ?? 'Server error',
            statusCode: statusCode,
          );
        }

      case DioExceptionType.cancel:
        throw ServerException(message: 'Request cancelled');

      case DioExceptionType.connectionError:
        throw NetworkException(message: 'No internet connection');

      case DioExceptionType.badCertificate:
        throw ServerException(message: 'Bad certificate');

      case DioExceptionType.unknown:
        if (error.message?.contains('SocketException') ?? false) {
          throw NetworkException(message: 'No internet connection');
        }
        throw ServerException(message: 'Unexpected error occurred');
    }
  }

  /// Extracts error message from response data
  String? _extractErrorMessage(dynamic responseData) {
    if (responseData is Map<String, dynamic>) {
      // Try common API error formats
      return responseData['message'] ??
          responseData['error']?.toString() ??
          responseData['error_message']?.toString();
    }
    return null;
  }
}
