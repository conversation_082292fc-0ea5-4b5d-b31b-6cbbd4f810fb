/// Generic API response model for consistent response handling
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final List<String>? errors;
  final Map<String, dynamic>? metadata;

  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
    this.metadata,
  });

  /// Create ApiResponse from JSON
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic data)? fromData,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? true,
      message: json['message'],
      data:
          json['data'] != null && fromData != null
              ? fromData(json['data'])
              : json['data'],
      errors: json['errors'] != null ? List<String>.from(json['errors']) : null,
      metadata: json['metadata'],
    );
  }

  /// Convert ApiResponse to JSON
  Map<String, dynamic> toJson([Map<String, dynamic> Function(T data)? toData]) {
    return {
      'success': success,
      if (message != null) 'message': message,
      if (data != null) 'data': toData != null ? toData(data as T) : data,
      if (errors != null) 'errors': errors,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a successful response
  factory ApiResponse.success({
    T? data,
    String? message,
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      metadata: metadata,
    );
  }

  /// Create an error response
  factory ApiResponse.error({
    String? message,
    List<String>? errors,
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errors: errors,
      metadata: metadata,
    );
  }

  /// Check if the response is successful
  bool get isSuccess => success;

  /// Check if the response has errors
  bool get hasErrors => errors != null && errors!.isNotEmpty;

  /// Get first error message
  String? get firstError => hasErrors ? errors!.first : null;

  /// Get error message or first error
  String? get errorMessage => message ?? firstError;

  @override
  String toString() {
    return 'ApiResponse{success: $success, message: $message, data: $data, errors: $errors}';
  }
}

/// Paginated API response model
class PaginatedApiResponse<T> extends ApiResponse<List<T>> {
  final int? page;
  final int? limit;
  final int? total;
  final int? totalPages;
  final bool? hasNext;
  final bool? hasPrevious;

  PaginatedApiResponse({
    required bool success,
    String? message,
    List<T>? data,
    List<String>? errors,
    Map<String, dynamic>? metadata,
    this.page,
    this.limit,
    this.total,
    this.totalPages,
    this.hasNext,
    this.hasPrevious,
  }) : super(
         success: success,
         message: message,
         data: data,
         errors: errors,
         metadata: metadata,
       );

  /// Create PaginatedApiResponse from JSON
  factory PaginatedApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic data) fromData,
  ) {
    final pagination = json['pagination'] ?? {};

    return PaginatedApiResponse<T>(
      success: json['success'] ?? true,
      message: json['message'],
      data:
          json['data'] != null
              ? (json['data'] as List).map((item) => fromData(item)).toList()
              : null,
      errors: json['errors'] != null ? List<String>.from(json['errors']) : null,
      metadata: json['metadata'],
      page: pagination['page'],
      limit: pagination['limit'],
      total: pagination['total'],
      totalPages: pagination['totalPages'],
      hasNext: pagination['hasNext'],
      hasPrevious: pagination['hasPrevious'],
    );
  }

  /// Convert PaginatedApiResponse to JSON
  @override
  Map<String, dynamic> toJson([
    Map<String, dynamic> Function(List<T> data)? toData,
  ]) {
    final baseJson = super.toJson(toData);

    baseJson['pagination'] = {
      if (page != null) 'page': page,
      if (limit != null) 'limit': limit,
      if (total != null) 'total': total,
      if (totalPages != null) 'totalPages': totalPages,
      if (hasNext != null) 'hasNext': hasNext,
      if (hasPrevious != null) 'hasPrevious': hasPrevious,
    };

    return baseJson;
  }

  @override
  String toString() {
    return 'PaginatedApiResponse{success: $success, page: $page, total: $total, data: ${data?.length} items}';
  }
}
