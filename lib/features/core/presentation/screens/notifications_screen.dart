import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../../../notifications/presentation/controllers/notification_controller.dart';
import '../../../notifications/presentation/widgets/notification_list_item.dart';
import '../../../notifications/presentation/widgets/notification_filter_chips.dart';
import '../../../notifications/domain/entities/notification_entity.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final controller = Get.find<NotificationController>();

    return MainLayoutScreen(
      child: Column(
        children: [
          // Header Section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Notifications',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Obx(() {
                          final unreadCount = controller.unreadCount.value;
                          if (unreadCount > 0) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: theme.primaryColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                unreadCount.toString(),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        }),
                      ],
                    ),
                    Text(
                      'Stay updated with your progress',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'preferences',
                          child: Row(
                            children: [
                              Icon(Icons.settings_outlined),
                              SizedBox(width: 8),
                              Text('Preferences'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'mark_all_read',
                          child: Row(
                            children: [
                              Icon(Icons.mark_email_read_outlined),
                              SizedBox(width: 8),
                              Text('Mark all read'),
                            ],
                          ),
                        ),
                      ],
                  onSelected: (value) {
                    switch (value) {
                      case 'preferences':
                        Get.toNamed('/notification-preferences');
                        break;
                      case 'mark_all_read':
                        _markAllAsRead(controller);
                        break;
                    }
                  },
                ),
              ],
            ),
          ),

          // Filter chips
          NotificationFilterChips(
            selectedType: controller.selectedType.value,
            showOnlyUnread: controller.showOnlyUnread.value,
            onTypeSelected: controller.filterByType,
            onUnreadFilterChanged:
                (showUnread) => controller.toggleUnreadFilter(),
            onClearFilters: controller.clearFilters,
          ),

          const SizedBox(height: 16),

          // Notifications list
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value &&
                  controller.notifications.isEmpty) {
                return const Center(child: CircularProgressIndicator());
              }

              if (controller.errorMessage.value.isNotEmpty &&
                  controller.notifications.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: theme.colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load notifications',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        controller.errorMessage.value,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed:
                            () => controller.loadNotifications(refresh: true),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              }

              if (controller.notifications.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.notifications_none,
                        size: 64,
                        color: theme.disabledColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No notifications',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'You\'re all caught up!',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.disabledColor,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: () => controller.loadNotifications(refresh: true),
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount:
                      controller.notifications.length +
                      (controller.hasMoreNotifications.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == controller.notifications.length) {
                      // Load more indicator
                      if (controller.isLoadingMore.value) {
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      } else {
                        return Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Center(
                            child: ElevatedButton(
                              onPressed: controller.loadMoreNotifications,
                              child: const Text('Load More'),
                            ),
                          ),
                        );
                      }
                    }

                    final notification = controller.notifications[index];
                    return NotificationListItem(
                      notification: notification,
                      onTap: () => _handleNotificationTap(notification),
                      onMarkAsRead:
                          () => controller.markAsRead(notification.id),
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  void _markAllAsRead(NotificationController controller) {
    // Mark all unread notifications as read
    for (final notification in controller.notifications) {
      if (!notification.isRead) {
        controller.markAsRead(notification.id);
      }
    }
  }

  void _handleNotificationTap(NotificationEntity notification) {
    // Navigate based on notification type or action URL
    if (notification.actionUrl != null) {
      // Handle custom action URL
      Get.toNamed(notification.actionUrl!);
    } else {
      // Default navigation based on type
      switch (notification.type) {
        case NotificationType.taskReminder:
          Get.toNamed('/tasks');
          break;
        case NotificationType.habitReminder:
          Get.toNamed('/habits');
          break;
        case NotificationType.streakAlert:
          Get.toNamed('/streaks');
          break;
        case NotificationType.milestoneAchievement:
          Get.toNamed('/achievements');
          break;
        case NotificationType.challengeUpdate:
          Get.toNamed('/challenges');
          break;
        case NotificationType.communityPost:
          Get.toNamed('/community');
          break;
        case NotificationType.podcastReady:
          Get.toNamed('/podcasts');
          break;
        case NotificationType.systemUpdate:
          // Show update details or changelog
          break;
      }
    }
  }
}
