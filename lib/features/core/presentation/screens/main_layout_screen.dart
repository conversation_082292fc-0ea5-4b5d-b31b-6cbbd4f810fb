import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../../core/routes/app_routes.dart';
import '../widgets/custom_floating_tab_bar.dart';
import '../widgets/custom_3d_drawer.dart';

/// Controller for managing main layout state
class MainLayoutController extends GetxController {
  RxInt currentTabIndex = 0.obs;
  RxBool hasNotifications = true.obs;

  final List<FloatingTabItem> tabItems = const [
    FloatingTabItem(
      icon: Icons.home_rounded,
      label: 'Home',
      route: AppRoutes.home,
    ),
    FloatingTabItem(
      icon: Icons.calendar_month_rounded,
      label: 'Calendar',
      route: AppRoutes.calendar,
    ),
    FloatingTabItem(
      icon: Icons.school_rounded,
      label: 'Skill Plans',
      route: AppRoutes.skillPlans,
    ),
    FloatingTabItem(
      icon: Icons.people_rounded,
      label: 'Community',
      route: AppRoutes.community,
    ),
    FloatingTabItem(
      icon: Icons.podcasts_rounded,
      label: 'Podcasts',
      route: AppRoutes.podcasts,
    ),
  ];

  void onTabChanged(int index) {
    currentTabIndex.value = index;
    Get.toNamed(tabItems[index].route);
  }

  void updateCurrentIndex(String route) {
    final index = tabItems.indexWhere((item) => item.route == route);
    if (index != -1) {
      currentTabIndex.value = index;
    }
  }
}

/// Main layout screen with mixed design from podcasts and main layout
class MainLayoutScreen extends StatelessWidget {
  final Widget child;
  final bool hasTabbar;

  const MainLayoutScreen({
    super.key,
    required this.child,
    this.hasTabbar = false,
  });

  @override
  Widget build(BuildContext context) {
    final layoutController = Get.put(MainLayoutController());
    final drawerController = Get.put(Custom3DDrawerController());

    // Update current tab index based on current route
    WidgetsBinding.instance.addPostFrameCallback((_) {
      layoutController.updateCurrentIndex(Get.currentRoute);
    });

    return Custom3DDrawer(
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: _buildMixedAppBar(context, drawerController, layoutController),
        body: Stack(
          children: [
            // Mixed background gradient
            Container(
              decoration: _buildMixedBackgroundGradient(context),
              child: Column(
                children: [
                  // Enhanced header with podcast-style design
                  _buildEnhancedHeader(context, layoutController),

                  // Main content area with rounded corners
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surface.withValues(alpha: 0.95),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 20,
                            offset: const Offset(0, -4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: child,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Enhanced floating tab bar
            if (hasTabbar)
              Positioned(
                left: 16,
                right: 16,
                bottom: 24,
                child: Obx(
                  () => Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(28),
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(
                            context,
                          ).colorScheme.surface.withValues(alpha: 0.95),
                          Theme.of(
                            context,
                          ).colorScheme.surface.withValues(alpha: 0.98),
                        ],
                      ),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withValues(alpha: 0.1),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                        BoxShadow(
                          color: Theme.of(
                            context,
                          ).colorScheme.shadow.withValues(alpha: 0.05),
                          blurRadius: 40,
                          offset: const Offset(0, 16),
                        ),
                      ],
                    ),
                    child: CustomFloatingTabBar(
                      currentIndex: layoutController.currentTabIndex.value,
                      onTap: layoutController.onTabChanged,
                      items: layoutController.tabItems,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildMixedBackgroundGradient(BuildContext context) {
    final theme = Theme.of(context);

    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          theme.colorScheme.primary.withValues(alpha: 0.8),
          theme.colorScheme.primary.withValues(alpha: 0.6),
          theme.colorScheme.secondary.withValues(alpha: 0.4),
          theme.colorScheme.surface,
        ],
        stops: const [0.0, 0.3, 0.6, 1.0],
      ),
    );
  }

  Widget _buildEnhancedHeader(
    BuildContext context,
    MainLayoutController layoutController,
  ) {
    final theme = Theme.of(context);
    final currentRoute = Get.currentRoute;

    // Get page-specific info
    String title = 'Power Up';
    IconData? titleIcon;
    String subtitle = 'Welcome back!';

    switch (currentRoute) {
      case AppRoutes.home:
        title = 'Home';
        subtitle = 'Your productivity dashboard';
        titleIcon = Icons.home_rounded;
        break;
      case AppRoutes.habits:
        title = 'Habits';
        subtitle = 'Build lasting positive habits';
        titleIcon = Icons.task_alt_rounded;
        break;
      case AppRoutes.tasks:
        title = 'Tasks';
        subtitle = 'Manage your daily tasks';
        titleIcon = Icons.checklist_rounded;
        break;
      case AppRoutes.calendar:
        title = 'Calendar';
        subtitle = 'Plan your schedule';
        titleIcon = Icons.calendar_month_rounded;
        break;
      case AppRoutes.community:
        title = 'Community';
        subtitle = 'Connect and grow together';
        titleIcon = Icons.people_rounded;
        break;
      case AppRoutes.podcasts:
        title = 'Podcasts';
        subtitle = 'Listen to inspiring stories';
        titleIcon = Icons.podcasts_rounded;
        break;
      case AppRoutes.skillPlans:
        title = 'Skill Plans';
        subtitle = 'Develop new skills';
        titleIcon = Icons.school_rounded;
        break;
    }

    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + kToolbarHeight + 20,
        left: 20,
        right: 20,
        bottom: 20,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (titleIcon != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.1),
                    ),
                  ),
                  child: Icon(
                    titleIcon,
                    color: theme.colorScheme.onPrimary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.headlineLarge?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.onPrimary.withValues(
                          alpha: 0.9,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildMixedAppBar(
    BuildContext context,
    Custom3DDrawerController drawerController,
    MainLayoutController layoutController,
  ) {
    final theme = Theme.of(context);

    return AppBar(
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            theme.brightness == Brightness.dark
                ? Brightness.light
                : Brightness.dark,
        statusBarBrightness: theme.brightness,
      ),
      leading: Obx(
        () => Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.onPrimary.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.onPrimary.withValues(alpha: 0.1),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: AnimatedIcon(
              icon: AnimatedIcons.menu_close,
              progress:
                  drawerController.isDrawerOpen.value
                      ? const AlwaysStoppedAnimation(1.0)
                      : const AlwaysStoppedAnimation(0.0),
              color: theme.colorScheme.onPrimary,
              size: 20,
            ),
            onPressed: drawerController.toggleDrawer,
            style: IconButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: const Size(32, 32),
            ),
          ),
        ),
      ),
      title: null, // No title since we have enhanced header
      actions: [
        // Enhanced notifications button
        Obx(
          () => Container(
            margin: const EdgeInsets.only(right: 8),
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.1),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.notifications_rounded,
                      color: theme.colorScheme.onPrimary,
                      size: 20,
                    ),
                    onPressed: () {
                      Get.toNamed(AppRoutes.notifications);
                    },
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                      minimumSize: const Size(40, 40),
                    ),
                  ),
                ),
                if (layoutController.hasNotifications.value)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: theme.colorScheme.onPrimary,
                          width: 1,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),

        // Enhanced profile button
        Container(
          margin: const EdgeInsets.only(right: 16),
          decoration: BoxDecoration(
            color: theme.colorScheme.onPrimary.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.onPrimary.withValues(alpha: 0.1),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.onPrimary,
                    theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.person_rounded,
                color: theme.colorScheme.primary,
                size: 16,
              ),
            ),
            onPressed: () {
              Get.toNamed(AppRoutes.profile);
            },
            style: IconButton.styleFrom(
              padding: const EdgeInsets.all(8),
              minimumSize: const Size(40, 40),
            ),
          ),
        ),
      ],
    );
  }
}
