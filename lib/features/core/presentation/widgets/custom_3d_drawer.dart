import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../authentication/presentation/controllers/auth_controller.dart';

/// Controller for managing 3D drawer state
///
/// Handles animations for:
/// - Slide animation (0.0 to 0.6)
/// - Scale animation (1.0 to 0.8)
/// - Rotation animation (0.0 to -0.1 for LTR, 0.0 to 0.1 for RTL)
class Custom3DDrawerController extends GetxController
    with GetTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  RxBool isDrawerOpen = false.obs;

  @override
  void onInit() {
    super.onInit();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 0.0, end: 0.6).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.8).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Rotation will be determined dynamically based on text direction
    _rotationAnimation = Tween<double>(begin: 0.0, end: -0.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  void toggleDrawer() {
    if (isDrawerOpen.value) {
      _animationController.reverse();
    } else {
      _animationController.forward();
    }
    isDrawerOpen.value = !isDrawerOpen.value;
  }

  void closeDrawer() {
    if (isDrawerOpen.value) {
      _animationController.reverse();
      isDrawerOpen.value = false;
    }
  }

  Animation<double> get slideAnimation => _slideAnimation;
  Animation<double> get scaleAnimation => _scaleAnimation;
  Animation<double> get rotationAnimation => _rotationAnimation;

  @override
  void onClose() {
    _animationController.dispose();
    super.onClose();
  }
}

/// 3D Drawer Navigation Widget
///
/// Features:
/// - Dynamic user information display from AuthController
/// - App branding with logo
/// - Smooth 3D animations
/// - Profile section with tap-to-navigate functionality
/// - Proper logout handling with error feedback
class Custom3DDrawer extends StatelessWidget {
  final Widget child;

  const Custom3DDrawer({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final drawerController = Get.find<Custom3DDrawerController>();
    final theme = Theme.of(context);
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return Scaffold(
      body: Stack(
        children: [
          // Drawer Background
          Container(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.only(
                  left: isRTL ? 0 : 32,
                  right: isRTL ? 32 : 0,
                  top: 60,
                ),
                child: Column(
                  crossAxisAlignment:
                      isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                  children: [
                    // User Profile Section - Safe access to AuthController
                    _buildUserProfileSection(context, theme),

                    const SizedBox(height: 32),

                    // Navigation Items
                    Expanded(
                      child: ListView(
                        children: [
                          _buildDrawerItem(
                            context,
                            icon: Icons.home,
                            title: 'home'.tr,
                            route: AppRoutes.home,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.analytics,
                            title: 'analytics'.tr,
                            route: AppRoutes.statistics,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.assessment,
                            title: 'ai_report'.tr,
                            route: AppRoutes.progressReport,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.timer,
                            title: 'focus_timer'.tr,
                            route: AppRoutes.focusTimer,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.repeat,
                            title: 'habits'.tr,
                            route: AppRoutes.habits,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.task,
                            title: 'tasks'.tr,
                            route: AppRoutes.tasks,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.calendar_month,
                            title: 'calendar'.tr,
                            route: AppRoutes.calendar,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.abc,
                            title: 'skill_plans'.tr,
                            route: AppRoutes.skillPlans,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.people,
                            title: 'community'.tr,
                            route: AppRoutes.community,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.podcasts,
                            title: 'podcasts'.tr,
                            route: AppRoutes.podcasts,
                            drawerController: drawerController,
                          ),
                          const Divider(height: 32),
                          _buildDrawerItem(
                            context,
                            icon: Icons.settings,
                            title: 'settings'.tr,
                            route: AppRoutes.settings,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.person,
                            title: 'profile'.tr,
                            route: AppRoutes.profile,
                            drawerController: drawerController,
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.notifications,
                            title: 'notifications'.tr,
                            route: AppRoutes.notifications,
                            drawerController: drawerController,
                          ),
                          const SizedBox(height: 32),
                          _buildDrawerItem(
                            context,
                            icon: Icons.logout,
                            title: 'logout'.tr,
                            route: AppRoutes.login,
                            drawerController: drawerController,
                            isLogout: true,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Main Content with 3D Transform
          AnimatedBuilder(
            animation: drawerController.slideAnimation,
            builder: (context, _) {
              final screenWidth = MediaQuery.of(context).size.width;
              final slideValue = drawerController.slideAnimation.value;
              final rotationValue = drawerController.rotationAnimation.value;

              return Transform(
                alignment: isRTL ? Alignment.centerRight : Alignment.centerLeft,
                transform:
                    Matrix4.identity()
                      ..setEntry(3, 2, 0.001) // perspective
                      ..translate(
                        isRTL
                            ? -slideValue * screenWidth
                            : slideValue * screenWidth,
                      )
                      ..scale(drawerController.scaleAnimation.value)
                      ..rotateY(isRTL ? -rotationValue : rotationValue),
                child: GestureDetector(
                  onTap: () {
                    if (drawerController.isDrawerOpen.value) {
                      drawerController.closeDrawer();
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                        drawerController.isDrawerOpen.value ? 20 : 0,
                      ),
                      boxShadow:
                          drawerController.isDrawerOpen.value
                              ? [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  offset:
                                      isRTL
                                          ? const Offset(5, 0)
                                          : const Offset(-5, 0),
                                ),
                              ]
                              : null,
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: child,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String route,
    required Custom3DDrawerController drawerController,
    bool isLogout = false,
  }) {
    final theme = Theme.of(context);
    final currentRoute = Get.currentRoute;
    final isSelected = currentRoute == route;
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return Container(
      margin: EdgeInsets.only(
        bottom: 8,
        left: isRTL ? 32 : 0,
        right: isRTL ? 0 : 32,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface.withValues(alpha: 0.7),
          size: 24,
        ),
        title: Text(
          title,
          style: theme.textTheme.bodyLarge?.copyWith(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withValues(alpha: 0.7),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: isRTL ? TextAlign.right : TextAlign.left,
        ),
        selected: isSelected,
        selectedColor: theme.colorScheme.primary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        onTap: () async {
          drawerController.closeDrawer();

          if (isLogout) {
            // Handle logout logic
            final authController = Get.find<AuthController>();
            final success = await authController.logout();

            if (success) {
              Get.offAllNamed(route);
            } else {
              Get.snackbar(
                'error'.tr,
                'Failed to logout. Please try again.',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: theme.colorScheme.errorContainer,
                colorText: theme.colorScheme.onErrorContainer,
              );
            }
          } else {
            Get.toNamed(route);
          }
        },
      ),
    );
  }

  /// Generate user initials from full name
  /// Returns first letter of first two words, or 'U' as fallback
  String _getUserInitials(String? fullName) {
    if (fullName == null || fullName.trim().isEmpty) {
      return 'U';
    }

    final names = fullName.trim().split(' ');
    if (names.isEmpty) {
      return 'U';
    }

    String initials = '';

    // Take first letter of first name
    if (names[0].isNotEmpty) {
      initials += names[0][0].toUpperCase();
    }

    // Take first letter of last name if available
    if (names.length > 1 && names[1].isNotEmpty) {
      initials += names[1][0].toUpperCase();
    }

    return initials.isNotEmpty ? initials : 'U';
  }

  /// Safely builds the user profile section with proper error handling
  Widget _buildUserProfileSection(BuildContext context, ThemeData theme) {
    try {
      // Check if AuthController is registered before accessing
      if (!Get.isRegistered<AuthController>()) {
        return _buildGuestUserProfile(context, theme);
      }

      return Obx(() {
        final authController = Get.find<AuthController>();
        final user = authController.currentUser;
        final isLoading = authController.isLoading;

        if (isLoading) {
          return _buildLoadingProfile(context, theme);
        }

        return _buildUserProfile(context, theme, user);
      });
    } catch (e) {
      // Fallback to guest user if any error occurs
      return _buildGuestUserProfile(context, theme);
    }
  }

  /// Builds the loading state for user profile
  Widget _buildLoadingProfile(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            child: const CircularProgressIndicator(),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 20,
                  width: 120,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 16,
                  width: 160,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the authenticated user profile display
  Widget _buildUserProfile(BuildContext context, ThemeData theme, user) {
    final drawerController = Get.find<Custom3DDrawerController>();
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return GestureDetector(
      onTap: () {
        drawerController.closeDrawer();
        Get.toNamed(AppRoutes.profile);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.transparent,
        ),
        child: Row(
          textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
          children: [
            // Profile Avatar
            CircleAvatar(
              radius: 40,
              backgroundColor: theme.colorScheme.primary,
              backgroundImage:
                  user?.hasProfileImage == true
                      ? NetworkImage(user!.profileImage!)
                      : null,
              child:
                  user?.hasProfileImage != true
                      ? Text(
                        _getUserInitials(user?.fullName),
                        style: theme.textTheme.headlineSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                      : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment:
                    isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                children: [
                  Text(
                    user?.fullName ?? 'Guest User',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: isRTL ? TextAlign.right : TextAlign.left,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user?.email ?? '<EMAIL>',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: isRTL ? TextAlign.right : TextAlign.left,
                  ),
                ],
              ),
            ),
            Icon(
              isRTL ? Icons.arrow_back_ios : Icons.arrow_forward_ios,
              size: 16,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the guest user profile when no auth is available
  Widget _buildGuestUserProfile(BuildContext context, ThemeData theme) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;

    return GestureDetector(
      onTap: () {
        Get.toNamed(AppRoutes.login);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.transparent,
        ),
        child: Row(
          textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: theme.colorScheme.primary,
              child: Icon(Icons.person, size: 32, color: Colors.white),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment:
                    isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                children: [
                  Text(
                    'guest_user'.tr,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: isRTL ? TextAlign.right : TextAlign.left,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'tap_to_login'.tr,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                    textAlign: isRTL ? TextAlign.right : TextAlign.left,
                  ),
                ],
              ),
            ),
            Icon(Icons.login, size: 16, color: theme.colorScheme.primary),
          ],
        ),
      ),
    );
  }
}
