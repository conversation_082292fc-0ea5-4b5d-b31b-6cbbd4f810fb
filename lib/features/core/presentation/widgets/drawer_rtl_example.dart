import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Example demonstrating RTL/LTR support in Custom3D Drawer
///
/// This example shows how the drawer adapts to different text directions
/// and provides a seamless user experience in both Arabic and English.
class DrawerRTLExample extends StatelessWidget {
  const DrawerRTLExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('drawer_rtl_example'.tr),
        actions: [
          // Language toggle button for testing
          IconButton(
            onPressed: () => _toggleLanguage(),
            icon: const Icon(Icons.language),
            tooltip: 'toggle_language'.tr,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'rtl_ltr_support'.tr,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'drawer_rtl_description'.tr,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'features'.tr,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    _buildFeatureItem('rtl_layout_support'.tr),
                    _buildFeatureItem('dynamic_animations'.tr),
                    _buildFeatureItem('mirrored_shadows'.tr),
                    _buildFeatureItem('proper_text_alignment'.tr),
                    _buildFeatureItem('responsive_icons'.tr),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'how_it_works'.tr,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'drawer_mechanism'.tr,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 16),
          const SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  void _toggleLanguage() {
    final currentLocale = Get.locale;
    if (currentLocale?.languageCode == 'ar') {
      Get.updateLocale(const Locale('en', 'US'));
    } else {
      Get.updateLocale(const Locale('ar', 'SA'));
    }
  }
}

/// Technical details about the RTL implementation
class DrawerRTLTechnicalNotes {
  static const String implementation = '''
# Custom 3D Drawer RTL/LTR Implementation

## Key Features:

### 1. Dynamic Text Direction Detection
```dart
final isRTL = Directionality.of(context) == TextDirection.rtl;
```

### 2. Adaptive Positioning
- **LTR**: Drawer slides from left, content padding on left
- **RTL**: Drawer slides from right, content padding on right

### 3. Transform Animations
- **Translation**: Positive for LTR, negative for RTL
- **Rotation**: Negative Y-rotation for LTR, positive for RTL
- **Alignment**: centerLeft for LTR, centerRight for RTL

### 4. Shadow Direction
- **LTR**: Shadow offset (-5, 0) - shadow on left
- **RTL**: Shadow offset (5, 0) - shadow on right

### 5. Text Alignment
- Dynamic text alignment based on language direction
- Icons mirror appropriately (arrow directions)

## Implementation Benefits:

1. **Seamless UX**: Natural behavior for both language directions
2. **Performance**: No overhead when switching languages
3. **Consistency**: All drawer elements adapt correctly
4. **Accessibility**: Proper reading flow for all users
5. **Translations**: Full GetX translation support

## Usage:

The drawer automatically detects the current locale and adjusts:
- Layout direction
- Animation direction
- Text alignment
- Icon orientation
- Shadow positioning

No additional configuration required!
''';
}
