import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// A reusable widget for displaying the application logo
/// Follows the UI/UX Agent guidelines for consistent logo usage
class AppLogo extends StatelessWidget {
  /// The width of the logo
  final double? width;

  /// The height of the logo
  final double? height;

  /// How the logo should fit within the bounds
  final BoxFit fit;

  /// Custom color filter for the logo (optional)
  final ColorFilter? colorFilter;

  const AppLogo({
    super.key,
    this.width,
    this.height,
    this.fit = BoxFit.contain,
    this.colorFilter,
  });

  /// Factory constructor for splash screen logo
  AppLogo.splash({super.key, this.fit = BoxFit.contain, this.colorFilter})
    : width = AppConstants.splashLogoSize,
      height = AppConstants.splashLogoSize;

  /// Factory constructor for app bar logo
  AppLogo.appBar({super.key, this.fit = BoxFit.contain, this.colorFilter})
    : width = null,
      height = AppConstants.appBarLogoHeight;

  /// Factory constructor for drawer logo
  AppLogo.drawer({super.key, this.fit = BoxFit.contain, this.colorFilter})
    : width = AppConstants.drawerLogoSize,
      height = AppConstants.drawerLogoSize;

  @override
  Widget build(BuildContext context) {
    Widget logoImage = ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Image.asset(
        AppConstants.logoPath,
        width: width,
        height: height,
        fit: fit,
        // Add semantic label for accessibility
        semanticLabel: 'Power Up application logo',
        // Error handling - show placeholder if image fails to load
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Icon(
              Icons.image_not_supported_outlined,
              size:
                  (width != null && height != null)
                      ? (width! < height! ? width! * 0.6 : height! * 0.6)
                      : 24.0,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          );
        },
      ),
    );

    // Apply color filter if provided
    if (colorFilter != null) {
      return ColorFiltered(colorFilter: colorFilter!, child: logoImage);
    }

    return logoImage;
  }
}
