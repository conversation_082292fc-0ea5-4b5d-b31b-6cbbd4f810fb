import 'package:get/get.dart';
import 'package:power_up/core/routes/app_routes.dart';
import 'package:power_up/features/authentication/presentation/controllers/auth_controller.dart';

/// Controller for the splash screen
class SplashController extends GetxController {
  final _isLoading = true.obs;

  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    _initializeApp();
  }

  /// Initialize the app and navigate to appropriate screen
  Future<void> _initializeApp() async {
    try {
      // Show splash screen for at least 2 seconds
      await Future.delayed(const Duration(seconds: 2));

      // Check if user is authenticated
      final authController = Get.find<AuthController>();

      // Check authentication status
      await authController.checkAuthStatus();

      _isLoading.value = false;

      if (authController.isAuthenticated) {
        // User is authenticated, go to home
        Get.offAllNamed(AppRoutes.home);
      } else {
        // User is not authenticated, go to login
        Get.offAllNamed(AppRoutes.login);
      }
    } catch (e) {
      _isLoading.value = false;
      // If there's an error, go to login screen
      Get.offAllNamed(AppRoutes.login);
    }
  }
}
