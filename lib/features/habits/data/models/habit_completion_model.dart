import 'package:equatable/equatable.dart';

/// A model representing the completion details of a habit
class HabitCompletionModel extends Equatable {
  final String habitId;
  final DateTime date;
  final bool synced;
  final DateTime timestamp;

  const HabitCompletionModel({
    required this.habitId,
    required this.date,
    this.synced = false,
    required this.timestamp,
  });

  /// Create a new HabitCompletionModel from JSON
  factory HabitCompletionModel.fromJson(Map<String, dynamic> json) {
    return HabitCompletionModel(
      habitId: json['habitId'],
      date: DateTime.parse(json['date']),
      synced: json['synced'] ?? false,
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  /// Convert this model to JSON format
  Map<String, dynamic> toJson() {
    return {
      'habitId': habitId,
      'date': date.toIso8601String(),
      'synced': synced,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create a synced version of this completion
  HabitCompletionModel markSynced() {
    return HabitCompletionModel(
      habitId: habitId,
      date: date,
      synced: true,
      timestamp: timestamp,
    );
  }

  @override
  List<Object?> get props => [habitId, date, synced, timestamp];
}
