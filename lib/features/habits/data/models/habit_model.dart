import 'package:power_up/features/habits/domain/entities/habit_entity.dart';

/// Data model for Habit, used for serialization/deserialization
class HabitModel extends HabitEntity {
  const HabitModel({
    required super.id,
    required super.name,
    super.description,
    required super.frequency,
    super.customDays,
    required super.currentStreak,
    required super.longestStreak,
    super.completionHistory,
    required super.createdAt,
    required super.updatedAt,
    super.reminderSettings,
  });

  /// Create a HabitModel from a JSON map
  factory HabitModel.fromJson(Map<String, dynamic> json) {
    return HabitModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      frequency: _parseFrequency(json['frequency']),
      customDays:
          json['customDays'] != null
              ? List<int>.from(json['customDays'])
              : null,
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      completionHistory:
          json['completion'] != null
              ? Map<String, bool>.from(json['completion'])
              : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      reminderSettings: json['reminderSettings'],
    );
  }

  /// Convert HabitModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'frequency': _frequencyToString(frequency),
      'customDays': customDays,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'completion': completionHistory,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'reminderSettings': reminderSettings,
    };
  }

  /// Helper method to parse frequency string to enum
  static HabitFrequency _parseFrequency(String? frequencyString) {
    switch (frequencyString?.toLowerCase()) {
      case 'daily':
        return HabitFrequency.daily;
      case 'weekdays':
        return HabitFrequency.weekdays;
      case 'weekends':
        return HabitFrequency.weekends;
      case 'weekly':
        return HabitFrequency.weekly;
      case 'monthly':
        return HabitFrequency.monthly;
      case 'custom':
        return HabitFrequency.custom;
      default:
        return HabitFrequency.daily;
    }
  }

  /// Helper method to convert frequency enum to string
  static String _frequencyToString(HabitFrequency frequency) {
    switch (frequency) {
      case HabitFrequency.daily:
        return 'daily';
      case HabitFrequency.weekdays:
        return 'weekdays';
      case HabitFrequency.weekends:
        return 'weekends';
      case HabitFrequency.weekly:
        return 'weekly';
      case HabitFrequency.monthly:
        return 'monthly';
      case HabitFrequency.custom:
        return 'custom';
    }
  }

  /// Convert domain entity to a model
  factory HabitModel.fromEntity(HabitEntity entity) {
    return HabitModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      frequency: entity.frequency,
      customDays: entity.customDays,
      currentStreak: entity.currentStreak,
      longestStreak: entity.longestStreak,
      completionHistory: entity.completionHistory,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      reminderSettings: entity.reminderSettings,
    );
  }
}
