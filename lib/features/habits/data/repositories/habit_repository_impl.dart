import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/habits/data/datasources/habit_local_data_source.dart';
import 'package:power_up/features/habits/data/datasources/habit_remote_data_source.dart';
import 'package:power_up/features/habits/data/models/habit_model.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/habits/domain/repositories/habit_repository.dart';

/// Implementation of the HabitRepository
class HabitRepositoryImpl implements HabitRepository {
  final HabitRemoteDataSource remoteDataSource;
  final HabitLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  HabitRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<void> init() async {
    await localDataSource.init();
  }

  @override
  Future<void> clearData() async {
    await localDataSource.clear();
  }

  @override
  Future<Either<Failure, HabitEntity>> createHabit({
    required String name,
    String? description,
    required HabitFrequency frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final habitModel = await remoteDataSource.createHabit(
          name: name,
          description: description,
          frequency: frequency,
          customDays: customDays,
          reminderSettings: reminderSettings,
        );

        // Save habit locally
        await localDataSource.saveHabit(habitModel);

        return Right(habitModel);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Create a local habit with temporary ID
        final newHabit = HabitModel(
          id: 'local_${DateTime.now().millisecondsSinceEpoch}',
          name: name,
          description: description,
          frequency: frequency,
          customDays: customDays,
          currentStreak: 0,
          longestStreak: 0,
          completionHistory: const {},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          reminderSettings: reminderSettings,
        );

        await localDataSource.saveHabit(newHabit);
        return Right(newHabit);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<HabitEntity>>> getHabits() async {
    if (await networkInfo.isConnected) {
      try {
        final habits = await remoteDataSource.getHabits();

        // Update local cache
        await localDataSource.saveHabits(habits);

        return Right(habits);
      } on ServerException {
        // Fallback to local data if server fails
        try {
          final localHabits = await localDataSource.getHabits();
          return Right(localHabits);
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final localHabits = await localDataSource.getHabits();
        return Right(localHabits);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<HabitEntity>>> getTodaysHabits() async {
    if (await networkInfo.isConnected) {
      try {
        final habits = await remoteDataSource.getTodaysHabits();

        // Update local cache with these habits
        for (var habit in habits) {
          await localDataSource.saveHabit(habit);
        }

        return Right(habits);
      } on ServerException {
        // Fallback to local data if server fails
        try {
          final localHabits = await localDataSource.getTodaysHabits();
          return Right(localHabits);
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final localHabits = await localDataSource.getTodaysHabits();
        return Right(localHabits);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, HabitEntity>> getHabitById(String id) async {
    // Try local first for faster response
    final localHabit = await localDataSource.getHabitById(id);

    if (await networkInfo.isConnected) {
      try {
        // Get from remote
        final remoteHabit = await remoteDataSource.getHabitById(id);

        // Update local cache
        await localDataSource.saveHabit(remoteHabit);

        return Right(remoteHabit);
      } on ServerException catch (e) {
        // Return local version if available
        if (localHabit != null) {
          return Right(localHabit);
        }
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else if (localHabit != null) {
      return Right(localHabit);
    } else {
      return const Left(
        NetworkFailure(
          message: 'No internet connection and habit not found locally',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, HabitEntity>> updateHabit({
    required String id,
    String? name,
    String? description,
    HabitFrequency? frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedHabit = await remoteDataSource.updateHabit(
          id: id,
          name: name,
          description: description,
          frequency: frequency,
          customDays: customDays,
          reminderSettings: reminderSettings,
        );

        // Update local cache
        await localDataSource.saveHabit(updatedHabit);

        return Right(updatedHabit);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Get existing habit
        final existingHabit = await localDataSource.getHabitById(id);

        if (existingHabit == null) {
          return const Left(CacheFailure(message: 'Habit not found locally'));
        }

        // Update local habit
        final updatedHabit = HabitModel(
          id: existingHabit.id,
          name: name ?? existingHabit.name,
          description: description ?? existingHabit.description,
          frequency: frequency ?? existingHabit.frequency,
          customDays: customDays ?? existingHabit.customDays,
          currentStreak: existingHabit.currentStreak,
          longestStreak: existingHabit.longestStreak,
          completionHistory: existingHabit.completionHistory,
          createdAt: existingHabit.createdAt,
          updatedAt: DateTime.now(),
          reminderSettings: reminderSettings ?? existingHabit.reminderSettings,
        );

        await localDataSource.saveHabit(updatedHabit);
        return Right(updatedHabit);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, bool>> deleteHabit(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.deleteHabit(id);

        // Delete from local cache
        await localDataSource.deleteHabit(id);

        return Right(result);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Mark for deletion when back online
        await localDataSource.deleteHabit(id);
        return const Right(true);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, HabitEntity>> markHabitComplete(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final completedHabit = await remoteDataSource.markHabitComplete(id);

        // Update local cache
        await localDataSource.saveHabit(completedHabit);

        return Right(completedHabit);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Get existing habit
        final existingHabit = await localDataSource.getHabitById(id);

        if (existingHabit == null) {
          return const Left(CacheFailure(message: 'Habit not found locally'));
        }

        // Update local habit with completion for today
        final completedHabit = existingHabit.markAsCompleteToday();
        await localDataSource.saveHabit(HabitModel.fromEntity(completedHabit));
        return Right(completedHabit);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getHabitStatistics(
    String id,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final statistics = await remoteDataSource.getHabitStatistics(id);
        return Right(statistics);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Calculate basic statistics locally
        final habit = await localDataSource.getHabitById(id);

        if (habit == null) {
          return const Left(CacheFailure(message: 'Habit not found locally'));
        }

        final statistics = {
          'currentStreak': habit.currentStreak,
          'longestStreak': habit.longestStreak,
          'completionRate': habit.completionRate,
          'totalCompletions':
              habit.completionHistory?.values.where((v) => v).length ?? 0,
        };

        return Right(statistics);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }
}
