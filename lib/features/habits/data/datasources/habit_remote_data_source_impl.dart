import 'package:dio/dio.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/habits/data/datasources/habit_remote_data_source.dart';
import 'package:power_up/features/habits/data/models/habit_model.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';

/// Implementation of the HabitRemoteDataSource
class HabitRemoteDataSourceImpl implements HabitRemoteDataSource {
  final Dio dio;

  HabitRemoteDataSourceImpl({required this.dio});

  @override
  Future<void> init() async {
    // No initialization needed for remote data source
  }

  Future<void> clearData() async {
    // No local data to clear
  }

  @override
  Future<HabitModel> createHabit({
    required String name,
    String? description,
    required HabitFrequency frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'name': name,
        'frequency': _frequencyToString(frequency),
      };

      if (description != null) data['description'] = description;
      if (customDays != null) {
        data['customDays'] = customDays.map((day) => day.toString()).toList();
      }
      if (reminderSettings != null) {
        data['reminderSettings'] = reminderSettings.toString();
      }

      final response = await dio.post('/habits', data: data);

      return HabitModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to create habit',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<List<HabitModel>> getHabits() async {
    try {
      final response = await dio.get('/habits');
      final List<dynamic> habitList = response.data;
      return habitList.map((json) => HabitModel.fromJson(json)).toList();
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get habits',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<List<HabitModel>> getTodaysHabits() async {
    try {
      // This is an assumption - actual API might use a different structure
      final response = await dio.get(
        '/habits',
        queryParameters: {'today': true},
      );
      final List<dynamic> habitList = response.data;
      return habitList.map((json) => HabitModel.fromJson(json)).toList();
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get today\'s habits',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<HabitModel> getHabitById(String id) async {
    try {
      final response = await dio.get('/habits/$id');
      return HabitModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get habit',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<HabitModel> updateHabit({
    required String id,
    String? name,
    String? description,
    HabitFrequency? frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (frequency != null) data['frequency'] = _frequencyToString(frequency);
      if (customDays != null) data['customDays'] = customDays;
      if (reminderSettings != null) data['reminderSettings'] = reminderSettings;

      final response = await dio.put('/habits/$id', data: data);

      return HabitModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to update habit',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<bool> deleteHabit(String id) async {
    try {
      await dio.delete('/habits/$id');
      return true;
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to delete habit',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<HabitModel> markHabitComplete(String id) async {
    try {
      final response = await dio.post('/habits/$id/complete');
      return HabitModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to mark habit as complete',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<Map<String, dynamic>> getHabitStatistics(String id) async {
    try {
      // This endpoint might not exist in the Swagger doc, but it would be useful
      final response = await dio.get('/habits/$id/statistics');
      return response.data;
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get habit statistics',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<T> request<T>({
    required String endpoint,
    required T Function(Map<String, dynamic> json) fromJson,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    String method = 'GET',
    Map<String, String>? headers,
  }) async {
    try {
      Response response;

      switch (method) {
        case 'GET':
          response = await dio.get(
            endpoint,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'POST':
          response = await dio.post(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'PUT':
          response = await dio.put(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'DELETE':
          response = await dio.delete(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        default:
          throw ServerException(message: 'Unsupported HTTP method: $method');
      }

      return fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Request failed',
        statusCode: e.response?.statusCode,
      );
    }
  }

  /// Helper method to convert frequency enum to string
  String _frequencyToString(HabitFrequency frequency) {
    switch (frequency) {
      case HabitFrequency.daily:
        return 'daily';
      case HabitFrequency.weekdays:
        return 'weekdays';
      case HabitFrequency.weekends:
        return 'weekends';
      case HabitFrequency.weekly:
        return 'weekly';
      case HabitFrequency.monthly:
        return 'monthly';
      case HabitFrequency.custom:
        return 'custom';
    }
  }

  @override
  Future<void> dispose() async {
    // No cleanup needed for remote data source
  }
}
