import 'package:power_up/core/data/remote_datasource.dart';
import 'package:power_up/features/habits/data/models/habit_model.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';

/// Definition for the remote data source for habits
abstract class HabitRemoteDataSource extends RemoteDataSource {
  /// Create a new habit
  Future<HabitModel> createHabit({
    required String name,
    String? description,
    required HabitFrequency frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  });

  /// Get all habits
  Future<List<HabitModel>> getHabits();

  /// Get today's habits
  Future<List<HabitModel>> getTodaysHabits();

  /// Get a habit by ID
  Future<HabitModel> getHabitById(String id);

  /// Update a habit
  Future<HabitModel> updateHabit({
    required String id,
    String? name,
    String? description,
    HabitFrequency? frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  });

  /// Delete a habit
  Future<bool> deleteHabit(String id);

  /// Mark a habit as complete for today
  Future<HabitModel> markHabitComplete(String id);

  /// Get habit statistics
  Future<Map<String, dynamic>> getHabitStatistics(String id);
}
