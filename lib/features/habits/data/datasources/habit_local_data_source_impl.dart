import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/habits/data/datasources/habit_local_data_source.dart';
import 'package:power_up/features/habits/data/models/habit_model.dart';
import 'package:power_up/features/habits/data/models/habit_completion_model.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';

/// Implementation of HabitLocalDataSource using StorageService
class HabitLocalDataSourceImpl implements HabitLocalDataSource {
  // Storage keys
  static const String _habitsKey = 'habits';
  static const String _unsyncedHabitsKey = 'unsynced_habits';
  static const String _modifiedHabitsKey = 'modified_habits';
  static const String _unsyncedCompletionsKey = 'unsynced_completions';

  late final StorageService _storageService;
  bool _isInitialized = false;

  HabitLocalDataSourceImpl({StorageService? storageService}) {
    if (storageService != null) {
      _storageService = storageService;
      _isInitialized = true;
    }
  }

  @override
  Future<void> init() async {
    if (!_isInitialized) {
      _storageService = await StorageService.getInstance();
      _isInitialized = true;
    }
  }

  @override
  Future<void> dispose() async {
    // No resources to dispose for StorageService
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await init();
    }
  }

  @override
  Future<T?> get<T>({
    required String key,
    required T Function(dynamic json) fromJson,
  }) async {
    await _ensureInitialized();
    final data = _storageService.getData(key);
    return data != null ? fromJson(data) : null;
  }

  @override
  Future<void> set<T>({required String key, required T data}) async {
    await _ensureInitialized();
    await _storageService.setData(key, data);
  }

  @override
  Future<void> remove(String key) async {
    await _ensureInitialized();
    await _storageService.removeData(key);
  }

  @override
  Future<void> clear() async {
    await _ensureInitialized();
    await _storageService.removeData(_habitsKey);
    await _storageService.removeData(_unsyncedHabitsKey);
    await _storageService.removeData(_modifiedHabitsKey);
    await _storageService.removeData(_unsyncedCompletionsKey);
  }

  Future<void> clearData() async {
    await clear();
  }

  @override
  Future<void> saveHabit(HabitModel habit) async {
    try {
      await _ensureInitialized();

      // Get existing habits
      final habits = await getHabits();

      // Find if habit already exists
      final existingHabitIndex = habits.indexWhere((h) => h.id == habit.id);

      if (existingHabitIndex >= 0) {
        // Update existing habit
        habits[existingHabitIndex] = habit;
        await _addToModifiedHabits(habit.id);
      } else {
        // Add new habit
        habits.add(habit);
      }

      // Save updated list
      final habitsJson = habits.map((h) => h.toJson()).toList();
      await _storageService.setData(_habitsKey, habitsJson);

      // Add to unsynced habits if it has a temporary ID
      if (habit.id.startsWith('temp_')) {
        await _addToUnsyncedHabits(habit.id);
      }
    } catch (e) {
      throw CacheException(message: 'Failed to save habit: $e');
    }
  }

  @override
  Future<void> saveHabits(List<HabitModel> habits) async {
    try {
      await _ensureInitialized();

      // Get existing habits
      final existingHabits = await getHabits();

      // Create a map for faster lookup
      final existingHabitsMap = {
        for (var habit in existingHabits) habit.id: habit,
      };

      // Update or add habits
      for (final habit in habits) {
        existingHabitsMap[habit.id] = habit;
      }

      // Convert back to list and save
      final allHabits = existingHabitsMap.values.toList();
      final habitsJson = allHabits.map((h) => h.toJson()).toList();
      await _storageService.setData(_habitsKey, habitsJson);

      // Add unsynced habits (those with temporary IDs)
      for (final habit in habits) {
        if (habit.id.startsWith('temp_')) {
          await _addToUnsyncedHabits(habit.id);
        }
      }
    } catch (e) {
      throw CacheException(message: 'Failed to save habits: $e');
    }
  }

  @override
  Future<List<HabitModel>> getHabits() async {
    try {
      await _ensureInitialized();

      final habitsJson =
          _storageService.getData<List<dynamic>>(_habitsKey) ?? [];
      return habitsJson
          .map((json) => HabitModel.fromJson(Map<String, dynamic>.from(json)))
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get habits: $e');
    }
  }

  @override
  Future<List<HabitModel>> getTodaysHabits() async {
    try {
      await _ensureInitialized();

      final habits = await getHabits();
      final today = DateTime.now();

      return habits.where((habit) {
        // Check if habit is scheduled for today based on frequency
        return _isHabitDueToday(habit, today);
      }).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get today\'s habits: $e');
    }
  }

  @override
  Future<HabitModel?> getHabitById(String id) async {
    try {
      await _ensureInitialized();

      final habits = await getHabits();
      try {
        return habits.firstWhere((habit) => habit.id == id);
      } catch (e) {
        return null; // Habit not found
      }
    } catch (e) {
      throw CacheException(message: 'Failed to get habit by ID: $e');
    }
  }

  @override
  Future<void> deleteHabit(String id) async {
    try {
      await _ensureInitialized();

      final habits = await getHabits();
      habits.removeWhere((habit) => habit.id == id);

      final habitsJson = habits.map((h) => h.toJson()).toList();
      await _storageService.setData(_habitsKey, habitsJson);

      // Remove from unsynced and modified lists
      await _removeFromUnsyncedHabits(id);
      await _removeFromModifiedHabits(id);
    } catch (e) {
      throw CacheException(message: 'Failed to delete habit: $e');
    }
  }

  @override
  Future<void> deleteAllHabits() async {
    try {
      await _ensureInitialized();

      await _storageService.removeData(_habitsKey);
      await _storageService.removeData(_unsyncedHabitsKey);
      await _storageService.removeData(_modifiedHabitsKey);
      await _storageService.removeData(_unsyncedCompletionsKey);
    } catch (e) {
      throw CacheException(message: 'Failed to delete all habits: $e');
    }
  }

  @override
  Future<List<HabitModel>> getUnsyncedHabits() async {
    try {
      await _ensureInitialized();

      final unsyncedIds =
          _storageService.getData<List<dynamic>>(_unsyncedHabitsKey) ?? [];
      final habits = await getHabits();

      return habits
          .where(
            (habit) =>
                unsyncedIds.contains(habit.id) || habit.id.startsWith('temp_'),
          )
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get unsynced habits: $e');
    }
  }

  @override
  Future<void> markHabitSynced(String id) async {
    try {
      await _ensureInitialized();

      await _removeFromUnsyncedHabits(id);
      await _removeFromModifiedHabits(id);
    } catch (e) {
      throw CacheException(message: 'Failed to mark habit as synced: $e');
    }
  }

  @override
  Future<List<HabitModel>> getLocalOnlyHabits() async {
    try {
      await _ensureInitialized();

      final habits = await getHabits();
      return habits.where((habit) => habit.id.startsWith('temp_')).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get local-only habits: $e');
    }
  }

  @override
  Future<List<HabitModel>> getModifiedHabits() async {
    try {
      await _ensureInitialized();

      final modifiedIds =
          _storageService.getData<List<dynamic>>(_modifiedHabitsKey) ?? [];
      final habits = await getHabits();

      return habits.where((habit) => modifiedIds.contains(habit.id)).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get modified habits: $e');
    }
  }

  @override
  Future<List<HabitCompletionModel>> getUnsyncedCompletions() async {
    try {
      await _ensureInitialized();

      final completionsJson =
          _storageService.getData<List<dynamic>>(_unsyncedCompletionsKey) ?? [];
      return completionsJson
          .map(
            (json) =>
                HabitCompletionModel.fromJson(Map<String, dynamic>.from(json)),
          )
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get unsynced completions: $e');
    }
  }

  @override
  Future<void> markCompletionSynced(String habitId, DateTime date) async {
    try {
      await _ensureInitialized();

      final completions = await getUnsyncedCompletions();
      completions.removeWhere(
        (completion) =>
            completion.habitId == habitId &&
            completion.date.year == date.year &&
            completion.date.month == date.month &&
            completion.date.day == date.day,
      );

      final completionsJson = completions.map((c) => c.toJson()).toList();
      await _storageService.setData(_unsyncedCompletionsKey, completionsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to mark completion as synced: $e');
    }
  }

  // Helper methods
  Future<void> _addToUnsyncedHabits(String id) async {
    final unsyncedIds =
        _storageService.getData<List<dynamic>>(_unsyncedHabitsKey) ?? [];
    final updatedIds = Set<String>.from(unsyncedIds.cast<String>())..add(id);
    await _storageService.setData(_unsyncedHabitsKey, updatedIds.toList());
  }

  Future<void> _removeFromUnsyncedHabits(String id) async {
    final unsyncedIds =
        _storageService.getData<List<dynamic>>(_unsyncedHabitsKey) ?? [];
    final updatedIds = unsyncedIds.where((habitId) => habitId != id).toList();
    await _storageService.setData(_unsyncedHabitsKey, updatedIds);
  }

  Future<void> _addToModifiedHabits(String id) async {
    final modifiedIds =
        _storageService.getData<List<dynamic>>(_modifiedHabitsKey) ?? [];
    final updatedIds = Set<String>.from(modifiedIds.cast<String>())..add(id);
    await _storageService.setData(_modifiedHabitsKey, updatedIds.toList());
  }

  Future<void> _removeFromModifiedHabits(String id) async {
    final modifiedIds =
        _storageService.getData<List<dynamic>>(_modifiedHabitsKey) ?? [];
    final updatedIds = modifiedIds.where((habitId) => habitId != id).toList();
    await _storageService.setData(_modifiedHabitsKey, updatedIds);
  }

  bool _isHabitDueToday(HabitModel habit, DateTime today) {
    // Simple logic - you can enhance this based on habit frequency patterns
    // For now, assume all habits are due daily unless they have specific scheduling
    return true;
  }
}
