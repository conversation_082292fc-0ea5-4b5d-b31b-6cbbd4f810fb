import 'package:power_up/core/data/local_datasource.dart';
import 'package:power_up/features/habits/data/models/habit_model.dart';
import 'package:power_up/features/habits/data/models/habit_completion_model.dart';

/// Definition for the local data source for habits
abstract class HabitLocalDataSource extends LocalDataSource {
  /// Save a habit to local storage
  Future<void> saveHabit(HabitModel habit);

  /// Save multiple habits to local storage
  Future<void> saveHabits(List<HabitModel> habits);

  /// Get all habits from local storage
  Future<List<HabitModel>> getHabits();

  /// Get habits due today
  Future<List<HabitModel>> getTodaysHabits();

  /// Get a habit by ID from local storage
  Future<HabitModel?> getHabitById(String id);

  /// Delete a habit from local storage
  Future<void> deleteHabit(String id);

  /// Delete all habits from local storage
  Future<void> deleteAllHabits();

  /// Get habits that need syncing with the server
  Future<List<HabitModel>> getUnsyncedHabits();

  /// Mark a habit as synced
  Future<void> markHabitSynced(String id);

  /// Get habits that only exist locally (with temporary IDs)
  Future<List<HabitModel>> getLocalOnlyHabits();

  /// Get habits that have been modified locally
  Future<List<HabitModel>> getModifiedHabits();

  /// Get habit completions that need syncing
  Future<List<HabitCompletionModel>> getUnsyncedCompletions();

  /// Mark a habit completion as synced
  Future<void> markCompletionSynced(String habitId, DateTime date);
}
