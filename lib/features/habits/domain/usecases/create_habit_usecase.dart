import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/habits/domain/repositories/habit_repository.dart';

/// Use case for creating a new habit
class CreateHabitUseCase implements UseCase<HabitEntity, CreateHabitParams> {
  final HabitRepository repository;

  CreateHabitUseCase(this.repository);

  @override
  Future<Either<Failure, HabitEntity>> call(CreateHabitParams params) async {
    // Input validation
    if (params.name.isEmpty) {
      return const Left(ValidationFailure(message: 'Habit name cannot be empty'));
    }

    // Validate custom days if frequency is custom
    if (params.frequency == HabitFrequency.custom &&
        (params.customDays == null || params.customDays!.isEmpty)) {
      return const Left(
        ValidationFailure(
          message: 'Custom frequency requires at least one day to be selected',
        ),
      );
    }

    // Create habit
    return repository.createHabit(
      name: params.name,
      description: params.description,
      frequency: params.frequency,
      customDays: params.customDays,
      reminderSettings: params.reminderSettings,
    );
  }
}

/// Parameters for CreateHabitUseCase
class CreateHabitParams extends Equatable {
  final String name;
  final String? description;
  final HabitFrequency frequency;
  final List<int>? customDays;
  final Map<String, dynamic>? reminderSettings;

  const CreateHabitParams({
    required this.name,
    this.description,
    required this.frequency,
    this.customDays,
    this.reminderSettings,
  });

  @override
  List<Object?> get props => [
    name,
    description,
    frequency,
    customDays,
    reminderSettings,
  ];
}
