import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/habits/domain/repositories/habit_repository.dart';

/// Use case for marking a habit as complete for today
class MarkHabitCompleteUseCase implements UseCase<HabitEntity, HabitIdParams> {
  final HabitRepository repository;

  MarkHabitCompleteUseCase(this.repository);

  @override
  Future<Either<Failure, HabitEntity>> call(HabitIdParams params) async {
    return repository.markHabitComplete(params.id);
  }
}

/// Parameter for use cases that only need a habit ID
class HabitIdParams extends Equatable {
  final String id;

  const HabitIdParams({required this.id});

  @override
  List<Object?> get props => [id];
}
