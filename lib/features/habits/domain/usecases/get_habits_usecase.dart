import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/habits/domain/repositories/habit_repository.dart';

/// Use case for getting all habits
class GetHabitsUseCase implements UseCase<List<HabitEntity>, NoParams> {
  final HabitRepository repository;

  GetHabitsUseCase(this.repository);

  @override
  Future<Either<Failure, List<HabitEntity>>> call(NoParams params) {
    return repository.getHabits();
  }
}

/// Use case for getting today's due habits
class GetTodaysHabitsUseCase implements UseCase<List<HabitEntity>, NoParams> {
  final HabitRepository repository;

  GetTodaysHabitsUseCase(this.repository);

  @override
  Future<Either<Failure, List<HabitEntity>>> call(NoParams params) {
    return repository.getTodaysHabits();
  }
}
