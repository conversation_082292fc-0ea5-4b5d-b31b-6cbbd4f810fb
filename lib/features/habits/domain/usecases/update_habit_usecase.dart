import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/habits/domain/repositories/habit_repository.dart';

/// Use case for updating an existing habit
class UpdateHabitUseCase implements UseCase<HabitEntity, UpdateHabitParams> {
  final HabitRepository repository;

  UpdateHabitUseCase(this.repository);

  @override
  Future<Either<Failure, HabitEntity>> call(UpdateHabitParams params) async {
    // Basic validation
    if (params.name != null && params.name!.isEmpty) {
      return const Left(ValidationFailure(message: 'Habit name cannot be empty'));
    }

    // Validate custom days if frequency is custom
    if (params.frequency == HabitFrequency.custom &&
        (params.customDays == null || params.customDays!.isEmpty)) {
      return const Left(
        ValidationFailure(
          message: 'Custom frequency requires at least one day to be selected',
        ),
      );
    }

    // Update habit
    return repository.updateHabit(
      id: params.id,
      name: params.name,
      description: params.description,
      frequency: params.frequency,
      customDays: params.customDays,
      reminderSettings: params.reminderSettings,
    );
  }
}

/// Parameters for UpdateHabitUseCase
class UpdateHabitParams extends Equatable {
  final String id;
  final String? name;
  final String? description;
  final HabitFrequency? frequency;
  final List<int>? customDays;
  final Map<String, dynamic>? reminderSettings;

  const UpdateHabitParams({
    required this.id,
    this.name,
    this.description,
    this.frequency,
    this.customDays,
    this.reminderSettings,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    frequency,
    customDays,
    reminderSettings,
  ];
}
