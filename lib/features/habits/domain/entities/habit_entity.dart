import 'package:equatable/equatable.dart';

/// Frequency options for habits
enum HabitFrequency { daily, weekdays, weekends, weekly, monthly, custom }

/// Domain entity for a habit in the application
class HabitEntity extends Equatable {
  final String id;
  final String name;
  final String? description;
  final HabitFrequency frequency;
  final List<int>?
  customDays; // For custom frequency (e.g., [1, 3, 5] for Mon, Wed, Fri)
  final int currentStreak;
  final int longestStreak;
  final Map<String, bool>?
  completionHistory; // Map of dates to completion status
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? reminderSettings;

  const HabitEntity({
    required this.id,
    required this.name,
    this.description,
    required this.frequency,
    this.customDays,
    required this.currentStreak,
    required this.longestStreak,
    this.completionHistory,
    required this.createdAt,
    required this.updatedAt,
    this.reminderSettings,
  });

  /// Creates a copy of this HabitEntity with the given fields replaced with the new values
  HabitEntity copyWith({
    String? id,
    String? name,
    String? description,
    HabitFrequency? frequency,
    List<int>? customDays,
    int? currentStreak,
    int? longestStreak,
    Map<String, bool>? completionHistory,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? reminderSettings,
  }) {
    return HabitEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      frequency: frequency ?? this.frequency,
      customDays: customDays ?? this.customDays,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      completionHistory: completionHistory ?? this.completionHistory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      reminderSettings: reminderSettings ?? this.reminderSettings,
    );
  }

  /// Mark a habit as complete for today
  HabitEntity markAsCompleteToday() {
    final today = _getTodayDateString();
    final newHistory = Map<String, bool>.from(completionHistory ?? {});

    // Check if already completed today
    if (newHistory[today] == true) {
      return this; // Already completed, no changes needed
    }

    newHistory[today] = true;

    // Calculate new streak based on completion history
    int newStreak = _calculateStreakFromHistory(newHistory);
    int newLongestStreak = longestStreak;

    // Update longest streak if new streak is higher
    if (newStreak > longestStreak) {
      newLongestStreak = newStreak;
    }

    return copyWith(
      completionHistory: newHistory,
      currentStreak: newStreak,
      longestStreak: newLongestStreak,
      updatedAt: DateTime.now(),
    );
  }

  /// Check if habit is due today
  bool get isDueToday {
    final now = DateTime.now();
    return _shouldCountForFrequency(now);
  }

  /// Check if habit was completed today
  bool get isCompletedToday {
    final today = _getTodayDateString();
    return completionHistory?[today] ?? false;
  }

  /// Get completion rate as a percentage
  double get completionRate {
    if (completionHistory == null || completionHistory!.isEmpty) return 0.0;

    final totalDays = completionHistory!.length;
    final completedDays = completionHistory!.values.where((v) => v).length;

    return (completedDays / totalDays) * 100;
  }

  /// Helper to format a DateTime as YYYY-MM-DD
  String _formatDate(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Calculate current streak from completion history
  int _calculateStreakFromHistory(Map<String, bool> history) {
    if (history.isEmpty) return 0;

    final today = DateTime.now();
    int streak = 0;

    // Check backwards from today to find consecutive completed days
    // that match the habit's frequency
    for (int i = 0; i < 365; i++) {
      final checkDate = today.subtract(Duration(days: i));
      final dateString = _formatDate(checkDate);

      // Check if this date should be counted based on frequency
      if (_shouldCountForFrequency(checkDate)) {
        if (history[dateString] == true) {
          streak++;
        } else {
          break; // Streak is broken
        }
      }
    }

    return streak;
  }

  /// Check if a given date should be counted for this habit's frequency
  bool _shouldCountForFrequency(DateTime date) {
    final weekday = date.weekday; // 1-7, where 1 is Monday

    switch (frequency) {
      case HabitFrequency.daily:
        return true;
      case HabitFrequency.weekdays:
        return weekday >= 1 && weekday <= 5;
      case HabitFrequency.weekends:
        return weekday > 5;
      case HabitFrequency.weekly:
        // For weekly, only count if it's the same weekday as creation
        return date.weekday == createdAt.weekday;
      case HabitFrequency.monthly:
        // For monthly, only count if it's the same day of month as creation
        return date.day == createdAt.day;
      case HabitFrequency.custom:
        return customDays?.contains(weekday) ?? false;
    }
  }

  /// Helper method to get today's date in a consistent format
  String _getTodayDateString() {
    return _formatDate(DateTime.now());
  }

  /// Whether this habit is currently active (not archived or deleted)
  bool get isActive =>
      true; // Default to true as there's no archive feature yet

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    frequency,
    customDays,
    currentStreak,
    longestStreak,
    completionHistory,
    createdAt,
    updatedAt,
    reminderSettings,
  ];
}
