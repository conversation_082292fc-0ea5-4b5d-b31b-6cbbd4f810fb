import 'package:dartz/dartz.dart';
import 'package:power_up/core/domain/repositories/repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';

/// Repository interface for habit management
abstract class HabitRepository extends Repository {
  /// Create a new habit
  /// Returns Either [Failure] or the created [HabitEntity]
  Future<Either<Failure, HabitEntity>> createHabit({
    required String name,
    String? description,
    required HabitFrequency frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  });

  /// Get all habits
  /// Returns Either [Failure] or a list of [HabitEntity]
  Future<Either<Failure, List<HabitEntity>>> getHabits();

  /// Get today's habits
  /// Returns Either [Failure] or a list of [HabitEntity] due today
  Future<Either<Failure, List<HabitEntity>>> getTodaysHabits();

  /// Get a habit by ID
  /// Returns Either [Failure] or the [HabitEntity]
  Future<Either<Failure, HabitEntity>> getHabitById(String id);

  /// Update a habit
  /// Returns Either [Failure] or the updated [HabitEntity]
  Future<Either<Failure, HabitEntity>> updateHabit({
    required String id,
    String? name,
    String? description,
    HabitFrequency? frequency,
    List<int>? customDays,
    Map<String, dynamic>? reminderSettings,
  });

  /// Delete a habit
  /// Returns Either [Failure] or a [bool] indicating success
  Future<Either<Failure, bool>> deleteHabit(String id);

  /// Mark a habit as complete for today
  /// Returns Either [Failure] or the updated [HabitEntity]
  Future<Either<Failure, HabitEntity>> markHabitComplete(String id);

  /// Get habit statistics (streaks, completion rates)
  /// Returns Either [Failure] or a map of statistics
  Future<Either<Failure, Map<String, dynamic>>> getHabitStatistics(String id);
}
