import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/core/routes/app_routes.dart';

/// Bottom sheet that displays coaching suggestions for a specific habit
class HabitSuggestionsBottomSheet extends StatelessWidget {
  final HabitEntity habit;
  final List<CoachingSuggestionEntity> suggestions;

  const HabitSuggestionsBottomSheet({
    super.key,
    required this.habit,
    required this.suggestions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Filter suggestions to only include ones related to this habit
    final habitSuggestions =
        suggestions.where((s) => s.relatedHabitId == habit.id).toList();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Header
          Row(
            children: [
              Icon(Icons.psychology, color: theme.primaryColor, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Coaching for "${habit.name}"',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (habitSuggestions.isEmpty)
            _buildEmptyState(theme, context)
          else
            ..._buildSuggestionsList(habitSuggestions, theme),

          const SizedBox(height: 16),

          // See all suggestions button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                Get.toNamed(AppRoutes.coachingSuggestions);
              },
              icon: const Icon(Icons.list_alt),
              label: const Text('See all my coaching suggestions'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme, BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 20),
        Icon(Icons.emoji_objects_outlined, size: 64, color: Colors.grey[400]),
        const SizedBox(height: 16),
        Text(
          'No coaching suggestions yet',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Keep tracking this habit for personalized coaching',
          style: theme.textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  List<Widget> _buildSuggestionsList(
    List<CoachingSuggestionEntity> habitSuggestions,
    ThemeData theme,
  ) {
    return [
      Text('Personalized suggestions:', style: theme.textTheme.titleMedium),
      const SizedBox(height: 12),
      ...habitSuggestions.map(
        (suggestion) => _buildSuggestionItem(suggestion, theme),
      ),
    ];
  }

  Widget _buildSuggestionItem(
    CoachingSuggestionEntity suggestion,
    ThemeData theme,
  ) {
    // Choose icon based on suggestion type
    final icons = {
      'insight': Icons.lightbulb_outline,
      'recommendation': Icons.stars,
      'tip': Icons.tips_and_updates,
    };
    final icon = icons[suggestion.type] ?? Icons.psychology;

    // Choose background color based on priority
    final colors = {
      'high': Colors.red.shade50,
      'medium': Colors.orange.shade50,
      'low': Colors.blue.shade50,
    };
    final bgColor = colors[suggestion.priority] ?? Colors.grey.shade50;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: theme.primaryColor),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  suggestion.suggestionText,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (suggestion.impactArea.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Impact area: ${suggestion.impactArea[0].toUpperCase()}${suggestion.impactArea.substring(1)}',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
