import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/habits/presentation/controllers/habit_controller.dart';

class AddHabitBottomSheet extends GetView<HabitController> {
  final bool isEditing;

  const AddHabitBottomSheet({Key? key, this.isEditing = false})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);

    return Padding(
      // Add padding for keyboard
      padding: EdgeInsets.only(
        bottom: mediaQuery.viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              isEditing ? 'Edit Habit' : 'Create New Habit',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // Habit Name
            TextField(
              controller: controller.nameController,
              decoration: InputDecoration(
                labelText: 'Habit Name',
                hintText: 'e.g., Morning Exercise',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Habit Description
            TextField(
              controller: controller.descriptionController,
              decoration: InputDecoration(
                labelText: 'Description (optional)',
                hintText: 'e.g., 30 minutes workout',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Frequency Selection
            Text(
              'Frequency',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            Obx(
              () => Column(
                children: [
                  // Frequency Options
                  _buildFrequencyOption(theme, HabitFrequency.daily, 'Daily'),
                  _buildFrequencyOption(
                    theme,
                    HabitFrequency.weekdays,
                    'Weekdays (Mon-Fri)',
                  ),
                  _buildFrequencyOption(
                    theme,
                    HabitFrequency.weekends,
                    'Weekends (Sat-Sun)',
                  ),
                  _buildFrequencyOption(theme, HabitFrequency.weekly, 'Weekly'),
                  _buildFrequencyOption(
                    theme,
                    HabitFrequency.monthly,
                    'Monthly',
                  ),
                  _buildFrequencyOption(
                    theme,
                    HabitFrequency.custom,
                    'Custom Days',
                  ),

                  // Custom Days Selection
                  if (controller.selectedFrequency.value ==
                      HabitFrequency.custom)
                    Padding(
                      padding: const EdgeInsets.only(left: 32.0, top: 8.0),
                      child: _buildCustomDaysSelector(theme),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Reminders Section
            Text(
              'Reminders',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            Obx(
              () => SwitchListTile(
                title: const Text('Enable Reminders'),
                value: controller.hasReminders.value,
                onChanged: (value) {
                  controller.hasReminders.value = value;
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),

            // Reminder Settings
            Obx(
              () =>
                  controller.hasReminders.value
                      ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Time Selection
                          ListTile(
                            contentPadding: EdgeInsets.zero,
                            title: const Text('Reminder Time'),
                            subtitle: Text(
                              _formatTimeOfDay(controller.reminderTime.value),
                            ),
                            trailing: const Icon(Icons.access_time),
                            onTap: () async {
                              final pickedTime = await showTimePicker(
                                context: context,
                                initialTime: controller.reminderTime.value,
                              );
                              if (pickedTime != null) {
                                controller.setReminderTime(pickedTime);
                              }
                            },
                          ),

                          // Days Selection
                          const Text('Reminder Days'),
                          const SizedBox(height: 8),
                          _buildReminderDaysSelector(theme),
                        ],
                      )
                      : const SizedBox.shrink(),
            ),

            const SizedBox(height: 32),

            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  bool success;
                  if (isEditing) {
                    success = await controller.updateHabit();
                  } else {
                    success = await controller.createHabit();
                  }
                  if (success) {
                    Get.back();
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(isEditing ? 'Update Habit' : 'Create Habit'),
              ),
            ),

            // Cancel Button
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () {
                  controller.clearForm();
                  Get.back();
                },
                child: const Text('Cancel'),
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildFrequencyOption(
    ThemeData theme,
    HabitFrequency frequency,
    String label,
  ) {
    return Obx(
      () => RadioListTile<HabitFrequency>(
        title: Text(label),
        value: frequency,
        groupValue: controller.selectedFrequency.value,
        onChanged: (value) {
          if (value != null) {
            controller.selectedFrequency.value = value;
          }
        },
        contentPadding: EdgeInsets.zero,
        dense: true,
      ),
    );
  }

  Widget _buildCustomDaysSelector(ThemeData theme) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return Wrap(
      spacing: 8,
      children: List.generate(7, (index) {
        final day = index + 1; // 1 = Monday, 7 = Sunday

        return Obx(
          () => GestureDetector(
            onTap: () => controller.toggleCustomDay(day),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    controller.selectedCustomDays.contains(day)
                        ? theme.colorScheme.primary
                        : Colors.transparent,
                border: Border.all(
                  color:
                      controller.selectedCustomDays.contains(day)
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline,
                ),
              ),
              child: Center(
                child: Text(
                  days[index],
                  style: TextStyle(
                    color:
                        controller.selectedCustomDays.contains(day)
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildReminderDaysSelector(ThemeData theme) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return Wrap(
      spacing: 8,
      children: List.generate(7, (index) {
        return Obx(
          () => GestureDetector(
            onTap: () => controller.toggleReminderDay(index),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    controller.reminderDays[index]
                        ? theme.colorScheme.primary
                        : Colors.transparent,
                border: Border.all(
                  color:
                      controller.reminderDays[index]
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline,
                ),
              ),
              child: Center(
                child: Text(
                  days[index],
                  style: TextStyle(
                    color:
                        controller.reminderDays[index]
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
