import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/core/presentation/controllers/base_controller.dart';
import 'package:power_up/core/services/auth_service.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/habits/domain/usecases/create_habit_usecase.dart';
import 'package:power_up/features/habits/domain/usecases/get_habits_usecase.dart';
import 'package:power_up/features/habits/domain/usecases/mark_habit_complete_usecase.dart';
import 'package:power_up/features/habits/domain/usecases/update_habit_usecase.dart';
import 'package:power_up/features/gamification/domain/usecases/process_gamification_event_usecase.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/podcasts/presentation/controllers/podcast_controller.dart';

/// Controller for managing habit-related operations
class HabitController extends BaseController {
  // Use cases
  final CreateHabitUseCase _createHabitUseCase;
  final GetHabitsUseCase _getHabitsUseCase;
  final GetTodaysHabitsUseCase _getTodaysHabitsUseCase;
  final UpdateHabitUseCase _updateHabitUseCase;
  final MarkHabitCompleteUseCase _markHabitCompleteUseCase;
  final ProcessGamificationEventUseCase _processGamificationEventUseCase;
  final AuthService _authService;

  // Observable state
  final RxList<HabitEntity> allHabits = <HabitEntity>[].obs;
  final RxList<HabitEntity> todayHabits = <HabitEntity>[].obs;

  // Habit filter
  final Rx<HabitFrequency?> frequencyFilter = Rx<HabitFrequency?>(null);

  // Form controllers for creating/editing habits
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  // Selected habit for editing
  final Rx<HabitEntity?> selectedHabit = Rx<HabitEntity?>(null);

  // Create new habit form values
  final Rx<HabitFrequency> selectedFrequency = HabitFrequency.daily.obs;
  final RxList<int> selectedCustomDays = <int>[].obs;
  final RxBool hasReminders = false.obs;
  final Rx<TimeOfDay> reminderTime = TimeOfDay.now().obs;
  final RxList<bool> reminderDays = List.generate(7, (_) => false).obs;

  HabitController({
    required CreateHabitUseCase createHabitUseCase,
    required GetHabitsUseCase getHabitsUseCase,
    required GetTodaysHabitsUseCase getTodaysHabitsUseCase,
    required UpdateHabitUseCase updateHabitUseCase,
    required MarkHabitCompleteUseCase markHabitCompleteUseCase,
    required ProcessGamificationEventUseCase processGamificationEventUseCase,
  }) : _createHabitUseCase = createHabitUseCase,
       _getHabitsUseCase = getHabitsUseCase,
       _getTodaysHabitsUseCase = getTodaysHabitsUseCase,
       _updateHabitUseCase = updateHabitUseCase,
       _markHabitCompleteUseCase = markHabitCompleteUseCase,
       _processGamificationEventUseCase = processGamificationEventUseCase,
       _authService = Get.find<AuthService>();

  @override
  void onInit() {
    super.onInit();
    fetchAllHabits();
    fetchTodayHabits();
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  /// Fetch all habits
  Future<void> fetchAllHabits() async {
    setLoading(true);

    final result = await _getHabitsUseCase(NoParams());
    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message);
      },
      (habits) {
        allHabits.assignAll(habits);
      },
    );

    setLoading(false);
  }

  /// Fetch today's habits
  Future<void> fetchTodayHabits() async {
    setLoading(true);

    final result = await _getTodaysHabitsUseCase(NoParams());
    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message);
      },
      (habits) {
        todayHabits.assignAll(habits);
      },
    );

    setLoading(false);
  }

  /// Create a new habit
  Future<bool> createHabit() async {
    setLoading(true);

    // Prepare reminder settings if enabled
    Map<String, dynamic>? reminderSettingsMap;
    if (hasReminders.value) {
      reminderSettingsMap = {
        'enabled': true,
        'time': '${reminderTime.value.hour}:${reminderTime.value.minute}',
        'days':
            List.generate(
              7,
              (i) => reminderDays[i] ? i + 1 : null,
            ).where((day) => day != null).toList(),
      };
    }

    final params = CreateHabitParams(
      name: nameController.text.trim(),
      description: descriptionController.text.trim(),
      frequency: selectedFrequency.value,
      customDays:
          selectedFrequency.value == HabitFrequency.custom
              ? selectedCustomDays
              : null,
      reminderSettings: reminderSettingsMap,
    );

    final result = await _createHabitUseCase(params);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message);
        setLoading(false);
        return false;
      },
      (habit) {
        allHabits.add(habit);
        if (habit.isDueToday) {
          todayHabits.add(habit);
        }

        // Clear form
        clearForm();

        showSnackbar('Success', 'Habit created successfully');
        setLoading(false);
        return true;
      },
    );
  }

  /// Mark a habit as complete for today
  Future<void> markHabitComplete(String habitId) async {
    final params = HabitIdParams(id: habitId);
    final result = await _markHabitCompleteUseCase(params);

    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message);
      },
      (completedHabit) async {
        // Update habit in lists
        final allIndex = allHabits.indexWhere((habit) => habit.id == habitId);
        if (allIndex != -1) {
          allHabits[allIndex] = completedHabit;
        }

        final todayIndex = todayHabits.indexWhere(
          (habit) => habit.id == habitId,
        );
        if (todayIndex != -1) {
          todayHabits[todayIndex] = completedHabit;
        }

        // Process gamification event
        await _processGamificationEvent(habitId);

        showSnackbar('Success', 'Habit marked as complete for today');
      },
    );
  }

  /// Select a habit for editing
  void selectHabitForEdit(HabitEntity habit) {
    selectedHabit.value = habit;
    nameController.text = habit.name;
    descriptionController.text = habit.description ?? '';
    selectedFrequency.value = habit.frequency;

    if (habit.frequency == HabitFrequency.custom && habit.customDays != null) {
      selectedCustomDays.assignAll(habit.customDays!);
    } else {
      selectedCustomDays.clear();
    }

    // Set reminder settings
    if (habit.reminderSettings != null &&
        habit.reminderSettings!['enabled'] == true) {
      hasReminders.value = true;

      // Parse time string (HH:MM)
      final timeString = habit.reminderSettings!['time'] as String? ?? '08:00';
      final timeParts = timeString.split(':');
      if (timeParts.length == 2) {
        reminderTime.value = TimeOfDay(
          hour: int.parse(timeParts[0]),
          minute: int.parse(timeParts[1]),
        );
      }

      // Set days
      final days = habit.reminderSettings!['days'] as List? ?? [];
      for (int i = 0; i < 7; i++) {
        reminderDays[i] = days.contains(i + 1);
      }
    } else {
      hasReminders.value = false;
      reminderTime.value = TimeOfDay.now();
      reminderDays.value = List.generate(7, (_) => false);
    }
  }

  /// Clear form and selected habit
  void clearForm() {
    selectedHabit.value = null;
    nameController.clear();
    descriptionController.clear();
    selectedFrequency.value = HabitFrequency.daily;
    selectedCustomDays.clear();
    hasReminders.value = false;
    reminderTime.value = TimeOfDay.now();
    reminderDays.value = List.generate(7, (_) => false);
  }

  /// Update an existing habit
  Future<bool> updateHabit() async {
    if (selectedHabit.value == null) return false;

    setLoading(true);

    // Prepare reminder settings if enabled
    Map<String, dynamic>? reminderSettingsMap;
    if (hasReminders.value) {
      reminderSettingsMap = {
        'enabled': true,
        'time': '${reminderTime.value.hour}:${reminderTime.value.minute}',
        'days':
            List.generate(
              7,
              (i) => reminderDays[i] ? i + 1 : null,
            ).where((day) => day != null).toList(),
      };
    }

    final params = UpdateHabitParams(
      id: selectedHabit.value!.id,
      name: nameController.text.trim(),
      description: descriptionController.text.trim(),
      frequency: selectedFrequency.value,
      customDays:
          selectedFrequency.value == HabitFrequency.custom
              ? selectedCustomDays
              : null,
      reminderSettings: reminderSettingsMap,
    );

    final result = await _updateHabitUseCase(params);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message);
        setLoading(false);
        return false;
      },
      (updatedHabit) {
        // Update habit in lists
        final allIndex = allHabits.indexWhere(
          (habit) => habit.id == updatedHabit.id,
        );
        if (allIndex != -1) {
          allHabits[allIndex] = updatedHabit;
        }

        // Check if habit is due today
        if (updatedHabit.isDueToday) {
          final todayIndex = todayHabits.indexWhere(
            (habit) => habit.id == updatedHabit.id,
          );
          if (todayIndex != -1) {
            todayHabits[todayIndex] = updatedHabit;
          } else {
            todayHabits.add(updatedHabit);
          }
        } else {
          // Remove from today's habits if no longer due today
          todayHabits.removeWhere((habit) => habit.id == updatedHabit.id);
        }

        clearForm();

        showSnackbar('Success', 'Habit updated successfully');
        setLoading(false);
        return true;
      },
    );
  }

  /// Toggle day selection for custom frequency
  void toggleCustomDay(int day) {
    if (selectedCustomDays.contains(day)) {
      selectedCustomDays.remove(day);
    } else {
      selectedCustomDays.add(day);
    }
  }

  /// Toggle reminder day
  void toggleReminderDay(int dayIndex) {
    reminderDays[dayIndex] = !reminderDays[dayIndex];
  }

  /// Set reminder time
  void setReminderTime(TimeOfDay time) {
    reminderTime.value = time;
  }

  /// Get habit completion stats
  Map<String, dynamic> getCompletionStats() {
    if (todayHabits.isEmpty) {
      return {'completed': 0, 'total': 0, 'percentage': 0.0};
    }

    final completed =
        todayHabits.where((habit) => habit.isCompletedToday).length;
    final total = todayHabits.length;
    final percentage = (completed / total * 100).round();

    return {'completed': completed, 'total': total, 'percentage': percentage};
  }

  /// Process gamification event for habit completion
  Future<void> _processGamificationEvent(String habitId) async {
    final userId = _authService.currentUserId;
    if (userId == null) return;

    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.habitCompleted,
      relatedId: habitId,
      eventDate: DateTime.now(),
    );

    final result = await _processGamificationEventUseCase(params);
    result.fold(
      (failure) {
        // Log error but don't show to user as this is background processing
        print('Gamification event processing failed: ${failure.message}');
      },
      (eventResult) {
        // Handle gamification results - could show achievements UI
        if (eventResult.hasAchievements) {
          _showGamificationResults(eventResult);
        }
      },
    );
  }

  /// Show gamification achievement results to user
  void _showGamificationResults(GamificationEventResult result) {
    String message = '';

    if (result.xpGained > 0) {
      message += '+${result.xpGained} XP earned! ';
    }

    if (result.hasLeveledUp) {
      message += 'Level up! You are now level ${result.newLevel}! ';
    }

    if (result.badgesEarned.isNotEmpty) {
      message += '${result.badgesEarned.length} new badge(s) earned! ';
    }

    if (message.isNotEmpty) {
      showSnackbar('Achievement!', message.trim());
    }
  }

  /// Check for missed habits and trigger a podcast if any are found
  Future<void> checkMissedHabitsAndTriggerPodcast() async {
    // Find habits due today but not completed
    final missedHabits =
        todayHabits.where((h) => h.isDueToday && !h.isCompletedToday).toList();
    if (missedHabits.isEmpty) return;

    // Get PodcastController
    final podcastController =
        Get.isRegistered<PodcastController>()
            ? Get.find<PodcastController>()
            : null;
    if (podcastController == null) return;

    // Trigger podcast with topic/mood for overcoming setbacks
    try {
      await podcastController.generateNewPodcast(
        topics: ["overcoming setbacks", "motivation after missing habits"],
        style: "encouragement",
        duration: 300,
      );
      showSnackbar(
        'Motivation',
        'You missed some habits today. Listen to a quick podcast to get back on track!',
      );
    } catch (e) {
      // Optionally log or show error
      showSnackbar('Error', 'Could not generate motivational podcast.');
    }
  }

  /// Show snackbar with message
  @override
  void showSnackbar(String title, String message, {bool isError = false}) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: title == 'Error' ? Colors.red[100] : Colors.green[100],
      colorText: title == 'Error' ? Colors.red[900] : Colors.green[900],
      margin: const EdgeInsets.all(8),
    );
  }
}
