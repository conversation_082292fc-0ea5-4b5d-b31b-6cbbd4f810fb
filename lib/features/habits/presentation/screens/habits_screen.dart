import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../controllers/habit_controller.dart';
import '../../domain/entities/habit_entity.dart';
import '../widgets/add_habit_bottom_sheet.dart';
import '../../../gamification/presentation/controllers/gamification_controller.dart';
import '../../../gamification/presentation/widgets/streak_display_widget.dart';
import '../../../gamification/presentation/screens/achievements_screen.dart';

class HabitsScreen extends GetView<HabitController> {
  const HabitsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return MainLayoutScreen(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Text(
              'Your Habits',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Build better habits, one day at a time',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),

            const SizedBox(height: 16),

            // Progress Overview
            Obx(() {
              final stats = controller.getCompletionStats();
              final completed = stats['completed'] as int;
              final total = stats['total'] as num;
              final percentage = stats['percentage'] as num;

              return Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: theme.colorScheme.primary.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.trending_up,
                      color: theme.colorScheme.primary,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Today\'s Progress',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '$completed out of $total habits completed',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '$percentage%',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              );
            }),

            const SizedBox(height: 16),

            // Gamification Overview
            _buildGamificationOverview(context, theme),

            const SizedBox(height: 16),

            // Today's Habits
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Today\'s Habits',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.add, color: theme.colorScheme.primary),
                  onPressed: () {
                    _showAddHabitBottomSheet(context);
                  },
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Habit List
            Expanded(
              child: Obx(() {
                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.todayHabits.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.calendar_today_outlined,
                          size: 64,
                          color: theme.colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No habits for today',
                          style: theme.textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Tap + to add a new habit',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  );
                }

                return Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        itemCount: controller.todayHabits.length,
                        itemBuilder: (context, index) {
                          final habit = controller.todayHabits[index];
                          return _buildHabitCard(context, habit: habit);
                        },
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 64,
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: controller.nameController,
                              decoration: InputDecoration(
                                hintText: 'Type a habit...',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(24),
                                  borderSide: BorderSide(
                                    color: theme.colorScheme.outline.withAlpha(
                                      80,
                                    ),
                                  ),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            icon: Icon(
                              Icons.send,
                              color: theme.colorScheme.primary,
                            ),
                            onPressed: controller.createHabit,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHabitCard(BuildContext context, {required HabitEntity habit}) {
    final theme = Theme.of(context);
    final isCompleted = habit.isCompletedToday;
    final streak = habit.currentStreak;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isCompleted
                  ? theme.colorScheme.primary.withValues(alpha: 0.3)
                  : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Completion Checkbox
          GestureDetector(
            onTap: () {
              if (!isCompleted) {
                controller.markHabitComplete(habit.id);
              }
            },
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color:
                    isCompleted
                        ? theme.colorScheme.primary
                        : Colors.transparent,
                border: Border.all(
                  color:
                      isCompleted
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child:
                  isCompleted
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
            ),
          ),

          const SizedBox(width: 16),

          // Habit Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  habit.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
                Text(
                  habit.description ?? _getFrequencyText(habit.frequency),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          // Actions
          Row(
            children: [
              // Edit button
              IconButton(
                icon: Icon(
                  Icons.edit_outlined,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                onPressed: () {
                  controller.selectHabitForEdit(habit);
                  _showAddHabitBottomSheet(context, isEditing: true);
                },
              ),

              // Streak Info with Animation
              AnimatedStreakCounter(streak: streak),
            ],
          ),
        ],
      ),
    );
  }

  String _getFrequencyText(HabitFrequency frequency) {
    switch (frequency) {
      case HabitFrequency.daily:
        return 'Every day';
      case HabitFrequency.weekdays:
        return 'Weekdays (Mon-Fri)';
      case HabitFrequency.weekends:
        return 'Weekends (Sat-Sun)';
      case HabitFrequency.weekly:
        return 'Once a week';
      case HabitFrequency.monthly:
        return 'Once a month';
      case HabitFrequency.custom:
        return 'Custom schedule';
    }
  }

  void _showAddHabitBottomSheet(
    BuildContext context, {
    bool isEditing = false,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => AddHabitBottomSheet(isEditing: isEditing),
    );
  }

  /// Build gamification overview widget
  Widget _buildGamificationOverview(BuildContext context, ThemeData theme) {
    final gamificationController = Get.find<GamificationController>();

    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.1),
              theme.colorScheme.secondary.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: theme.colorScheme.primary.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          children: [
            // Header with Achievements button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Your Progress',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: () => Get.to(() => const AchievementsScreen()),
                  icon: const Icon(Icons.emoji_events, size: 18),
                  label: const Text('Achievements'),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Stats Row
            Row(
              children: [
                // Level & XP
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Level',
                    '${gamificationController.currentLevel}',
                    Icons.star,
                    Colors.amber,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                ),
                // Current XP
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total XP',
                    '${gamificationController.currentXP}',
                    Icons.bolt,
                    Colors.blue,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: theme.colorScheme.outline.withValues(alpha: 0.3),
                ),
                // Longest Streak
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Best Streak',
                    '${gamificationController.longestCurrentStreak}',
                    Icons.local_fire_department,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// Build individual stat item
  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}
