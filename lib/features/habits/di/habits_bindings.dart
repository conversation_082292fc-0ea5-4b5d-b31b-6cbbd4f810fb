import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/habits/data/datasources/habit_local_data_source.dart';
import 'package:power_up/features/habits/data/datasources/habit_local_data_source_impl.dart';
import 'package:power_up/features/habits/data/datasources/habit_remote_data_source.dart';
import 'package:power_up/features/habits/data/datasources/habit_remote_data_source_impl.dart';
import 'package:power_up/features/habits/data/repositories/habit_repository_impl.dart';
import 'package:power_up/features/habits/domain/repositories/habit_repository.dart';
import 'package:power_up/features/habits/domain/usecases/create_habit_usecase.dart';
import 'package:power_up/features/habits/domain/usecases/get_habits_usecase.dart';
import 'package:power_up/features/habits/domain/usecases/mark_habit_complete_usecase.dart';
import 'package:power_up/features/habits/domain/usecases/update_habit_usecase.dart';
import 'package:power_up/features/habits/presentation/controllers/habit_controller.dart';
import 'package:power_up/features/gamification/domain/usecases/process_gamification_event_usecase.dart';

/// Bindings for habit feature
class HabitsBindings implements Bindings {
  @override
  void dependencies() {
    // Core dependencies
    final dio = Get.find<Dio>();
    final networkInfo = Get.find<NetworkInfo>();
    final storageService = Get.find<StorageService>();

    // Data sources
    Get.lazyPut<HabitLocalDataSource>(
      () => HabitLocalDataSourceImpl(storageService: storageService),
    );
    Get.lazyPut<HabitRemoteDataSource>(
      () => HabitRemoteDataSourceImpl(dio: dio),
    );

    // Repository
    Get.lazyPut<HabitRepository>(
      () => HabitRepositoryImpl(
        remoteDataSource: Get.find<HabitRemoteDataSource>(),
        localDataSource: Get.find<HabitLocalDataSource>(),
        networkInfo: networkInfo,
      ),
    );

    // Use cases
    Get.lazyPut(() => CreateHabitUseCase(Get.find<HabitRepository>()));
    Get.lazyPut(() => GetHabitsUseCase(Get.find<HabitRepository>()));
    Get.lazyPut(() => GetTodaysHabitsUseCase(Get.find<HabitRepository>()));
    Get.lazyPut(() => UpdateHabitUseCase(Get.find<HabitRepository>()));
    Get.lazyPut(() => MarkHabitCompleteUseCase(Get.find<HabitRepository>()));

    // Controller
    Get.lazyPut<HabitController>(
      () => HabitController(
        createHabitUseCase: Get.find<CreateHabitUseCase>(),
        getHabitsUseCase: Get.find<GetHabitsUseCase>(),
        getTodaysHabitsUseCase: Get.find<GetTodaysHabitsUseCase>(),
        updateHabitUseCase: Get.find<UpdateHabitUseCase>(),
        markHabitCompleteUseCase: Get.find<MarkHabitCompleteUseCase>(),
        processGamificationEventUseCase:
            Get.find<ProcessGamificationEventUseCase>(),
      ),
    );
  }
}
