import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'dart:io';

/// Service to handle social authentication with different providers
class SocialAuthService {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;

  SocialAuthService({
    required FirebaseAuth firebaseAuth,
    required GoogleSignIn googleSignIn,
  }) : _firebaseAuth = firebaseAuth,
       _googleSignIn = googleSignIn;

  /// Sign in with Google and return the id token
  Future<String> signInWithGoogle() async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        throw AuthException(
          message: 'Google sign-in was cancelled',
          statusCode: 400,
        );
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Once signed in, return the id token
      final userCredential = await _firebaseAuth.signInWithCredential(
        credential,
      );
      final idToken = await userCredential.user?.getIdToken() ?? '';

      if (idToken.isEmpty) {
        throw AuthException(
          message: 'Failed to get ID token from Firebase',
          statusCode: 401,
        );
      }

      return idToken;
    } catch (e) {
      if (e is FirebaseAuthException) {
        throw AuthException(
          message: 'Firebase Auth Error: ${e.message}',
          statusCode: e.code == 'user-not-found' ? 404 : 400,
        );
      } else if (e is AuthException) {
        rethrow;
      } else {
        throw AuthException(
          message: 'Failed to sign in with Google: ${e.toString()}',
          statusCode: 500,
        );
      }
    }
  }

  /// Sign in with Apple and return the id token
  Future<String> signInWithApple() async {
    // Check if the platform is iOS
    if (!Platform.isIOS && !Platform.isMacOS) {
      throw AuthException(
        message: 'Sign in with Apple is only available on iOS and macOS',
        statusCode: 400,
      );
    }

    try {
      // Request credentials for the user
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create an OAuthProvider credential for Firebase
      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in with the credential
      final userCredential = await _firebaseAuth.signInWithCredential(
        oauthCredential,
      );
      final idToken = await userCredential.user?.getIdToken() ?? '';

      if (idToken.isEmpty) {
        throw AuthException(
          message: 'Failed to get ID token from Firebase',
          statusCode: 401,
        );
      }

      // Make sure we have a name (Apple doesn't always provide it)
      final user = userCredential.user;
      if (user != null &&
          (user.displayName == null || user.displayName!.isEmpty)) {
        // If we have the Apple provided name, update the Firebase user
        if (appleCredential.givenName != null &&
            appleCredential.familyName != null) {
          await user.updateDisplayName(
            "${appleCredential.givenName} ${appleCredential.familyName}",
          );
        }
      }

      return idToken;
    } catch (e) {
      if (e is SignInWithAppleAuthorizationException) {
        throw AuthException(
          message: 'Apple Sign In: ${e.message}',
          statusCode: e.code == AuthorizationErrorCode.canceled ? 400 : 401,
        );
      } else if (e is FirebaseAuthException) {
        throw AuthException(
          message: 'Firebase Auth Error: ${e.message}',
          statusCode: e.code == 'user-not-found' ? 404 : 400,
        );
      } else if (e is AuthException) {
        rethrow;
      } else {
        throw AuthException(
          message: 'Failed to sign in with Apple: ${e.toString()}',
          statusCode: 500,
        );
      }
    }
  }

  /// Sign out from social providers
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
    } catch (e) {
      throw AuthException(
        message: 'Failed to sign out: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
