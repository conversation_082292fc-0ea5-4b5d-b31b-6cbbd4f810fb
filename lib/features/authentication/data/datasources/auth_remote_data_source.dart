import 'package:power_up/features/authentication/data/models/auth_response_model.dart';
import 'package:power_up/features/authentication/data/models/user_model.dart';

/// Abstract class defining remote data source operations for authentication
abstract class AuthRemoteDataSource {
  /// Register a new user with email and password
  Future<AuthResponseModel> registerUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  });

  /// Login with email and password
  Future<AuthResponseModel> loginWithEmailPassword({
    required String email,
    required String password,
  });

  /// Login with social providers (Google, Apple) via Firebase
  Future<AuthResponseModel> loginWithSocialProvider({
    required String idToken,
    String? provider,
  });

  /// Request password reset for user's email
  Future<bool> resetPassword({required String email});

  /// Logout current user
  Future<bool> logout();

  /// Get current user data
  Future<UserModel> getCurrentUser();

  /// Update user profile
  Future<UserModel> updateProfile({String? firstName, String? lastName});

  /// Check if user is authenticated
  Future<bool> isAuthenticated();
}
