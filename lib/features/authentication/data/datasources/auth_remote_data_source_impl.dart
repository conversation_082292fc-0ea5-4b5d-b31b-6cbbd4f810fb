import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/core/util/app_constants.dart';
import 'package:power_up/features/authentication/data/datasources/auth_remote_data_source.dart';
import 'package:power_up/features/authentication/data/models/auth_response_model.dart';
import 'package:power_up/features/authentication/data/models/user_model.dart';
import 'package:power_up/features/authentication/data/services/social_auth_service.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/core/services/secure_storage_service.dart';

/// Implementation of [AuthRemoteDataSource] using Dio and Firebase Auth
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final Dio dio;
  final firebase_auth.FirebaseAuth firebaseAuth;
  final NetworkInfo networkInfo;
  final SocialAuthService? socialAuthService;
  final StorageService storageService;
  final SecureStorageService secureStorageService;

  /// Auth token storage
  String? _accessToken;

  /// Access token getter
  String? get accessToken => _accessToken;

  AuthRemoteDataSourceImpl({
    required this.dio,
    required this.firebaseAuth,
    required this.networkInfo,
    required this.storageService,
    required this.secureStorageService,
    this.socialAuthService,
  });

  /// Load saved authentication data from storage
  Future<void> _loadSavedAuthData() async {
    // Try secure storage first, then fall back to regular storage
    String? savedToken = await secureStorageService.getAccessToken();

    if (savedToken == null || savedToken.isEmpty) {
      savedToken = storageService.getData<String>(
        AppConstants.storageUserToken,
      );
    }

    if (savedToken != null && savedToken.isNotEmpty) {
      _accessToken = savedToken;
      // Set auth token for requests
      dio.options.headers['Authorization'] = 'Bearer $_accessToken';
    }
  }

  /// Save authentication data to persistent storage
  Future<void> _saveAuthData(AuthResponseModel authResponse) async {
    _accessToken = authResponse.accessToken;

    // Save token and user data to persistent storage
    await Future.wait([
      // Save to regular storage
      storageService.setData(
        AppConstants.storageUserToken,
        authResponse.accessToken,
      ),
      // Save to secure storage for API calls
      secureStorageService.storeAccessToken(authResponse.accessToken),
      if (authResponse.user != null) ...[
        storageService.setData(
          AppConstants.storageUserId,
          authResponse.user!.id,
        ),
        storageService.setData(
          AppConstants.storageUserEmail,
          authResponse.user!.email,
        ),
        storageService.setData(
          AppConstants.storageUserProfile,
          authResponse.user!.toJson(),
        ),
        // Save user ID to secure storage as well
        secureStorageService.storeUserId(authResponse.user!.id),
        secureStorageService.storeEmail(authResponse.user!.email),
      ],
    ]);

    // Set auth token for future requests
    dio.options.headers['Authorization'] = 'Bearer $_accessToken';
  }

  /// Register a new user with email and password via API
  @override
  Future<AuthResponseModel> registerUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final response = await dio.post(
        '/auth/register',
        data: {
          'email': email,
          'password': password,
          'firstName': firstName,
          'lastName': lastName,
        },
      );

      final authResponse = AuthResponseModel.fromJson(response.data);
      await _saveAuthData(authResponse);

      return authResponse;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Failed to register user: ${e.toString()}',
      );
    }
  }

  /// Login with email and password via API
  @override
  Future<AuthResponseModel> loginWithEmailPassword({
    required String email,
    required String password,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final response = await dio.post(
        '/auth/login',
        data: {'email': email, 'password': password},
      );

      final authResponse = AuthResponseModel.fromJson(response.data);
      await _saveAuthData(authResponse);

      return authResponse;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(message: 'Failed to login: ${e.toString()}');
    }
  }

  /// Login with social providers (Google, Apple) via Firebase and backend API
  @override
  Future<AuthResponseModel> loginWithSocialProvider({
    required String idToken,
    String? provider,
  }) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    // If a provider is specified (google, apple) and socialAuthService is available, use it to get the token
    String tokenToUse = idToken;
    if (socialAuthService != null && provider != null) {
      try {
        if (provider.toLowerCase() == 'google') {
          tokenToUse = await socialAuthService!.signInWithGoogle();
        } else if (provider.toLowerCase() == 'apple') {
          tokenToUse = await socialAuthService!.signInWithApple();
        }
      } catch (e) {
        if (e is AuthException) {
          rethrow;
        } else {
          throw AuthException(
            message: 'Failed to authenticate with $provider: ${e.toString()}',
            statusCode: 401,
          );
        }
      }
    }

    try {
      final response = await dio.post(
        '/auth/social-login',
        data: {
          'idToken': tokenToUse,
          if (provider != null) 'provider': provider,
        },
      );

      final authResponse = AuthResponseModel.fromJson(response.data);
      await _saveAuthData(authResponse);

      return authResponse;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Failed to login with social provider: ${e.toString()}',
      );
    }
  }

  /// Request password reset for user's email using Firebase Auth
  @override
  Future<bool> resetPassword({required String email}) async {
    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      await firebaseAuth.sendPasswordResetEmail(email: email);
      return true;
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw _handleFirebaseAuthException(e);
    } catch (e) {
      throw ServerException(
        message: 'Failed to reset password: ${e.toString()}',
      );
    }
  }

  /// Logout current user from Firebase and clear local token
  @override
  Future<bool> logout() async {
    try {
      await firebaseAuth.signOut();
      _accessToken = null;
      dio.options.headers.remove('Authorization');

      // Clear stored auth data from both storage systems
      await Future.wait([
        storageService.removeData(AppConstants.storageUserToken),
        storageService.removeData(AppConstants.storageUserId),
        storageService.removeData(AppConstants.storageUserEmail),
        storageService.removeData(AppConstants.storageUserProfile),
        secureStorageService.clearAll(),
      ]);

      return true;
    } catch (e) {
      throw ServerException(message: 'Failed to logout: ${e.toString()}');
    }
  }

  /// Get current user data from API
  @override
  Future<UserModel> getCurrentUser() async {
    // Try to load saved token if we don't have one in memory
    if (_accessToken == null) {
      await _loadSavedAuthData();
    }

    if (_accessToken == null) {
      throw AuthException(message: 'User not authenticated', statusCode: 401);
    }

    try {
      final response = await dio.get('/users/profile');
      final value = response.data;
      return UserModel.fromJson(value);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Failed to get current user: ${e.toString()}',
      );
    }
  }

  /// Update user profile
  @override
  Future<UserModel> updateProfile({String? firstName, String? lastName}) async {
    // Try to load saved token if we don't have one in memory
    if (_accessToken == null) {
      await _loadSavedAuthData();
    }

    if (_accessToken == null) {
      throw AuthException(message: 'User not authenticated', statusCode: 401);
    }

    if (!await networkInfo.isConnected) {
      throw NetworkException(message: 'No internet connection');
    }

    try {
      final data = <String, dynamic>{};
      if (firstName != null) data['firstName'] = firstName;
      if (lastName != null) data['lastName'] = lastName;

      final response = await dio.put('/users/profile', data: data);
      final updatedUser = UserModel.fromJson(response.data);

      // Update stored user data
      await storageService.setData(
        AppConstants.storageUserProfile,
        updatedUser.toJson(),
      );

      return updatedUser;
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Failed to update profile: ${e.toString()}',
      );
    }
  }

  /// Check if user is authenticated by verifying token existence
  @override
  Future<bool> isAuthenticated() async {
    // First check if we have a token in memory
    if (_accessToken != null && firebaseAuth.currentUser != null) {
      return true;
    }

    // If not, try to load from storage
    await _loadSavedAuthData();

    // Check again after loading
    return _accessToken != null && firebaseAuth.currentUser != null;
  }

  /// Handle Dio exceptions and convert them to our domain exceptions
  Exception _handleDioException(DioException e) {
    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout ||
        e.type == DioExceptionType.sendTimeout) {
      return NetworkException(message: 'Connection timeout');
    } else if (e.type == DioExceptionType.connectionError) {
      return NetworkException(message: 'Connection error');
    }

    final statusCode = e.response?.statusCode;
    final responseData = e.response?.data;

    if (statusCode == 401 || statusCode == 403) {
      return AuthException(
        message:
            responseData is Map
                ? responseData['message']
                : 'Authentication error',
        statusCode: statusCode,
      );
    } else if (statusCode == 400) {
      return ValidationException(
        message:
            responseData is Map ? responseData['message'] : 'Validation error',
      );
    } else {
      return ServerException(
        message: responseData is Map ? responseData['message'] : 'Server error',
        statusCode: statusCode,
      );
    }
  }

  /// Handle Firebase Auth exceptions and convert them to our domain exceptions
  Exception _handleFirebaseAuthException(
    firebase_auth.FirebaseAuthException e,
  ) {
    switch (e.code) {
      case 'user-not-found':
      case 'wrong-password':
        return AuthException(
          message: 'Invalid email or password',
          statusCode: 401,
        );
      case 'email-already-in-use':
        return ValidationException(message: 'Email already in use');
      case 'weak-password':
        return ValidationException(message: 'Password is too weak');
      case 'invalid-email':
        return ValidationException(message: 'Email is invalid');
      case 'user-disabled':
        return AuthException(
          message: 'User account has been disabled',
          statusCode: 403,
        );
      case 'too-many-requests':
        return ServerException(
          message: 'Too many requests, try again later',
          statusCode: 429,
        );
      default:
        return ServerException(
          message: 'Authentication error: ${e.message}',
          statusCode: 500,
        );
    }
  }
}
