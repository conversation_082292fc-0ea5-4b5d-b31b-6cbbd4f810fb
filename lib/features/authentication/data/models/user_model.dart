import 'package:power_up/features/authentication/domain/entities/user_entity.dart';

/// Data model for User that extends UserEntity domain class
/// Used for serialization/deserialization
class UserModel extends UserEntity {
  const UserModel({
    required String id,
    required String email,
    required String firstName,
    required String lastName,
    String? profileImage,
    int? xp,
    List<String>? badges,
    String? provider,
    String? picture,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : super(
         id: id,
         email: email,
         firstName: firstName,
         lastName: lastName,
         profileImage: profileImage,
         xp: xp,
         badges: badges,
         provider: provider,
         picture: picture,
         createdAt: createdAt,
         updatedAt: updatedAt,
       );

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      profileImage: json['profileImage'] as String?,
      xp: json['xp'] as int? ?? 0,
      badges:
          json['badges'] != null
              ? List<String>.from(json['badges'] as List)
              : [],
      provider: json['provider'] as String?,
      picture: json['picture'] as String?,
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'] as String)
              : null,
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : null,
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'profileImage': profileImage,
      'xp': xp,
      'badges': badges,
      'provider': provider,
      'picture': picture,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    }..removeWhere((key, value) => value == null);
  }

  /// Create a copy of this UserModel with modified fields
  @override
  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? profileImage,
    int? xp,
    List<String>? badges,
    String? provider,
    String? picture,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      profileImage: profileImage ?? this.profileImage,
      xp: xp ?? this.xp,
      badges: badges ?? this.badges,
      provider: provider ?? this.provider,
      picture: picture ?? this.picture,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
