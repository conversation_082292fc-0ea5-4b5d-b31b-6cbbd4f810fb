import 'package:power_up/features/authentication/data/models/user_model.dart';

/// Data model for authentication responses from the API
class AuthResponseModel {
  final String accessToken;
  final UserModel? user;

  AuthResponseModel({required this.accessToken, required this.user});

  /// Create AuthResponseModel from JSON
  factory AuthResponseModel.fromJson(Map<String, dynamic> json) {
    return AuthResponseModel(
      accessToken: json['access_token'] as String,
      user:
          json['user'] == null
              ? null
              : UserModel.fromJson(json['user'] as Map<String, dynamic>),
    );
  }

  /// Convert AuthResponseModel to JSON
  Map<String, dynamic> toJson() {
    return {'access_token': accessToken, 'user': user?.toJson()};
  }
}
