import 'package:get/get.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/authentication/domain/entities/user_entity.dart';
import 'package:power_up/features/authentication/domain/usecases/get_current_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/login_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/logout_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/register_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/reset_password_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/social_login_usecase.dart';

/// Controller for handling authentication operations using GetX
class AuthController extends GetxController {
  final LoginUserUseCase loginUserUseCase;
  final RegisterUserUseCase registerUserUseCase;
  final SocialLoginUseCase socialLoginUseCase;
  final ResetPasswordUseCase resetPasswordUseCase;
  final LogoutUserUseCase logoutUserUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;

  AuthController({
    required this.loginUserUseCase,
    required this.registerUserUseCase,
    required this.socialLoginUseCase,
    required this.resetPasswordUseCase,
    required this.logoutUserUseCase,
    required this.getCurrentUserUseCase,
  });

  // Observable state variables
  final Rx<UserEntity?> _currentUser = Rx<UserEntity?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxBool _isAuthenticated = false.obs;

  // Getters for the state
  UserEntity? get currentUser => _currentUser.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  bool get isAuthenticated => _isAuthenticated.value;

  @override
  void onInit() {
    super.onInit();
    // In tests, we'll mock the auth status directly
    // In production, we'll check it here
    if (!Get.testMode) {
      initializeAuth();
    }
  }

  /// Initialize authentication state when app starts
  Future<void> initializeAuth() async {
    await checkAuthStatus();
  }

  /// Check if user is authenticated and fetch current user
  Future<void> checkAuthStatus() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final result = await getCurrentUserUseCase(NoParams());
    result.fold(
      (failure) {
        _isAuthenticated.value = false;
        _currentUser.value = null;
        if (failure is! AuthFailure) {
          _errorMessage.value = failure.message;
        }
      },
      (user) {
        _isAuthenticated.value = true;
        _currentUser.value = user;
      },
    );

    _isLoading.value = false;
  }

  /// Register a new user
  Future<bool> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = RegisterParams(
      email: email,
      password: password,
      firstName: firstName,
      lastName: lastName,
    );

    final result = await registerUserUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (user) {
        _currentUser.value = user;
        _isAuthenticated.value = true;
        _isLoading.value = false;
        return true;
      },
    );
  }

  /// Login with email and password
  Future<bool> login({required String email, required String password}) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = LoginParams(email: email, password: password);
    final result = await loginUserUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (user) {
        _currentUser.value = user;
        _isAuthenticated.value = true;
        _isLoading.value = false;
        return true;
      },
    );
  }

  /// Login with social provider
  Future<bool> socialLogin({required String idToken, String? provider}) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = SocialLoginParams(idToken: idToken, provider: provider);
    final result = await socialLoginUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (user) {
        _currentUser.value = user;
        _isAuthenticated.value = true;
        _isLoading.value = false;
        return true;
      },
    );
  }

  /// Reset password with email
  Future<bool> resetPassword({required String email}) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = ResetPasswordParams(email: email);
    final result = await resetPasswordUseCase(params);

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (success) {
        _isLoading.value = false;
        return success;
      },
    );
  }

  /// Logout current user
  Future<bool> logout() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final result = await logoutUserUseCase(NoParams());

    return result.fold(
      (failure) {
        _errorMessage.value = failure.message;
        _isLoading.value = false;
        return false;
      },
      (success) {
        if (success) {
          _currentUser.value = null;
          _isAuthenticated.value = false;
        }
        _isLoading.value = false;
        return success;
      },
    );
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }
}
