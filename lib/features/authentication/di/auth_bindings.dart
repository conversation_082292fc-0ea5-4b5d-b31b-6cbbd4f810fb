import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:get/get.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/authentication/data/datasources/auth_remote_data_source.dart';
import 'package:power_up/features/authentication/data/datasources/auth_remote_data_source_impl.dart';
import 'package:power_up/features/authentication/data/repositories/auth_repository_impl.dart';
import 'package:power_up/features/authentication/data/services/social_auth_service.dart';
import 'package:power_up/features/authentication/domain/repositories/auth_repository.dart';
import 'package:power_up/features/authentication/domain/usecases/get_current_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/login_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/logout_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/register_user_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/reset_password_usecase.dart';
import 'package:power_up/features/authentication/domain/usecases/social_login_usecase.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/core/services/secure_storage_service.dart';
import 'package:power_up/features/authentication/presentation/controllers/auth_controller.dart';

/// Dependency injection binding for authentication module
class AuthBindings extends Bindings {
  @override
  void dependencies() {
    // Services
    Get.lazyPut(
      () => SocialAuthService(
        firebaseAuth: FirebaseAuth.instance,
        googleSignIn: GoogleSignIn(),
      ),
    );

    // Data sources
    Get.lazyPut<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(
        dio: Get.find<Dio>(),
        firebaseAuth: FirebaseAuth.instance,
        networkInfo: Get.find<NetworkInfo>(),
        storageService: Get.find<StorageService>(),
        secureStorageService: Get.find<SecureStorageService>(),
        socialAuthService: Get.find<SocialAuthService>(),
      ),
    );

    // Repositories
    Get.lazyPut<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: Get.find<AuthRemoteDataSource>(),
      ),
    );

    // Use cases
    Get.lazyPut(() => LoginUserUseCase(Get.find<AuthRepository>()));
    Get.lazyPut(() => RegisterUserUseCase(Get.find<AuthRepository>()));
    Get.lazyPut(() => SocialLoginUseCase(Get.find<AuthRepository>()));
    Get.lazyPut(() => LogoutUserUseCase(Get.find<AuthRepository>()));
    Get.lazyPut(() => ResetPasswordUseCase(Get.find<AuthRepository>()));
    Get.lazyPut(() => GetCurrentUserUseCase(Get.find<AuthRepository>()));

    // Controllers
    Get.lazyPut(
      () => AuthController(
        loginUserUseCase: Get.find<LoginUserUseCase>(),
        registerUserUseCase: Get.find<RegisterUserUseCase>(),
        socialLoginUseCase: Get.find<SocialLoginUseCase>(),
        resetPasswordUseCase: Get.find<ResetPasswordUseCase>(),
        logoutUserUseCase: Get.find<LogoutUserUseCase>(),
        getCurrentUserUseCase: Get.find<GetCurrentUserUseCase>(),
      ),
    );
  }
}
