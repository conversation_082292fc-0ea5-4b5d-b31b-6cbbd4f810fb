import 'package:equatable/equatable.dart';

/// Domain entity for a user in the application
class UserEntity extends Equatable {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? profileImage;
  final int? xp;
  final List<String>? badges;
  final String? provider;
  final String? picture;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserEntity({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.profileImage,
    this.xp,
    this.badges,
    this.provider,
    this.picture,
    this.createdAt,
    this.updatedAt,
  });

  /// Full name derived from firstName and lastName
  String get fullName => '$firstName $lastName';

  /// Check if user has a profile image
  bool get hasProfileImage => profileImage != null && profileImage!.isNotEmpty;

  /// Check if user has a picture
  bool get hasPicture => picture != null && picture!.isNotEmpty;

  /// Get the display image URL (prefer picture over profileImage)
  String? get displayImageUrl => picture ?? profileImage;

  /// Get user level based on XP (simple calculation)
  int get level => ((xp ?? 0) / 100).floor() + 1;

  @override
  List<Object?> get props => [
    id,
    email,
    firstName,
    lastName,
    profileImage,
    xp,
    badges,
    provider,
    picture,
    createdAt,
    updatedAt,
  ];

  /// Create a copy of this UserEntity with modified fields
  UserEntity copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? profileImage,
    int? xp,
    List<String>? badges,
    String? provider,
    String? picture,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserEntity(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      profileImage: profileImage ?? this.profileImage,
      xp: xp ?? this.xp,
      badges: badges ?? this.badges,
      provider: provider ?? this.provider,
      picture: picture ?? this.picture,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
