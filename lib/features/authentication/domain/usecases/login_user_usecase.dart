import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/authentication/domain/entities/user_entity.dart';
import 'package:power_up/features/authentication/domain/repositories/auth_repository.dart';

/// Use case for logging in a user with email and password
class LoginUserUseCase implements UseCase<UserEntity, LoginParams> {
  final AuthRepository repository;

  LoginUserUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(LoginParams params) async {
    // Input validation
    if (params.email.isEmpty) {
      return const Left(ValidationFailure(message: 'Email cannot be empty'));
    }
    if (params.password.isEmpty) {
      return const Left(ValidationFailure(message: 'Password cannot be empty'));
    }

    // Basic email format validation
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(params.email)) {
      return const Left(ValidationFailure(message: 'Invalid email format'));
    }

    // Call repository to log in the user
    return repository.loginWithEmailPassword(
      email: params.email,
      password: params.password,
    );
  }
}

/// Parameters for LoginUserUseCase
class LoginParams extends Equatable {
  final String email;
  final String password;

  const LoginParams({required this.email, required this.password});

  @override
  List<Object?> get props => [email, password];
}
