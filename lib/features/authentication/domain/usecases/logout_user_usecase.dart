import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/authentication/domain/repositories/auth_repository.dart';

/// Use case for logging out a user
class LogoutUserUseCase implements UseCase<bool, NoParams> {
  final AuthRepository repository;

  LogoutUserUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(NoParams params) {
    // Call repository to log out the user
    return repository.logout();
  }
}
