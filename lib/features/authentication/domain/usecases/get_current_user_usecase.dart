import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/authentication/domain/entities/user_entity.dart';
import 'package:power_up/features/authentication/domain/repositories/auth_repository.dart';

/// Use case for getting the currently logged in user
class GetCurrentUserUseCase implements UseCase<UserEntity, NoParams> {
  final AuthRepository repository;

  GetCurrentUserUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(NoParams params) {
    // Call repository to get current user
    return repository.getCurrentUser();
  }
}
