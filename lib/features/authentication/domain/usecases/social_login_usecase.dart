import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/authentication/domain/entities/user_entity.dart';
import 'package:power_up/features/authentication/domain/repositories/auth_repository.dart';

/// Use case for logging in a user with social providers
class SocialLoginUseCase implements UseCase<UserEntity, SocialLoginParams> {
  final AuthRepository repository;

  SocialLoginUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(SocialLoginParams params) async {
    // Input validation
    if (params.idToken.isEmpty) {
      return const Left(ValidationFailure(message: 'ID token cannot be empty'));
    }

    // Call repository to log in the user with social provider
    return repository.loginWithSocialProvider(
      idToken: params.idToken,
      provider: params.provider,
    );
  }
}

/// Parameters for SocialLoginUseCase
class SocialLoginParams extends Equatable {
  final String idToken;
  final String? provider; // 'google' or 'apple'

  const SocialLoginParams({required this.idToken, this.provider});

  @override
  List<Object?> get props => [idToken, provider];
}
