import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/authentication/domain/entities/user_entity.dart';
import 'package:power_up/features/authentication/domain/repositories/auth_repository.dart';

/// Use case for registering a new user with email and password
class RegisterUserUseCase implements UseCase<UserEntity, RegisterParams> {
  final AuthRepository repository;

  RegisterUserUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(RegisterParams params) async {
    // Input validation
    if (params.email.isEmpty) {
      return const Left(ValidationFailure(message: 'Email cannot be empty'));
    }
    if (params.password.isEmpty) {
      return const Left(ValidationFailure(message: 'Password cannot be empty'));
    }
    if (params.firstName.isEmpty) {
      return const Left(ValidationFailure(message: 'First name cannot be empty'));
    }
    if (params.lastName.isEmpty) {
      return const Left(ValidationFailure(message: 'Last name cannot be empty'));
    }

    // Basic email format validation
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(params.email)) {
      return const Left(ValidationFailure(message: 'Invalid email format'));
    }

    // Basic password strength validation
    if (params.password.length < 6) {
      return const Left(
        ValidationFailure(message: 'Password must be at least 6 characters'),
      );
    }

    // Call repository to register the user
    return repository.registerUser(
      email: params.email,
      password: params.password,
      firstName: params.firstName,
      lastName: params.lastName,
    );
  }
}

/// Parameters for RegisterUserUseCase
class RegisterParams extends Equatable {
  final String email;
  final String password;
  final String firstName;
  final String lastName;

  const RegisterParams({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
  });

  @override
  List<Object?> get props => [email, password, firstName, lastName];
}
