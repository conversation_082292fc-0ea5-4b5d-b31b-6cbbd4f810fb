import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/authentication/domain/entities/user_entity.dart';

/// Auth repository interface defining authentication operations
abstract class AuthRepository {
  /// Register a new user with email and password
  /// Returns Either [AuthFailure] or [UserEntity] with authentication token
  Future<Either<Failure, UserEntity>> registerUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  });

  /// Login with email and password
  /// Returns Either [AuthFailure] or [UserEntity] with authentication token
  Future<Either<Failure, UserEntity>> loginWithEmailPassword({
    required String email,
    required String password,
  });

  /// Login with social providers (Google, Apple) via Firebase
  /// Returns Either [AuthFailure] or [UserEntity] with authentication token
  Future<Either<Failure, UserEntity>> loginWithSocialProvider({
    required String idToken,
    String? provider, // 'google', 'apple', etc.
  });

  /// Request password reset for a user's email
  /// Returns Either [Failure] or [bool] indicating success
  Future<Either<Failure, bool>> resetPassword({required String email});

  /// Logout the current user
  /// Returns Either [Failure] or [bool] indicating success
  Future<Either<Failure, bool>> logout();

  /// Get the current authenticated user if any
  /// Returns Either [AuthFailure] or [UserEntity]
  Future<Either<Failure, UserEntity>> getCurrentUser();

  /// Update user profile
  /// Returns Either [Failure] or [UserEntity] with updated data
  Future<Either<Failure, UserEntity>> updateProfile({
    String? firstName,
    String? lastName,
  });

  /// Check if user is authenticated
  /// Returns [bool] indicating if user is authenticated
  Future<bool> isAuthenticated();
}
