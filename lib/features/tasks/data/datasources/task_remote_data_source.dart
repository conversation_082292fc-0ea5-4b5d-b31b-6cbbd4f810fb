import 'package:power_up/core/data/remote_datasource.dart';
import 'package:power_up/features/tasks/data/models/task_model.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';

/// Definition for the remote data source for tasks
abstract class TaskRemoteDataSource extends RemoteDataSource {
  /// Create a new task
  Future<TaskModel> createTask({
    required String title,
    String? description,
    required DateTime dueDate,
    required TaskPriority priority,
  });

  /// Get all tasks with optional filtering
  Future<List<TaskModel>> getTasks({
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
    TaskPriority? priority,
  });

  /// Get a task by ID
  Future<TaskModel> getTaskById(String id);

  /// Update a task
  Future<TaskModel> updateTask({
    required String id,
    String? title,
    String? description,
    DateTime? dueDate,
    TaskPriority? priority,
  });

  /// Delete a task
  Future<bool> deleteTask(String id);

  /// Mark a task as complete
  Future<TaskModel> markTaskComplete(String id);

  /// Mark a task as incomplete
  Future<TaskModel> markTaskIncomplete(String id);
}
