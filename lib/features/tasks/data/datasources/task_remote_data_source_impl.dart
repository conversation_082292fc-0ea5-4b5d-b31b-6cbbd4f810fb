import 'package:dio/dio.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/tasks/data/datasources/task_remote_data_source.dart';
import 'package:power_up/features/tasks/data/models/task_model.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';

/// Implementation of the TaskRemoteDataSource
class TaskRemoteDataSourceImpl implements TaskRemoteDataSource {
  final Dio dio;

  TaskRemoteDataSourceImpl({required this.dio});

  @override
  Future<void> init() async {
    // No initialization needed for remote data source
  }

  Future<void> clearData() async {
    // No local data to clear
  }

  @override
  Future<TaskModel> createTask({
    required String title,
    String? description,
    required DateTime dueDate,
    required TaskPriority priority,
  }) async {
    try {
      final response = await dio.post(
        '/tasks',
        data: {
          'title': title,
          'description': description,
          'dueDate': dueDate.toIso8601String(),
          'priority': _priorityToString(priority),
        },
      );

      return TaskModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to create task',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<List<TaskModel>> getTasks({
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
    TaskPriority? priority,
  }) async {
    try {
      final queryParameters = <String, dynamic>{};

      if (isCompleted != null) {
        queryParameters['isCompleted'] = isCompleted;
      }

      if (startDate != null) {
        queryParameters['startDate'] = startDate.toIso8601String();
      }

      if (endDate != null) {
        queryParameters['endDate'] = endDate.toIso8601String();
      }

      if (priority != null) {
        queryParameters['priority'] = _priorityToString(priority);
      }

      final response = await dio.get(
        '/tasks',
        queryParameters: queryParameters,
      );

      final List<dynamic> taskList = response.data;
      return taskList.map((json) => TaskModel.fromJson(json)).toList();
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get tasks',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<TaskModel> getTaskById(String id) async {
    try {
      final response = await dio.get('/tasks/$id');
      return TaskModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get task',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<TaskModel> updateTask({
    required String id,
    String? title,
    String? description,
    DateTime? dueDate,
    TaskPriority? priority,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (title != null) data['title'] = title;
      if (description != null) data['description'] = description;
      if (dueDate != null) data['dueDate'] = dueDate.toIso8601String();
      if (priority != null) data['priority'] = _priorityToString(priority);

      final response = await dio.put('/tasks/$id', data: data);

      return TaskModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to update task',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<bool> deleteTask(String id) async {
    try {
      await dio.delete('/tasks/$id');
      return true;
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to delete task',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<TaskModel> markTaskComplete(String id) async {
    try {
      final response = await dio.post('/tasks/$id/complete');
      return TaskModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to mark task as complete',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<TaskModel> markTaskIncomplete(String id) async {
    try {
      // This API endpoint might not exist in the Swagger doc, but adding it for completeness
      final response = await dio.post('/tasks/$id/incomplete');
      return TaskModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to mark task as incomplete',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<T> request<T>({
    required String endpoint,
    required T Function(Map<String, dynamic> json) fromJson,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    String method = 'GET',
    Map<String, String>? headers,
  }) async {
    try {
      Response response;

      switch (method) {
        case 'GET':
          response = await dio.get(
            endpoint,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'POST':
          response = await dio.post(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'PUT':
          response = await dio.put(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'DELETE':
          response = await dio.delete(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        default:
          throw ServerException(message: 'Unsupported HTTP method: $method');
      }

      return fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Request failed',
        statusCode: e.response?.statusCode,
      );
    }
  }

  /// Helper method to convert priority enum to string
  String _priorityToString(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return 'high';
      case TaskPriority.medium:
        return 'medium';
      case TaskPriority.low:
        return 'low';
    }
  }

  @override
  Future<void> dispose() async {
    // No cleanup needed for remote data source
  }
}
