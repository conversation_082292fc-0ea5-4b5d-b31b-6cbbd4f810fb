import 'package:power_up/core/data/local_datasource.dart';
import 'package:power_up/features/tasks/data/models/task_model.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';

/// Definition for the local data source for tasks
abstract class TaskLocalDataSource extends LocalDataSource {
  /// Save a task to local storage
  Future<void> saveTask(TaskModel task);

  /// Save multiple tasks to local storage
  Future<void> saveTasks(List<TaskModel> tasks);

  /// Get all tasks from local storage with optional filtering
  Future<List<TaskModel>> getTasks({
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
    TaskPriority? priority,
  });

  /// Get a task by ID from local storage
  Future<TaskModel?> getTaskById(String id);

  /// Delete a task from local storage
  Future<void> deleteTask(String id);

  /// Delete all tasks from local storage
  Future<void> deleteAllTasks();

  /// Get tasks that need syncing with the server
  Future<List<TaskModel>> getUnsyncedTasks();

  /// Mark a task as synced
  Future<void> markTaskSynced(String id);

  /// Get tasks that only exist locally (with temporary IDs)
  Future<List<TaskModel>> getLocalOnlyTasks();

  /// Get tasks that have been modified locally
  Future<List<TaskModel>> getModifiedTasks();
}
