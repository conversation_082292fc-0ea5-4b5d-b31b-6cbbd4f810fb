import 'dart:convert';

import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/tasks/data/datasources/task_local_data_source.dart';
import 'package:power_up/features/tasks/data/models/task_model.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';

/// Implementation of TaskLocalDataSource using StorageService
class TaskLocalDataSourceImpl implements TaskLocalDataSource {
  static const String _tasksKey = 'tasks';
  static const String _unsyncedTasksKey = 'unsynced_tasks';
  static const String _modifiedTasksKey = 'modified_tasks';

  late final StorageService _storageService;
  bool _isInitialized = false;

  TaskLocalDataSourceImpl({StorageService? storageService}) {
    if (storageService != null) {
      _storageService = storageService;
      _isInitialized = true;
    }
  }

  @override
  Future<void> init() async {
    if (!_isInitialized) {
      _storageService = await StorageService.getInstance();
      _isInitialized = true;
    }
  }

  @override
  Future<void> dispose() async {
    // No resources to dispose for StorageService
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await init();
    }
  }

  @override
  Future<T?> get<T>({
    required String key,
    required T Function(dynamic json) fromJson,
  }) async {
    await _ensureInitialized();
    final data = _storageService.getData(key);
    return data != null ? fromJson(data) : null;
  }

  @override
  Future<void> set<T>({required String key, required T data}) async {
    await _ensureInitialized();
    await _storageService.setData(key, data);
  }

  @override
  Future<void> remove(String key) async {
    await _ensureInitialized();
    await _storageService.removeData(key);
  }

  @override
  Future<void> clear() async {
    await _ensureInitialized();
    await _storageService.removeData(_tasksKey);
    await _storageService.removeData(_unsyncedTasksKey);
    await _storageService.removeData(_modifiedTasksKey);
  }

  Future<void> clearData() async {
    await clear();
  }

  @override
  Future<void> saveTask(TaskModel task) async {
    try {
      await _ensureInitialized();

      // Get existing tasks
      final tasks = await getTasks();

      // Find if task already exists
      final existingTaskIndex = tasks.indexWhere((t) => t.id == task.id);

      if (existingTaskIndex >= 0) {
        // Update existing task
        tasks[existingTaskIndex] = task;
        // Add to modified tasks if it was updated
        await _addToModifiedTasks(task);
      } else {
        // Add new task
        tasks.add(task);
      }

      // Save updated list
      final tasksJson = tasks.map((t) => t.toJson()).toList();
      await _storageService.setData(_tasksKey, jsonEncode(tasksJson));

      // Add to unsynced tasks if it has a temporary ID
      if (task.id.startsWith('temp_')) {
        await _addToUnsyncedTasks(task);
      }
    } catch (e) {
      throw CacheException(message: 'Failed to save task: $e');
    }
  }

  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    try {
      await _ensureInitialized();

      // Get existing tasks
      final existingTasks = await getTasks();
      // Create a map for faster lookup
      final existingTasksMap = {for (var task in existingTasks) task.id: task};

      // Update or add tasks
      for (final task in tasks) {
        existingTasksMap[task.id] = task;
      }

      // Convert back to list and save
      final allTasks = existingTasksMap.values.toList();
      final tasksJson = allTasks.map((t) => t.toJson()).toList();
      await _storageService.setData(_tasksKey, tasksJson);

      // Add unsynced tasks (those with temporary IDs)
      for (final task in tasks) {
        if (task.id.startsWith('temp_')) {
          await _addToUnsyncedTasks(task);
        }
      }
    } catch (e) {
      throw CacheException(message: 'Failed to save tasks: $e');
    }
  }

  @override
  Future<List<TaskModel>> getTasks({
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
    TaskPriority? priority,
  }) async {
    try {
      await _ensureInitialized();

      final List<dynamic> tasksJson = _storageService.getData(_tasksKey) ?? [];

      List<TaskModel> tasks =
          tasksJson
              .map(
                (json) => TaskModel.fromJson(Map<String, dynamic>.from(json)),
              )
              .toList();

      // Apply filters
      if (isCompleted != null) {
        tasks = tasks.where((task) => task.isCompleted == isCompleted).toList();
      }

      if (startDate != null) {
        tasks =
            tasks.where((task) {
              // Compare dates only (ignore time)
              final taskDueDay = DateTime(
                task.dueDate.year,
                task.dueDate.month,
                task.dueDate.day,
              );
              final filterStartDay = DateTime(
                startDate.year,
                startDate.month,
                startDate.day,
              );
              return taskDueDay.isAfter(filterStartDay) ||
                  taskDueDay.isAtSameMomentAs(filterStartDay);
            }).toList();
      }

      if (endDate != null) {
        tasks =
            tasks.where((task) {
              // Compare dates only (ignore time)
              final taskDueDay = DateTime(
                task.dueDate.year,
                task.dueDate.month,
                task.dueDate.day,
              );
              final filterEndDay = DateTime(
                endDate.year,
                endDate.month,
                endDate.day,
              );
              return taskDueDay.isBefore(filterEndDay) ||
                  taskDueDay.isAtSameMomentAs(filterEndDay);
            }).toList();
      }

      if (priority != null) {
        tasks = tasks.where((task) => task.priority == priority).toList();
      }

      return tasks;
    } catch (e) {
      throw CacheException(message: 'Failed to get tasks: $e');
    }
  }

  @override
  Future<TaskModel?> getTaskById(String id) async {
    try {
      await _ensureInitialized();

      final tasks = await getTasks();
      try {
        return tasks.firstWhere((task) => task.id == id);
      } catch (e) {
        return null; // Task not found
      }
    } catch (e) {
      throw CacheException(message: 'Failed to get task by ID: $e');
    }
  }

  @override
  Future<void> deleteTask(String id) async {
    try {
      await _ensureInitialized();

      final tasks = await getTasks();
      tasks.removeWhere((task) => task.id == id);

      final tasksJson = tasks.map((t) => t.toJson()).toList();
      await _storageService.setData(_tasksKey, tasksJson);

      // Remove from unsynced and modified lists
      await _removeFromUnsyncedTasks(id);
      await _removeFromModifiedTasks(id);
    } catch (e) {
      throw CacheException(message: 'Failed to delete task: $e');
    }
  }

  @override
  Future<void> deleteAllTasks() async {
    try {
      await _ensureInitialized();

      await _storageService.removeData(_tasksKey);
      await _storageService.removeData(_unsyncedTasksKey);
      await _storageService.removeData(_modifiedTasksKey);
    } catch (e) {
      throw CacheException(message: 'Failed to delete all tasks: $e');
    }
  }

  @override
  Future<List<TaskModel>> getUnsyncedTasks() async {
    try {
      await _ensureInitialized();

      final unsyncedIds =
          _storageService.getData<List<dynamic>>(_unsyncedTasksKey) ?? [];
      final tasks = await getTasks();

      return tasks
          .where(
            (task) =>
                unsyncedIds.contains(task.id) || task.id.startsWith('temp_'),
          )
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get unsynced tasks: $e');
    }
  }

  @override
  Future<void> markTaskSynced(String id) async {
    try {
      await _ensureInitialized();

      await _removeFromUnsyncedTasks(id);
      await _removeFromModifiedTasks(id);

      // Note: TaskModel doesn't have isSynced property,
      // so we just remove from unsynced/modified lists
    } catch (e) {
      throw CacheException(message: 'Failed to mark task as synced: $e');
    }
  }

  @override
  Future<List<TaskModel>> getLocalOnlyTasks() async {
    try {
      await _ensureInitialized();

      final tasks = await getTasks();
      return tasks.where((task) => task.id.startsWith('temp_')).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get local-only tasks: $e');
    }
  }

  @override
  Future<List<TaskModel>> getModifiedTasks() async {
    try {
      await _ensureInitialized();

      final modifiedIds =
          _storageService.getData<List<dynamic>>(_modifiedTasksKey) ?? [];
      final tasks = await getTasks();

      return tasks.where((task) => modifiedIds.contains(task.id)).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get modified tasks: $e');
    }
  }

  // Helper methods
  Future<void> _addToUnsyncedTasks(TaskModel task) async {
    final unsyncedIds =
        _storageService.getData<List<dynamic>>(_unsyncedTasksKey) ?? [];
    final updatedIds = Set<String>.from(unsyncedIds.cast<String>())
      ..add(task.id);
    await _storageService.setData(_unsyncedTasksKey, updatedIds.toList());
  }

  Future<void> _removeFromUnsyncedTasks(String id) async {
    final unsyncedIds =
        _storageService.getData<List<dynamic>>(_unsyncedTasksKey) ?? [];
    final updatedIds = unsyncedIds.where((taskId) => taskId != id).toList();
    await _storageService.setData(_unsyncedTasksKey, updatedIds);
  }

  Future<void> _addToModifiedTasks(TaskModel task) async {
    final modifiedIds =
        _storageService.getData<List<dynamic>>(_modifiedTasksKey) ?? [];
    final updatedIds = Set<String>.from(modifiedIds.cast<String>())
      ..add(task.id);
    await _storageService.setData(_modifiedTasksKey, updatedIds.toList());
  }

  Future<void> _removeFromModifiedTasks(String id) async {
    final modifiedIds =
        _storageService.getData<List<dynamic>>(_modifiedTasksKey) ?? [];
    final updatedIds = modifiedIds.where((taskId) => taskId != id).toList();
    await _storageService.setData(_modifiedTasksKey, updatedIds);
  }
}
