import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/tasks/data/datasources/task_local_data_source.dart';
import 'package:power_up/features/tasks/data/datasources/task_remote_data_source.dart';
import 'package:power_up/features/tasks/data/models/task_model.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/domain/repositories/task_repository.dart';

/// Implementation of the TaskRepository
class TaskRepositoryImpl implements TaskRepository {
  final TaskRemoteDataSource remoteDataSource;
  final TaskLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  TaskRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<void> init() async {
    await localDataSource.init();
  }

  @override
  Future<void> clearData() async {
    await localDataSource.clear();
  }

  @override
  Future<Either<Failure, TaskEntity>> createTask({
    required String title,
    String? description,
    required DateTime dueDate,
    required TaskPriority priority,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final taskModel = await remoteDataSource.createTask(
          title: title,
          description: description,
          dueDate: dueDate,
          priority: priority,
        );

        // Save task locally
        await localDataSource.saveTask(taskModel);

        return Right(taskModel);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Create a local task with temporary ID
        final newTask = TaskModel(
          id: 'local_${DateTime.now().millisecondsSinceEpoch}',
          title: title,
          description: description,
          dueDate: dueDate,
          isCompleted: false,
          priority: priority,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await localDataSource.saveTask(newTask);
        return Right(newTask);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<TaskEntity>>> getTasks({
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
    TaskPriority? priority,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final tasks = await remoteDataSource.getTasks(
          isCompleted: isCompleted,
          startDate: startDate,
          endDate: endDate,
          priority: priority,
        );

        // Update local cache
        await localDataSource.saveTasks(tasks);

        return Right(tasks);
      } on ServerException {
        // Fallback to local data if server fails
        try {
          final localTasks = await localDataSource.getTasks(
            isCompleted: isCompleted,
            startDate: startDate,
            endDate: endDate,
            priority: priority,
          );
          return Right(localTasks);
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final localTasks = await localDataSource.getTasks(
          isCompleted: isCompleted,
          startDate: startDate,
          endDate: endDate,
          priority: priority,
        );
        return Right(localTasks);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, TaskEntity>> getTaskById(String id) async {
    // Try local first for faster response
    final localTask = await localDataSource.getTaskById(id);

    if (await networkInfo.isConnected) {
      try {
        // Get from remote
        final remoteTask = await remoteDataSource.getTaskById(id);

        // Update local cache
        await localDataSource.saveTask(remoteTask);

        return Right(remoteTask);
      } on ServerException catch (e) {
        // Return local version if available
        if (localTask != null) {
          return Right(localTask);
        }
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else if (localTask != null) {
      return Right(localTask);
    } else {
      return const Left(
        NetworkFailure(
          message: 'No internet connection and task not found locally',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, TaskEntity>> updateTask({
    required String id,
    String? title,
    String? description,
    DateTime? dueDate,
    TaskPriority? priority,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedTask = await remoteDataSource.updateTask(
          id: id,
          title: title,
          description: description,
          dueDate: dueDate,
          priority: priority,
        );

        // Update local cache
        await localDataSource.saveTask(updatedTask);

        return Right(updatedTask);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Get existing task
        final existingTask = await localDataSource.getTaskById(id);

        if (existingTask == null) {
          return const Left(CacheFailure(message: 'Task not found locally'));
        }

        // Update local task
        final updatedTask = TaskModel(
          id: existingTask.id,
          title: title ?? existingTask.title,
          description: description ?? existingTask.description,
          dueDate: dueDate ?? existingTask.dueDate,
          isCompleted: existingTask.isCompleted,
          priority: priority ?? existingTask.priority,
          completedAt: existingTask.completedAt,
          createdAt: existingTask.createdAt,
          updatedAt: DateTime.now(),
        );

        await localDataSource.saveTask(updatedTask);
        return Right(updatedTask);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, bool>> deleteTask(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.deleteTask(id);

        // Delete from local cache
        await localDataSource.deleteTask(id);

        return Right(result);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Mark for deletion when back online
        await localDataSource.deleteTask(id);
        return const Right(true);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, TaskEntity>> markTaskComplete(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final completedTask = await remoteDataSource.markTaskComplete(id);

        // Update local cache
        await localDataSource.saveTask(completedTask);

        return Right(completedTask);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Get existing task
        final existingTask = await localDataSource.getTaskById(id);

        if (existingTask == null) {
          return const Left(CacheFailure(message: 'Task not found locally'));
        }

        // Update local task
        final completedTask = TaskModel(
          id: existingTask.id,
          title: existingTask.title,
          description: existingTask.description,
          dueDate: existingTask.dueDate,
          isCompleted: true,
          priority: existingTask.priority,
          completedAt: DateTime.now(),
          createdAt: existingTask.createdAt,
          updatedAt: DateTime.now(),
        );

        await localDataSource.saveTask(completedTask);
        return Right(completedTask);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, TaskEntity>> markTaskIncomplete(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final incompleteTask = await remoteDataSource.markTaskIncomplete(id);

        // Update local cache
        await localDataSource.saveTask(incompleteTask);

        return Right(incompleteTask);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Get existing task
        final existingTask = await localDataSource.getTaskById(id);

        if (existingTask == null) {
          return const Left(CacheFailure(message: 'Task not found locally'));
        }

        // Update local task
        final incompleteTask = TaskModel(
          id: existingTask.id,
          title: existingTask.title,
          description: existingTask.description,
          dueDate: existingTask.dueDate,
          isCompleted: false,
          priority: existingTask.priority,
          completedAt: null,
          createdAt: existingTask.createdAt,
          updatedAt: DateTime.now(),
        );

        await localDataSource.saveTask(incompleteTask);
        return Right(incompleteTask);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }
}
