import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/core/presentation/controllers/base_controller.dart';
import 'package:power_up/core/services/notification_service.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/domain/usecases/create_task_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/delete_task_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/get_tasks_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/mark_task_complete_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/update_task_usecase.dart';

/// Controller for managing task-related operations
class TaskController extends BaseController {
  // Use cases
  final CreateTaskUseCase _createTaskUseCase;
  final GetTasksUseCase _getTasksUseCase;
  final UpdateTaskUseCase _updateTaskUseCase;
  final DeleteTaskUseCase _deleteTaskUseCase;
  final MarkTaskCompleteUseCase _markTaskCompleteUseCase;

  // Services
  final NotificationService _notificationService;

  // Observable state
  final RxList<TaskEntity> allTasks = <TaskEntity>[].obs;
  final RxList<TaskEntity> todayTasks = <TaskEntity>[].obs;
  final RxList<TaskEntity> upcomingTasks = <TaskEntity>[].obs;
  final RxList<TaskEntity> overdueTasks = <TaskEntity>[].obs;
  final RxList<TaskEntity> completedTasks = <TaskEntity>[].obs;

  // Task filters
  final Rx<TaskPriority?> priorityFilter = Rx<TaskPriority?>(null);
  final Rx<DateTime?> startDateFilter = Rx<DateTime?>(null);
  final Rx<DateTime?> endDateFilter = Rx<DateTime?>(null);
  final RxBool showCompletedFilter = false.obs;

  // Tab selection (0: All, 1: Today, 2: Upcoming, 3: Overdue, 4: Completed)
  final RxInt selectedTab = 0.obs;

  // Form controllers for creating/editing tasks
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  // Selected task for editing
  final Rx<TaskEntity?> selectedTask = Rx<TaskEntity?>(null);

  // Create new task form values
  final Rx<DateTime> selectedDueDate =
      DateTime.now().add(const Duration(days: 1)).obs;
  final Rx<TaskPriority> selectedPriority = TaskPriority.medium.obs;

  TaskController({
    required CreateTaskUseCase createTaskUseCase,
    required GetTasksUseCase getTasksUseCase,
    required UpdateTaskUseCase updateTaskUseCase,
    required DeleteTaskUseCase deleteTaskUseCase,
    required MarkTaskCompleteUseCase markTaskCompleteUseCase,
    required NotificationService notificationService,
  }) : _createTaskUseCase = createTaskUseCase,
       _getTasksUseCase = getTasksUseCase,
       _updateTaskUseCase = updateTaskUseCase,
       _deleteTaskUseCase = deleteTaskUseCase,
       _markTaskCompleteUseCase = markTaskCompleteUseCase,
       _notificationService = notificationService;

  @override
  void onInit() {
    super.onInit();
    fetchAllTasks();
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  /// Fetch all tasks and update the state
  Future<void> fetchAllTasks() async {
    setLoading(true);

    // Get all tasks
    final result = await _getTasksUseCase(const GetTasksParams());
    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message, isError: true);
      },
      (tasks) {
        allTasks.assignAll(tasks);
        _updateFilteredLists();
      },
    );

    setLoading(false);
  }

  /// Refresh tasks from server (only when needed)
  Future<void> refreshTasks() async {
    await fetchAllTasks();
  }

  /// Update the filtered task lists (today, upcoming, overdue, completed)
  void _updateFilteredLists() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final nextWeek = today.add(const Duration(days: 7));

    // Filter tasks locally instead of making multiple API calls
    final allTasksList = allTasks.toList();

    // Today's tasks (due today)
    todayTasks.assignAll(
      allTasksList.where((task) {
        final taskDueDay = DateTime(
          task.dueDate.year,
          task.dueDate.month,
          task.dueDate.day,
        );
        return taskDueDay.isAtSameMomentAs(today) && !task.isCompleted;
      }).toList(),
    );

    // Upcoming tasks (due in next 7 days, excluding today)
    upcomingTasks.assignAll(
      allTasksList.where((task) {
        final taskDueDay = DateTime(
          task.dueDate.year,
          task.dueDate.month,
          task.dueDate.day,
        );
        return taskDueDay.isAfter(today) &&
            (taskDueDay.isBefore(nextWeek) ||
                taskDueDay.isAtSameMomentAs(nextWeek)) &&
            !task.isCompleted;
      }).toList(),
    );

    // Overdue tasks (due before today and not completed)
    overdueTasks.assignAll(
      allTasksList.where((task) {
        final taskDueDay = DateTime(
          task.dueDate.year,
          task.dueDate.month,
          task.dueDate.day,
        );
        return taskDueDay.isBefore(today) && !task.isCompleted;
      }).toList(),
    );

    // Completed tasks
    completedTasks.assignAll(
      allTasksList.where((task) => task.isCompleted).toList(),
    );
  }

  /// Create a new task
  Future<bool> createTask() async {
    setLoading(true);

    final params = CreateTaskParams(
      title: titleController.text.trim(),
      description: descriptionController.text.trim(),
      dueDate: selectedDueDate.value,
      priority: selectedPriority.value,
    );

    final result = await _createTaskUseCase(params);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message, isError: true);
        setLoading(false);
        return false;
      },
      (task) {
        allTasks.add(task);

        // Apply current filters if any exist, otherwise use default filtering
        if (_hasActiveFilters()) {
          _applyFiltersLocally();
        } else {
          _updateFilteredLists();
        }

        // Schedule a notification for the task
        _scheduleTaskNotification(task);

        // Clear form
        titleController.clear();
        descriptionController.clear();
        selectedDueDate.value = DateTime.now();
        selectedPriority.value = TaskPriority.medium;

        showSnackbar('Success', 'Task created successfully');
        setLoading(false);
        return true;
      },
    );
  }

  /// Mark a task as complete
  Future<void> markTaskComplete(String taskId) async {
    final params = TaskIdParams(id: taskId);
    final result = await _markTaskCompleteUseCase(params);

    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message, isError: true);
      },
      (completedTask) {
        // Update task in lists
        final index = allTasks.indexWhere((task) => task.id == taskId);
        if (index != -1) {
          allTasks[index] = completedTask;
        }

        // Cancel notification for completed task
        _notificationService.cancelNotification(taskId.hashCode);

        // Apply current filters if any exist, otherwise use default filtering
        if (_hasActiveFilters()) {
          _applyFiltersLocally();
        } else {
          _updateFilteredLists();
        }

        showSnackbar('Success', 'Task marked as complete');
      },
    );
  }

  /// Delete a task
  Future<void> deleteTask(String taskId) async {
    final params = TaskIdParams(id: taskId);
    final result = await _deleteTaskUseCase(params);

    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message, isError: true);
      },
      (success) {
        if (success) {
          // Remove task from lists
          allTasks.removeWhere((task) => task.id == taskId);

          // Cancel notification for deleted task
          _notificationService.cancelNotification(taskId.hashCode);

          // Apply current filters if any exist, otherwise use default filtering
          if (_hasActiveFilters()) {
            _applyFiltersLocally();
          } else {
            _updateFilteredLists();
          }

          showSnackbar('Success', 'Task deleted successfully');
        }
      },
    );
  }

  /// Select a task for editing
  void selectTaskForEdit(TaskEntity task) {
    selectedTask.value = task;
    titleController.text = task.title;
    descriptionController.text = task.description ?? '';
    selectedDueDate.value = task.dueDate;
    selectedPriority.value = task.priority;
  }

  /// Clear selected task
  void clearSelectedTask() {
    selectedTask.value = null;
    titleController.clear();
    descriptionController.clear();
    selectedDueDate.value = DateTime.now().add(const Duration(days: 1));
    selectedPriority.value = TaskPriority.medium;
  }

  /// Update an existing task
  Future<bool> updateTask() async {
    if (selectedTask.value == null) return false;

    setLoading(true);

    final params = UpdateTaskParams(
      id: selectedTask.value!.id,
      title: titleController.text.trim(),
      description: descriptionController.text.trim(),
      dueDate: selectedDueDate.value,
      priority: selectedPriority.value,
    );

    final result = await _updateTaskUseCase(params);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message);
        setLoading(false);
        return false;
      },
      (updatedTask) {
        // Update task in lists
        final index = allTasks.indexWhere((task) => task.id == updatedTask.id);
        if (index != -1) {
          allTasks[index] = updatedTask;
        }

        // First cancel any existing notifications
        _notificationService.cancelNotification(updatedTask.id.hashCode);

        // Then reschedule if needed
        _scheduleTaskNotification(updatedTask);

        // Apply current filters if any exist, otherwise use default filtering
        if (_hasActiveFilters()) {
          _applyFiltersLocally();
        } else {
          _updateFilteredLists();
        }

        clearSelectedTask();

        showSnackbar('Success', 'Task updated successfully');
        setLoading(false);
        return true;
      },
    );
  }

  /// Apply filters to tasks
  void applyFilters({
    TaskPriority? priority,
    DateTime? startDate,
    DateTime? endDate,
    bool? showCompleted,
  }) {
    if (priority != null) priorityFilter.value = priority;
    if (startDate != null) startDateFilter.value = startDate;
    if (endDate != null) endDateFilter.value = endDate;
    if (showCompleted != null) showCompletedFilter.value = showCompleted;

    // Apply filters to already loaded tasks locally instead of fetching again
    _applyFiltersLocally();
  }

  /// Apply filters to locally loaded tasks
  void _applyFiltersLocally() {
    List<TaskEntity> filteredTasks = allTasks.toList();

    // Apply completion status filter
    if (!showCompletedFilter.value) {
      filteredTasks = filteredTasks.where((task) => !task.isCompleted).toList();
    }

    // Apply priority filter
    if (priorityFilter.value != null) {
      filteredTasks =
          filteredTasks
              .where((task) => task.priority == priorityFilter.value)
              .toList();
    }

    // Apply date range filters
    if (startDateFilter.value != null) {
      filteredTasks =
          filteredTasks.where((task) {
            final taskDueDay = DateTime(
              task.dueDate.year,
              task.dueDate.month,
              task.dueDate.day,
            );
            final filterStartDay = DateTime(
              startDateFilter.value!.year,
              startDateFilter.value!.month,
              startDateFilter.value!.day,
            );
            return taskDueDay.isAfter(filterStartDay) ||
                taskDueDay.isAtSameMomentAs(filterStartDay);
          }).toList();
    }

    if (endDateFilter.value != null) {
      filteredTasks =
          filteredTasks.where((task) {
            final taskDueDay = DateTime(
              task.dueDate.year,
              task.dueDate.month,
              task.dueDate.day,
            );
            final filterEndDay = DateTime(
              endDateFilter.value!.year,
              endDateFilter.value!.month,
              endDateFilter.value!.day,
            );
            return taskDueDay.isBefore(filterEndDay) ||
                taskDueDay.isAtSameMomentAs(filterEndDay);
          }).toList();
    }

    // Update filtered lists with filtered tasks
    _updateFilteredListsWithTasks(filteredTasks);
  }

  /// Update filtered lists with provided tasks
  void _updateFilteredListsWithTasks(List<TaskEntity> tasks) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final nextWeek = today.add(const Duration(days: 7));

    // Today's tasks (due today)
    todayTasks.assignAll(
      tasks.where((task) {
        final taskDueDay = DateTime(
          task.dueDate.year,
          task.dueDate.month,
          task.dueDate.day,
        );
        return taskDueDay.isAtSameMomentAs(today) && !task.isCompleted;
      }).toList(),
    );

    // Upcoming tasks (due in next 7 days, excluding today)
    upcomingTasks.assignAll(
      tasks.where((task) {
        final taskDueDay = DateTime(
          task.dueDate.year,
          task.dueDate.month,
          task.dueDate.day,
        );
        return taskDueDay.isAfter(today) &&
            (taskDueDay.isBefore(nextWeek) ||
                taskDueDay.isAtSameMomentAs(nextWeek)) &&
            !task.isCompleted;
      }).toList(),
    );

    // Overdue tasks (due before today and not completed)
    overdueTasks.assignAll(
      tasks.where((task) {
        final taskDueDay = DateTime(
          task.dueDate.year,
          task.dueDate.month,
          task.dueDate.day,
        );
        return taskDueDay.isBefore(today) && !task.isCompleted;
      }).toList(),
    );

    // Completed tasks
    completedTasks.assignAll(tasks.where((task) => task.isCompleted).toList());
  }

  /// Clear all filters
  void clearFilters() {
    priorityFilter.value = null;
    startDateFilter.value = null;
    endDateFilter.value = null;
    showCompletedFilter.value = false;

    // Reset to show all tasks without fetching again
    _updateFilteredLists();
  }

  /// Show snackbar with message
  @override
  void showSnackbar(String title, String message, {bool isError = false}) {
    // Check if we're in test mode or if Get.context is available
    if (Get.testMode == true || Get.context == null) {
      // In test mode or no context, just log the message
      debugPrint('$title: $message');
      return;
    }

    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: isError ? Colors.red[100] : Colors.green[100],
      colorText: isError ? Colors.red[900] : Colors.green[900],
      margin: const EdgeInsets.all(8),
    );
  }

  /// Select a tab for displaying different task categories
  void selectTab(int tabIndex) {
    selectedTab.value = tabIndex;
  }

  /// Schedule a notification for a task
  void _scheduleTaskNotification(TaskEntity task) {
    // Bail if the task is already completed or the due date is in the past
    if (task.isCompleted || task.dueDate.isBefore(DateTime.now())) {
      return;
    }

    // Set the notification time - 1 hour before the due date
    final notificationTime = task.dueDate.subtract(const Duration(hours: 1));

    // Only schedule if the notification time is in the future
    if (notificationTime.isAfter(DateTime.now())) {
      _notificationService.scheduleTaskReminder(
        id: task.id.hashCode, // Use hash of ID for notification ID
        title: 'Task Reminder: ${task.title}',
        body: 'Due ${_formatDueTime(task.dueDate)}',
        scheduledDate: notificationTime,
        payload:
            task.id, // Store task ID in payload for handling notification tap
      );
    }
  }

  /// Format due time in a readable format
  String _formatDueTime(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dueDay = DateTime(dueDate.year, dueDate.month, dueDate.day);

    if (dueDay.isAtSameMomentAs(today)) {
      return 'today at ${_formatTimeOfDay(dueDate)}';
    } else if (dueDay.isAtSameMomentAs(tomorrow)) {
      return 'tomorrow at ${_formatTimeOfDay(dueDate)}';
    } else {
      return 'on ${dueDate.day}/${dueDate.month} at ${_formatTimeOfDay(dueDate)}';
    }
  }

  /// Format time as HH:MM
  String _formatTimeOfDay(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Check if any filters are currently active
  bool _hasActiveFilters() {
    return priorityFilter.value != null ||
        startDateFilter.value != null ||
        endDateFilter.value != null ||
        showCompletedFilter.value;
  }
}
