import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/presentation/controllers/task_controller.dart';

class TaskFilterBottomSheet extends StatefulWidget {
  const TaskFilterBottomSheet({super.key});

  @override
  State<TaskFilterBottomSheet> createState() => _TaskFilterBottomSheetState();
}

class _TaskFilterBottomSheetState extends State<TaskFilterBottomSheet> {
  final TaskController controller = Get.find<TaskController>();

  // Local state for filters before applying
  late TaskPriority? selectedPriority;
  late DateTime? startDate;
  late DateTime? endDate;
  late bool showCompleted;

  @override
  void initState() {
    super.initState();
    // Initialize local state from controller
    selectedPriority = controller.priorityFilter.value;
    startDate = controller.startDateFilter.value;
    endDate = controller.endDateFilter.value;
    showCompleted = controller.showCompletedFilter.value;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter Tasks',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Priority filter
          Text('Priority', style: theme.textTheme.titleMedium),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildPriorityChip(context, null, 'All'),
              _buildPriorityChip(context, TaskPriority.low, 'Low'),
              _buildPriorityChip(context, TaskPriority.medium, 'Medium'),
              _buildPriorityChip(context, TaskPriority.high, 'High'),
            ],
          ),

          const SizedBox(height: 24),

          // Date range filter
          Text('Date Range', style: theme.textTheme.titleMedium),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildDateSelector(
                  context,
                  'Start Date',
                  startDate,
                  (date) => setState(() => startDate = date),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDateSelector(
                  context,
                  'End Date',
                  endDate,
                  (date) => setState(() => endDate = date),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Show completed tasks filter
          SwitchListTile(
            title: const Text('Show Completed Tasks'),
            value: showCompleted,
            onChanged: (value) {
              setState(() {
                showCompleted = value;
              });
            },
            contentPadding: EdgeInsets.zero,
          ),

          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    controller.clearFilters();
                    Navigator.pop(context);
                  },
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Clear Filters'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    controller.applyFilters(
                      priority: selectedPriority,
                      startDate: startDate,
                      endDate: endDate,
                      showCompleted: showCompleted,
                    );
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Apply Filters'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(
    BuildContext context,
    TaskPriority? priority,
    String label,
  ) {
    final theme = Theme.of(context);
    final isSelected = selectedPriority == priority;

    Color chipColor;
    if (priority == TaskPriority.high) {
      chipColor = Colors.red;
    } else if (priority == TaskPriority.medium) {
      chipColor = Colors.orange;
    } else if (priority == TaskPriority.low) {
      chipColor = Colors.green;
    } else {
      chipColor = theme.colorScheme.primary;
    }

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            selectedPriority = priority;
          } else if (selectedPriority == priority) {
            selectedPriority = null;
          }
        });
      },
      selectedColor: chipColor.withValues(alpha: 0.2),
      checkmarkColor: chipColor,
      labelStyle: TextStyle(
        color: isSelected ? chipColor : null,
        fontWeight: isSelected ? FontWeight.bold : null,
      ),
    );
  }

  Widget _buildDateSelector(
    BuildContext context,
    String label,
    DateTime? selectedDate,
    Function(DateTime?) onDateChanged,
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: () => _selectDate(context, selectedDate, onDateChanged),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.5),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  selectedDate != null
                      ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                      : 'Not set',
                  style: theme.textTheme.bodyMedium,
                ),
                if (selectedDate != null) ...[
                  const Spacer(),
                  InkWell(
                    onTap: () => onDateChanged(null),
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: theme.colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(
    BuildContext context,
    DateTime? initialDate,
    Function(DateTime?) onDateChanged,
  ) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );

    if (pickedDate != null) {
      onDateChanged(pickedDate);
    }
  }
}
