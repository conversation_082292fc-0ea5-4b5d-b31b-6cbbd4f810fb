import 'package:flutter/material.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/presentation/widgets/task_suggestions_bottom_sheet.dart';

class TaskListItem extends StatelessWidget {
  final TaskEntity task;
  final VoidCallback onEdit;
  final VoidCallback onComplete;
  final VoidCallback onDelete;

  const TaskListItem({
    super.key,
    required this.task,
    required this.onEdit,
    required this.onComplete,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isCompleted = task.isCompleted;
    final isOverdue = task.isOverdue;

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getBorderColor(theme, isCompleted, isOverdue),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Dismissible(
        key: Key(task.id),
        background: _buildDismissibleBackground(theme, true),
        secondaryBackground: _buildDismissibleBackground(theme, false),
        confirmDismiss: (direction) async {
          if (direction == DismissDirection.endToStart) {
            return await _confirmDelete(context);
          } else {
            onComplete();
            return false;
          }
        },
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          leading: _buildTaskCheckbox(theme, isCompleted),
          title: Text(
            task.title,
            style: theme.textTheme.bodyLarge?.copyWith(
              decoration: isCompleted ? TextDecoration.lineThrough : null,
              color: isCompleted ? theme.colorScheme.outline : null,
              fontWeight: isCompleted ? FontWeight.normal : FontWeight.w600,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (task.description != null && task.description!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 4.0),
                  child: Text(
                    task.description!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      decoration:
                          isCompleted ? TextDecoration.lineThrough : null,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              const SizedBox(height: 4),
              Row(
                children: [
                  _buildPriorityIndicator(theme, task.priority),
                  const SizedBox(width: 8),
                  Text(
                    task.dueStatus,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getDueStatusColor(theme, isCompleted, isOverdue),
                      fontWeight:
                          isOverdue && !isCompleted ? FontWeight.bold : null,
                    ),
                  ),
                ],
              ),
            ],
          ),
          trailing: IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showTaskOptions(context);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTaskCheckbox(ThemeData theme, bool isCompleted) {
    return GestureDetector(
      onTap: onComplete,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: isCompleted ? theme.colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color:
                isCompleted
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline,
            width: 2,
          ),
        ),
        child:
            isCompleted
                ? Icon(
                  Icons.check,
                  color: theme.colorScheme.onPrimary,
                  size: 16,
                )
                : null,
      ),
    );
  }

  Widget _buildPriorityIndicator(ThemeData theme, TaskPriority priority) {
    Color color;
    String label;

    switch (priority) {
      case TaskPriority.high:
        color = Colors.red;
        label = 'High';
        break;
      case TaskPriority.medium:
        color = Colors.orange;
        label = 'Medium';
        break;
      case TaskPriority.low:
        color = Colors.green;
        label = 'Low';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: theme.textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 10,
        ),
      ),
    );
  }

  Color _getBorderColor(ThemeData theme, bool isCompleted, bool isOverdue) {
    if (isCompleted) {
      return theme.colorScheme.primary.withValues(alpha: 0.3);
    } else if (isOverdue) {
      return theme.colorScheme.error.withValues(alpha: 0.5);
    } else {
      return theme.colorScheme.outline.withValues(alpha: 0.2);
    }
  }

  Color _getDueStatusColor(ThemeData theme, bool isCompleted, bool isOverdue) {
    if (isCompleted) {
      return theme.colorScheme.primary;
    } else if (isOverdue) {
      return theme.colorScheme.error;
    } else {
      return theme.colorScheme.onSurface.withValues(alpha: 0.7);
    }
  }

  Widget _buildDismissibleBackground(ThemeData theme, bool isStart) {
    return Container(
      color: isStart ? theme.colorScheme.primary : theme.colorScheme.error,
      alignment: isStart ? Alignment.centerLeft : Alignment.centerRight,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Icon(
        isStart ? Icons.check_circle : Icons.delete,
        color: theme.colorScheme.onPrimary,
      ),
    );
  }

  Future<bool> _confirmDelete(BuildContext context) async {
    return await showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Delete Task'),
              content: Text('Are you sure you want to delete "${task.title}"?'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('CANCEL'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                    onDelete();
                  },
                  child: Text(
                    'DELETE',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ??
        false; // In case dialog is dismissed
  }

  void _showTaskOptions(BuildContext context) {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.edit, color: theme.colorScheme.primary),
                title: const Text('Edit Task'),
                onTap: () {
                  Navigator.pop(context);
                  onEdit();
                },
              ),
              ListTile(
                leading: Icon(
                  task.isCompleted ? Icons.refresh : Icons.check_circle,
                  color: theme.colorScheme.primary,
                ),
                title: Text(
                  task.isCompleted ? 'Mark as Incomplete' : 'Mark as Complete',
                ),
                onTap: () {
                  Navigator.pop(context);
                  onComplete();
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.psychology,
                  color: theme.colorScheme.primary,
                ),
                title: const Text('Coaching Suggestions'),
                onTap: () {
                  Navigator.pop(context);
                  _showCoachingSuggestions(context);
                },
              ),
              ListTile(
                leading: Icon(Icons.delete, color: theme.colorScheme.error),
                title: const Text('Delete Task'),
                onTap: () {
                  Navigator.pop(context);
                  _confirmDelete(context).then((confirm) {
                    if (confirm) {
                      onDelete();
                    }
                  });
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// Show analytics for this task
  void _showCoachingSuggestions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => TaskSuggestionsBottomSheet(task: task),
    );
  }
}
