import 'package:flutter/material.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/presentation/widgets/task_list_item.dart';

class TasksList extends StatelessWidget {
  final List<TaskEntity> tasks;
  final bool showEmptyState;
  final String emptyStateMessage;
  final Function(TaskEntity task) onEdit;
  final Function(TaskEntity task) onComplete;
  final Function(TaskEntity task) onDelete;
  final bool showDividers;
  final EdgeInsets padding;

  const TasksList({
    super.key,
    required this.tasks,
    this.showEmptyState = true,
    this.emptyStateMessage = 'No tasks to display',
    required this.onEdit,
    required this.onComplete,
    required this.onDelete,
    this.showDividers = false,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  });

  @override
  Widget build(BuildContext context) {
    if (tasks.isEmpty && showEmptyState) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      padding: padding,
      itemCount: tasks.length,
      separatorBuilder:
          (context, index) =>
              showDividers
                  ? const Divider(height: 24)
                  : const SizedBox(height: 16),
      itemBuilder: (context, index) {
        final task = tasks[index];
        return TaskListItem(
          key: ValueKey(task.id),
          task: task,
          onEdit: () => onEdit(task),
          onComplete: () => onComplete(task),
          onDelete: () => onDelete(task),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 72.0,
              color: theme.colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              emptyStateMessage,
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Tap the + button to create a new task',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
