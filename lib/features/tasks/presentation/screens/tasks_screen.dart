import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/presentation/controllers/ai_reports_controller.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/task_coaching_suggestions.dart';
import 'package:power_up/features/core/presentation/screens/main_layout_screen.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/presentation/controllers/task_controller.dart';
import '../widgets/add_task_bottom_sheet.dart';
import '../widgets/task_list_item.dart';
import '../widgets/task_filter_bottom_sheet.dart';

class TasksScreen extends GetView<TaskController> {
  const TasksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Get AI Reports controller for coaching suggestions
    final aiReportsController = Get.find<AiReportsController>();

    return MainLayoutScreen(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Text(
              'Your Tasks',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Organize your day, boost productivity',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),

            const SizedBox(height: 16),

            // Tasks Overview Cards
            _buildTasksOverviewSection(context),

            const SizedBox(height: 16),

            // Task Coaching Suggestions (if any)
            Obx(() {
              final suggestions = aiReportsController.coachingSuggestions;
              // Show coaching widget if there are task-related suggestions
              if (suggestions.any((s) => s.relatedTaskId != null)) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: TaskCoachingSuggestionsWidget(
                    suggestions: suggestions,
                    taskId:
                        controller.selectedTab.value == 0 &&
                                controller.allTasks.isNotEmpty
                            ? controller
                                .allTasks
                                .first
                                .id // Show for first task if all tasks are selected
                            : controller.selectedTab.value == 1 &&
                                controller.todayTasks.isNotEmpty
                            ? controller
                                .todayTasks
                                .first
                                .id // Show for first today task if viewing today's tasks
                            : controller.allTasks.isNotEmpty
                            ? controller
                                .allTasks
                                .first
                                .id // Fallback to first task
                            : '', // Empty if no tasks
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // Task Filters and Add Button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Task Filters
                Obx(
                  () => TextButton.icon(
                    onPressed: () => _showFilterBottomSheet(context),
                    icon: Icon(
                      Icons.filter_list,
                      color:
                          controller.priorityFilter.value != null ||
                                  controller.startDateFilter.value != null ||
                                  controller.endDateFilter.value != null ||
                                  controller.showCompletedFilter.value
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                    ),
                    label: Text(
                      'Filter',
                      style: TextStyle(
                        color:
                            controller.priorityFilter.value != null ||
                                    controller.startDateFilter.value != null ||
                                    controller.endDateFilter.value != null ||
                                    controller.showCompletedFilter.value
                                ? theme.colorScheme.primary
                                : theme.colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                      ),
                    ),
                  ),
                ),

                // Add Task Button
                ElevatedButton.icon(
                  onPressed: () => _showAddTaskBottomSheet(context),
                  icon: const Icon(Icons.add),
                  label: const Text('New Task'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Task Category Tabs
            _buildTaskCategoryTabs(context),

            const SizedBox(height: 4),

            // Tasks List
            Expanded(
              child: Obx(() {
                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final List<TaskEntity> tasks = _getTasksForCurrentTab();

                if (tasks.isEmpty) {
                  return _buildEmptyTasksView(context);
                }

                return Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        itemCount: tasks.length,
                        padding: const EdgeInsets.only(top: 16),
                        itemBuilder: (context, index) {
                          final task = tasks[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: TaskListItem(
                              task: task,
                              onEdit: () {
                                controller.selectTaskForEdit(task);
                                _showAddTaskBottomSheet(
                                  context,
                                  isEditing: true,
                                );
                              },
                              onComplete:
                                  () => controller.markTaskComplete(task.id),
                              onDelete:
                                  () => _showDeleteConfirmation(context, task),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 64,
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: controller.titleController,
                              decoration: InputDecoration(
                                hintText: 'Type a task...',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(24),
                                  borderSide: BorderSide(
                                    color: theme.colorScheme.outline.withAlpha(
                                      80,
                                    ),
                                  ),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            icon: Icon(
                              Icons.send,
                              color: theme.colorScheme.primary,
                            ),
                            onPressed: controller.createTask,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTasksOverviewSection(BuildContext context) {
    return SizedBox(
      height: 100,
      child: Obx(() {
        return ListView(
          scrollDirection: Axis.horizontal,
          children: [
            _buildOverviewCard(
              context,
              title: 'Today',
              count: controller.todayTasks.length,
              icon: Icons.today,
              color: Colors.blue,
            ),
            _buildOverviewCard(
              context,
              title: 'Upcoming',
              count: controller.upcomingTasks.length,
              icon: Icons.event_available,
              color: Colors.green,
            ),
            _buildOverviewCard(
              context,
              title: 'Overdue',
              count: controller.overdueTasks.length,
              icon: Icons.event_busy,
              color: Colors.red,
            ),
            _buildOverviewCard(
              context,
              title: 'Completed',
              count: controller.completedTasks.length,
              icon: Icons.task_alt,
              color: Colors.purple,
            ),
          ],
        );
      }),
    );
  }

  Widget _buildOverviewCard(
    BuildContext context, {
    required String title,
    required int count,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color),
          const Spacer(),
          Text(
            count.toString(),
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskCategoryTabs(BuildContext context) {
    return Obx(() {
      final selectedTab = controller.selectedTab.value;

      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildTabButton(
              context,
              title: 'All',
              isSelected: selectedTab == 0,
              onTap: () => controller.selectTab(0),
            ),
            _buildTabButton(
              context,
              title: 'Today',
              isSelected: selectedTab == 1,
              onTap: () => controller.selectTab(1),
            ),
            _buildTabButton(
              context,
              title: 'Upcoming',
              isSelected: selectedTab == 2,
              onTap: () => controller.selectTab(2),
            ),
            _buildTabButton(
              context,
              title: 'Overdue',
              isSelected: selectedTab == 3,
              onTap: () => controller.selectTab(3),
            ),
            _buildTabButton(
              context,
              title: 'Completed',
              isSelected: selectedTab == 4,
              onTap: () => controller.selectTab(4),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildTabButton(
    BuildContext context, {
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color:
                  isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.outline.withValues(alpha: 0.5),
            ),
          ),
          child: Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color:
                  isSelected
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  List<TaskEntity> _getTasksForCurrentTab() {
    switch (controller.selectedTab.value) {
      case 0:
        return controller.allTasks;
      case 1:
        return controller.todayTasks;
      case 2:
        return controller.upcomingTasks;
      case 3:
        return controller.overdueTasks;
      case 4:
        return controller.completedTasks;
      default:
        return controller.allTasks;
    }
  }

  Widget _buildEmptyTasksView(BuildContext context) {
    final theme = Theme.of(context);
    String message;
    IconData icon;

    switch (controller.selectedTab.value) {
      case 1:
        message = "No tasks for today";
        icon = Icons.today;
        break;
      case 2:
        message = "No upcoming tasks";
        icon = Icons.event_available;
        break;
      case 3:
        message = "No overdue tasks";
        icon = Icons.event_busy;
        break;
      case 4:
        message = "No completed tasks yet";
        icon = Icons.task_alt;
        break;
      default:
        message = "You don't have any tasks yet";
        icon = Icons.check_box_outline_blank;
    }

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 72, color: theme.colorScheme.outline),
          const SizedBox(height: 16),
          Text(message, style: theme.textTheme.titleMedium),
          const SizedBox(height: 8),
          Text(
            'Tap + to add a new task',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddTaskBottomSheet(BuildContext context, {bool isEditing = false}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => AddTaskBottomSheet(isEditing: isEditing),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const TaskFilterBottomSheet(),
    );
  }

  void _showDeleteConfirmation(BuildContext context, TaskEntity task) {
    final theme = Theme.of(context);

    Get.dialog(
      AlertDialog(
        title: const Text('Delete Task'),
        content: Text('Are you sure you want to delete "${task.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Cancel',
              style: TextStyle(color: theme.colorScheme.onSurface),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deleteTask(task.id);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
