import 'package:equatable/equatable.dart';

/// Priority levels for tasks
enum TaskPriority { low, medium, high }

/// Domain entity for a task in the application
class TaskEntity extends Equatable {
  final String id;
  final String title;
  final String? description;
  final DateTime dueDate;
  final bool isCompleted;
  final TaskPriority priority;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TaskEntity({
    required this.id,
    required this.title,
    this.description,
    required this.dueDate,
    required this.isCompleted,
    required this.priority,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this TaskEntity with the given fields replaced with the new values
  TaskEntity copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? dueDate,
    bool? isCompleted,
    TaskPriority? priority,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaskEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      dueDate: dueDate ?? this.dueDate,
      isCompleted: isCompleted ?? this.isCompleted,
      priority: priority ?? this.priority,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Mark a task as complete
  TaskEntity markAsComplete() {
    return copyWith(
      isCompleted: true,
      completedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Check if a task is overdue
  bool get isOverdue => !isCompleted && dueDate.isBefore(DateTime.now());

  /// Get remaining days until due date
  int get daysUntilDue {
    final now = DateTime.now();
    return dueDate.difference(now).inDays;
  }

  /// Get human-readable due date status
  String get dueStatus {
    if (isCompleted) return 'Completed';
    if (isOverdue) return 'Overdue';

    final days = daysUntilDue;
    if (days == 0) return 'Due today';
    if (days == 1) return 'Due tomorrow';
    return 'Due in $days days';
  }

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    dueDate,
    isCompleted,
    priority,
    completedAt,
    createdAt,
    updatedAt,
  ];
}
