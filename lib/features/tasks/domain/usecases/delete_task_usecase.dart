import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/tasks/domain/repositories/task_repository.dart';
import 'package:power_up/features/tasks/domain/usecases/mark_task_complete_usecase.dart';

/// Use case for deleting a task
class DeleteTaskUseCase implements UseCase<bool, TaskIdParams> {
  final TaskRepository repository;

  DeleteTaskUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(TaskIdParams params) async {
    return repository.deleteTask(params.id);
  }
}
