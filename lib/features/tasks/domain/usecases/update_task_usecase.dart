import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/domain/repositories/task_repository.dart';

/// Use case for updating an existing task
class UpdateTaskUseCase implements UseCase<TaskEntity, UpdateTaskParams> {
  final TaskRepository repository;

  UpdateTaskUseCase(this.repository);

  @override
  Future<Either<Failure, TaskEntity>> call(UpdateTaskParams params) async {
    // Basic validation
    if (params.title != null && params.title!.isEmpty) {
      return const Left(ValidationFailure(message: 'Task title cannot be empty'));
    }

    if (params.dueDate != null &&
        params.dueDate!.isBefore(DateTime.now()) &&
        !isSameDay(params.dueDate!, DateTime.now())) {
      return const Left(ValidationFailure(message: 'Due date cannot be in the past'));
    }

    // Update task
    return repository.updateTask(
      id: params.id,
      title: params.title,
      description: params.description,
      dueDate: params.dueDate,
      priority: params.priority,
    );
  }

  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}

/// Parameters for UpdateTaskUseCase
class UpdateTaskParams extends Equatable {
  final String id;
  final String? title;
  final String? description;
  final DateTime? dueDate;
  final TaskPriority? priority;

  const UpdateTaskParams({
    required this.id,
    this.title,
    this.description,
    this.dueDate,
    this.priority,
  });

  @override
  List<Object?> get props => [id, title, description, dueDate, priority];
}
