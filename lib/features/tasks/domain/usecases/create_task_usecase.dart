import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/domain/repositories/task_repository.dart';

/// Use case for creating a new task
class CreateTaskUseCase implements UseCase<TaskEntity, CreateTaskParams> {
  final TaskRepository repository;

  CreateTaskUseCase(this.repository);

  @override
  Future<Either<Failure, TaskEntity>> call(CreateTaskParams params) async {
    // Validate input
    if (params.title.isEmpty) {
      return const Left(ValidationFailure(message: 'Task title cannot be empty'));
    }

    if (params.dueDate.isBefore(DateTime.now()) &&
        !isSameDay(params.dueDate, DateTime.now())) {
      return const Left(ValidationFailure(message: 'Due date cannot be in the past'));
    }

    // Create task
    return repository.createTask(
      title: params.title,
      description: params.description,
      dueDate: params.dueDate,
      priority: params.priority,
    );
  }

  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}

/// Parameters for CreateTaskUseCase
class CreateTaskParams extends Equatable {
  final String title;
  final String? description;
  final DateTime dueDate;
  final TaskPriority priority;

  const CreateTaskParams({
    required this.title,
    this.description,
    required this.dueDate,
    required this.priority,
  });

  @override
  List<Object?> get props => [title, description, dueDate, priority];
}
