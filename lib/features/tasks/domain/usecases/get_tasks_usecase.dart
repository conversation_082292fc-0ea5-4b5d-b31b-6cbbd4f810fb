import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/domain/repositories/task_repository.dart';

/// Use case for retrieving tasks
class GetTasksUseCase implements UseCase<List<TaskEntity>, GetTasksParams> {
  final TaskRepository repository;

  GetTasksUseCase(this.repository);

  @override
  Future<Either<Failure, List<TaskEntity>>> call(GetTasksParams params) async {
    return repository.getTasks(
      isCompleted: params.isCompleted,
      startDate: params.startDate,
      endDate: params.endDate,
      priority: params.priority,
    );
  }
}

/// Parameters for GetTasksUseCase - all optional for filtering
class GetTasksParams extends Equatable {
  final bool? isCompleted;
  final DateTime? startDate;
  final DateTime? endDate;
  final TaskPriority? priority;

  const GetTasksParams({
    this.isCompleted,
    this.startDate,
    this.endDate,
    this.priority,
  });

  /// Get today's tasks
  factory GetTasksParams.today() {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
    return GetTasksParams(startDate: startOfDay, endDate: endOfDay);
  }

  /// Get overdue tasks
  factory GetTasksParams.overdue() {
    final now = DateTime.now();
    final startOfDay = DateTime(2000, 1, 1); // Far in the past
    final endOfDay = DateTime(
      now.year,
      now.month,
      now.day,
    ).add(const Duration(days: -1));
    return GetTasksParams(
      startDate: startOfDay,
      endDate: endOfDay,
      isCompleted: false,
    );
  }

  /// Get upcoming tasks
  factory GetTasksParams.upcoming({int days = 7}) {
    final now = DateTime.now();
    final startOfDay = DateTime(
      now.year,
      now.month,
      now.day,
    ).add(const Duration(days: 1));
    final endOfPeriod = DateTime(
      now.year,
      now.month,
      now.day,
    ).add(Duration(days: days));
    return GetTasksParams(
      startDate: startOfDay,
      endDate: endOfPeriod,
      isCompleted: false,
    );
  }

  /// Get completed tasks
  factory GetTasksParams.completed() {
    return const GetTasksParams(isCompleted: true);
  }

  @override
  List<Object?> get props => [isCompleted, startDate, endDate, priority];
}
