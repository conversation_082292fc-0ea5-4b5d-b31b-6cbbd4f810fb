import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/tasks/domain/repositories/task_repository.dart';

/// Use case for marking a task as complete
class MarkTaskCompleteUseCase implements UseCase<TaskEntity, TaskIdParams> {
  final TaskRepository repository;

  MarkTaskCompleteUseCase(this.repository);

  @override
  Future<Either<Failure, TaskEntity>> call(TaskIdParams params) async {
    // Call repository to mark task complete
    return repository.markTaskComplete(params.id);
  }
}

/// Parameter for use cases that only need a task ID
class TaskIdParams extends Equatable {
  final String id;

  const TaskIdParams({required this.id});

  @override
  List<Object?> get props => [id];
}
