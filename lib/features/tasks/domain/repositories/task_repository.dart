import 'package:dartz/dartz.dart';
import 'package:power_up/core/domain/repositories/repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';

/// Repository interface for task management
abstract class TaskRepository extends Repository {
  /// Create a new task
  /// Returns Either [Failure] or the created [TaskEntity]
  Future<Either<Failure, TaskEntity>> createTask({
    required String title,
    String? description,
    required DateTime dueDate,
    required TaskPriority priority,
  });

  /// Get all tasks with optional filtering
  /// Returns Either [Failure] or a list of [TaskEntity]
  Future<Either<Failure, List<TaskEntity>>> getTasks({
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
    TaskPriority? priority,
  });

  /// Get a task by ID
  /// Returns Either [Failure] or the [TaskEntity]
  Future<Either<Failure, TaskEntity>> getTaskById(String id);

  /// Update a task
  /// Returns Either [Failure] or the updated [TaskEntity]
  Future<Either<Failure, TaskEntity>> updateTask({
    required String id,
    String? title,
    String? description,
    DateTime? dueDate,
    TaskPriority? priority,
  });

  /// Delete a task
  /// Returns Either [Failure] or a [bool] indicating success
  Future<Either<Failure, bool>> deleteTask(String id);

  /// Mark a task as complete
  /// Returns Either [Failure] or the updated [TaskEntity]
  Future<Either<Failure, TaskEntity>> markTaskComplete(String id);

  /// Mark a task as incomplete
  /// Returns Either [Failure] or the updated [TaskEntity]
  Future<Either<Failure, TaskEntity>> markTaskIncomplete(String id);
}
