import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/tasks/data/datasources/task_local_data_source.dart';
import 'package:power_up/features/tasks/data/datasources/task_local_data_source_impl.dart';
import 'package:power_up/features/tasks/data/datasources/task_remote_data_source.dart';
import 'package:power_up/features/tasks/data/datasources/task_remote_data_source_impl.dart';
import 'package:power_up/features/tasks/data/repositories/task_repository_impl.dart';
import 'package:power_up/features/tasks/domain/repositories/task_repository.dart';
import 'package:power_up/features/tasks/domain/usecases/create_task_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/delete_task_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/get_tasks_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/mark_task_complete_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/update_task_usecase.dart';
import 'package:power_up/features/tasks/presentation/controllers/task_controller.dart';
import 'package:power_up/core/services/notification_service.dart';

/// Bindings for task feature
class TasksBindings implements Bindings {
  @override
  void dependencies() {
    // Core dependencies
    final dio = Get.find<Dio>();
    final networkInfo = Get.find<NetworkInfo>();
    final storageService = Get.find<StorageService>();

    // Data sources
    Get.lazyPut<TaskLocalDataSource>(
      () => TaskLocalDataSourceImpl(storageService: storageService),
    );
    Get.lazyPut<TaskRemoteDataSource>(() => TaskRemoteDataSourceImpl(dio: dio));

    // Repository
    Get.lazyPut<TaskRepository>(
      () => TaskRepositoryImpl(
        remoteDataSource: Get.find<TaskRemoteDataSource>(),
        localDataSource: Get.find<TaskLocalDataSource>(),
        networkInfo: networkInfo,
      ),
    );

    // Use cases
    Get.lazyPut(() => CreateTaskUseCase(Get.find<TaskRepository>()));
    Get.lazyPut(() => GetTasksUseCase(Get.find<TaskRepository>()));
    Get.lazyPut(() => UpdateTaskUseCase(Get.find<TaskRepository>()));
    Get.lazyPut(() => DeleteTaskUseCase(Get.find<TaskRepository>()));
    Get.lazyPut(() => MarkTaskCompleteUseCase(Get.find<TaskRepository>()));

    // Controller
    Get.lazyPut<TaskController>(
      () => TaskController(
        createTaskUseCase: Get.find<CreateTaskUseCase>(),
        getTasksUseCase: Get.find<GetTasksUseCase>(),
        updateTaskUseCase: Get.find<UpdateTaskUseCase>(),
        deleteTaskUseCase: Get.find<DeleteTaskUseCase>(),
        markTaskCompleteUseCase: Get.find<MarkTaskCompleteUseCase>(),
        notificationService: Get.find<NotificationService>(),
      ),
    );
  }
}
