import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';
import 'package:power_up/features/podcasts/domain/repositories/podcast_repository.dart';

/// Use case for marking a podcast as listened
class MarkPodcastAsListenedUseCase
    implements UseCase<PodcastEntity, MarkPodcastAsListenedParams> {
  final PodcastRepository repository;

  MarkPodcastAsListenedUseCase(this.repository);

  @override
  Future<Either<Failure, PodcastEntity>> call(
    MarkPodcastAsListenedParams params,
  ) async {
    // Validate input
    if (params.podcastId.isEmpty) {
      return const Left(
        ValidationFailure(message: 'Podcast ID cannot be empty'),
      );
    }

    return repository.markAsListened(params.podcastId);
  }
}

/// Parameters for MarkPodcastAsListenedUseCase
class MarkPodcastAsListenedParams extends Equatable {
  final String podcastId;

  const MarkPodcastAsListenedParams({required this.podcastId});

  @override
  List<Object> get props => [podcastId];
}
