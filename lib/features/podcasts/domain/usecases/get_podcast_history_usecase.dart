import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';
import 'package:power_up/features/podcasts/domain/repositories/podcast_repository.dart';

/// Use case for getting podcast history
class GetPodcastHistoryUseCase
    implements UseCase<List<PodcastEntity>, GetPodcastHistoryParams> {
  final PodcastRepository repository;

  GetPodcastHistoryUseCase(this.repository);

  @override
  Future<Either<Failure, List<PodcastEntity>>> call(
    GetPodcastHistoryParams params,
  ) async {
    return repository.getPodcastHistory(
      limit: params.limit,
      offset: params.offset,
    );
  }
}

/// Parameters for GetPodcastHistoryUseCase
class GetPodcastHistoryParams extends Equatable {
  final int? limit;
  final int? offset;

  const GetPodcastHistoryParams({this.limit, this.offset});

  /// Default parameters for recent podcasts
  factory GetPodcastHistoryParams.recent() {
    return const GetPodcastHistoryParams(limit: 20, offset: 0);
  }

  /// Parameters for pagination
  factory GetPodcastHistoryParams.paginated({
    required int page,
    int pageSize = 10,
  }) {
    return GetPodcastHistoryParams(limit: pageSize, offset: page * pageSize);
  }

  @override
  List<Object?> get props => [limit, offset];
}
