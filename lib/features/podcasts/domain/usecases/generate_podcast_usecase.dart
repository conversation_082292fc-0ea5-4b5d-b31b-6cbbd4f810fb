import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';
import 'package:power_up/features/podcasts/domain/repositories/podcast_repository.dart';

/// Use case for generating a new podcast
class GeneratePodcastUseCase
    implements UseCase<PodcastEntity, GeneratePodcastParams> {
  final PodcastRepository repository;

  GeneratePodcastUseCase(this.repository);

  @override
  Future<Either<Failure, PodcastEntity>> call(
    GeneratePodcastParams params,
  ) async {
    // Validate duration if provided
    if (params.duration != null && params.duration! < 60) {
      return const Left(
        ValidationFailure(
          message: 'Podcast duration must be at least 60 seconds',
        ),
      );
    }

    if (params.duration != null && params.duration! > 3600) {
      return const Left(
        ValidationFailure(message: 'Podcast duration cannot exceed 1 hour'),
      );
    }

    return repository.generatePodcast(
      topics: params.topics,
      style: params.style,
      duration: params.duration,
      mood: params.mood,
      goals: params.goals,
    );
  }
}

/// Parameters for GeneratePodcastUseCase
class GeneratePodcastParams extends Equatable {
  final List<String>? topics;
  final String? style;
  final int? duration; // Duration in seconds
  final String? mood; // User's current mood
  final List<String>? goals; // User's active goals (titles)

  const GeneratePodcastParams({
    this.topics,
    this.style,
    this.duration,
    this.mood,
    this.goals,
  });

  /// Default parameters for daily podcast generation
  factory GeneratePodcastParams.daily() {
    return const GeneratePodcastParams(
      topics: [],
      style: 'inspirational',
      duration: 300, // 5 minutes
    );
  }

  /// Parameters for custom podcast generation
  factory GeneratePodcastParams.custom({
    required List<String> topics,
    String style = 'informative',
    int duration = 600, // 10 minutes
  }) {
    return GeneratePodcastParams(
      topics: topics,
      style: style,
      duration: duration,
    );
  }

  /// Parameters for mood and goal-based podcast generation
  factory GeneratePodcastParams.withMoodAndGoals({
    List<String>? topics,
    String style = 'personalized',
    int duration = 600, // 10 minutes
    required String mood,
    required List<String> goals,
  }) {
    return GeneratePodcastParams(
      topics: topics,
      style: style,
      duration: duration,
      mood: mood,
      goals: goals,
    );
  }

  @override
  List<Object?> get props => [topics, style, duration, mood, goals];
}
