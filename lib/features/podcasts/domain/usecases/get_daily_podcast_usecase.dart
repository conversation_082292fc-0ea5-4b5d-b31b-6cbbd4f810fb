import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';
import 'package:power_up/features/podcasts/domain/repositories/podcast_repository.dart';

/// Use case for getting today's daily podcast
class GetDailyPodcastUseCase implements UseCase<PodcastEntity?, NoParams> {
  final PodcastRepository repository;

  GetDailyPodcastUseCase(this.repository);

  @override
  Future<Either<Failure, PodcastEntity?>> call(NoParams params) async {
    return repository.getDailyPodcast();
  }
}
