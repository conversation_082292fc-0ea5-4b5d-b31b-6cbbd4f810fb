import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';
import 'package:power_up/features/podcasts/domain/repositories/podcast_repository.dart';

/// Use case for getting a podcast by ID
class GetPodcastByIdUseCase
    implements UseCase<PodcastEntity, GetPodcastByIdParams> {
  final PodcastRepository repository;

  GetPodcastByIdUseCase(this.repository);

  @override
  Future<Either<Failure, PodcastEntity>> call(
    GetPodcastByIdParams params,
  ) async {
    // Validate input
    if (params.id.isEmpty) {
      return const Left(
        ValidationFailure(message: 'Podcast ID cannot be empty'),
      );
    }

    return repository.getPodcastById(params.id);
  }
}

/// Parameters for GetPodcastByIdUseCase
class GetPodcastByIdParams extends Equatable {
  final String id;

  const GetPodcastByIdParams({required this.id});

  @override
  List<Object> get props => [id];
}
