import 'package:dartz/dartz.dart';
import 'package:power_up/core/domain/repositories/repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';

/// Repository interface for podcast management
abstract class PodcastRepository extends Repository {
  /// Get today's daily podcast
  /// Returns Either [Failure] or the daily [PodcastEntity]
  Future<Either<Failure, PodcastEntity?>> getDailyPodcast();

  /// Get podcast history with optional pagination
  /// Returns Either [Failure] or a list of [PodcastEntity]
  Future<Either<Failure, List<PodcastEntity>>> getPodcastHistory({
    int? limit,
    int? offset,
  });

  /// Get a podcast by ID
  /// Returns Either [Failure] or the [PodcastEntity]
  Future<Either<Failure, PodcastEntity>> getPodcastById(String id);

  /// Get all podcasts with optional filtering
  /// Returns Either [Failure] or a list of [PodcastEntity]
  Future<Either<Failure, List<PodcastEntity>>> getAllPodcasts({
    bool? isListened,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  });

  /// Mark a podcast as listened
  /// Returns Either [Failure] or the updated [PodcastEntity]
  Future<Either<Failure, PodcastEntity>> markAsListened(String id);

  /// Generate a new podcast (triggers AI generation on backend)
  /// Returns Either [Failure] or the generated [PodcastEntity]
  Future<Either<Failure, PodcastEntity>> generatePodcast({
    List<String>? topics,
    String? style,
    int? duration,
    String? mood,
    List<String>? goals,
  });

  /// Delete a podcast by ID
  /// Returns Either [Failure] or a [bool] indicating success
  Future<Either<Failure, bool>> deletePodcast(String id);

  /// Get podcast audio file URL for streaming
  /// Returns Either [Failure] or the audio URL [String]
  Future<Either<Failure, String>> getPodcastAudioUrl(String filename);
}
