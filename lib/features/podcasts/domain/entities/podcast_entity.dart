import 'package:equatable/equatable.dart';

/// Domain entity for a podcast episode in the application
class PodcastEntity extends Equatable {
  final String id;
  final String title;
  final String audioUrl;
  final int duration; // Duration in seconds
  final String description;
  final DateTime generatedDate;
  final bool isListened;
  final DateTime? listenedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? imageUrl;
  final List<String> tags;

  const PodcastEntity({
    required this.id,
    required this.title,
    required this.audioUrl,
    required this.duration,
    required this.description,
    required this.generatedDate,
    required this.isListened,
    this.listenedAt,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrl,
    this.tags = const [],
  });

  /// Creates a copy of this PodcastEntity with the given fields replaced with the new values
  PodcastEntity copyWith({
    String? id,
    String? title,
    String? audioUrl,
    int? duration,
    String? description,
    DateTime? generatedDate,
    bool? isListened,
    DateTime? listenedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
    List<String>? tags,
  }) {
    return PodcastEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      description: description ?? this.description,
      generatedDate: generatedDate ?? this.generatedDate,
      isListened: isListened ?? this.isListened,
      listenedAt: listenedAt ?? this.listenedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
      tags: tags ?? this.tags,
    );
  }

  /// Returns formatted duration as a string (e.g., "12:34")
  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Returns whether this is today's podcast
  bool get isToday {
    final today = DateTime.now();
    return generatedDate.year == today.year &&
        generatedDate.month == today.month &&
        generatedDate.day == today.day;
  }

  @override
  List<Object?> get props => [
    id,
    title,
    audioUrl,
    duration,
    description,
    generatedDate,
    isListened,
    listenedAt,
    createdAt,
    updatedAt,
    imageUrl,
    tags,
  ];

  @override
  String toString() {
    return 'PodcastEntity(id: $id, title: $title, duration: $formattedDuration, isListened: $isListened)';
  }
}
