import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import '../../domain/entities/podcast_entity.dart';
import '../../domain/usecases/get_daily_podcast_usecase.dart';
import '../../domain/usecases/get_podcast_history_usecase.dart';
import '../../domain/usecases/mark_podcast_as_listened_usecase.dart';
import '../../domain/usecases/get_podcast_by_id_usecase.dart';
import '../../domain/usecases/generate_podcast_usecase.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/utils/logger.dart';
import '../../../ai_reports/presentation/controllers/ai_reports_controller.dart';

/// Controller for managing podcasts and audio playback
class PodcastController extends GetxController {
  final GetDailyPodcastUseCase getDailyPodcastUseCase;
  final GetPodcastHistoryUseCase getPodcastHistoryUseCase;
  final MarkPodcastAsListenedUseCase markPodcastAsListenedUseCase;
  final GetPodcastByIdUseCase getPodcastByIdUseCase;
  final GeneratePodcastUseCase generatePodcastUseCase;

  // Audio player
  late final AudioPlayer _audioPlayer;

  // Reactive state
  final Rx<PodcastEntity?> _currentPodcast = Rx<PodcastEntity?>(null);
  final Rx<PodcastEntity?> _dailyPodcast = Rx<PodcastEntity?>(null);
  final RxList<PodcastEntity> _podcastHistory = <PodcastEntity>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isPlaying = false.obs;
  final RxBool _isBuffering = false.obs;
  final Rx<Duration> _currentPosition = Duration.zero.obs;
  final Rx<Duration> _totalDuration = Duration.zero.obs;
  final RxDouble _playbackSpeed = 1.0.obs;
  final RxDouble _volume = 1.0.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _selectedFilter = 'all'.obs;

  PodcastController({
    required this.getDailyPodcastUseCase,
    required this.getPodcastHistoryUseCase,
    required this.markPodcastAsListenedUseCase,
    required this.getPodcastByIdUseCase,
    required this.generatePodcastUseCase,
  });

  // Getters
  PodcastEntity? get currentPodcast => _currentPodcast.value;
  PodcastEntity? get dailyPodcast => _dailyPodcast.value;
  List<PodcastEntity> get podcastHistory => _podcastHistory;
  bool get isLoading => _isLoading.value;
  bool get isPlaying => _isPlaying.value;
  bool get isBuffering => _isBuffering.value;
  Duration get currentPosition => _currentPosition.value;
  Duration get totalDuration => _totalDuration.value;
  Duration get position => _currentPosition.value;
  Duration get duration => _totalDuration.value;
  double get playbackSpeed => _playbackSpeed.value;
  double get volume => _volume.value;
  String get errorMessage => _errorMessage.value;
  bool get hasError => _errorMessage.value.isNotEmpty;
  String get selectedFilter => _selectedFilter.value;
  List<PodcastEntity> get podcasts => _getFilteredPodcasts();

  /// Get filtered podcasts based on selected filter
  List<PodcastEntity> _getFilteredPodcasts() {
    switch (_selectedFilter.value) {
      case 'recent':
        // Return podcasts from the last 7 days
        final recentDate = DateTime.now().subtract(const Duration(days: 7));
        return _podcastHistory
            .where((podcast) => podcast.generatedDate.isAfter(recentDate))
            .toList();
      case 'listened':
        // Return only listened podcasts
        return _podcastHistory.where((podcast) => podcast.isListened).toList();
      case 'favorites':
        // Return only favorite podcasts (listened ones for now)
        return _podcastHistory.where((podcast) => podcast.isListened).toList();
      case 'all':
      default:
        // Return all podcasts
        return _podcastHistory;
    }
  }

  /// Set the selected filter
  void setFilter(String filter) {
    if (_selectedFilter.value != filter) {
      _selectedFilter.value = filter;
      Logger.info('Filter changed to: $filter');
    }
  }

  @override
  void onInit() {
    super.onInit();
    _initializeAudioPlayer();
    loadDailyPodcast();
    loadPodcastHistory();
  }

  @override
  void onClose() {
    // Dispose audio player and clear state
    _audioPlayer.stop().catchError(
      (e) => Logger.error('Error stopping audio player: $e'),
    );
    _audioPlayer.dispose();

    // Clear reactive state
    _currentPodcast.value = null;
    _currentPosition.value = Duration.zero;
    _totalDuration.value = Duration.zero;
    _isPlaying.value = false;
    _isBuffering.value = false;

    super.onClose();
  }

  void _initializeAudioPlayer() {
    _audioPlayer = AudioPlayer();

    // Listen to player state changes
    _audioPlayer.playerStateStream.listen(
      (playerState) {
        _isPlaying.value = playerState.playing;
        _isBuffering.value =
            playerState.processingState == ProcessingState.buffering ||
            playerState.processingState == ProcessingState.loading;

        // Log state changes for debugging
        Logger.info(
          'Audio player state: ${playerState.processingState}, playing: ${playerState.playing}',
        );
      },
      onError: (error) {
        Logger.error('Player state stream error: $error');
        _handleAudioError(error);
      },
    );

    // Listen to position changes
    _audioPlayer.positionStream.listen(
      (position) {
        _currentPosition.value = position;
      },
      onError: (error) {
        Logger.error('Position stream error: $error');
      },
    );

    // Listen to duration changes
    _audioPlayer.durationStream.listen(
      (duration) {
        if (duration != null) {
          _totalDuration.value = duration;
          Logger.info('Audio duration loaded: ${duration.inSeconds}s');
        }
      },
      onError: (error) {
        Logger.error('Duration stream error: $error');
      },
    );

    // Listen to completion
    _audioPlayer.playerStateStream
        .where((state) => state.processingState == ProcessingState.completed)
        .listen((_) {
          Logger.info('Podcast playback completed');
          _onPodcastCompleted();
        });

    // Listen to errors
    _audioPlayer.playerStateStream
        .where(
          (state) =>
              state.processingState == ProcessingState.idle &&
              _currentPodcast.value != null,
        )
        .listen((_) {
          Logger.warning('Audio player went idle unexpectedly');
        });
  }

  /// Load today's daily podcast
  Future<void> loadDailyPodcast() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await getDailyPodcastUseCase(NoParams());

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          Logger.error('Failed to load daily podcast: ${failure.message}');
        },
        (podcast) {
          _dailyPodcast.value = podcast;
          Logger.info('Daily podcast loaded: ${podcast?.title ?? "None"}');
        },
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load podcast history
  Future<void> loadPodcastHistory() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      final result = await getPodcastHistoryUseCase(
        GetPodcastHistoryParams.recent(),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          Logger.error('Failed to load podcast history: ${failure.message}');
        },
        (podcasts) {
          _podcastHistory.value = podcasts;
          Logger.info('Loaded ${podcasts.length} podcasts in history');
        },
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Play a specific podcast
  Future<void> playPodcast(PodcastEntity podcast) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      Logger.info('Attempting to play podcast: ${podcast.title}');
      Logger.info('Audio URL: ${podcast.audioUrl}');

      // Enhanced URL validation
      if (!_isValidAudioUrl(podcast.audioUrl)) {
        throw Exception(
          'Invalid or unsupported audio format: ${podcast.audioUrl}',
        );
      }

      // Test URL accessibility first
      final isAccessible = await _testAudioUrlAccessibility(podcast.audioUrl);
      if (!isAccessible) {
        throw Exception(
          'Audio URL is not accessible or returns invalid content',
        );
      }

      // Stop current playback first if playing
      if (_audioPlayer.playing) {
        Logger.info('Stopping current playback...');
        await _audioPlayer.stop();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // Set current podcast
      _currentPodcast.value = podcast;

      Logger.info('Loading audio from: ${podcast.audioUrl}');

      // Try multiple loading strategies
      bool loadSuccess = false;

      // Strategy 1: AudioSource.uri with headers
      if (!loadSuccess) {
        try {
          Logger.info('Strategy 1: Using AudioSource.uri with headers');
          await _audioPlayer.setAudioSource(
            AudioSource.uri(
              Uri.parse(podcast.audioUrl),
              headers: {
                'User-Agent': 'PowerUp/1.0',
                'Accept': 'audio/*, application/octet-stream, */*',
                'Accept-Encoding': 'identity',
                'Range': 'bytes=0-',
              },
            ),
          );

          // Wait for loading to complete
          await _waitForAudioReady();

          await _audioPlayer.play();
          loadSuccess = true;
          Logger.info('Strategy 1 successful for podcast: ${podcast.title}');
        } catch (e) {
          Logger.warning('Strategy 1 failed: $e');
        }
      }

      // Strategy 2: Simple setUrl
      if (!loadSuccess) {
        try {
          Logger.info('Strategy 2: Using setUrl method');
          await _audioPlayer.stop();
          await Future.delayed(const Duration(milliseconds: 500));

          await _audioPlayer.setUrl(podcast.audioUrl);
          await _waitForAudioReady();
          await _audioPlayer.play();
          loadSuccess = true;
          Logger.info('Strategy 2 successful for podcast: ${podcast.title}');
        } catch (e) {
          Logger.warning('Strategy 2 failed: $e');
        }
      }

      // Strategy 3: AudioSource.uri without headers
      if (!loadSuccess) {
        try {
          Logger.info('Strategy 3: Using AudioSource.uri without headers');
          await _audioPlayer.stop();
          await Future.delayed(const Duration(milliseconds: 500));

          await _audioPlayer.setAudioSource(
            AudioSource.uri(Uri.parse(podcast.audioUrl)),
          );
          await _waitForAudioReady();
          await _audioPlayer.play();
          loadSuccess = true;
          Logger.info('Strategy 3 successful for podcast: ${podcast.title}');
        } catch (e) {
          Logger.warning('Strategy 3 failed: $e');
        }
      }

      if (!loadSuccess) {
        throw Exception('All loading strategies failed for audio URL');
      }
    } catch (e) {
      Logger.error('Error playing podcast: $e');
      _errorMessage.value = 'Failed to play podcast: $e';
      _currentPodcast.value = null; // Clear current podcast on error
    } finally {
      _isLoading.value = false;
    }
  }

  /// Pause playback
  Future<void> pausePodcast() async {
    try {
      if (_audioPlayer.playing) {
        await _audioPlayer.pause();
        Logger.info('Podcast paused');
      } else {
        Logger.info('Podcast is not playing, cannot pause');
      }
    } catch (e) {
      _errorMessage.value = 'Failed to pause podcast: $e';
      Logger.error('Error pausing podcast: $e');
    }
  }

  /// Resume playback
  Future<void> resumePodcast() async {
    try {
      if (!_audioPlayer.playing && _currentPodcast.value != null) {
        await _audioPlayer.play();
        Logger.info('Podcast resumed');
      } else if (_currentPodcast.value == null) {
        Logger.warning('No podcast loaded to resume');
      } else {
        Logger.info('Podcast is already playing');
      }
    } catch (e) {
      _errorMessage.value = 'Failed to resume podcast: $e';
      Logger.error('Error resuming podcast: $e');
    }
  }

  /// Stop playback
  Future<void> stopPodcast() async {
    try {
      await _audioPlayer.stop();
      _currentPodcast.value = null;
      _currentPosition.value = Duration.zero;
      _totalDuration.value = Duration.zero;
      Logger.info('Podcast stopped');
    } catch (e) {
      _errorMessage.value = 'Failed to stop podcast: $e';
      Logger.error('Error stopping podcast: $e');
    }
  }

  /// Seek to a specific position
  Future<void> seekTo(Duration position) async {
    try {
      if (_currentPodcast.value != null &&
          _totalDuration.value.inMilliseconds > 0) {
        // Clamp position to valid range
        Duration clampedPosition;
        if (position < Duration.zero) {
          clampedPosition = Duration.zero;
        } else if (position > _totalDuration.value) {
          clampedPosition = _totalDuration.value;
        } else {
          clampedPosition = position;
        }

        await _audioPlayer.seek(clampedPosition);
        Logger.info('Seeked to: ${clampedPosition.inSeconds}s');
      } else {
        Logger.warning('Cannot seek: no podcast loaded or duration unknown');
      }
    } catch (e) {
      _errorMessage.value = 'Failed to seek: $e';
      Logger.error('Error seeking: $e');
    }
  }

  /// Change playback speed
  Future<void> setPlaybackSpeed(double speed) async {
    try {
      await _audioPlayer.setSpeed(speed);
      _playbackSpeed.value = speed;
      Logger.info('Playback speed set to: ${speed}x');
    } catch (e) {
      _errorMessage.value = 'Failed to change speed: $e';
      Logger.error('Error changing speed: $e');
    }
  }

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    try {
      final clampedVolume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(clampedVolume);
      _volume.value = clampedVolume;
      Logger.info('Volume set to: ${(clampedVolume * 100).round()}%');
    } catch (e) {
      _errorMessage.value = 'Failed to set volume: $e';
      Logger.error('Error setting volume: $e');
    }
  }

  /// Play a podcast (same as playPodcast but with different name for UI consistency)
  Future<void> play(PodcastEntity podcast) async {
    // If the same podcast is currently loaded, just resume
    if (_currentPodcast.value?.id == podcast.id && !_audioPlayer.playing) {
      await resumePodcast();
    } else {
      // Otherwise load and play the new podcast
      await playPodcast(podcast);
    }
  }

  /// Pause the current podcast
  Future<void> pause() async {
    await pausePodcast();
  }

  /// Skip forward by 15 seconds
  Future<void> skipForward() async {
    final newPosition = _currentPosition.value + const Duration(seconds: 15);
    final clampedPosition =
        newPosition > _totalDuration.value ? _totalDuration.value : newPosition;
    await seekTo(clampedPosition);
  }

  /// Skip backward by 15 seconds
  Future<void> skipBackward() async {
    final newPosition = _currentPosition.value - const Duration(seconds: 15);
    final clampedPosition =
        newPosition < Duration.zero ? Duration.zero : newPosition;
    await seekTo(clampedPosition);
  }

  /// Mark podcast as listened
  Future<void> markAsListened(String podcastId) async {
    try {
      final result = await markPodcastAsListenedUseCase(
        MarkPodcastAsListenedParams(podcastId: podcastId),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          Logger.error(
            'Failed to mark podcast as listened: ${failure.message}',
          );
        },
        (updatedPodcast) {
          // Update local state
          _updatePodcastInLists(updatedPodcast);
          Logger.info('Podcast marked as listened: ${updatedPodcast.title}');
        },
      );
    } catch (e) {
      _errorMessage.value = 'Failed to mark podcast as listened: $e';
      Logger.error('Error marking podcast as listened: $e');
    }
  }

  /// Generate a new podcast
  Future<void> generateNewPodcast({
    List<String>? topics,
    String? style,
    int? duration,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Get mood and goal data from AiReportsController
      String? mood;
      List<String>? goalTitles;

      try {
        final aiReportsController = Get.find<AiReportsController>();

        // Get current mood if available
        mood =
            aiReportsController.currentMood.isNotEmpty
                ? aiReportsController.currentMood
                : null;

        // Get active goals titles
        final activeGoals = aiReportsController.getActiveGoals();
        goalTitles =
            activeGoals.isNotEmpty
                ? activeGoals.map((goal) => goal.title).toList()
                : null;

        Logger.info('Generating podcast with mood: $mood, goals: $goalTitles');
      } catch (e) {
        // If AiReportsController is not available, continue without mood/goal data
        Logger.warning(
          'AiReportsController not available, generating podcast without mood/goal data: $e',
        );
      }

      final result = await generatePodcastUseCase(
        mood != null || goalTitles != null
            ? GeneratePodcastParams.withMoodAndGoals(
              topics: topics,
              style: style ?? 'personalized',
              duration: duration ?? 600,
              mood: mood ?? 'neutral', // default mood if not available
              goals: goalTitles ?? [], // empty list if no goals
            )
            : GeneratePodcastParams(
              topics: topics,
              style: style,
              duration: duration,
            ),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          Logger.error('Failed to generate podcast: ${failure.message}');
        },
        (podcast) {
          // Add to history and set as daily if it's today's
          _podcastHistory.assignAll([podcast, ..._podcastHistory]);
          if (podcast.isToday) {
            _dailyPodcast.value = podcast;
          }
          Logger.info('New podcast generated: ${podcast.title}');
        },
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Generate a mood-based podcast using current mood and goals
  Future<void> generateMoodBasedPodcast({
    List<String>? topics,
    String? style,
    int? duration,
  }) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      // Get mood and goal data from AiReportsController
      final aiReportsController = Get.find<AiReportsController>();

      // Get current mood
      final mood =
          aiReportsController.currentMood.isNotEmpty
              ? aiReportsController.currentMood
              : 'neutral';

      // Get active goals titles
      final activeGoals = aiReportsController.getActiveGoals();
      final goalTitles = activeGoals.map((goal) => goal.title).toList();

      Logger.info(
        'Generating mood-based podcast with mood: $mood, goals: $goalTitles',
      );

      final result = await generatePodcastUseCase(
        GeneratePodcastParams.withMoodAndGoals(
          topics: topics,
          style: style ?? 'personalized',
          duration: duration ?? 600,
          mood: mood,
          goals: goalTitles,
        ),
      );

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          Logger.error(
            'Failed to generate mood-based podcast: ${failure.message}',
          );
        },
        (podcast) {
          // Add to history and set as daily if it's today's
          _podcastHistory.assignAll([podcast, ..._podcastHistory]);
          if (podcast.isToday) {
            _dailyPodcast.value = podcast;
          }
          Logger.info('New mood-based podcast generated: ${podcast.title}');
        },
      );
    } catch (e) {
      _errorMessage.value = 'Failed to generate mood-based podcast: $e';
      Logger.error('Error generating mood-based podcast: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh data
  @override
  Future<void> refresh() async {
    await Future.wait([loadDailyPodcast(), loadPodcastHistory()]);
  }

  /// Handle podcast completion
  void _onPodcastCompleted() {
    final current = _currentPodcast.value;
    if (current != null && !current.isListened) {
      markAsListened(current.id);
    }
  }

  /// Update podcast in all relevant lists
  void _updatePodcastInLists(PodcastEntity updatedPodcast) {
    // Update daily podcast if it matches
    if (_dailyPodcast.value?.id == updatedPodcast.id) {
      _dailyPodcast.value = updatedPodcast;
    }

    // Update current podcast if it matches
    if (_currentPodcast.value?.id == updatedPodcast.id) {
      _currentPodcast.value = updatedPodcast;
    }

    // Update in history list
    final historyIndex = _podcastHistory.indexWhere(
      (p) => p.id == updatedPodcast.id,
    );
    if (historyIndex != -1) {
      final updatedList = List<PodcastEntity>.from(_podcastHistory);
      updatedList[historyIndex] = updatedPodcast;
      _podcastHistory.assignAll(updatedList);
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Get formatted current position
  String get formattedCurrentPosition {
    return _formatDuration(_currentPosition.value);
  }

  /// Get formatted total duration
  String get formattedTotalDuration {
    return _formatDuration(_totalDuration.value);
  }

  /// Get progress as percentage (0.0 to 1.0)
  double get progress {
    if (_totalDuration.value.inMilliseconds == 0) return 0.0;
    return _currentPosition.value.inMilliseconds /
        _totalDuration.value.inMilliseconds;
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  /// Validate audio URL and format
  bool _isValidAudioUrl(String url) {
    try {
      Logger.info('Validating audio URL: $url');

      if (url.isEmpty) {
        Logger.error('Audio URL is empty');
        return false;
      }

      final uri = Uri.parse(url);

      // Check if URL has a valid scheme
      if (!uri.hasScheme) {
        Logger.error('Audio URL missing scheme: $url');
        return false;
      }

      // Allow http, https, and file schemes
      if (!['http', 'https', 'file'].contains(uri.scheme.toLowerCase())) {
        Logger.error('Unsupported URL scheme: ${uri.scheme}');
        return false;
      }

      // Check for supported audio formats
      final supportedFormats = [
        '.mp3',
        '.wav',
        '.aac',
        '.ogg',
        '.m4a',
        '.flac',
        '.mp4', // Audio in MP4 container
        '.3gp',
        '.amr',
        '.ts', // MPEG Transport Stream
      ];

      final urlLower = url.toLowerCase();

      // Check file extension
      bool hasValidExtension = supportedFormats.any(
        (format) => urlLower.endsWith(format),
      );

      // Check for audio MIME types in URL (for streaming URLs)
      bool hasMimeType =
          urlLower.contains('audio/') ||
          urlLower.contains('type=audio') ||
          urlLower.contains('content-type=audio');

      // Check for streaming indicators
      bool isStreamingUrl =
          urlLower.contains('stream') ||
          urlLower.contains('playlist') ||
          urlLower.contains('radio') ||
          uri.pathSegments.isEmpty; // Could be a streaming endpoint

      if (!hasValidExtension && !hasMimeType && !isStreamingUrl) {
        Logger.warning('URL does not appear to be an audio file: $url');
        // Don't return false immediately, let the player try anyway
      }

      Logger.info('Audio URL validation passed: $url');
      return true;
    } catch (e) {
      Logger.error('Invalid audio URL: $url, error: $e');
      return false;
    }
  }

  /// Test if audio URL is accessible and returns valid content
  Future<bool> _testAudioUrlAccessibility(String url) async {
    try {
      Logger.info('Testing audio URL accessibility: $url');

      // For now, do basic URL validation
      // In a production app, you might want to make a HEAD request
      final uri = Uri.parse(url);
      if (!uri.hasScheme ||
          (!uri.scheme.startsWith('http') && !uri.scheme.startsWith('file'))) {
        Logger.warning('Invalid URL scheme: ${uri.scheme}');
        return false;
      }

      // Additional checks for known problematic URLs
      if (url.contains('localhost') &&
          !url.contains('http://') &&
          !url.contains('https://')) {
        Logger.warning('Localhost URL without proper protocol');
        return false;
      }

      Logger.info('URL appears to be valid');
      return true;
    } catch (e) {
      Logger.error('Error testing URL accessibility: $e');
      return false;
    }
  }

  /// Wait for audio to be ready for playback
  Future<void> _waitForAudioReady() async {
    Logger.info('Waiting for audio to be ready...');

    // Wait for the player to be in a ready state
    int attempts = 0;
    const maxAttempts = 10;
    const delayBetweenAttempts = Duration(milliseconds: 300);

    while (attempts < maxAttempts) {
      final state = _audioPlayer.processingState;
      Logger.info('Audio processing state: $state (attempt ${attempts + 1})');

      if (state == ProcessingState.ready ||
          state == ProcessingState.buffering) {
        Logger.info('Audio is ready for playback');
        return;
      }

      if (state == ProcessingState.idle) {
        // If still idle after a few attempts, it might be an error
        if (attempts > 3) {
          throw Exception('Audio source failed to load (remains idle)');
        }
      }

      await Future.delayed(delayBetweenAttempts);
      attempts++;
    }

    // If we get here, we've exceeded max attempts
    Logger.warning('Timeout waiting for audio to be ready, proceeding anyway');
  }

  /// Handle audio player errors and recovery
  void _handleAudioError(dynamic error) {
    Logger.error('Audio player error: $error');

    if (error.toString().contains('(-11850)') ||
        error.toString().contains('Operation Stopped')) {
      // This is the specific error we're trying to fix
      Logger.warning(
        'Operation stopped error detected, attempting recovery...',
      );
      _attemptAudioRecovery();
    } else if (error.toString().contains('Network')) {
      _errorMessage.value =
          'Network error: Please check your internet connection';
    } else if (error.toString().contains('Format')) {
      _errorMessage.value = 'Unsupported audio format';
    } else {
      _errorMessage.value = 'Audio playback error: $error';
    }
  }

  /// Attempt to recover from audio errors
  void _attemptAudioRecovery() async {
    try {
      final currentPodcast = _currentPodcast.value;
      if (currentPodcast != null) {
        Logger.info('Attempting audio recovery for: ${currentPodcast.title}');

        // Reset player state
        await _audioPlayer.stop();
        _isPlaying.value = false;
        _isBuffering.value = false;

        // Wait a moment then retry
        await Future.delayed(const Duration(seconds: 1));

        // Retry playback
        await playPodcast(currentPodcast);
      }
    } catch (e) {
      Logger.error('Audio recovery failed: $e');
      _errorMessage.value = 'Failed to recover audio playback';
    }
  }
}
