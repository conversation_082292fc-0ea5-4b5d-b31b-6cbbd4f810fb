import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../../domain/entities/podcast_entity.dart';
import '../controllers/podcast_controller.dart';
import 'podcast_player_screen.dart';

class PodcastsScreen extends StatelessWidget {
  const PodcastsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final controller = Get.find<PodcastController>();

    return MainLayoutScreen(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Text(
              'Podcasts',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Listen to inspiring stories and learn from experts',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),

            const SizedBox(height: 24),

            // Currently Playing Section
            Obx(
              () =>
                  controller.currentPodcast != null
                      ? Column(
                        children: [
                          _buildCurrentlyPlayingCard(context, controller),
                          const SizedBox(height: 24),
                        ],
                      )
                      : const SizedBox.shrink(),
            ),

            // Generate Daily Podcast Button
            _buildGeneratePodcastButton(context, controller),

            const SizedBox(height: 24),

            // Categories
            Text(
              'Categories',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            SizedBox(
              height: 40,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildCategoryChip(context, 'All', true),
                  _buildCategoryChip(context, 'Productivity', false),
                  _buildCategoryChip(context, 'Wellness', false),
                  _buildCategoryChip(context, 'Mindfulness', false),
                  _buildCategoryChip(context, 'Business', false),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Episodes List
            Text(
              'Your Podcasts',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // Loading or Content
            Expanded(
              child: Obx(() {
                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (controller.hasError) {
                  return _buildErrorState(context, controller);
                }
                if (controller.podcasts.isEmpty) {
                  return _buildEmptyState(context, controller);
                }
                return _buildPodcastsList(context, controller);
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(
    BuildContext context,
    String label,
    bool isSelected,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          // Category filter logic
        },
        backgroundColor: theme.colorScheme.surface,
        selectedColor: theme.colorScheme.primary.withValues(alpha: 0.2),
        labelStyle: TextStyle(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface.withValues(alpha: 0.7),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
        side: BorderSide(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
    );
  }

  Widget _buildCurrentlyPlayingCard(
    BuildContext context,
    PodcastController controller,
  ) {
    final theme = Theme.of(context);
    final podcast = controller.currentPodcast!;

    return GestureDetector(
      onTap: () => Get.to(() => PodcastPlayerScreen(podcast: podcast)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary.withValues(alpha: 0.1),
              theme.colorScheme.secondary.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: theme.colorScheme.primary.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.podcasts, color: Colors.white, size: 30),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    podcast.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    'Currently Playing',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Obx(
                    () => LinearProgressIndicator(
                      value:
                          controller.duration.inMilliseconds > 0
                              ? controller.position.inMilliseconds /
                                  controller.duration.inMilliseconds
                              : 0.0,
                      backgroundColor: theme.colorScheme.primary.withValues(
                        alpha: 0.2,
                      ),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Obx(
              () => IconButton(
                icon: Icon(
                  controller.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: theme.colorScheme.primary,
                  size: 32,
                ),
                onPressed: () {
                  if (controller.isPlaying) {
                    controller.pause();
                  } else {
                    controller.play(podcast);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneratePodcastButton(
    BuildContext context,
    PodcastController controller,
  ) {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      child: Obx(
        () => ElevatedButton.icon(
          onPressed:
              controller.isLoading
                  ? null
                  : () async {
                    await controller.generateNewPodcast();
                    if (!controller.hasError) {
                      Get.snackbar(
                        'Success',
                        'New podcast generated successfully!',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.green.withValues(alpha: 0.1),
                        colorText: Colors.green,
                      );
                    }
                  },
          icon:
              controller.isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Icon(Icons.auto_awesome),
          label: Text(
            controller.isLoading ? 'Generating...' : 'Generate Daily Podcast',
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, PodcastController controller) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.errorMessage,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              controller.loadDailyPodcast();
              controller.loadPodcastHistory();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, PodcastController controller) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.podcasts,
            size: 64,
            color: theme.colorScheme.primary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No podcasts yet',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Generate your first AI-powered podcast to get started!',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => controller.generateNewPodcast(),
            icon: const Icon(Icons.auto_awesome),
            label: const Text('Generate Podcast'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPodcastsList(
    BuildContext context,
    PodcastController controller,
  ) {
    return ListView.builder(
      itemCount: controller.podcasts.length,
      itemBuilder: (context, index) {
        final podcast = controller.podcasts[index];
        return _buildPodcastCard(context, controller, podcast);
      },
    );
  }

  Widget _buildPodcastCard(
    BuildContext context,
    PodcastController controller,
    PodcastEntity podcast,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Podcast Thumbnail
          GestureDetector(
            onTap: () => Get.to(() => PodcastPlayerScreen(podcast: podcast)),
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.podcasts,
                      color: theme.colorScheme.primary,
                      size: 32,
                    ),
                  ),
                  if (podcast.isToday)
                    Positioned(
                      top: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'TODAY',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Podcast Info
          Expanded(
            child: GestureDetector(
              onTap: () => Get.to(() => PodcastPlayerScreen(podcast: podcast)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    podcast.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'AI Generated',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        ' • ${podcast.formattedDuration}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    podcast.description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Play Button
          Obx(
            () => IconButton(
              icon: Icon(
                controller.currentPodcast?.id == podcast.id &&
                        controller.isPlaying
                    ? Icons.pause_circle_filled
                    : Icons.play_circle_filled,
                color: theme.colorScheme.primary,
                size: 32,
              ),
              onPressed: () {
                if (controller.currentPodcast?.id == podcast.id &&
                    controller.isPlaying) {
                  controller.pause();
                } else {
                  controller.play(podcast);
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
