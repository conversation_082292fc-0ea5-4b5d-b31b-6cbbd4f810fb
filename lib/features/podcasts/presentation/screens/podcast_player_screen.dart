import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../domain/entities/podcast_entity.dart';
import '../controllers/podcast_controller.dart';

class PodcastPlayerScreen extends StatelessWidget {
  final PodcastEntity podcast;

  const PodcastPlayerScreen({super.key, required this.podcast});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PodcastController>();
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and more options
            _buildHeader(context, controller),

            // Expanded content area
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  children: [
                    const SizedBox(height: 20),

                    // Podcast Artwork
                    _buildPodcastArtwork(context),

                    const SizedBox(height: 32),

                    // Podcast Title and Description
                    _buildPodcastInfo(context),

                    const SizedBox(height: 32),

                    // Progress Bar
                    _buildProgressBar(context, controller),

                    const SizedBox(height: 24),

                    // Time Labels
                    _buildTimeLabels(context, controller),

                    const SizedBox(height: 32),

                    // Playback Controls
                    _buildPlaybackControls(context, controller),

                    const SizedBox(height: 24),

                    // Secondary Controls
                    _buildSecondaryControls(context, controller),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, PodcastController controller) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: () => Get.back(),
          ),
          Text(
            'Now Playing',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildPodcastArtwork(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: 280,
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary.withValues(alpha: 0.8),
                theme.colorScheme.secondary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Icon(Icons.podcasts, size: 120, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildPodcastInfo(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Text(
          podcast.title,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          podcast.description,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildProgressBar(BuildContext context, PodcastController controller) {
    final theme = Theme.of(context);

    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        activeTrackColor: theme.colorScheme.primary,
        inactiveTrackColor: theme.colorScheme.primary.withValues(alpha: 0.3),
        thumbColor: theme.colorScheme.primary,
        overlayColor: theme.colorScheme.primary.withValues(alpha: 0.2),
        trackHeight: 4.0,
        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8.0),
      ),
      child: Obx(
        () => Slider(
          value: controller.position.inMilliseconds.toDouble(),
          max: controller.duration.inMilliseconds.toDouble(),
          onChanged: (value) {
            controller.seekTo(Duration(milliseconds: value.toInt()));
          },
        ),
      ),
    );
  }

  Widget _buildTimeLabels(BuildContext context, PodcastController controller) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Obx(
            () => Text(
              _formatDuration(controller.position),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Obx(
            () => Text(
              _formatDuration(controller.duration),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaybackControls(
    BuildContext context,
    PodcastController controller,
  ) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Skip backward
        IconButton(
          icon: Icon(
            Icons.replay_10,
            size: 36,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
          onPressed: () => controller.skipBackward(),
        ),

        // Previous track (disabled for single podcast)
        IconButton(
          icon: Icon(
            Icons.skip_previous,
            size: 40,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          onPressed: null,
        ),

        // Play/Pause
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Obx(
            () => IconButton(
              icon: Icon(
                controller.isPlaying ? Icons.pause : Icons.play_arrow,
                size: 40,
                color: Colors.white,
              ),
              onPressed: () {
                if (controller.isPlaying) {
                  controller.pause();
                } else {
                  controller.play(podcast);
                }
              },
            ),
          ),
        ),

        // Next track (disabled for single podcast)
        IconButton(
          icon: Icon(
            Icons.skip_next,
            size: 40,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          onPressed: null,
        ),

        // Skip forward
        IconButton(
          icon: Icon(
            Icons.forward_30,
            size: 36,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
          onPressed: () => controller.skipForward(),
        ),
      ],
    );
  }

  Widget _buildSecondaryControls(
    BuildContext context,
    PodcastController controller,
  ) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Playback speed
        InkWell(
          onTap: () => _showSpeedSelector(context, controller),
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${controller.playbackSpeed}x',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ),

        // Volume control
        IconButton(
          icon: Icon(
            controller.volume == 0
                ? Icons.volume_off
                : controller.volume < 0.5
                ? Icons.volume_down
                : Icons.volume_up,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
          onPressed: () => _showVolumeSlider(context, controller),
        ),

        // Add to favorites/listened
        IconButton(
          icon: Icon(
            podcast.isListened ? Icons.favorite : Icons.favorite_border,
            color:
                podcast.isListened
                    ? Colors.red
                    : theme.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
          onPressed: () {
            controller.markAsListened(podcast.id);
            Get.snackbar(
              'Success',
              podcast.isListened
                  ? 'Removed from favorites'
                  : 'Added to favorites',
              snackPosition: SnackPosition.BOTTOM,
            );
          },
        ),
      ],
    );
  }

  void _showSpeedSelector(BuildContext context, PodcastController controller) {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Playback Speed',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                ...speeds.map(
                  (speed) => ListTile(
                    title: Text('${speed}x', textAlign: TextAlign.center),
                    selected: controller.playbackSpeed == speed,
                    onTap: () {
                      controller.setPlaybackSpeed(speed);
                      Get.back();
                    },
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _showVolumeSlider(BuildContext context, PodcastController controller) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Volume'),
            content: StatefulBuilder(
              builder:
                  (context, setState) => Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Slider(
                        value: controller.volume,
                        onChanged: (value) {
                          controller.setVolume(value);
                          setState(() {});
                        },
                      ),
                      Text('${(controller.volume * 100).round()}%'),
                    ],
                  ),
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Done'),
              ),
            ],
          ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }
}
