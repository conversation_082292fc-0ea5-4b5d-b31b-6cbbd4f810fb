import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../../domain/entities/podcast_entity.dart';
import '../controllers/podcast_controller.dart';
import 'podcast_player_screen.dart';

class PodcastsScreen extends StatelessWidget {
  const PodcastsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PodcastController>();

    return MainLayoutScreen(
      hasTabbar: true,
      child: RefreshIndicator(
        onRefresh: () => controller.refresh(),
        child: CustomScrollView(
          slivers: [
            // Currently Playing Section
            SliverToBoxAdapter(
              child: Obx(
                () =>
                    controller.currentPodcast != null
                        ? Padding(
                          padding: const EdgeInsets.fromLTRB(16, 20, 16, 0),
                          child: _buildCurrentlyPlayingCard(
                            context,
                            controller,
                          ),
                        )
                        : const SizedBox(height: 20),
              ),
            ),

            // Generate Podcast Button
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: _buildGeneratePodcastButton(context, controller),
              ),
            ),

            // Floating Filter Tabs
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: _buildFloatingFilterTabs(context),
              ),
            ),

            // Episodes List
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              sliver: Obx(() {
                if (controller.isLoading) {
                  return const SliverToBoxAdapter(
                    child: Center(child: CircularProgressIndicator()),
                  );
                }

                if (controller.hasError) {
                  return SliverToBoxAdapter(
                    child: _buildErrorState(context, controller),
                  );
                }

                if (controller.podcasts.isEmpty) {
                  return SliverToBoxAdapter(
                    child: _buildEmptyFilterState(context, controller),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    if (index >= controller.podcasts.length) return null;
                    final podcast = controller.podcasts[index];
                    return _buildModernPodcastCard(
                      context,
                      controller,
                      podcast,
                      index,
                    );
                  }, childCount: controller.podcasts.length),
                );
              }),
            ),

            // Bottom padding for tab bar
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingFilterTabs(BuildContext context) {
    final theme = Theme.of(context);
    final controller = Get.find<PodcastController>();

    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Obx(
        () => Row(
          children: [
            _buildFilterTab(
              context,
              'All',
              'all',
              controller.selectedFilter == 'all',
            ),
            _buildFilterTab(
              context,
              'Recent',
              'recent',
              controller.selectedFilter == 'recent',
            ),
            _buildFilterTab(
              context,
              'Listened',
              'listened',
              controller.selectedFilter == 'listened',
            ),
            _buildFilterTab(
              context,
              'Favorites',
              'favorites',
              controller.selectedFilter == 'favorites',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterTab(
    BuildContext context,
    String label,
    String filter,
    bool isSelected,
  ) {
    final theme = Theme.of(context);
    final controller = Get.find<PodcastController>();

    return Expanded(
      child: GestureDetector(
        onTap: () {
          controller.setFilter(filter);
          // Show feedback to user
          final filterName =
              filter == 'all'
                  ? 'All Podcasts'
                  : filter == 'recent'
                  ? 'Recent Podcasts'
                  : filter == 'listened'
                  ? 'Listened Podcasts'
                  : 'Favorite Podcasts';

          Get.snackbar(
            'Filter Applied',
            'Showing $filterName',
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 1),
            backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.1),
            colorText: theme.colorScheme.primary,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(21),
            gradient:
                isSelected
                    ? LinearGradient(
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.secondary,
                      ],
                    )
                    : null,
            color: isSelected ? null : Colors.transparent,
          ),
          child: Center(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: 13,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color:
                    isSelected
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernPodcastCard(
    BuildContext context,
    PodcastController controller,
    PodcastEntity podcast,
    int index,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          onTap: () => Get.to(() => PodcastPlayerScreen(podcast: podcast)),
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Podcast Artwork
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.secondary,
                      ],
                    ),
                  ),
                  child:
                      podcast.imageUrl != null
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: Image.network(
                              podcast.imageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder:
                                  (context, error, stackTrace) =>
                                      _buildDefaultPodcastIcon(theme),
                            ),
                          )
                          : _buildDefaultPodcastIcon(theme),
                ),

                const SizedBox(width: 16),

                // Podcast Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        podcast.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        podcast.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  podcast.isListened
                                      ? theme.colorScheme.primary.withValues(
                                        alpha: 0.1,
                                      )
                                      : theme.colorScheme.secondary.withValues(
                                        alpha: 0.1,
                                      ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              podcast.isListened
                                  ? 'Listened'
                                  : '${podcast.duration ~/ 60} min',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color:
                                    podcast.isListened
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.secondary,
                                fontWeight: FontWeight.w500,
                                fontSize: 11,
                              ),
                            ),
                          ),
                          const Spacer(),
                          Text(
                            _formatDate(podcast.generatedDate),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.5,
                              ),
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // Play Button
                Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    onPressed: () => controller.play(podcast),
                    icon: Icon(
                      controller.currentPodcast?.id == podcast.id &&
                              controller.isPlaying
                          ? Icons.pause_rounded
                          : Icons.play_arrow_rounded,
                      color: theme.colorScheme.primary,
                    ),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                      minimumSize: const Size(40, 40),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultPodcastIcon(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
        ),
      ),
      child: Icon(
        Icons.podcasts_rounded,
        color: theme.colorScheme.onPrimary,
        size: 40,
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildCurrentlyPlayingCard(
    BuildContext context,
    PodcastController controller,
  ) {
    final theme = Theme.of(context);
    final podcast = controller.currentPodcast!;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          onTap: () => Get.to(() => PodcastPlayerScreen(podcast: podcast)),
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            theme.colorScheme.primary,
                            theme.colorScheme.secondary,
                          ],
                        ),
                      ),
                      child: Icon(
                        Icons.podcasts_rounded,
                        color: theme.colorScheme.onPrimary,
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Now Playing',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            podcast.title,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    Obx(
                      () => Container(
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.1,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: () {
                            if (controller.isPlaying) {
                              controller.pause();
                            } else {
                              controller.play(podcast);
                            }
                          },
                          icon: Icon(
                            controller.isPlaying
                                ? Icons.pause_rounded
                                : Icons.play_arrow_rounded,
                            color: theme.colorScheme.primary,
                            size: 28,
                          ),
                          style: IconButton.styleFrom(
                            padding: const EdgeInsets.all(8),
                            minimumSize: const Size(44, 44),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Obx(
                  () => Column(
                    children: [
                      LinearProgressIndicator(
                        value:
                            controller.duration.inMilliseconds > 0
                                ? controller.position.inMilliseconds /
                                    controller.duration.inMilliseconds
                                : 0.0,
                        backgroundColor: theme.colorScheme.primary.withValues(
                          alpha: 0.2,
                        ),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            controller.formattedCurrentPosition,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                          Text(
                            controller.formattedTotalDuration,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGeneratePodcastButton(
    BuildContext context,
    PodcastController controller,
  ) {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      child: Obx(
        () => ElevatedButton.icon(
          onPressed:
              controller.isLoading
                  ? null
                  : () async {
                    await controller.generateNewPodcast();
                    if (!controller.hasError) {
                      Get.snackbar(
                        'Success',
                        'New podcast generated successfully!',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.green.withValues(alpha: 0.1),
                        colorText: Colors.green,
                      );
                    }
                  },
          icon:
              controller.isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Icon(Icons.auto_awesome_rounded),
          label: Text(
            controller.isLoading ? 'Generating...' : 'Generate New Podcast',
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 0,
            shadowColor: Colors.transparent,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, PodcastController controller) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.errorMessage,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              controller.loadDailyPodcast();
              controller.loadPodcastHistory();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyFilterState(
    BuildContext context,
    PodcastController controller,
  ) {
    final theme = Theme.of(context);

    String getEmptyStateMessage() {
      switch (controller.selectedFilter) {
        case 'recent':
          return 'No recent podcasts found.\nPodcasts from the last 7 days will appear here.';
        case 'listened':
          return 'No listened podcasts yet.\nPodcasts you\'ve completed will appear here.';
        case 'favorites':
          return 'No favorite podcasts yet.\nYour favorite podcasts will appear here.';
        default:
          return 'No podcasts available.\nGenerate your first podcast to get started!';
      }
    }

    IconData getEmptyStateIcon() {
      switch (controller.selectedFilter) {
        case 'recent':
          return Icons.schedule_rounded;
        case 'listened':
          return Icons.check_circle_outline_rounded;
        case 'favorites':
          return Icons.favorite_border_rounded;
        default:
          return Icons.podcasts_rounded;
      }
    }

    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              getEmptyStateIcon(),
              size: 60,
              color: theme.colorScheme.primary.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            getEmptyStateMessage(),
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              height: 1.5,
            ),
          ),
          if (controller.selectedFilter != 'all') ...[
            const SizedBox(height: 24),
            OutlinedButton.icon(
              onPressed: () => controller.setFilter('all'),
              icon: const Icon(Icons.clear_all_rounded),
              label: const Text('Show All Podcasts'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
