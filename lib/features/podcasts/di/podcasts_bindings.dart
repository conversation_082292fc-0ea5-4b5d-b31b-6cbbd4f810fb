import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_local_data_source.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_local_data_source_impl.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_remote_data_source.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_remote_data_source_impl.dart';
import 'package:power_up/features/podcasts/data/repositories/podcast_repository_impl.dart';
import 'package:power_up/features/podcasts/domain/repositories/podcast_repository.dart';
import 'package:power_up/features/podcasts/domain/usecases/get_daily_podcast_usecase.dart';
import 'package:power_up/features/podcasts/domain/usecases/get_podcast_history_usecase.dart';
import 'package:power_up/features/podcasts/domain/usecases/mark_podcast_as_listened_usecase.dart';
import 'package:power_up/features/podcasts/domain/usecases/get_podcast_by_id_usecase.dart';
import 'package:power_up/features/podcasts/domain/usecases/generate_podcast_usecase.dart';
import 'package:power_up/features/podcasts/presentation/controllers/podcast_controller.dart';

/// Bindings for podcast feature
class PodcastsBindings implements Bindings {
  @override
  void dependencies() {
    // Core dependencies
    final dio = Get.find<Dio>();
    final networkInfo = Get.find<NetworkInfo>();
    final storageService = Get.find<StorageService>();

    // Data sources
    Get.lazyPut<PodcastLocalDataSource>(
      () => PodcastLocalDataSourceImpl(storageService: storageService),
    );
    Get.lazyPut<PodcastRemoteDataSource>(
      () => PodcastRemoteDataSourceImpl(dio: dio),
    );

    // Repository
    Get.lazyPut<PodcastRepository>(
      () => PodcastRepositoryImpl(
        remoteDataSource: Get.find<PodcastRemoteDataSource>(),
        localDataSource: Get.find<PodcastLocalDataSource>(),
        networkInfo: networkInfo,
      ),
    );

    // Use cases
    Get.lazyPut(() => GetDailyPodcastUseCase(Get.find<PodcastRepository>()));
    Get.lazyPut(() => GetPodcastHistoryUseCase(Get.find<PodcastRepository>()));
    Get.lazyPut(
      () => MarkPodcastAsListenedUseCase(Get.find<PodcastRepository>()),
    );
    Get.lazyPut(() => GetPodcastByIdUseCase(Get.find<PodcastRepository>()));
    Get.lazyPut(() => GeneratePodcastUseCase(Get.find<PodcastRepository>()));

    // Controller
    Get.lazyPut(
      () => PodcastController(
        getDailyPodcastUseCase: Get.find<GetDailyPodcastUseCase>(),
        getPodcastHistoryUseCase: Get.find<GetPodcastHistoryUseCase>(),
        markPodcastAsListenedUseCase: Get.find<MarkPodcastAsListenedUseCase>(),
        getPodcastByIdUseCase: Get.find<GetPodcastByIdUseCase>(),
        generatePodcastUseCase: Get.find<GeneratePodcastUseCase>(),
      ),
    );
  }
}
