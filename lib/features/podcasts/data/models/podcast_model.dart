import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';

/// Data model for Podcast, used for serialization/deserialization
class PodcastModel extends PodcastEntity {
  const PodcastModel({
    required super.id,
    required super.title,
    required super.audioUrl,
    required super.duration,
    required super.description,
    required super.generatedDate,
    required super.isListened,
    super.listenedAt,
    required super.createdAt,
    required super.updatedAt,
    super.imageUrl,
    super.tags,
  });

  /// Create a PodcastModel from a JSON map
  factory PodcastModel.fromJson(Map<String, dynamic> json) {
    return PodcastModel(
      id: json['id'],
      title: json['title'],
      audioUrl: json['audioUrl'],
      duration: json['duration'] ?? 0,
      description: json['description'] ?? '',
      generatedDate:
          json['generatedDate'] == null
              ? DateTime.now()
              : DateTime.parse(json['generatedDate']),
      isListened: json['isListened'] ?? false,
      listenedAt:
          json['listenedAt'] != null
              ? DateTime.parse(json['listenedAt'])
              : null,
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'])
              : DateTime.now(),
      imageUrl: json['imageUrl'],
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  /// Convert PodcastModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'audioUrl': audioUrl,
      'duration': duration,
      'description': description,
      'generatedDate': generatedDate.toIso8601String(),
      'isListened': isListened,
      'listenedAt': listenedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'imageUrl': imageUrl,
      'tags': tags,
    };
  }

  /// Create a PodcastModel from a PodcastEntity
  factory PodcastModel.fromEntity(PodcastEntity entity) {
    return PodcastModel(
      id: entity.id,
      title: entity.title,
      audioUrl: entity.audioUrl,
      duration: entity.duration,
      description: entity.description,
      generatedDate: entity.generatedDate,
      isListened: entity.isListened,
      listenedAt: entity.listenedAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      imageUrl: entity.imageUrl,
      tags: entity.tags,
    );
  }

  /// Create a copy of this PodcastModel with the given fields replaced
  @override
  PodcastModel copyWith({
    String? id,
    String? title,
    String? audioUrl,
    int? duration,
    String? description,
    DateTime? generatedDate,
    bool? isListened,
    DateTime? listenedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
    List<String>? tags,
  }) {
    return PodcastModel(
      id: id ?? this.id,
      title: title ?? this.title,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      description: description ?? this.description,
      generatedDate: generatedDate ?? this.generatedDate,
      isListened: isListened ?? this.isListened,
      listenedAt: listenedAt ?? this.listenedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
      tags: tags ?? this.tags,
    );
  }

  @override
  String toString() {
    return 'PodcastModel(id: $id, title: $title, duration: $duration, isListened: $isListened)';
  }
}
