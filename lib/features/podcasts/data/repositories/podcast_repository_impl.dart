import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_local_data_source.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_remote_data_source.dart';
import 'package:power_up/features/podcasts/domain/entities/podcast_entity.dart';
import 'package:power_up/features/podcasts/domain/repositories/podcast_repository.dart';

/// Implementation of the PodcastRepository
class PodcastRepositoryImpl implements PodcastRepository {
  final PodcastRemoteDataSource remoteDataSource;
  final PodcastLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  PodcastRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<void> init() async {
    await localDataSource.init();
  }

  @override
  Future<void> clearData() async {
    await localDataSource.clearData();
  }

  @override
  Future<Either<Failure, PodcastEntity?>> getDailyPodcast() async {
    if (await networkInfo.isConnected) {
      try {
        final podcast = await remoteDataSource.getDailyPodcast();

        if (podcast != null) {
          // Cache the daily podcast locally
          await localDataSource.cacheDailyPodcast(podcast);
          return Right(podcast);
        } else {
          // Try to get from local cache
          final localPodcast = await localDataSource.getDailyPodcast();
          return Right(localPodcast);
        }
      } on ServerException catch (e) {
        // Fallback to local cache on server error
        try {
          final localPodcast = await localDataSource.getDailyPodcast();
          return Right(localPodcast);
        } on CacheException {
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        }
      }
    } else {
      try {
        final localPodcast = await localDataSource.getDailyPodcast();
        return Right(localPodcast);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<PodcastEntity>>> getPodcastHistory({
    int? limit,
    int? offset,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final podcasts = await remoteDataSource.getPodcastHistory(
          limit: limit,
          offset: offset,
        );

        // Save podcasts locally for offline access
        await localDataSource.savePodcasts(podcasts);

        return Right(podcasts.cast<PodcastEntity>());
      } on ServerException catch (e) {
        // Fallback to local cache
        try {
          final localPodcasts = await localDataSource.getPodcastHistory(
            limit: limit,
            offset: offset,
          );
          return Right(localPodcasts.cast<PodcastEntity>());
        } on CacheException {
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        }
      }
    } else {
      try {
        final localPodcasts = await localDataSource.getPodcastHistory(
          limit: limit,
          offset: offset,
        );
        return Right(localPodcasts.cast<PodcastEntity>());
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, PodcastEntity>> getPodcastById(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final podcast = await remoteDataSource.getPodcastById(id);
        await localDataSource.savePodcast(podcast);
        return Right(podcast);
      } on ServerException catch (e) {
        // Fallback to local cache
        try {
          final localPodcast = await localDataSource.getPodcastById(id);
          if (localPodcast != null) {
            return Right(localPodcast);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException {
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        }
      }
    } else {
      try {
        final localPodcast = await localDataSource.getPodcastById(id);
        if (localPodcast != null) {
          return Right(localPodcast);
        }
        return const Left(
          CacheFailure(message: 'Podcast not found in local cache'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<PodcastEntity>>> getAllPodcasts({
    bool? isListened,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final podcasts = await remoteDataSource.getAllPodcasts(
          isListened: isListened,
          startDate: startDate,
          endDate: endDate,
          limit: limit,
          offset: offset,
        );

        // Save podcasts locally
        await localDataSource.savePodcasts(podcasts);

        return Right(podcasts.cast<PodcastEntity>());
      } on ServerException catch (e) {
        // Fallback to local cache
        try {
          final localPodcasts = await localDataSource.getPodcasts(
            isListened: isListened,
            startDate: startDate,
            endDate: endDate,
            limit: limit,
            offset: offset,
          );
          return Right(localPodcasts.cast<PodcastEntity>());
        } on CacheException {
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        }
      }
    } else {
      try {
        final localPodcasts = await localDataSource.getPodcasts(
          isListened: isListened,
          startDate: startDate,
          endDate: endDate,
          limit: limit,
          offset: offset,
        );
        return Right(localPodcasts.cast<PodcastEntity>());
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, PodcastEntity>> markAsListened(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final podcast = await remoteDataSource.markAsListened(id);
        await localDataSource.savePodcast(podcast);
        return Right(podcast);
      } on ServerException catch (e) {
        // Update locally and mark for sync
        try {
          await localDataSource.updatePodcastListenedStatus(id, true);
          final updatedPodcast = await localDataSource.getPodcastById(id);
          if (updatedPodcast != null) {
            return Right(updatedPodcast);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException {
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        }
      }
    } else {
      try {
        await localDataSource.updatePodcastListenedStatus(id, true);
        final updatedPodcast = await localDataSource.getPodcastById(id);
        if (updatedPodcast != null) {
          return Right(updatedPodcast);
        }
        return const Left(
          CacheFailure(message: 'Podcast not found in local cache'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, PodcastEntity>> generatePodcast({
    List<String>? topics,
    String? style,
    int? duration,
    String? mood,
    List<String>? goals,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final podcast = await remoteDataSource.generatePodcast(
          topics: topics,
          style: style,
          duration: duration,
          mood: mood,
          goals: goals,
        );
        await localDataSource.savePodcast(podcast);
        return Right(podcast);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      return const Left(
        NetworkFailure(
          message: 'Cannot generate podcast without internet connection',
        ),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> deletePodcast(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.deletePodcast(id);
        await localDataSource.deletePodcast(id);
        return Right(result);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        await localDataSource.deletePodcast(id);
        return const Right(true);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, String>> getPodcastAudioUrl(String filename) async {
    if (await networkInfo.isConnected) {
      try {
        final audioUrl = await remoteDataSource.getPodcastAudioUrl(filename);
        return Right(audioUrl);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      return const Left(
        NetworkFailure(
          message: 'Cannot get audio URL without internet connection',
        ),
      );
    }
  }
}
