import 'package:power_up/core/data/remote_datasource.dart';
import 'package:power_up/features/podcasts/data/models/podcast_model.dart';

/// Definition for the remote data source for podcasts
abstract class PodcastRemoteDataSource extends RemoteDataSource {
  /// Get today's daily podcast
  Future<PodcastModel?> getDailyPodcast();

  /// Get podcast history with optional pagination
  Future<List<PodcastModel>> getPodcastHistory({int? limit, int? offset});

  /// Get a podcast by ID
  Future<PodcastModel> getPodcastById(String id);

  /// Get all podcasts with optional filtering
  Future<List<PodcastModel>> getAllPodcasts({
    bool? isListened,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  });

  /// Mark a podcast as listened
  Future<PodcastModel> markAsListened(String id);

  /// Generate a new podcast
  Future<PodcastModel> generatePodcast({
    List<String>? topics,
    String? style,
    int? duration,
    String? mood,
    List<String>? goals,
  });

  /// Delete a podcast by ID
  Future<bool> deletePodcast(String id);

  /// Get podcast audio file URL for streaming
  Future<String> getPodcastAudioUrl(String filename);
}
