import 'package:dio/dio.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_remote_data_source.dart';
import 'package:power_up/features/podcasts/data/models/podcast_model.dart';

/// Implementation of the PodcastRemoteDataSource
class PodcastRemoteDataSourceImpl extends PodcastRemoteDataSource {
  final Dio dio;

  PodcastRemoteDataSourceImpl({required this.dio});

  @override
  Future<void> init() async {
    // No initialization needed for remote data source
  }

  @override
  Future<void> dispose() async {
    // No resources to dispose for remote data source
  }

  @override
  Future<T> request<T>({
    required String endpoint,
    required T Function(Map<String, dynamic> json) fromJson,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    String method = 'GET',
    Map<String, String>? headers,
  }) async {
    try {
      late Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await dio.get(
            endpoint,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'POST':
          response = await dio.post(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'PUT':
          response = await dio.put(
            endpoint,
            data: body,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        case 'DELETE':
          response = await dio.delete(
            endpoint,
            queryParameters: queryParameters,
            options: Options(headers: headers),
          );
          break;
        default:
          throw UnsupportedError('HTTP method $method is not supported');
      }

      return fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Network request failed',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<PodcastModel?> getDailyPodcast() async {
    try {
      final response = await dio.get('/podcasts/daily');

      if (response.data == null) {
        return null;
      }

      return PodcastModel.fromJson(response.data);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return null; // No daily podcast available
      }
      throw ServerException(
        message: e.message ?? 'Failed to get daily podcast',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<List<PodcastModel>> getPodcastHistory({
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParameters = <String, dynamic>{};

      if (limit != null) {
        queryParameters['limit'] = limit;
      }
      if (offset != null) {
        queryParameters['offset'] = offset;
      }

      final response = await dio.get(
        '/podcasts',
        queryParameters: queryParameters,
      );

      return (response.data as List)
          .map((json) => PodcastModel.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get podcast history',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<PodcastModel> getPodcastById(String id) async {
    try {
      final response = await dio.get('/podcasts/$id');
      return PodcastModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get podcast by ID',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<List<PodcastModel>> getAllPodcasts({
    bool? isListened,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParameters = <String, dynamic>{};

      if (isListened != null) {
        queryParameters['isListened'] = isListened;
      }
      if (startDate != null) {
        queryParameters['startDate'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParameters['endDate'] = endDate.toIso8601String();
      }
      if (limit != null) {
        queryParameters['limit'] = limit;
      }
      if (offset != null) {
        queryParameters['offset'] = offset;
      }

      final response = await dio.get(
        '/podcasts',
        queryParameters: queryParameters,
      );

      return (response.data as List)
          .map((json) => PodcastModel.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get podcasts',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<PodcastModel> markAsListened(String id) async {
    try {
      final response = await dio.post('/podcasts/$id/listened');
      return PodcastModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to mark podcast as listened',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<PodcastModel> generatePodcast({
    List<String>? topics,
    String? style,
    int? duration,
    String? mood,
    List<String>? goals,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (topics != null && topics.isNotEmpty) {
        data['topics'] = topics;
      }
      if (style != null) {
        data['style'] = style;
      }
      if (duration != null) {
        data['duration'] = duration;
      }
      if (mood != null) {
        data['mood'] = mood;
      }
      if (goals != null && goals.isNotEmpty) {
        data['goals'] = goals;
      }

      final response = await dio.post('/podcasts/generate', data: data);

      return PodcastModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to generate podcast',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<bool> deletePodcast(String id) async {
    try {
      await dio.delete('/podcasts/$id');
      return true;
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to delete podcast',
        statusCode: e.response?.statusCode,
      );
    }
  }

  @override
  Future<String> getPodcastAudioUrl(String filename) async {
    try {
      final response = await dio.get('/podcasts/$filename');
      return response.data['audioUrl'] ?? response.realUri.toString();
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get podcast audio URL',
        statusCode: e.response?.statusCode,
      );
    }
  }
}
