import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/podcasts/data/datasources/podcast_local_data_source.dart';
import 'package:power_up/features/podcasts/data/models/podcast_model.dart';

/// Implementation of the PodcastLocalDataSource using StorageService
class PodcastLocalDataSourceImpl implements PodcastLocalDataSource {
  final StorageService? _providedStorageService;
  late final StorageService _storageService;
  bool _isInitialized = false;

  static const String _podcastsKey = 'podcasts';
  static const String _dailyPodcastKey = 'daily_podcast';
  static const String _unsyncedPodcastsKey = 'unsynced_podcasts';

  PodcastLocalDataSourceImpl({StorageService? storageService})
    : _providedStorageService = storageService;

  @override
  Future<void> init() async {
    if (!_isInitialized) {
      if (_providedStorageService != null) {
        _storageService = _providedStorageService;
      } else {
        _storageService = await StorageService.getInstance();
      }
      _isInitialized = true;
    }
  }

  @override
  Future<void> dispose() async {
    // No resources to dispose for StorageService
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await init();
    }
  }

  @override
  Future<T?> get<T>({
    required String key,
    required T Function(dynamic json) fromJson,
  }) async {
    await _ensureInitialized();
    final data = _storageService.getData(key);
    return data != null ? fromJson(data) : null;
  }

  @override
  Future<void> set<T>({required String key, required T data}) async {
    await _ensureInitialized();
    await _storageService.setData(key, data);
  }

  @override
  Future<void> remove(String key) async {
    await _ensureInitialized();
    await _storageService.removeData(key);
  }

  @override
  Future<void> clear() async {
    await _ensureInitialized();
    await _storageService.removeData(_podcastsKey);
    await _storageService.removeData(_dailyPodcastKey);
    await _storageService.removeData(_unsyncedPodcastsKey);
  }

  @override
  Future<void> clearData() async {
    await clear();
  }

  @override
  Future<void> savePodcast(PodcastModel podcast) async {
    try {
      await _ensureInitialized();
      final podcasts = await getPodcasts();
      final existingIndex = podcasts.indexWhere((p) => p.id == podcast.id);

      if (existingIndex != -1) {
        podcasts[existingIndex] = podcast;
      } else {
        podcasts.add(podcast);
      }

      final podcastsJson = podcasts.map((p) => p.toJson()).toList();
      await _storageService.setData(_podcastsKey, podcastsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to save podcast: $e');
    }
  }

  @override
  Future<void> savePodcasts(List<PodcastModel> podcasts) async {
    try {
      await _ensureInitialized();
      final podcastsJson = podcasts.map((p) => p.toJson()).toList();
      await _storageService.setData(_podcastsKey, podcastsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to save podcasts: $e');
    }
  }

  @override
  Future<List<PodcastModel>> getPodcasts({
    bool? isListened,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  }) async {
    try {
      await _ensureInitialized();
      final podcastsData = _storageService.getData<List>(_podcastsKey);
      if (podcastsData == null) {
        return [];
      }
      var podcasts =
          podcastsData
              .cast<Map<String, dynamic>>()
              .map((json) => PodcastModel.fromJson(json))
              .toList();

      // Apply filters
      if (isListened != null) {
        podcasts = podcasts.where((p) => p.isListened == isListened).toList();
      }

      if (startDate != null) {
        podcasts =
            podcasts
                .where(
                  (p) =>
                      p.generatedDate.isAfter(startDate) ||
                      p.generatedDate.isAtSameMomentAs(startDate),
                )
                .toList();
      }

      if (endDate != null) {
        podcasts =
            podcasts
                .where(
                  (p) =>
                      p.generatedDate.isBefore(endDate) ||
                      p.generatedDate.isAtSameMomentAs(endDate),
                )
                .toList();
      }

      // Sort by generated date (newest first)
      podcasts.sort((a, b) => b.generatedDate.compareTo(a.generatedDate));

      // Apply pagination
      if (offset != null) {
        podcasts = podcasts.skip(offset).toList();
      }
      if (limit != null) {
        podcasts = podcasts.take(limit).toList();
      }

      return podcasts;
    } catch (e) {
      throw CacheException(message: 'Failed to get podcasts: $e');
    }
  }

  @override
  Future<PodcastModel?> getPodcastById(String id) async {
    try {
      await _ensureInitialized();
      final podcasts = await getPodcasts();
      return podcasts.firstWhere(
        (podcast) => podcast.id == id,
        orElse: () => throw CacheException(message: 'Podcast not found'),
      );
    } catch (e) {
      if (e is CacheException && e.message.contains('Podcast not found')) {
        return null;
      }
      throw CacheException(message: 'Failed to get podcast by ID: $e');
    }
  }

  @override
  Future<PodcastModel?> getDailyPodcast() async {
    try {
      await _ensureInitialized();
      final dailyPodcastJson = _storageService.getData<Map<String, dynamic>>(
        _dailyPodcastKey,
      );
      if (dailyPodcastJson == null) return null;

      final dailyPodcast = PodcastModel.fromJson(dailyPodcastJson);

      // Check if it's still today's podcast
      if (dailyPodcast.isToday) {
        return dailyPodcast;
      }

      // Remove outdated daily podcast
      await _storageService.removeData(_dailyPodcastKey);
      return null;
    } catch (e) {
      throw CacheException(message: 'Failed to get daily podcast: $e');
    }
  }

  @override
  Future<List<PodcastModel>> getPodcastHistory({
    int? limit,
    int? offset,
  }) async {
    return getPodcasts(limit: limit, offset: offset);
  }

  @override
  Future<void> deletePodcast(String id) async {
    try {
      await _ensureInitialized();
      final podcasts = await getPodcasts();
      podcasts.removeWhere((podcast) => podcast.id == id);
      await savePodcasts(podcasts);
    } catch (e) {
      throw CacheException(message: 'Failed to delete podcast: $e');
    }
  }

  @override
  Future<void> deleteAllPodcasts() async {
    try {
      await _ensureInitialized();
      await _storageService.removeData(_podcastsKey);
      await _storageService.removeData(_dailyPodcastKey);
    } catch (e) {
      throw CacheException(message: 'Failed to delete all podcasts: $e');
    }
  }

  @override
  Future<void> updatePodcastListenedStatus(String id, bool isListened) async {
    try {
      await _ensureInitialized();
      final podcast = await getPodcastById(id);
      if (podcast != null) {
        final updatedPodcast = podcast.copyWith(
          isListened: isListened,
          listenedAt: isListened ? DateTime.now() : null,
        );
        await savePodcast(updatedPodcast);
      } else {
        throw CacheException(message: 'Podcast with ID $id not found');
      }
    } catch (e) {
      if (e is CacheException) {
        rethrow;
      }
      throw CacheException(
        message: 'Failed to update podcast listened status: $e',
      );
    }
  }

  @override
  Future<List<PodcastModel>> getUnsyncedPodcasts() async {
    try {
      await _ensureInitialized();
      final unsyncedJson =
          _storageService.getData<List>(_unsyncedPodcastsKey) ?? [];
      return unsyncedJson
          .cast<Map<String, dynamic>>()
          .map((json) => PodcastModel.fromJson(json))
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get unsynced podcasts: $e');
    }
  }

  @override
  Future<void> markPodcastSynced(String id) async {
    try {
      await _ensureInitialized();
      final unsyncedPodcasts = await getUnsyncedPodcasts();
      unsyncedPodcasts.removeWhere((podcast) => podcast.id == id);

      final unsyncedJson = unsyncedPodcasts.map((p) => p.toJson()).toList();
      await _storageService.setData(_unsyncedPodcastsKey, unsyncedJson);
    } catch (e) {
      throw CacheException(message: 'Failed to mark podcast as synced: $e');
    }
  }

  @override
  Future<void> cacheDailyPodcast(PodcastModel podcast) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(_dailyPodcastKey, podcast.toJson());
      await savePodcast(podcast); // Also save to general podcasts list
    } catch (e) {
      throw CacheException(message: 'Failed to cache daily podcast: $e');
    }
  }

  @override
  Future<List<PodcastModel>> getCachedPodcasts() async {
    // Return all locally stored podcasts for offline access
    return getPodcasts();
  }
}
