import 'package:power_up/core/data/local_datasource.dart';
import 'package:power_up/features/podcasts/data/models/podcast_model.dart';

/// Definition for the local data source for podcasts
abstract class PodcastLocalDataSource extends LocalDataSource {
  /// Save a podcast to local storage
  Future<void> savePodcast(PodcastModel podcast);

  /// Save multiple podcasts to local storage
  Future<void> savePodcasts(List<PodcastModel> podcasts);

  /// Get all podcasts from local storage with optional filtering
  Future<List<PodcastModel>> getPodcasts({
    bool? isListened,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
    int? offset,
  });

  /// Get a podcast by ID from local storage
  Future<PodcastModel?> getPodcastById(String id);

  /// Get today's daily podcast from local storage
  Future<PodcastModel?> getDailyPodcast();

  /// Get podcast history from local storage
  Future<List<PodcastModel>> getPodcastHistory({int? limit, int? offset});

  /// Delete a podcast from local storage
  Future<void> deletePodcast(String id);

  /// Delete all podcasts from local storage
  Future<void> deleteAllPodcasts();

  /// Update podcast listened status
  Future<void> updatePodcastListenedStatus(String id, bool isListened);

  /// Get podcasts that need syncing with the server
  Future<List<PodcastModel>> getUnsyncedPodcasts();

  /// Mark a podcast as synced
  Future<void> markPodcastSynced(String id);

  /// Cache the daily podcast
  Future<void> cacheDailyPodcast(PodcastModel podcast);

  /// Get cached podcasts for offline playback
  Future<List<PodcastModel>> getCachedPodcasts();

  /// Clear all data from local storage
  Future<void> clearData();
}
