import 'package:get/get.dart';
import '../../authentication/domain/usecases/get_current_user_usecase.dart';
import '../../authentication/domain/usecases/update_profile_usecase.dart';
import '../../user_management/domain/usecases/request_data_export_usecase.dart';
import '../../user_management/domain/usecases/delete_account_usecase.dart';
import '../presentation/controllers/profile_controller.dart';

/// Dependency injection bindings for profile feature
class ProfileBindings extends Bindings {
  @override
  void dependencies() {
    // Use cases
    Get.lazyPut<UpdateProfileUseCase>(() => UpdateProfileUseCase(Get.find()));

    // Controller
    Get.lazyPut<ProfileController>(
      () => ProfileController(
        getCurrentUserUseCase: Get.find<GetCurrentUserUseCase>(),
        updateProfileUseCase: Get.find<UpdateProfileUseCase>(),
        downloadUserDataPdfUseCase: Get.find<DownloadUserDataPdfUseCase>(),
        deleteAccountUseCase: Get.find<DeleteAccountUseCase>(),
      ),
    );
  }
}
