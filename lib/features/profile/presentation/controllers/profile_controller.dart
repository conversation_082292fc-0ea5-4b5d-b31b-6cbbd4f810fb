import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../authentication/domain/entities/user_entity.dart';
import '../../../authentication/domain/usecases/get_current_user_usecase.dart';
import '../../../authentication/domain/usecases/update_profile_usecase.dart';
import '../../../user_management/domain/usecases/request_data_export_usecase.dart';
import '../../../user_management/domain/usecases/delete_account_usecase.dart';

/// Controller for managing user profile operations
class ProfileController extends GetxController {
  final GetCurrentUserUseCase _getCurrentUserUseCase;
  final UpdateProfileUseCase _updateProfileUseCase;
  final DownloadUserDataPdfUseCase _downloadUserDataPdfUseCase;
  final DeleteAccountUseCase _deleteAccountUseCase;

  ProfileController({
    required GetCurrentUserUseCase getCurrentUserUseCase,
    required UpdateProfileUseCase updateProfileUseCase,
    required DownloadUserDataPdfUseCase downloadUserDataPdfUseCase,
    required DeleteAccountUseCase deleteAccountUseCase,
  }) : _getCurrentUserUseCase = getCurrentUserUseCase,
       _updateProfileUseCase = updateProfileUseCase,
       _downloadUserDataPdfUseCase = downloadUserDataPdfUseCase,
       _deleteAccountUseCase = deleteAccountUseCase;

  // Reactive variables
  final Rx<UserEntity?> _currentUser = Rx<UserEntity?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isUpdatingProfile = false.obs;
  final RxBool _isRequestingExport = false.obs;
  final RxBool _isDeletingAccount = false.obs;

  // Form controllers
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();

  // Getters
  UserEntity? get currentUser => _currentUser.value;
  bool get isLoading => _isLoading.value;
  bool get isUpdatingProfile => _isUpdatingProfile.value;
  bool get isRequestingExport => _isRequestingExport.value;
  bool get isDeletingAccount => _isDeletingAccount.value;

  @override
  void onInit() {
    super.onInit();
    loadUserProfile();
  }

  @override
  void onClose() {
    firstNameController.dispose();
    lastNameController.dispose();
    super.onClose();
  }

  /// Load current user profile
  Future<void> loadUserProfile() async {
    _isLoading.value = true;

    try {
      final result = await _getCurrentUserUseCase.call(NoParams());
      result.fold(
        (failure) {
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Get.theme.colorScheme.error,
            colorText: Get.theme.colorScheme.onError,
          );
        },
        (user) {
          _currentUser.value = user;
          // Update form controllers with current data
          firstNameController.text = user.firstName;
          lastNameController.text = user.lastName;
        },
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update user profile
  Future<void> updateProfile() async {
    if (firstNameController.text.trim().isEmpty ||
        lastNameController.text.trim().isEmpty) {
      Get.snackbar(
        'Validation Error',
        'First name and last name are required',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    _isUpdatingProfile.value = true;

    try {
      final params = UpdateProfileParams(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim(),
      );

      final result = await _updateProfileUseCase.call(params);
      result.fold(
        (failure) {
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Get.theme.colorScheme.error,
            colorText: Get.theme.colorScheme.onError,
          );
        },
        (user) {
          _currentUser.value = user;
          Get.snackbar(
            'Success',
            'Profile updated successfully',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Get.theme.colorScheme.primary,
            colorText: Get.theme.colorScheme.onPrimary,
          );
          Get.back(); // Go back to profile screen
        },
      );
    } finally {
      _isUpdatingProfile.value = false;
    }
  }

  /// Download user data as PDF
  Future<void> downloadUserDataPdf() async {
    _isRequestingExport.value = true;

    try {
      final result = await _downloadUserDataPdfUseCase();
      result.fold(
        (failure) {
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Get.theme.colorScheme.error,
            colorText: Get.theme.colorScheme.onError,
          );
        },
        (pdfData) async {
          try {
            final directory = await getDownloadsDirectory();
            if (directory != null) {
              final fileName =
                  'user_data_${DateTime.now().millisecondsSinceEpoch}.pdf';
              final file = File('${directory.path}/$fileName');
              await file.writeAsBytes(pdfData);

              Get.snackbar(
                'Success',
                'User data PDF downloaded to Downloads folder',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Get.theme.colorScheme.primary,
                colorText: Get.theme.colorScheme.onPrimary,
              );
            } else {
              Get.snackbar(
                'Error',
                'Could not access Downloads folder',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Get.theme.colorScheme.error,
                colorText: Get.theme.colorScheme.onError,
              );
            }
          } catch (e) {
            Get.snackbar(
              'Error',
              'Failed to save PDF: $e',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Get.theme.colorScheme.error,
              colorText: Get.theme.colorScheme.onError,
            );
          }
        },
      );
    } finally {
      _isRequestingExport.value = false;
    }
  }

  /// Delete user account
  Future<void> deleteAccount() async {
    _isDeletingAccount.value = true;

    try {
      final result = await _deleteAccountUseCase();
      result.fold(
        (failure) {
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Get.theme.colorScheme.error,
            colorText: Get.theme.colorScheme.onError,
          );
        },
        (success) {
          if (success) {
            Get.snackbar(
              'Success',
              'Account deletion request sent. You will be contacted via email.',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Get.theme.colorScheme.primary,
              colorText: Get.theme.colorScheme.onPrimary,
            );
          }
        },
      );
    } finally {
      _isDeletingAccount.value = false;
    }
  }

  /// Show confirmation dialog for account deletion
  Future<void> showDeleteAccountConfirmation() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'Are you sure you want to delete your account? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(
              foregroundColor: Get.theme.colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await deleteAccount();
    }
  }

  /// Show confirmation dialog for data export
  Future<void> showDataExportConfirmation() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Download Data'),
        content: const Text(
          'Download all your data as a PDF file? The file will be saved to your Downloads folder.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Download'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await downloadUserDataPdf();
    }
  }
}
