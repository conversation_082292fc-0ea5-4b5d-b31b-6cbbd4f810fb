import 'package:dio/dio.dart';
import '../../../../../core/error/exceptions.dart';
import '../../../../core/data/api/base_api_service.dart';
import '../../models/help_article_model.dart';
import 'help_remote_data_source.dart';

/// Implementation of help remote data source using API
class HelpRemoteDataSourceImpl extends BaseApiService
    implements HelpRemoteDataSource {
  HelpRemoteDataSourceImpl(super.dio);

  @override
  Future<List<HelpArticleModel>> getFAQs() async {
    try {
      return await get<List<HelpArticleModel>>(
        endpoint: '/help/articles/faq',
        fromData:
            (data) =>
                (data as List)
                    .map((json) => HelpArticleModel.fromJson(json))
                    .toList(),
      );
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Failed to get FAQs');
    } catch (e) {
      throw ServerException(message: 'Unknown error occurred');
    }
  }

  @override
  Future<List<HelpArticleModel>> getPopularArticles({int? limit}) async {
    try {
      return await get<List<HelpArticleModel>>(
        endpoint: '/help/articles/popular',
        queryParameters: limit != null ? {'limit': limit} : null,
        fromData:
            (data) =>
                (data as List)
                    .map((json) => HelpArticleModel.fromJson(json))
                    .toList(),
      );
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get popular articles',
      );
    } catch (e) {
      throw ServerException(message: 'Unknown error occurred');
    }
  }

  @override
  Future<List<HelpArticleModel>> searchArticles({
    String? query,
    String? category,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (query != null) queryParams['query'] = query;
      if (category != null) queryParams['category'] = category;
      if (limit != null) queryParams['limit'] = limit;
      if (offset != null) queryParams['offset'] = offset;

      return await get<List<HelpArticleModel>>(
        endpoint: '/help/articles/search',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
        fromData:
            (data) =>
                (data as List)
                    .map((json) => HelpArticleModel.fromJson(json))
                    .toList(),
      );
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Failed to search articles');
    } catch (e) {
      throw ServerException(message: 'Unknown error occurred');
    }
  }

  @override
  Future<List<HelpArticleModel>> getArticlesByCategory(String category) async {
    try {
      return await get<List<HelpArticleModel>>(
        endpoint: '/help/articles/category/$category',
        fromData:
            (data) =>
                (data as List)
                    .map((json) => HelpArticleModel.fromJson(json))
                    .toList(),
      );
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to get articles by category',
      );
    } catch (e) {
      throw ServerException(message: 'Unknown error occurred');
    }
  }

  @override
  Future<HelpArticleModel> getArticle(String id) async {
    try {
      return await get<HelpArticleModel>(
        endpoint: '/help/articles/$id',
        fromData: (data) => HelpArticleModel.fromJson(data),
      );
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Failed to get article');
    } catch (e) {
      throw ServerException(message: 'Unknown error occurred');
    }
  }

  @override
  Future<void> markArticleHelpful(String id, bool helpful) async {
    try {
      await post<void>(
        endpoint: '/help/articles/$id/helpful',
        data: {'helpful': helpful},
        fromData: (data) => {},
      );
    } on DioException catch (e) {
      throw ServerException(
        message: e.message ?? 'Failed to mark article as helpful',
      );
    } catch (e) {
      throw ServerException(message: 'Unknown error occurred');
    }
  }
}
