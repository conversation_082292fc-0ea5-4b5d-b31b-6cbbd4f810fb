import '../../models/help_article_model.dart';

/// Remote data source for help articles
abstract class HelpRemoteDataSource {
  /// Get frequently asked questions
  Future<List<HelpArticleModel>> getFAQs();

  /// Get popular help articles
  Future<List<HelpArticleModel>> getPopularArticles({int? limit});

  /// Search help articles
  Future<List<HelpArticleModel>> searchArticles({
    String? query,
    String? category,
    int? limit,
    int? offset,
  });

  /// Get help articles by category
  Future<List<HelpArticleModel>> getArticlesByCategory(String category);

  /// Get specific help article by ID
  Future<HelpArticleModel> getArticle(String id);

  /// Mark article as helpful
  Future<void> markArticleHelpful(String id, bool helpful);
}
