import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/help_article.dart';
import '../../domain/repositories/help_repository.dart';
import '../datasources/remote/help_remote_data_source.dart';

/// Implementation of help repository
class HelpRepositoryImpl implements HelpRepository {
  final HelpRemoteDataSource remoteDataSource;

  HelpRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<HelpArticle>>> getFAQs() async {
    try {
      final models = await remoteDataSource.getFAQs();
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unknown error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<HelpArticle>>> getPopularArticles({
    int? limit,
  }) async {
    try {
      final models = await remoteDataSource.getPopularArticles(limit: limit);
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unknown error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<HelpArticle>>> searchArticles({
    String? query,
    String? category,
    int? limit,
    int? offset,
  }) async {
    try {
      final models = await remoteDataSource.searchArticles(
        query: query,
        category: category,
        limit: limit,
        offset: offset,
      );
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unknown error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<HelpArticle>>> getArticlesByCategory(
    String category,
  ) async {
    try {
      final models = await remoteDataSource.getArticlesByCategory(category);
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unknown error occurred'));
    }
  }

  @override
  Future<Either<Failure, HelpArticle>> getArticle(String id) async {
    try {
      final model = await remoteDataSource.getArticle(id);
      return Right(model.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unknown error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> markArticleHelpful(
    String id,
    bool helpful,
  ) async {
    try {
      await remoteDataSource.markArticleHelpful(id, helpful);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unknown error occurred'));
    }
  }
}
