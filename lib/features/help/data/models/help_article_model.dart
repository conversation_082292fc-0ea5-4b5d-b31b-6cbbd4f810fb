import '../../domain/entities/help_article.dart';

/// Data model for help articles from API
class HelpArticleModel extends HelpArticle {
  const HelpArticleModel({
    required super.id,
    required super.title,
    required super.description,
    required super.content,
    required super.category,
    required super.tags,
    required super.published,
    required super.sortOrder,
    required super.viewCount,
    required super.helpfulCount,
    required super.notHelpfulCount,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create from JSON
  factory HelpArticleModel.fromJson(Map<String, dynamic> json) {
    return HelpArticleModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      content: json['content'] as String,
      category: json['category'] as String,
      tags: List<String>.from(json['tags'] as List),
      published: json['published'] as bool,
      sortOrder: json['sortOrder'] as int,
      viewCount: json['viewCount'] as int,
      helpfulCount: json['helpfulCount'] as int,
      notHelpfulCount: json['notHelpfulCount'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'content': content,
      'category': category,
      'tags': tags,
      'published': published,
      'sortOrder': sortOrder,
      'viewCount': viewCount,
      'helpfulCount': helpfulCount,
      'notHelpfulCount': notHelpfulCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Convert from domain entity
  factory HelpArticleModel.fromEntity(HelpArticle entity) {
    return HelpArticleModel(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      content: entity.content,
      category: entity.category,
      tags: entity.tags,
      published: entity.published,
      sortOrder: entity.sortOrder,
      viewCount: entity.viewCount,
      helpfulCount: entity.helpfulCount,
      notHelpfulCount: entity.notHelpfulCount,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to domain entity
  HelpArticle toEntity() {
    return HelpArticle(
      id: id,
      title: title,
      description: description,
      content: content,
      category: category,
      tags: tags,
      published: published,
      sortOrder: sortOrder,
      viewCount: viewCount,
      helpfulCount: helpfulCount,
      notHelpfulCount: notHelpfulCount,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
