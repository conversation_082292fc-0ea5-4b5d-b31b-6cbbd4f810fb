import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/help_article.dart';
import '../repositories/help_repository.dart';

/// Parameters for searching help articles
class SearchArticlesParams {
  final String? query;
  final String? category;
  final int? limit;
  final int? offset;

  const SearchArticlesParams({
    this.query,
    this.category,
    this.limit,
    this.offset,
  });
}

/// Use case for searching help articles
class SearchArticlesUseCase
    implements UseCase<List<HelpArticle>, SearchArticlesParams> {
  final HelpRepository repository;

  SearchArticlesUseCase(this.repository);

  @override
  Future<Either<Failure, List<HelpArticle>>> call(SearchArticlesParams params) {
    return repository.searchArticles(
      query: params.query,
      category: params.category,
      limit: params.limit,
      offset: params.offset,
    );
  }
}
