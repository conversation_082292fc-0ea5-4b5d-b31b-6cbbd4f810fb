import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/help_article.dart';
import '../repositories/help_repository.dart';

/// Use case for getting frequently asked questions
class GetFAQsUseCase implements UseCase<List<HelpArticle>, NoParams> {
  final HelpRepository repository;

  GetFAQsUseCase(this.repository);

  @override
  Future<Either<Failure, List<HelpArticle>>> call(NoParams params) {
    return repository.getFAQs();
  }
}
