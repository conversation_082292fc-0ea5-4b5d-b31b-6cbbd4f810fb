import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/help_article.dart';

/// Repository interface for help-related operations
abstract class HelpRepository {
  /// Get frequently asked questions
  Future<Either<Failure, List<HelpArticle>>> getFAQs();

  /// Get popular help articles
  Future<Either<Failure, List<HelpArticle>>> getPopularArticles({int? limit});

  /// Search help articles
  Future<Either<Failure, List<HelpArticle>>> searchArticles({
    String? query,
    String? category,
    int? limit,
    int? offset,
  });

  /// Get help articles by category
  Future<Either<Failure, List<HelpArticle>>> getArticlesByCategory(
    String category,
  );

  /// Get specific help article by ID
  Future<Either<Failure, HelpArticle>> getArticle(String id);

  /// Mark article as helpful
  Future<Either<Failure, void>> markArticleHelpful(String id, bool helpful);
}
