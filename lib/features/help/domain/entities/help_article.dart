import 'package:equatable/equatable.dart';

/// Represents a frequently asked question article
class HelpArticle extends Equatable {
  final String id;
  final String title;
  final String description;
  final String content;
  final String category;
  final List<String> tags;
  final bool published;
  final int sortOrder;
  final int viewCount;
  final int helpfulCount;
  final int notHelpfulCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const HelpArticle({
    required this.id,
    required this.title,
    required this.description,
    required this.content,
    required this.category,
    required this.tags,
    required this.published,
    required this.sortOrder,
    required this.viewCount,
    required this.helpfulCount,
    required this.notHelpfulCount,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object> get props => [
    id,
    title,
    description,
    content,
    category,
    tags,
    published,
    sortOrder,
    viewCount,
    helpfulCount,
    notHelpfulCount,
    createdAt,
    updatedAt,
  ];
}

/// Represents a categorized group of help articles
class HelpCategory extends Equatable {
  final String name;
  final String description;
  final String icon;
  final List<HelpArticle> articles;

  const HelpCategory({
    required this.name,
    required this.description,
    required this.icon,
    required this.articles,
  });

  @override
  List<Object> get props => [name, description, icon, articles];
}
