import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../../data/datasources/remote/help_remote_data_source.dart';
import '../../data/datasources/remote/help_remote_data_source_impl.dart';
import '../../data/repositories/help_repository_impl.dart';
import '../../domain/repositories/help_repository.dart';
import '../../domain/usecases/get_faqs_usecase.dart';
import '../../domain/usecases/search_articles_usecase.dart';
import '../controllers/help_controller.dart';

/// Dependency injection bindings for help feature
class HelpBindings extends Bindings {
  @override
  void dependencies() {
    // Data sources
    Get.lazyPut<HelpRemoteDataSource>(
      () => HelpRemoteDataSourceImpl(Get.find<Dio>()),
    );

    // Repositories
    Get.lazyPut<HelpRepository>(
      () => HelpRepositoryImpl(
        remoteDataSource: Get.find<HelpRemoteDataSource>(),
      ),
    );

    // Use cases
    Get.lazyPut<GetFAQsUseCase>(
      () => GetFAQsUseCase(Get.find<HelpRepository>()),
    );

    Get.lazyPut<SearchArticlesUseCase>(
      () => SearchArticlesUseCase(Get.find<HelpRepository>()),
    );

    // Controllers
    Get.lazyPut<HelpController>(
      () => HelpController(
        getFAQsUseCase: Get.find<GetFAQsUseCase>(),
        searchArticlesUseCase: Get.find<SearchArticlesUseCase>(),
      ),
    );
  }
}
