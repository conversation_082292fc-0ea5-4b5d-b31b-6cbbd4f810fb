import 'package:get/get.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/help_article.dart';
import '../../domain/usecases/get_faqs_usecase.dart';
import '../../domain/usecases/search_articles_usecase.dart';

/// Controller for help screen functionality
class HelpController extends GetxController {
  final GetFAQsUseCase getFAQsUseCase;
  final SearchArticlesUseCase searchArticlesUseCase;

  HelpController({
    required this.getFAQsUseCase,
    required this.searchArticlesUseCase,
  });

  // Observables
  final _isLoading = false.obs;
  final _faqArticles = <HelpArticle>[].obs;
  final _searchResults = <HelpArticle>[].obs;
  final _error = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  List<HelpArticle> get faqArticles => _faqArticles;
  List<HelpArticle> get searchResults => _searchResults;
  String get error => _error.value;

  // Categorized FAQs
  List<HelpArticle> get gettingStartedArticles =>
      _faqArticles
          .where((article) => article.category == 'getting-started')
          .toList();

  List<HelpArticle> get accountArticles =>
      _faqArticles.where((article) => article.category == 'account').toList();

  List<HelpArticle> get featuresArticles =>
      _faqArticles.where((article) => article.category == 'features').toList();

  List<HelpArticle> get technicalArticles =>
      _faqArticles
          .where((article) => article.category == 'troubleshooting')
          .toList();

  List<HelpArticle> get billingArticles =>
      _faqArticles.where((article) => article.category == 'billing').toList();

  @override
  void onInit() {
    super.onInit();
    loadFAQs();
  }

  /// Load frequently asked questions
  Future<void> loadFAQs() async {
    _isLoading.value = true;
    _error.value = '';

    final result = await getFAQsUseCase.call(NoParams());

    result.fold((failure) {
      // Provide mock data for demo purposes when API fails
      _loadMockData();
    }, (articles) => _faqArticles.value = articles);

    _isLoading.value = false;
  }

  /// Load mock data for demo purposes
  void _loadMockData() {
    _faqArticles.value = [
      HelpArticle(
        id: '1',
        title: 'Getting Started with Power Up',
        description:
            'Learn the basics of using Power Up for your wellness journey',
        content: '''
<h2>Welcome to Power Up!</h2>
<p>Power Up is your AI-driven personal wellness coach designed to help you achieve your health and wellness goals through personalized guidance, habit tracking, and community support.</p>

<h3>First Steps</h3>
<ol>
  <li><strong>Create Your Profile:</strong> Set up your personal information and wellness goals</li>
  <li><strong>Complete the Assessment:</strong> Answer questions about your current habits and lifestyle</li>
  <li><strong>Set Your Goals:</strong> Define what you want to achieve with Power Up</li>
  <li><strong>Start Your First Habit:</strong> Begin with one simple habit to build momentum</li>
</ol>

<h3>Key Features to Explore</h3>
<ul>
  <li>Skill development plans</li>
  <li>Habit tracking</li>
  <li>Community support</li>
  <li>AI-powered insights</li>
</ul>

<p>Take your time exploring each feature. Your AI coach will adapt to your preferences and provide increasingly personalized recommendations as you use the app.</p>
        ''',
        category: 'getting-started',
        tags: ['onboarding', 'basics', 'setup'],
        published: true,
        sortOrder: 1,
        viewCount: 245,
        helpfulCount: 23,
        notHelpfulCount: 2,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      HelpArticle(
        id: '2',
        title: 'How to Set Up Your First Habit',
        description:
            'Step-by-step guide to creating and tracking your first habit in Power Up',
        content: '''
<h2>Creating Your First Habit</h2>
<p>Building healthy habits is the foundation of personal wellness. Here's how to get started with your first habit in Power Up.</p>

<h3>Steps to Create a Habit</h3>
<ol>
  <li><strong>Choose a Simple Habit:</strong> Start with something easy like drinking a glass of water every morning</li>
  <li><strong>Set a Clear Time:</strong> Pick a specific time of day to perform your habit</li>
  <li><strong>Create a Trigger:</strong> Link your new habit to an existing routine</li>
  <li><strong>Track Your Progress:</strong> Use the app to log your daily completion</li>
</ol>

<h3>Tips for Success</h3>
<ul>
  <li>Start small - aim for just 1-2 minutes per day</li>
  <li>Be consistent rather than perfect</li>
  <li>Celebrate small wins</li>
  <li>Don't skip twice in a row</li>
</ul>

<p>Remember, it takes time to build a habit. Be patient with yourself and focus on consistency over perfection.</p>
        ''',
        category: 'getting-started',
        tags: ['habits', 'tracking', 'setup'],
        published: true,
        sortOrder: 2,
        viewCount: 189,
        helpfulCount: 31,
        notHelpfulCount: 1,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      HelpArticle(
        id: '3',
        title: 'Managing Your Account Settings',
        description: 'Learn how to update your profile and account preferences',
        content: '''
<h2>Account Management</h2>
<p>Keep your account information up to date and customize your Power Up experience.</p>

<h3>Profile Settings</h3>
<ul>
  <li><strong>Personal Information:</strong> Update your name, email, and profile picture</li>
  <li><strong>Wellness Goals:</strong> Modify your health and fitness objectives</li>
  <li><strong>Preferences:</strong> Set your notification preferences and privacy settings</li>
</ul>

<h3>Privacy & Security</h3>
<ul>
  <li>Change your password regularly</li>
  <li>Enable two-factor authentication</li>
  <li>Review your data sharing preferences</li>
</ul>
        ''',
        category: 'account',
        tags: ['account', 'settings', 'privacy'],
        published: true,
        sortOrder: 1,
        viewCount: 156,
        helpfulCount: 18,
        notHelpfulCount: 0,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      HelpArticle(
        id: '4',
        title: 'Understanding Premium Features',
        description:
            'Explore the advanced features available with a premium subscription',
        content: '''
<h2>Premium Features</h2>
<p>Unlock the full potential of Power Up with our premium subscription features.</p>

<h3>What's Included</h3>
<ul>
  <li><strong>Advanced AI Coaching:</strong> Personalized recommendations and insights</li>
  <li><strong>Custom Skill Plans:</strong> Create and share your own development plans</li>
  <li><strong>Priority Support:</strong> Get help faster with dedicated support</li>
  <li><strong>Data Analytics:</strong> Detailed progress tracking and analytics</li>
</ul>

<h3>Subscription Management</h3>
<p>You can upgrade, downgrade, or cancel your subscription at any time through your account settings.</p>
        ''',
        category: 'billing',
        tags: ['premium', 'subscription', 'features'],
        published: true,
        sortOrder: 1,
        viewCount: 201,
        helpfulCount: 25,
        notHelpfulCount: 3,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }

  /// Search help articles
  Future<void> searchArticles(String query) async {
    if (query.trim().isEmpty) {
      _searchResults.clear();
      return;
    }

    _isLoading.value = true;
    _error.value = '';

    final result = await searchArticlesUseCase.call(
      SearchArticlesParams(query: query, limit: 20),
    );

    result.fold((failure) {
      // Fallback to local search in mock data
      _performLocalSearch(query);
    }, (articles) => _searchResults.value = articles);

    _isLoading.value = false;
  }

  /// Perform local search in loaded FAQ articles
  void _performLocalSearch(String query) {
    final lowercaseQuery = query.toLowerCase();
    _searchResults.value =
        _faqArticles.where((article) {
          return article.title.toLowerCase().contains(lowercaseQuery) ||
              article.description.toLowerCase().contains(lowercaseQuery) ||
              article.content.toLowerCase().contains(lowercaseQuery) ||
              article.tags.any(
                (tag) => tag.toLowerCase().contains(lowercaseQuery),
              );
        }).toList();
  }

  /// Clear search results
  void clearSearch() {
    _searchResults.clear();
  }

  /// Show error snackbar
  void showError(String message) {
    Get.snackbar('Error', message, snackPosition: SnackPosition.BOTTOM);
  }
}
