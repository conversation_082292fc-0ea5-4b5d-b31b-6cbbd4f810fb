import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import '../../domain/entities/help_article.dart';
import '../controllers/help_controller.dart';

class HelpArticleDetailScreen extends StatelessWidget {
  const HelpArticleDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the article passed as argument
    final HelpArticle article = Get.arguments as HelpArticle;

    return Scaffold(
      appBar: AppBar(
        title: Text(article.title, style: const TextStyle(fontSize: 18)),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Article metadata
            if (article.category.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  article.category.toUpperCase(),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Article title
            Text(
              article.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),

            // Article metadata row
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDate(article.updatedAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.visibility_outlined,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  '${article.viewCount} views',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Article content rendered as HTML
            Html(
              data: article.content,
              style: {
                "body": Style(margin: Margins.zero, padding: HtmlPaddings.zero),
                "h1": Style(
                  fontSize: FontSize(24),
                  fontWeight: FontWeight.bold,
                  margin: Margins.only(bottom: 16, top: 24),
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                "h2": Style(
                  fontSize: FontSize(20),
                  fontWeight: FontWeight.bold,
                  margin: Margins.only(bottom: 12, top: 20),
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                "h3": Style(
                  fontSize: FontSize(18),
                  fontWeight: FontWeight.w600,
                  margin: Margins.only(bottom: 8, top: 16),
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                "p": Style(
                  fontSize: FontSize(16),
                  lineHeight: LineHeight.number(1.6),
                  margin: Margins.only(bottom: 12),
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                "li": Style(
                  fontSize: FontSize(16),
                  lineHeight: LineHeight.number(1.6),
                  margin: Margins.only(bottom: 8),
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                "ol": Style(margin: Margins.only(bottom: 16, left: 16)),
                "ul": Style(margin: Margins.only(bottom: 16, left: 16)),
                "strong": Style(fontWeight: FontWeight.bold),
                "em": Style(fontStyle: FontStyle.italic),
                "a": Style(
                  color: Theme.of(context).colorScheme.primary,
                  textDecoration: TextDecoration.underline,
                ),
                "blockquote": Style(
                  border: Border(
                    left: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 4,
                    ),
                  ),
                  margin: Margins.only(bottom: 16),
                  padding: HtmlPaddings.only(left: 16),
                  backgroundColor: Theme.of(context).colorScheme.surface,
                ),
                "code": Style(
                  backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                  padding: HtmlPaddings.all(4),
                  fontFamily: 'monospace',
                ),
                "pre": Style(
                  backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                  padding: HtmlPaddings.all(12),
                  margin: Margins.only(bottom: 16),
                  whiteSpace: WhiteSpace.pre,
                ),
              },
              onLinkTap: (url, attributes, element) {
                // Handle link taps
                if (url != null) {
                  // TODO: Implement proper URL handling
                  Get.snackbar(
                    'Link Clicked',
                    url,
                    snackPosition: SnackPosition.BOTTOM,
                  );
                }
              },
            ),
            const SizedBox(height: 32),

            // Helpful section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    'Was this article helpful?',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildHelpfulButton(
                        context: context,
                        icon: Icons.thumb_up_outlined,
                        label: 'Yes',
                        isPositive: true,
                        onTap: () => _markAsHelpful(article.id, true),
                      ),
                      const SizedBox(width: 16),
                      _buildHelpfulButton(
                        context: context,
                        icon: Icons.thumb_down_outlined,
                        label: 'No',
                        isPositive: false,
                        onTap: () => _markAsHelpful(article.id, false),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpfulButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required bool isPositive,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color:
                    isPositive
                        ? Colors.green
                        : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color:
                      isPositive
                          ? Colors.green
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _markAsHelpful(String articleId, bool helpful) {
    final helpController = Get.find<HelpController>();
    // TODO: Implement API call to mark article as helpful
    // helpController.markArticleHelpful(articleId, helpful);

    Get.snackbar(
      'Thank you!',
      helpful
          ? 'Thanks for your positive feedback'
          : 'Thanks for your feedback, we\'ll improve this article',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: helpful ? Colors.green.withOpacity(0.1) : null,
      colorText: helpful ? Colors.green : null,
    );
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'just now';
    }
  }
}
