import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../../core/network/network_info.dart';
import '../data/datasources/analytics_remote_data_source.dart';
import '../data/repositories/analytics_repository_impl.dart';
import '../domain/repositories/analytics_repository.dart';
import '../domain/usecases/get_habit_analytics_usecase.dart';
import '../domain/usecases/get_productivity_analytics_usecase.dart';
import '../domain/usecases/get_user_progress_usecase.dart';
import '../domain/usecases/get_weekly_stats_usecase.dart';
import '../presentation/controllers/statistics_controller.dart';

class StatisticsBindings extends Bindings {
  @override
  void dependencies() {
    // Data Sources
    Get.lazyPut<AnalyticsRemoteDataSource>(
      () => AnalyticsRemoteDataSourceImpl(dio: Get.find<Dio>()),
      fenix: true,
    );

    // Repository
    Get.lazyPut<AnalyticsRepository>(
      () => AnalyticsRepositoryImpl(
        remoteDataSource: Get.find<AnalyticsRemoteDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
      ),
      fenix: true,
    );

    // Use Cases
    Get.lazyPut<GetAnalyticsUserProgressUseCase>(
      () => GetAnalyticsUserProgressUseCase(Get.find<AnalyticsRepository>()),
      fenix: true,
    );

    Get.lazyPut<GetAnalyticsHabitAnalyticsUseCase>(
      () => GetAnalyticsHabitAnalyticsUseCase(Get.find<AnalyticsRepository>()),
      fenix: true,
    );

    Get.lazyPut<GetAnalyticsProductivityAnalyticsUseCase>(
      () => GetAnalyticsProductivityAnalyticsUseCase(
        Get.find<AnalyticsRepository>(),
      ),
      fenix: true,
    );

    Get.lazyPut<GetAnalyticsWeeklyStatsUseCase>(
      () => GetAnalyticsWeeklyStatsUseCase(Get.find<AnalyticsRepository>()),
      fenix: true,
    );

    // Controller
    Get.lazyPut<StatisticsController>(
      () => StatisticsController(
        getUserProgressUseCase: Get.find<GetAnalyticsUserProgressUseCase>(),
        getHabitAnalyticsUseCase: Get.find<GetAnalyticsHabitAnalyticsUseCase>(),
        getProductivityAnalyticsUseCase:
            Get.find<GetAnalyticsProductivityAnalyticsUseCase>(),
        getWeeklyStatsUseCase: Get.find<GetAnalyticsWeeklyStatsUseCase>(),
        analyticsRepository: Get.find<AnalyticsRepository>(),
      ),
      fenix: true,
    );
  }
}
