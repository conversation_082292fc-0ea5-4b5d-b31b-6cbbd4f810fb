import '../../domain/entities/task_completion_stats_entity.dart';

class TaskCompletionStatsModel extends TaskCompletionStatsEntity {
  const TaskCompletionStatsModel({
    required super.overallCompletionRate,
    required super.onTimeCompletionRate,
    required super.completionByDay,
    required super.completionByPriority,
    required super.averageTasksPerDay,
    required super.mostProductiveDay,
  });

  factory TaskCompletionStatsModel.fromJson(Map<String, dynamic> json) {
    return TaskCompletionStatsModel(
      overallCompletionRate:
          json['overallCompletionRate'] != null
              ? (json['overallCompletionRate'] as num).toDouble()
              : 0.0,
      onTimeCompletionRate:
          json['onTimeCompletionRate'] != null
              ? (json['onTimeCompletionRate'] as num).toDouble()
              : 0.0,
      completionByDay:
          json['completionByDay'] != null
              ? (json['completionByDay'] as List)
                  .map(
                    (e) => TaskCompletionByDayModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : [],
      completionByPriority:
          json['completionByPriority'] != null
              ? (json['completionByPriority'] as List)
                  .map(
                    (e) => TaskCompletionByPriorityModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : [],
      averageTasksPerDay:
          json['averageTasksPerDay'] != null
              ? (json['averageTasksPerDay'] as num).toDouble()
              : 0.0,
      mostProductiveDay: json['mostProductiveDay'] as String? ?? 'monday',
    );
  }
}

class TaskCompletionByDayModel extends TaskCompletionByDayEntity {
  const TaskCompletionByDayModel({
    required super.day,
    required super.completed,
    required super.due,
    required super.completionRate,
  });

  factory TaskCompletionByDayModel.fromJson(Map<String, dynamic> json) {
    return TaskCompletionByDayModel(
      day: DateTime.parse(json['day'] as String),
      completed: json['completed'] as int,
      due: json['due'] as int,
      completionRate: (json['completionRate'] as num).toDouble(),
    );
  }
}

class TaskCompletionByPriorityModel extends TaskCompletionByPriorityEntity {
  const TaskCompletionByPriorityModel({
    required super.priority,
    required super.completed,
    required super.due,
    required super.completionRate,
  });

  factory TaskCompletionByPriorityModel.fromJson(Map<String, dynamic> json) {
    return TaskCompletionByPriorityModel(
      priority: json['priority'] as String,
      completed: json['completed'] as int,
      due: json['due'] as int,
      completionRate: (json['completionRate'] as num).toDouble(),
    );
  }
}
