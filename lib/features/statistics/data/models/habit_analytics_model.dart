import '../../domain/entities/habit_analytics_entity.dart';

class HabitAnalyticsModel extends HabitAnalyticsEntity {
  const HabitAnalyticsModel({
    required super.overallCompletionRate,
    required super.streaks,
    required super.completionByDay,
    required super.mostConsistentHabit,
    required super.habitNeedingImprovement,
  });

  factory HabitAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return HabitAnalyticsModel(
      overallCompletionRate:
          json['overallCompletionRate'] != null
              ? (json['overallCompletionRate'] as num).toDouble()
              : 0.0,
      streaks:
          json['streaks'] != null
              ? (json['streaks'] as List)
                  .map(
                    (e) => HabitStreakModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList()
              : [],
      completionByDay:
          json['completionByDay'] != null
              ? (json['completionByDay'] as List)
                  .map(
                    (e) => HabitCompletionByDayModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : [],
      mostConsistentHabit: json['mostConsistentHabit'] as String? ?? 'none',
      habitNeedingImprovement:
          json['habitNeedingImprovement'] as String? ?? 'none',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overallCompletionRate': overallCompletionRate,
      'streaks': streaks.map((e) => (e as HabitStreakModel).toJson()).toList(),
      'completionByDay':
          completionByDay
              .map((e) => (e as HabitCompletionByDayModel).toJson())
              .toList(),
      'mostConsistentHabit': mostConsistentHabit,
      'habitNeedingImprovement': habitNeedingImprovement,
    };
  }
}

class HabitStreakModel extends HabitStreakEntity {
  const HabitStreakModel({
    required super.name,
    required super.currentStreak,
    required super.longestStreak,
    required super.completionRate,
  });

  factory HabitStreakModel.fromJson(Map<String, dynamic> json) {
    return HabitStreakModel(
      name: json['name'] as String,
      currentStreak: json['currentStreak'] as int,
      longestStreak: json['longestStreak'] as int,
      completionRate: (json['completionRate'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'completionRate': completionRate,
    };
  }
}

class HabitCompletionByDayModel extends HabitCompletionByDayEntity {
  const HabitCompletionByDayModel({
    required super.day,
    required super.completionPercentage,
  });

  factory HabitCompletionByDayModel.fromJson(Map<String, dynamic> json) {
    return HabitCompletionByDayModel(
      day: json['day'] as String,
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'day': day, 'completionPercentage': completionPercentage};
  }
}
