import '../../domain/entities/weekly_stats_entity.dart';

class WeeklyStatsModel extends WeeklyStatsEntity {
  const WeeklyStatsModel({
    required super.dailyStats,
    required super.totalTasks,
    required super.totalHabits,
    required super.totalFocusMinutes,
    required super.weeklyProductivityScore,
    required super.mostProductiveDay,
  });

  factory WeeklyStatsModel.fromJson(Map<String, dynamic> json) {
    return WeeklyStatsModel(
      dailyStats:
          json['dailyStats'] != null
              ? (json['dailyStats'] as List)
                  .map(
                    (e) => DailyStatsModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList()
              : [],
      totalTasks: json['totalTasks'] as int? ?? 0,
      totalHabits: json['totalHabits'] as int? ?? 0,
      totalFocusMinutes: json['totalFocusMinutes'] as int? ?? 0,
      weeklyProductivityScore:
          json['weeklyProductivityScore'] != null
              ? (json['weeklyProductivityScore'] as num).toDouble()
              : 0.0,
      mostProductiveDay: json['mostProductiveDay'] as String? ?? 'monday',
    );
  }
}

class DailyStatsModel extends DailyStatsEntity {
  const DailyStatsModel({
    required super.day,
    required super.tasksCompleted,
    required super.habitsCompleted,
    required super.focusMinutes,
    required super.productivityScore,
  });

  factory DailyStatsModel.fromJson(Map<String, dynamic> json) {
    return DailyStatsModel(
      day: json['day'] as String,
      tasksCompleted: json['tasksCompleted'] as int,
      habitsCompleted: json['habitsCompleted'] as int,
      focusMinutes: json['focusMinutes'] as int,
      productivityScore: (json['productivityScore'] as num).toDouble(),
    );
  }
}
