import '../../domain/entities/user_progress_entity.dart';

class UserProgressModel extends UserProgressEntity {
  const UserProgressModel({
    required super.overallScore,
    required super.dailyProgress,
    required super.totalTasksCompleted,
    required super.totalHabitsCompleted,
    required super.changeFromPreviousPeriod,
  });

  factory UserProgressModel.fromJson(Map<String, dynamic> json) {
    return UserProgressModel(
      overallScore:
          json['overallScore'] != null
              ? (json['overallScore'] as num).toDouble()
              : 0.0,
      dailyProgress:
          json['dailyProgress'] != null
              ? (json['dailyProgress'] as List)
                  .map(
                    (e) =>
                        DailyProgressModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList()
              : [],
      totalTasksCompleted: json['totalTasksCompleted'] as int? ?? 0,
      totalHabitsCompleted: json['totalHabitsCompleted'] as int? ?? 0,
      changeFromPreviousPeriod:
          json['changeFromPreviousPeriod'] != null
              ? (json['changeFromPreviousPeriod'] as num).toDouble()
              : 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overallScore': overallScore,
      'dailyProgress':
          dailyProgress.map((e) => (e as DailyProgressModel).toJson()).toList(),
      'totalTasksCompleted': totalTasksCompleted,
      'totalHabitsCompleted': totalHabitsCompleted,
      'changeFromPreviousPeriod': changeFromPreviousPeriod,
    };
  }
}

class DailyProgressModel extends DailyProgressEntity {
  const DailyProgressModel({
    required super.date,
    required super.score,
    required super.tasksCompleted,
    required super.habitsCompleted,
    required super.learningProgress,
  });

  factory DailyProgressModel.fromJson(Map<String, dynamic> json) {
    return DailyProgressModel(
      date: DateTime.tryParse(json['date'] as String? ?? '') ?? DateTime.now(),
      score: json['score'] != null ? (json['score'] as num).toDouble() : 0.0,
      tasksCompleted: json['tasksCompleted'] as int? ?? 0,
      habitsCompleted: json['habitsCompleted'] as int? ?? 0,
      learningProgress:
          json['learningProgress'] != null
              ? (json['learningProgress'] as num).toDouble()
              : 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'score': score,
      'tasksCompleted': tasksCompleted,
      'habitsCompleted': habitsCompleted,
      'learningProgress': learningProgress,
    };
  }
}
