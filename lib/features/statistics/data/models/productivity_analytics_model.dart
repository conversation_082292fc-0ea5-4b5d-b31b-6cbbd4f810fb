import '../../domain/entities/productivity_analytics_entity.dart';

class ProductivityAnalyticsModel extends ProductivityAnalyticsEntity {
  const ProductivityAnalyticsModel({
    required super.overallProductivity,
    required super.productivityByTimeOfDay,
    required super.productivityByDayOfWeek,
    required super.mostProductiveTime,
    required super.mostProductiveDay,
    required super.averageFocusTime,
  });

  factory ProductivityAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return ProductivityAnalyticsModel(
      overallProductivity:
          json['overallProductivity'] != null
              ? (json['overallProductivity'] as num).toDouble()
              : 0.0,
      productivityByTimeOfDay:
          json['productivityByTimeOfDay'] != null
              ? (json['productivityByTimeOfDay'] as List)
                  .map(
                    (e) => TimeOfDayProductivityModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : [],
      productivityByDayOfWeek:
          json['productivityByDayOfWeek'] != null
              ? (json['productivityByDayOfWeek'] as List)
                  .map(
                    (e) => DayOfWeekProductivityModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : [],
      mostProductiveTime: json['mostProductiveTime'] as String? ?? 'morning',
      mostProductiveDay: json['mostProductiveDay'] as String? ?? 'monday',
      averageFocusTime:
          json['averageFocusTime'] != null
              ? (json['averageFocusTime'] as num).toDouble()
              : 0.0,
    );
  }
}

class TimeOfDayProductivityModel extends TimeOfDayProductivityEntity {
  const TimeOfDayProductivityModel({
    required super.timeOfDay,
    required super.productivityScore,
    required super.tasksCompleted,
  });

  factory TimeOfDayProductivityModel.fromJson(Map<String, dynamic> json) {
    return TimeOfDayProductivityModel(
      timeOfDay: json['timeOfDay'] as String,
      productivityScore: (json['productivityScore'] as num).toDouble(),
      tasksCompleted: json['tasksCompleted'] as int,
    );
  }
}

class DayOfWeekProductivityModel extends DayOfWeekProductivityEntity {
  const DayOfWeekProductivityModel({
    required super.dayOfWeek,
    required super.productivityScore,
  });

  factory DayOfWeekProductivityModel.fromJson(Map<String, dynamic> json) {
    return DayOfWeekProductivityModel(
      dayOfWeek: json['dayOfWeek'] as String,
      productivityScore: (json['productivityScore'] as num).toDouble(),
    );
  }
}
