import '../../domain/entities/mood_analytics_entity.dart';

class MoodAnalyticsModel extends MoodAnalyticsEntity {
  const MoodAnalyticsModel({
    required super.moodEntries,
    required super.averageMood,
    required super.mostCommonMood,
    required super.moodTrend,
    required super.correlations,
  });

  factory MoodAnalyticsModel.fromJson(Map<String, dynamic> json) {
    // Handle new API response structure
    if (json.containsKey('moodTrends') || json.containsKey('correlations')) {
      return MoodAnalyticsModel(
        moodEntries: _parseMoodEntries(json),
        averageMood: _parseAverageMood(json),
        mostCommonMood: _parseMostCommonMood(json),
        moodTrend: _parseMoodTrend(json),
        correlations: _parseCorrelations(json),
      );
    }

    // Handle legacy API response structure
    return MoodAnalyticsModel(
      moodEntries:
          json['moodEntries'] != null
              ? (json['moodEntries'] as List)
                  .map(
                    (e) => DailyMoodModel.fromJson(e as Map<String, dynamic>),
                  )
                  .toList()
              : [],
      averageMood:
          json['averageMood'] != null
              ? (json['averageMood'] as num).toDouble()
              : 0.0,
      mostCommonMood: json['mostCommonMood'] as String? ?? 'neutral',
      moodTrend: json['moodTrend'] as String? ?? 'stable',
      correlations:
          json['correlations'] != null && json['correlations'] is List
              ? (json['correlations'] as List)
                  .map(
                    (e) => MoodCorrelationModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
              : [],
    );
  }

  static List<DailyMoodEntity> _parseMoodEntries(Map<String, dynamic> json) {
    // Try to extract from moodTrends.daily or fallback to moodEntries
    if (json['moodTrends'] != null && json['moodTrends']['daily'] != null) {
      final dailyTrends = json['moodTrends']['daily'] as List;
      return dailyTrends
          .map((e) => DailyMoodModel.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    if (json['moodEntries'] != null) {
      return (json['moodEntries'] as List)
          .map((e) => DailyMoodModel.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return [];
  }

  static double _parseAverageMood(Map<String, dynamic> json) {
    // Try to calculate from moodTrends or use direct value
    if (json['averageMood'] != null) {
      return (json['averageMood'] as num).toDouble();
    }

    // Calculate from mood entries if available
    final moodEntries = _parseMoodEntries(json);
    if (moodEntries.isNotEmpty) {
      final total = moodEntries.fold<int>(
        0,
        (sum, mood) => sum + mood.moodScore,
      );
      return total / moodEntries.length;
    }

    return 3.0; // Default neutral mood
  }

  static String _parseMostCommonMood(Map<String, dynamic> json) {
    if (json['mostCommonMood'] != null) {
      return json['mostCommonMood'] as String;
    }

    // Calculate from mood entries if available
    final moodEntries = _parseMoodEntries(json);
    if (moodEntries.isNotEmpty) {
      final moodCounts = <String, int>{};
      for (final entry in moodEntries) {
        moodCounts[entry.mood] = (moodCounts[entry.mood] ?? 0) + 1;
      }

      return moodCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    }

    return 'neutral';
  }

  static String _parseMoodTrend(Map<String, dynamic> json) {
    // Try to get from moodTrends.trending or direct moodTrend
    if (json['moodTrends'] != null && json['moodTrends']['trending'] != null) {
      return json['moodTrends']['trending'] as String;
    }

    if (json['moodTrend'] != null) {
      return json['moodTrend'] as String;
    }

    return 'stable';
  }

  static List<MoodCorrelationEntity> _parseCorrelations(
    Map<String, dynamic> json,
  ) {
    final correlations = <MoodCorrelationEntity>[];

    // Handle new correlations structure
    if (json['correlations'] != null && json['correlations'] is Map) {
      final correlationsMap = json['correlations'] as Map<String, dynamic>;

      // Add productivity correlation
      if (correlationsMap['productivity'] != null) {
        final productivity =
            correlationsMap['productivity'] as Map<String, dynamic>;
        correlations.add(
          MoodCorrelationModel(
            factor: 'productivity',
            correlationStrength:
                (productivity['correlation'] as num?)?.toDouble() ?? 0.0,
            correlationType:
                productivity['correlation'] != null &&
                        productivity['correlation'] > 0
                    ? 'positive'
                    : 'neutral',
          ),
        );
      }

      // Add habits correlation
      if (correlationsMap['habits'] != null) {
        final habits = correlationsMap['habits'] as Map<String, dynamic>;
        if (habits['topMoodBoosters'] != null &&
            habits['topMoodBoosters'] is List) {
          final boosters = habits['topMoodBoosters'] as List;
          for (final booster in boosters) {
            if (booster is String) {
              correlations.add(
                MoodCorrelationModel(
                  factor: booster,
                  correlationStrength:
                      0.7, // Default positive correlation for mood boosters
                  correlationType: 'positive',
                ),
              );
            }
          }
        }
      }
    }

    // Handle legacy correlations structure (List)
    if (json['correlations'] != null && json['correlations'] is List) {
      final legacyCorrelations = json['correlations'] as List;
      correlations.addAll(
        legacyCorrelations
            .map(
              (e) => MoodCorrelationModel.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
      );
    }

    return correlations;
  }
}

class DailyMoodModel extends DailyMoodEntity {
  const DailyMoodModel({
    required super.date,
    required super.mood,
    required super.moodScore,
  });

  factory DailyMoodModel.fromJson(Map<String, dynamic> json) {
    return DailyMoodModel(
      date:
          json['date'] != null
              ? DateTime.parse(json['date'] as String)
              : DateTime.now(),
      mood: json['mood'] as String? ?? 'neutral',
      moodScore: json['moodScore'] as int? ?? 3,
    );
  }
}

class MoodCorrelationModel extends MoodCorrelationEntity {
  const MoodCorrelationModel({
    required super.factor,
    required super.correlationStrength,
    required super.correlationType,
  });

  factory MoodCorrelationModel.fromJson(Map<String, dynamic> json) {
    return MoodCorrelationModel(
      factor: json['factor'] as String? ?? 'unknown',
      correlationStrength:
          json['correlationStrength'] != null
              ? (json['correlationStrength'] as num).toDouble()
              : 0.0,
      correlationType: json['correlationType'] as String? ?? 'neutral',
    );
  }
}
