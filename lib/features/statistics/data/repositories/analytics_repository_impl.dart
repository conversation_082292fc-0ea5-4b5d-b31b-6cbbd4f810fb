import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/user_progress_entity.dart';
import '../../domain/entities/habit_analytics_entity.dart';
import '../../domain/entities/productivity_analytics_entity.dart';
import '../../domain/entities/mood_analytics_entity.dart';
import '../../domain/entities/weekly_stats_entity.dart';
import '../../domain/entities/task_completion_stats_entity.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../datasources/analytics_remote_data_source.dart';
import '../models/productivity_analytics_model.dart';
import '../models/mood_analytics_model.dart';
import '../models/weekly_stats_model.dart';
import '../models/task_completion_stats_model.dart';

class AnalyticsRepositoryImpl implements AnalyticsRepository {
  final AnalyticsRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  AnalyticsRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, UserProgressEntity>> getUserProgress(
    AnalyticsPeriod period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getUserProgress(period);
        return Right(result);
      } catch (e) {
        return Left(ServerFailure(message: 'Failed to get user progress: $e'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, HabitAnalyticsEntity>> getHabitAnalytics(
    AnalyticsPeriod period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getHabitAnalytics(period);
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to get habit analytics: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ProductivityAnalyticsEntity>> getProductivityAnalytics(
    AnalyticsPeriod period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final json = await remoteDataSource.getProductivityAnalytics(period);
        final result = ProductivityAnalyticsModel.fromJson(json);
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to get productivity analytics: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, MoodAnalyticsEntity>> getMoodAnalytics(
    AnalyticsPeriod period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final json = await remoteDataSource.getMoodAnalytics(period);
        final result = MoodAnalyticsModel.fromJson(json);
        return Right(result);
      } catch (e) {
        return Left(ServerFailure(message: 'Failed to get mood analytics: $e'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, WeeklyStatsEntity>> getWeeklyStats() async {
    if (await networkInfo.isConnected) {
      try {
        final json = await remoteDataSource.getWeeklyStats();
        final result = WeeklyStatsModel.fromJson(json);
        return Right(result);
      } catch (e) {
        return Left(ServerFailure(message: 'Failed to get weekly stats: $e'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, TaskCompletionStatsEntity>> getTaskCompletionStats(
    AnalyticsPeriod? period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final json = await remoteDataSource.getTaskCompletionStats(period);
        final result = TaskCompletionStatsModel.fromJson(json);
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to get task completion stats: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> recordMoodEntry(String mood) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.recordMoodEntry(mood);
        return const Right(null);
      } catch (e) {
        return Left(ServerFailure(message: 'Failed to record mood entry: $e'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> recordFocusSession(int minutes) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.recordFocusSession(minutes);
        return const Right(null);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to record focus session: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>>
  getPersonalizedInsights() async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getPersonalizedInsights();
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to get personalized insights: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, MoodAnalyticsEntity>> getHabitCorrelations(
    AnalyticsPeriod period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getHabitCorrelations(period);
        // Convert HabitAnalyticsModel to MoodAnalyticsModel for correlations
        final moodData = {
          'moodEntries': [],
          'averageMood': 3.0,
          'mostCommonMood': 'neutral',
          'moodTrend': 'stable',
          'correlations': result.toJson()['streaks'] ?? [],
        };
        return Right(MoodAnalyticsModel.fromJson(moodData));
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to get habit correlations: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getStreakMilestones() async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getStreakMilestones();
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to get streak milestones: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getAIImprovementReport(
    int? days,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getAIImprovementReport(days);
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to get AI improvement report: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, UserProgressEntity>> generateProgressReport(
    AnalyticsPeriod period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.generateProgressReport(period);
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to generate progress report: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, HabitAnalyticsEntity>> generateHabitReport(
    AnalyticsPeriod period,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.generateHabitReport(period);
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to generate habit report: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ProductivityAnalyticsEntity>>
  generateProductivityReport(AnalyticsPeriod period) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.generateProductivityReport(
          period,
        );
        return Right(ProductivityAnalyticsModel.fromJson(result));
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to generate productivity report: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>>
  generateComprehensiveReport() async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.generateComprehensiveReport();
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to generate comprehensive report: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> updateDailyProgress({
    int? xpGained,
    String? badgeEarned,
    int? focusMinutes,
    bool? podcastListened,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.updateDailyProgress(
          xpGained: xpGained,
          badgeEarned: badgeEarned,
          focusMinutes: focusMinutes,
          podcastListened: podcastListened,
        );
        return const Right(null);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to update daily progress: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, UserProgressEntity>> updateUserProgress({
    int? activeSkillPlans,
    int? completedSteps,
    int? totalSteps,
    int? xpGained,
    List<String>? badgesEarned,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.updateUserProgress(
          activeSkillPlans: activeSkillPlans,
          completedSteps: completedSteps,
          totalSteps: totalSteps,
          xpGained: xpGained,
          badgesEarned: badgesEarned,
        );
        return Right(result);
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to update user progress: $e'),
        );
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
