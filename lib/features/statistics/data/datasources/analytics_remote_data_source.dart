import 'package:dio/dio.dart';
import '../models/user_progress_model.dart';
import '../models/habit_analytics_model.dart';
import '../../domain/repositories/analytics_repository.dart';

abstract class AnalyticsRemoteDataSource {
  Future<UserProgressModel> getUserProgress(AnalyticsPeriod period);
  Future<HabitAnalyticsModel> getHabitAnalytics(AnalyticsPeriod period);
  Future<Map<String, dynamic>> getProductivityAnalytics(AnalyticsPeriod period);
  Future<Map<String, dynamic>> getMoodAnalytics(AnalyticsPeriod period);
  Future<Map<String, dynamic>> getWeeklyStats();
  Future<Map<String, dynamic>> getTaskCompletionStats(AnalyticsPeriod? period);
  Future<Map<String, dynamic>> getPersonalizedInsights();
  Future<HabitAnalyticsModel> getHabitCorrelations(AnalyticsPeriod period);
  Future<Map<String, dynamic>> getStreakMilestones();
  Future<Map<String, dynamic>> getAIImprovementReport(int? days);
  Future<UserProgressModel> generateProgressReport(AnalyticsPeriod period);
  Future<HabitAnalyticsModel> generateHabitReport(AnalyticsPeriod period);
  Future<Map<String, dynamic>> generateProductivityReport(
    AnalyticsPeriod period,
  );
  Future<Map<String, dynamic>> generateComprehensiveReport();
  Future<void> recordMoodEntry(String mood);
  Future<void> recordFocusSession(int minutes);
  Future<void> updateDailyProgress({
    int? xpGained,
    String? badgeEarned,
    int? focusMinutes,
    bool? podcastListened,
  });
  Future<UserProgressModel> updateUserProgress({
    int? activeSkillPlans,
    int? completedSteps,
    int? totalSteps,
    int? xpGained,
    List<String>? badgesEarned,
  });
}

class AnalyticsRemoteDataSourceImpl implements AnalyticsRemoteDataSource {
  final Dio dio;

  AnalyticsRemoteDataSourceImpl({required this.dio});

  String _periodToString(AnalyticsPeriod period) {
    switch (period) {
      case AnalyticsPeriod.day:
        return 'day';
      case AnalyticsPeriod.week:
        return 'week';
      case AnalyticsPeriod.month:
        return 'month';
      case AnalyticsPeriod.year:
        return 'year';
    }
  }

  @override
  Future<UserProgressModel> getUserProgress(AnalyticsPeriod period) async {
    try {
      final response = await dio.get(
        '/api/analytics/user',
        queryParameters: {'period': _periodToString(period)},
      );
      if (response.data != null) {
        return UserProgressModel.fromJson(response.data);
      } else {
        return UserProgressModel.fromJson(_getFallbackUserProgress());
      }
    } catch (e) {
      return UserProgressModel.fromJson(_getFallbackUserProgress());
    }
  }

  @override
  Future<HabitAnalyticsModel> getHabitAnalytics(AnalyticsPeriod period) async {
    try {
      final response = await dio.get(
        '/api/analytics/habits',
        queryParameters: {'period': _periodToString(period)},
      );
      if (response.data != null) {
        return HabitAnalyticsModel.fromJson(response.data);
      } else {
        return HabitAnalyticsModel.fromJson(_getFallbackHabitAnalytics());
      }
    } catch (e) {
      return HabitAnalyticsModel.fromJson(_getFallbackHabitAnalytics());
    }
  }

  @override
  Future<Map<String, dynamic>> getProductivityAnalytics(
    AnalyticsPeriod period,
  ) async {
    try {
      final response = await dio.get(
        '/api/analytics/productivity',
        queryParameters: {'period': _periodToString(period)},
      );
      return response.data ?? _getFallbackProductivityAnalytics();
    } catch (e) {
      return _getFallbackProductivityAnalytics();
    }
  }

  @override
  Future<Map<String, dynamic>> getMoodAnalytics(AnalyticsPeriod period) async {
    try {
      // Use comprehensive analytics endpoint for mood data
      final response = await dio.get(
        '/api/analytics/comprehensive',
        queryParameters: {'period': _periodToString(period)},
      );
      // Extract mood analytics from comprehensive response
      final data = response.data;
      if (data != null && data['moodAnalytics'] != null) {
        return data['moodAnalytics'];
      }
      return _getFallbackMoodAnalytics();
    } catch (e) {
      return _getFallbackMoodAnalytics();
    }
  }

  @override
  Future<Map<String, dynamic>> getWeeklyStats() async {
    try {
      // Use comprehensive analytics endpoint for weekly stats
      final response = await dio.get(
        '/api/analytics/comprehensive',
        queryParameters: {'period': 'week'},
      );
      return response.data ?? _getFallbackWeeklyStats();
    } catch (e) {
      return _getFallbackWeeklyStats();
    }
  }

  @override
  Future<Map<String, dynamic>> getTaskCompletionStats(
    AnalyticsPeriod? period,
  ) async {
    try {
      final response = await dio.get(
        '/api/analytics/tasks',
        queryParameters:
            period != null ? {'period': _periodToString(period)} : {},
      );
      return response.data ?? _getFallbackTaskCompletionStats();
    } catch (e) {
      return _getFallbackTaskCompletionStats();
    }
  }

  @override
  Future<void> recordMoodEntry(String mood) async {
    await dio.post('/analytics/mood', data: {'mood': mood});
  }

  @override
  Future<void> recordFocusSession(int minutes) async {
    await dio.post('/analytics/focus-session', data: {'minutes': minutes});
  }

  @override
  Future<Map<String, dynamic>> getPersonalizedInsights() async {
    try {
      // Use comprehensive analytics endpoint for insights
      final response = await dio.get('/api/analytics/comprehensive');
      final data = response.data;
      if (data != null && data['insights'] != null) {
        return data['insights'];
      }
      return _getFallbackInsights();
    } catch (e) {
      return _getFallbackInsights();
    }
  }

  @override
  Future<HabitAnalyticsModel> getHabitCorrelations(
    AnalyticsPeriod period,
  ) async {
    try {
      // Use habit analytics endpoint for correlations
      final response = await dio.get(
        '/api/analytics/habits',
        queryParameters: {'period': _periodToString(period)},
      );
      if (response.data != null) {
        return HabitAnalyticsModel.fromJson(response.data);
      } else {
        return HabitAnalyticsModel.fromJson(_getFallbackHabitAnalytics());
      }
    } catch (e) {
      return HabitAnalyticsModel.fromJson(_getFallbackHabitAnalytics());
    }
  }

  @override
  Future<Map<String, dynamic>> getStreakMilestones() async {
    try {
      // Use habit analytics endpoint for streak data
      final response = await dio.get('/api/analytics/habits');
      final data = response.data;
      if (data != null && data['streaks'] != null) {
        return {'streaks': data['streaks']};
      }
      return _getFallbackStreakMilestones();
    } catch (e) {
      return _getFallbackStreakMilestones();
    }
  }

  @override
  Future<Map<String, dynamic>> getAIImprovementReport(int? days) async {
    try {
      // Use comprehensive analytics endpoint for AI insights
      final period = days != null && days <= 7 ? 'week' : 'month';
      final response = await dio.get(
        '/api/analytics/comprehensive',
        queryParameters: {'period': period},
      );
      final data = response.data;
      if (data != null && data['insights'] != null) {
        return data['insights'];
      }
      return _getFallbackAIReport();
    } catch (e) {
      return _getFallbackAIReport();
    }
  }

  @override
  Future<UserProgressModel> generateProgressReport(
    AnalyticsPeriod period,
  ) async {
    try {
      // Use user analytics endpoint for progress reports
      final response = await dio.get(
        '/api/analytics/user',
        queryParameters: {'period': _periodToString(period)},
      );
      if (response.data != null) {
        return UserProgressModel.fromJson(response.data);
      } else {
        return UserProgressModel.fromJson(_getFallbackUserProgress());
      }
    } catch (e) {
      return UserProgressModel.fromJson(_getFallbackUserProgress());
    }
  }

  @override
  Future<HabitAnalyticsModel> generateHabitReport(
    AnalyticsPeriod period,
  ) async {
    try {
      // Use habit analytics endpoint for habit reports
      final response = await dio.get(
        '/api/analytics/habits',
        queryParameters: {'period': _periodToString(period)},
      );
      if (response.data != null) {
        return HabitAnalyticsModel.fromJson(response.data);
      } else {
        return HabitAnalyticsModel.fromJson(_getFallbackHabitAnalytics());
      }
    } catch (e) {
      return HabitAnalyticsModel.fromJson(_getFallbackHabitAnalytics());
    }
  }

  @override
  Future<Map<String, dynamic>> generateProductivityReport(
    AnalyticsPeriod period,
  ) async {
    try {
      // Use productivity analytics endpoint for productivity reports
      final response = await dio.get(
        '/api/analytics/productivity',
        queryParameters: {'period': _periodToString(period)},
      );
      return response.data ?? _getFallbackProductivityAnalytics();
    } catch (e) {
      return _getFallbackProductivityAnalytics();
    }
  }

  @override
  Future<Map<String, dynamic>> generateComprehensiveReport() async {
    try {
      // Use comprehensive analytics endpoint
      final response = await dio.get('/api/analytics/comprehensive');
      return response.data ?? _getFallbackComprehensiveReport();
    } catch (e) {
      return _getFallbackComprehensiveReport();
    }
  }

  @override
  Future<void> updateDailyProgress({
    int? xpGained,
    String? badgeEarned,
    int? focusMinutes,
    bool? podcastListened,
  }) async {
    final data = <String, dynamic>{};
    if (xpGained != null) data['xpGained'] = xpGained;
    if (badgeEarned != null) data['badgeEarned'] = badgeEarned;
    if (focusMinutes != null) data['focusMinutes'] = focusMinutes;
    if (podcastListened != null) data['podcastListened'] = podcastListened;

    await dio.post('/analytics/daily-progress', data: data);
  }

  @override
  Future<UserProgressModel> updateUserProgress({
    int? activeSkillPlans,
    int? completedSteps,
    int? totalSteps,
    int? xpGained,
    List<String>? badgesEarned,
  }) async {
    final data = <String, dynamic>{};

    if (activeSkillPlans != null ||
        completedSteps != null ||
        totalSteps != null) {
      data['skillPlans'] = <String, dynamic>{};
      if (activeSkillPlans != null) {
        data['skillPlans']['active'] = activeSkillPlans;
      }
      if (completedSteps != null) {
        data['skillPlans']['completedSteps'] = completedSteps;
      }
      if (totalSteps != null) {
        data['skillPlans']['totalSteps'] = totalSteps;
      }
    }

    if (xpGained != null) data['xpGained'] = xpGained;
    if (badgesEarned != null) data['badgesEarned'] = badgesEarned;

    final response = await dio.post('/api/analytics/user', data: data);
    return UserProgressModel.fromJson(response.data);
  }

  /// Provides fallback data when API returns null or incomplete data
  Map<String, dynamic> _getFallbackMoodAnalytics() {
    return {
      'moodDistribution': {},
      'moodTrends': {'daily': [], 'trending': 'stable'},
      'correlations': {
        'productivity': {
          'correlation': 0.0,
          'insight': 'Not enough data to analyze correlation',
        },
        'habits': {'habitImpact': {}, 'topMoodBoosters': []},
      },
      'insights': [
        'Not enough data to analyze correlation',
        'Your mood has been relatively stable, which shows consistency in your emotional well-being',
      ],
      // Keep legacy fields for backward compatibility
      'moodEntries': [],
      'averageMood': 3.0,
      'mostCommonMood': 'neutral',
      'moodTrend': 'stable',
    };
  }

  Map<String, dynamic> _getFallbackProductivityAnalytics() {
    return {
      'overallProductivity': 0.0,
      'productivityByTimeOfDay': [],
      'productivityByDayOfWeek': [],
      'mostProductiveTime': 'morning',
      'mostProductiveDay': 'monday',
      'averageFocusTime': 0.0,
    };
  }

  Map<String, dynamic> _getFallbackWeeklyStats() {
    return {
      'dailyStats': [],
      'totalTasks': 0,
      'totalHabits': 0,
      'totalFocusMinutes': 0,
      'weeklyProductivityScore': 0.0,
      'mostProductiveDay': 'monday',
    };
  }

  Map<String, dynamic> _getFallbackTaskCompletionStats() {
    return {
      'overallCompletionRate': 0.0,
      'onTimeCompletionRate': 0.0,
      'completionByDay': [],
      'completionByPriority': [],
      'averageTasksPerDay': 0.0,
      'mostProductiveDay': 'monday',
    };
  }

  Map<String, dynamic> _getFallbackUserProgress() {
    return {
      'totalPoints': 0,
      'level': 1,
      'nextLevelProgress': 0.0,
      'achievements': [],
      'weeklyGoalsCompleted': 0,
      'totalGoals': 0,
    };
  }

  Map<String, dynamic> _getFallbackHabitAnalytics() {
    return {
      'overallCompletionRate': 0.0,
      'streaks': [],
      'completionByDay': [],
      'mostConsistentHabit': 'none',
      'habitNeedingImprovement': 'none',
    };
  }

  Map<String, dynamic> _getFallbackInsights() {
    return {
      'insights': [],
      'recommendations': [],
      'strengths': [],
      'improvementAreas': [],
    };
  }

  Map<String, dynamic> _getFallbackStreakMilestones() {
    return {
      'insights': [],
      'recommendations': [],
      'strengths': [],
      'improvementAreas': [],
    };
  }

  Map<String, dynamic> _getFallbackAIReport() {
    return {
      'overallScore': 50.0,
      'metrics': [],
      'improvements': [],
      'personalityInsights': [],
      'strengths': [],
      'criticalAreas': [],
      'projectedScore': 60.0,
      'analysisDateRange': 30,
      'generatedAt': DateTime.now().toIso8601String(),
      'motivationalMessage': 'Keep up the great work!',
    };
  }

  Map<String, dynamic> _getFallbackComprehensiveReport() {
    return {
      'insights': [],
      'recommendations': [],
      'strengths': [],
      'improvementAreas': [],
    };
  }
}
