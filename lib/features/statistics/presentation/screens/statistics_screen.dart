import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/statistics_controller.dart';
import '../widgets/user_progress_chart.dart';
import '../widgets/habit_analytics_chart.dart';
import '../widgets/productivity_radar_chart.dart';
import '../widgets/mood_trend_chart.dart';
import '../widgets/task_completion_pie_chart.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../../domain/repositories/analytics_repository.dart';

class StatisticsScreen extends GetView<StatisticsController> {
  const StatisticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return MainLayoutScreen(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeader(theme),
            const SizedBox(height: 16),

            // Period Selector
            _buildPeriodSelector(theme),
            const SizedBox(height: 20),

            // Charts Section
            Expanded(
              child: Obx(() {
                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.errorMessage.isNotEmpty) {
                  return _buildErrorState(theme);
                }

                return _buildChartsGrid(theme);
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Analytics Dashboard',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Visualize your progress with 3D charts',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
            Row(
              children: [
                IconButton(
                  onPressed: () => _showMoodDialog(),
                  icon: const Icon(Icons.mood),
                  tooltip: 'Record Mood',
                ),
                IconButton(
                  onPressed: () => controller.refreshData(),
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh Data',
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPeriodSelector(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Obx(() {
        return Row(
          children:
              AnalyticsPeriod.values.map((period) {
                final isSelected = controller.selectedPeriod == period;
                return Expanded(
                  child: GestureDetector(
                    onTap: () => controller.changePeriod(period),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? theme.colorScheme.primary
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getPeriodLabel(period),
                        textAlign: TextAlign.center,
                        style: theme.textTheme.labelLarge?.copyWith(
                          color:
                              isSelected
                                  ? theme.colorScheme.onPrimary
                                  : theme.colorScheme.onSurface,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        );
      }),
    );
  }

  Widget _buildChartsGrid(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          // User Progress Chart
          if (controller.userProgress != null)
            SizedBox(
              height: 280,
              child: UserProgressChart(
                data: controller.userProgress!,
                is3D: true,
              ),
            ),
          const SizedBox(height: 16),

          // Habit Analytics Chart
          if (controller.habitAnalytics != null)
            SizedBox(
              height: 280,
              child: HabitAnalyticsChart(
                data: controller.habitAnalytics!,
                is3D: true,
              ),
            ),
          const SizedBox(height: 16),

          // Row with Productivity and Task Completion
          SizedBox(
            height: 280,
            child: Row(
              children: [
                // Productivity Radar Chart
                if (controller.productivityAnalytics != null)
                  Expanded(
                    child: ProductivityRadarChart(
                      data: controller.productivityAnalytics!,
                      is3D: true,
                    ),
                  ),
                const SizedBox(width: 12),

                // Task Completion Pie Chart
                if (controller.taskCompletionStats != null)
                  Expanded(
                    child: TaskCompletionPieChart(
                      data: controller.taskCompletionStats!,
                      is3D: true,
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Mood Trend Chart
          if (controller.moodAnalytics != null)
            SizedBox(
              height: 280,
              child: MoodTrendChart(
                data: controller.moodAnalytics!,
                is3D: true,
              ),
            ),

          // Weekly Stats Summary
          if (controller.weeklyStats != null) _buildWeeklyStatsSummary(theme),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildWeeklyStatsSummary(ThemeData theme) {
    final stats = controller.weeklyStats!;

    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Weekly Summary',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildStatCard(
                theme,
                'Tasks',
                '${stats.totalTasks}',
                Icons.task_alt,
                Colors.blue,
              ),
              const SizedBox(width: 12),
              _buildStatCard(
                theme,
                'Habits',
                '${stats.totalHabits}',
                Icons.repeat,
                Colors.green,
              ),
              const SizedBox(width: 12),
              _buildStatCard(
                theme,
                'Focus Time',
                '${(stats.totalFocusMinutes / 60).toStringAsFixed(1)}h',
                Icons.timer,
                Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    ThemeData theme,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),
          Text(
            'Failed to load analytics data',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.errorMessage,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => controller.refreshData(),
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showMoodDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('How are you feeling?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMoodOption('😢', 'Terrible'),
            _buildMoodOption('🙁', 'Bad'),
            _buildMoodOption('😐', 'Neutral'),
            _buildMoodOption('🙂', 'Good'),
            _buildMoodOption('😊', 'Great'),
          ],
        ),
      ),
    );
  }

  Widget _buildMoodOption(String emoji, String mood) {
    return ListTile(
      leading: Text(emoji, style: const TextStyle(fontSize: 24)),
      title: Text(mood),
      onTap: () {
        Get.back();
        controller.recordMood(mood.toLowerCase());
      },
    );
  }

  String _getPeriodLabel(AnalyticsPeriod period) {
    switch (period) {
      case AnalyticsPeriod.day:
        return 'Day';
      case AnalyticsPeriod.week:
        return 'Week';
      case AnalyticsPeriod.month:
        return 'Month';
      case AnalyticsPeriod.year:
        return 'Year';
    }
  }
}
