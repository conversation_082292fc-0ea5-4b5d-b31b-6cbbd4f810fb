import 'package:get/get.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/user_progress_entity.dart';
import '../../domain/entities/habit_analytics_entity.dart';
import '../../domain/entities/productivity_analytics_entity.dart';
import '../../domain/entities/mood_analytics_entity.dart';
import '../../domain/entities/weekly_stats_entity.dart';
import '../../domain/entities/task_completion_stats_entity.dart';
import '../../domain/repositories/analytics_repository.dart';
import '../../domain/usecases/get_user_progress_usecase.dart';
import '../../domain/usecases/get_habit_analytics_usecase.dart';
import '../../domain/usecases/get_productivity_analytics_usecase.dart';
import '../../domain/usecases/get_weekly_stats_usecase.dart';

class StatisticsController extends GetxController {
  final GetAnalyticsUserProgressUseCase getUserProgressUseCase;
  final GetAnalyticsHabitAnalyticsUseCase getHabitAnalyticsUseCase;
  final GetAnalyticsProductivityAnalyticsUseCase
  getProductivityAnalyticsUseCase;
  final GetAnalyticsWeeklyStatsUseCase getWeeklyStatsUseCase;
  final AnalyticsRepository analyticsRepository;

  StatisticsController({
    required this.getUserProgressUseCase,
    required this.getHabitAnalyticsUseCase,
    required this.getProductivityAnalyticsUseCase,
    required this.getWeeklyStatsUseCase,
    required this.analyticsRepository,
  });

  // Observable state
  final _isLoading = false.obs;
  final _selectedPeriod = AnalyticsPeriod.week.obs;
  final _userProgress = Rxn<UserProgressEntity>();
  final _habitAnalytics = Rxn<HabitAnalyticsEntity>();
  final _productivityAnalytics = Rxn<ProductivityAnalyticsEntity>();
  final _weeklyStats = Rxn<WeeklyStatsEntity>();
  final _taskCompletionStats = Rxn<TaskCompletionStatsEntity>();
  final _moodAnalytics = Rxn<MoodAnalyticsEntity>();
  final _errorMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  AnalyticsPeriod get selectedPeriod => _selectedPeriod.value;
  UserProgressEntity? get userProgress => _userProgress.value;
  HabitAnalyticsEntity? get habitAnalytics => _habitAnalytics.value;
  ProductivityAnalyticsEntity? get productivityAnalytics =>
      _productivityAnalytics.value;
  WeeklyStatsEntity? get weeklyStats => _weeklyStats.value;
  TaskCompletionStatsEntity? get taskCompletionStats =>
      _taskCompletionStats.value;
  MoodAnalyticsEntity? get moodAnalytics => _moodAnalytics.value;
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    loadAllData();
  }

  Future<void> loadAllData() async {
    _setLoading(true);
    _errorMessage.value = '';

    try {
      await Future.wait([
        loadUserProgress(),
        loadHabitAnalytics(),
        loadProductivityAnalytics(),
        loadWeeklyStats(),
        loadTaskCompletionStats(),
        loadMoodAnalytics(),
      ]);
    } catch (e) {
      _errorMessage.value = 'Failed to load statistics data';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadUserProgress() async {
    final result = await getUserProgressUseCase(
      GetUserProgressParams(period: selectedPeriod),
    );

    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (data) => _userProgress.value = data,
    );
  }

  Future<void> loadHabitAnalytics() async {
    final result = await getHabitAnalyticsUseCase(
      GetHabitAnalyticsParams(period: selectedPeriod),
    );

    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (data) => _habitAnalytics.value = data,
    );
  }

  Future<void> loadProductivityAnalytics() async {
    final result = await getProductivityAnalyticsUseCase(
      GetProductivityAnalyticsParams(period: selectedPeriod),
    );

    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (data) => _productivityAnalytics.value = data,
    );
  }

  Future<void> loadWeeklyStats() async {
    final result = await getWeeklyStatsUseCase(NoParams());

    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (data) => _weeklyStats.value = data,
    );
  }

  Future<void> loadTaskCompletionStats() async {
    final result = await analyticsRepository.getTaskCompletionStats(
      selectedPeriod,
    );

    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (data) => _taskCompletionStats.value = data,
    );
  }

  Future<void> loadMoodAnalytics() async {
    final result = await analyticsRepository.getMoodAnalytics(selectedPeriod);

    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (data) => _moodAnalytics.value = data,
    );
  }

  void changePeriod(AnalyticsPeriod period) {
    if (period != selectedPeriod) {
      _selectedPeriod.value = period;
      loadAllData();
    }
  }

  Future<void> recordMood(String mood) async {
    final result = await analyticsRepository.recordMoodEntry(mood);
    result.fold((failure) => Get.snackbar('Error', failure.message), (_) {
      Get.snackbar('Success', 'Mood recorded successfully');
      loadMoodAnalytics(); // Refresh mood data
    });
  }

  Future<void> recordFocusSession(int minutes) async {
    final result = await analyticsRepository.recordFocusSession(minutes);
    result.fold((failure) => Get.snackbar('Error', failure.message), (_) {
      Get.snackbar('Success', 'Focus session recorded successfully');
      loadAllData(); // Refresh all data as focus sessions affect multiple metrics
    });
  }

  Future<void> refreshData() async {
    await loadAllData();
  }

  // New analytics methods
  final _personalizedInsights = Rxn<Map<String, dynamic>>();
  final _aiImprovementReport = Rxn<Map<String, dynamic>>();
  final _streakMilestones = Rxn<Map<String, dynamic>>();

  Map<String, dynamic>? get personalizedInsights => _personalizedInsights.value;
  Map<String, dynamic>? get aiImprovementReport => _aiImprovementReport.value;
  Map<String, dynamic>? get streakMilestones => _streakMilestones.value;

  Future<void> loadPersonalizedInsights() async {
    final result = await analyticsRepository.getPersonalizedInsights();
    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (insights) => _personalizedInsights.value = insights,
    );
  }

  Future<void> loadAIImprovementReport({int? days}) async {
    final result = await analyticsRepository.getAIImprovementReport(days);
    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (report) => _aiImprovementReport.value = report,
    );
  }

  Future<void> loadStreakMilestones() async {
    final result = await analyticsRepository.getStreakMilestones();
    result.fold(
      (failure) => _errorMessage.value = failure.message,
      (milestones) => _streakMilestones.value = milestones,
    );
  }

  Future<void> updateDailyProgress({
    int? xpGained,
    String? badgeEarned,
    int? focusMinutes,
    bool? podcastListened,
  }) async {
    final result = await analyticsRepository.updateDailyProgress(
      xpGained: xpGained,
      badgeEarned: badgeEarned,
      focusMinutes: focusMinutes,
      podcastListened: podcastListened,
    );
    result.fold((failure) => Get.snackbar('Error', failure.message), (_) {
      Get.snackbar('Success', 'Daily progress updated');
      loadAllData(); // Refresh data
    });
  }

  Future<void> updateUserProgress({
    int? activeSkillPlans,
    int? completedSteps,
    int? totalSteps,
    int? xpGained,
    List<String>? badgesEarned,
  }) async {
    final result = await analyticsRepository.updateUserProgress(
      activeSkillPlans: activeSkillPlans,
      completedSteps: completedSteps,
      totalSteps: totalSteps,
      xpGained: xpGained,
      badgesEarned: badgesEarned,
    );
    result.fold((failure) => Get.snackbar('Error', failure.message), (
      updatedProgress,
    ) {
      _userProgress.value = updatedProgress;
      Get.snackbar('Success', 'User progress updated');
    });
  }

  void _setLoading(bool loading) {
    _isLoading.value = loading;
  }
}
