import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../domain/entities/task_completion_stats_entity.dart';

class TaskCompletion<PERSON>ieChart extends StatelessWidget {
  final TaskCompletionStatsEntity data;
  final bool is3D;

  const TaskCompletionPieChart({
    super.key,
    required this.data,
    this.is3D = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow:
            is3D
                ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                  BoxShadow(
                    color: Colors.teal.withValues(alpha: 0.05),
                    blurRadius: 15,
                    offset: const Offset(-5, -5),
                  ),
                ]
                : null,
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Task Completion by Priority',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: PieChart(
                    PieChartData(
                      pieTouchData: PieTouchData(
                        enabled: true,
                        touchCallback:
                            (FlTouchEvent event, pieTouchResponse) {},
                      ),
                      borderData: FlBorderData(show: false),
                      sectionsSpace: is3D ? 4 : 2,
                      centerSpaceRadius: is3D ? 40 : 30,
                      sections: _buildPieSections(theme),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildLegendItem(
                        theme,
                        'High Priority',
                        _getPriorityColor('high'),
                        _getPriorityCompletionRate('high'),
                      ),
                      const SizedBox(height: 8),
                      _buildLegendItem(
                        theme,
                        'Medium Priority',
                        _getPriorityColor('medium'),
                        _getPriorityCompletionRate('medium'),
                      ),
                      const SizedBox(height: 8),
                      _buildLegendItem(
                        theme,
                        'Low Priority',
                        _getPriorityColor('low'),
                        _getPriorityCompletionRate('low'),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Overall Rate',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                            Text(
                              '${data.overallCompletionRate.toStringAsFixed(1)}%',
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieSections(ThemeData theme) {
    return data.completionByPriority.map((priorityData) {
      final color = _getPriorityColor(priorityData.priority);
      final completionRate = priorityData.completionRate;

      return PieChartSectionData(
        color: color,
        value: completionRate,
        title: '${completionRate.toStringAsFixed(1)}%',
        radius: is3D ? 80 : 70,
        titleStyle: theme.textTheme.bodySmall?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
        borderSide:
            is3D
                ? BorderSide(color: color.withValues(alpha: 0.3), width: 2)
                : BorderSide.none,
      );
    }).toList();
  }

  Widget _buildLegendItem(
    ThemeData theme,
    String label,
    Color color,
    double percentage,
  ) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(3),
            boxShadow:
                is3D
                    ? [
                      BoxShadow(
                        color: color.withValues(alpha: 0.3),
                        blurRadius: 3,
                        offset: const Offset(1, 1),
                      ),
                    ]
                    : null,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '${percentage.toStringAsFixed(1)}%',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  double _getPriorityCompletionRate(String priority) {
    final priorityData = data.completionByPriority.firstWhere(
      (item) => item.priority.toLowerCase() == priority.toLowerCase(),
      orElse:
          () => const TaskCompletionByPriorityEntity(
            priority: '',
            completed: 0,
            due: 0,
            completionRate: 0,
          ),
    );
    return priorityData.completionRate;
  }
}
