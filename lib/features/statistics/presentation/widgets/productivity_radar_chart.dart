import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../domain/entities/productivity_analytics_entity.dart';

class ProductivityRadarChart extends StatelessWidget {
  final ProductivityAnalyticsEntity data;
  final bool is3D;

  const ProductivityRadarChart({
    super.key,
    required this.data,
    this.is3D = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow:
            is3D
                ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                  BoxShadow(
                    color: theme.colorScheme.tertiary.withValues(alpha: 0.05),
                    blurRadius: 15,
                    offset: const Offset(-5, -5),
                  ),
                ]
                : null,
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'Productivity by Time of Day',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.tertiary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${data.overallProductivity.toStringAsFixed(1)}%',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.colorScheme.tertiary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Expanded(
            child: RadarChart(
              RadarChartData(
                radarTouchData: RadarTouchData(
                  enabled: true,
                  touchCallback: (FlTouchEvent event, response) {},
                ),
                dataSets: [
                  RadarDataSet(
                    fillColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                    borderColor: theme.colorScheme.primary,
                    borderWidth: 3,
                    entryRadius: 5,
                    dataEntries:
                        data.productivityByTimeOfDay.map((timeData) {
                          return RadarEntry(value: timeData.productivityScore);
                        }).toList(),
                  ),
                ],
                radarBackgroundColor: Colors.transparent,
                borderData: FlBorderData(show: false),
                radarBorderData: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
                titlePositionPercentageOffset: 0.2,
                titleTextStyle:
                    theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ) ??
                    const TextStyle(),
                getTitle: (index, angle) {
                  if (index < data.productivityByTimeOfDay.length) {
                    return RadarChartTitle(
                      text: data.productivityByTimeOfDay[index].timeOfDay,
                      angle: angle,
                    );
                  }
                  return const RadarChartTitle(text: '');
                },
                tickCount: 5,
                ticksTextStyle:
                    theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      fontSize: 10,
                    ) ??
                    const TextStyle(),
                tickBorderData: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
                gridBorderData: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
