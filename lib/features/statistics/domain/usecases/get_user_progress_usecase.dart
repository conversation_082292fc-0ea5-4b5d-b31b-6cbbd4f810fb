import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user_progress_entity.dart';
import '../repositories/analytics_repository.dart';

class GetAnalyticsUserProgressUseCase
    implements UseCase<UserProgressEntity, GetUserProgressParams> {
  final AnalyticsRepository repository;

  GetAnalyticsUserProgressUseCase(this.repository);

  @override
  Future<Either<Failure, UserProgressEntity>> call(
    GetUserProgressParams params,
  ) async {
    return await repository.getUserProgress(params.period);
  }
}

class GetUserProgressParams {
  final AnalyticsPeriod period;

  GetUserProgressParams({required this.period});
}
