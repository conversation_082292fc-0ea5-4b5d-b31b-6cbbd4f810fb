import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/analytics_repository.dart';

class GetAnalyticsPersonalizedInsightsUseCase
    implements UseCase<Map<String, dynamic>, NoParams> {
  final AnalyticsRepository repository;

  GetAnalyticsPersonalizedInsightsUseCase(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(NoParams params) async {
    return await repository.getPersonalizedInsights();
  }
}
