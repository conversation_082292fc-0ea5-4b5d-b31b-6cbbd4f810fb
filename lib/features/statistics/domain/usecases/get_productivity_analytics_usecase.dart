import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/productivity_analytics_entity.dart';
import '../repositories/analytics_repository.dart';

class GetAnalyticsProductivityAnalyticsUseCase
    implements
        UseCase<ProductivityAnalyticsEntity, GetProductivityAnalyticsParams> {
  final AnalyticsRepository repository;

  GetAnalyticsProductivityAnalyticsUseCase(this.repository);

  @override
  Future<Either<Failure, ProductivityAnalyticsEntity>> call(
    GetProductivityAnalyticsParams params,
  ) async {
    return await repository.getProductivityAnalytics(params.period);
  }
}

class GetProductivityAnalyticsParams {
  final AnalyticsPeriod period;

  GetProductivityAnalyticsParams({required this.period});
}
