import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/habit_analytics_entity.dart';
import '../repositories/analytics_repository.dart';

class GetAnalyticsHabitAnalyticsUseCase
    implements UseCase<HabitAnalyticsEntity, GetHabitAnalyticsParams> {
  final AnalyticsRepository repository;

  GetAnalyticsHabitAnalyticsUseCase(this.repository);

  @override
  Future<Either<Failure, HabitAnalyticsEntity>> call(
    GetHabitAnalyticsParams params,
  ) async {
    return await repository.getHabitAnalytics(params.period);
  }
}

class GetHabitAnalyticsParams {
  final AnalyticsPeriod period;

  GetHabitAnalyticsParams({required this.period});
}
