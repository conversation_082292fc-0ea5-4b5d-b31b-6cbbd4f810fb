import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/weekly_stats_entity.dart';
import '../repositories/analytics_repository.dart';

class GetAnalyticsWeeklyStatsUseCase
    implements UseCase<WeeklyStatsEntity, NoParams> {
  final AnalyticsRepository repository;

  GetAnalyticsWeeklyStatsUseCase(this.repository);

  @override
  Future<Either<Failure, WeeklyStatsEntity>> call(NoParams params) async {
    return await repository.getWeeklyStats();
  }
}
