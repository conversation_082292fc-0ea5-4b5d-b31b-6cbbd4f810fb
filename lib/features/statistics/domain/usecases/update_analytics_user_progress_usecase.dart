import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user_progress_entity.dart';
import '../repositories/analytics_repository.dart';

class UpdateAnalyticsUserProgressUseCase
    implements UseCase<UserProgressEntity, UpdateUserProgressParams> {
  final AnalyticsRepository repository;

  UpdateAnalyticsUserProgressUseCase(this.repository);

  @override
  Future<Either<Failure, UserProgressEntity>> call(
    UpdateUserProgressParams params,
  ) async {
    return await repository.updateUserProgress(
      activeSkillPlans: params.activeSkillPlans,
      completedSteps: params.completedSteps,
      totalSteps: params.totalSteps,
      xpGained: params.xpGained,
      badgesEarned: params.badgesEarned,
    );
  }
}

class UpdateUserProgressParams {
  final int? activeSkillPlans;
  final int? completedSteps;
  final int? totalSteps;
  final int? xpGained;
  final List<String>? badgesEarned;

  UpdateUserProgressParams({
    this.activeSkillPlans,
    this.completedSteps,
    this.totalSteps,
    this.xpGained,
    this.badgesEarned,
  });
}
