import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/analytics_repository.dart';

class UpdateAnalyticsDailyProgressUseCase
    implements UseCase<void, UpdateDailyProgressParams> {
  final AnalyticsRepository repository;

  UpdateAnalyticsDailyProgressUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(UpdateDailyProgressParams params) async {
    return await repository.updateDailyProgress(
      xpGained: params.xpGained,
      badgeEarned: params.badgeEarned,
      focusMinutes: params.focusMinutes,
      podcastListened: params.podcastListened,
    );
  }
}

class UpdateDailyProgressParams {
  final int? xpGained;
  final String? badgeEarned;
  final int? focusMinutes;
  final bool? podcastListened;

  UpdateDailyProgressParams({
    this.xpGained,
    this.badgeEarned,
    this.focusMinutes,
    this.podcastListened,
  });
}
