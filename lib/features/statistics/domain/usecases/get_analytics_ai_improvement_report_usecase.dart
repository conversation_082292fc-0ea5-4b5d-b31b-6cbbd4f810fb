import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/analytics_repository.dart';

class GetAnalyticsAIImprovementReportUseCase
    implements UseCase<Map<String, dynamic>, GetAIImprovementReportParams> {
  final AnalyticsRepository repository;

  GetAnalyticsAIImprovementReportUseCase(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(
    GetAIImprovementReportParams params,
  ) async {
    return await repository.getAIImprovementReport(params.days);
  }
}

class GetAIImprovementReportParams {
  final int? days;

  GetAIImprovementReportParams({this.days});
}
