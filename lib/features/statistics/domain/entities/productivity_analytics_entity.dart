import 'package:equatable/equatable.dart';

class ProductivityAnalyticsEntity extends Equatable {
  final double overallProductivity;
  final List<TimeOfDayProductivityEntity> productivityByTimeOfDay;
  final List<DayOfWeekProductivityEntity> productivityByDayOfWeek;
  final String mostProductiveTime;
  final String mostProductiveDay;
  final double averageFocusTime;

  const ProductivityAnalyticsEntity({
    required this.overallProductivity,
    required this.productivityByTimeOfDay,
    required this.productivityByDayOfWeek,
    required this.mostProductiveTime,
    required this.mostProductiveDay,
    required this.averageFocusTime,
  });

  @override
  List<Object?> get props => [
    overallProductivity,
    productivityByTimeOfDay,
    productivityByDayOfWeek,
    mostProductiveTime,
    mostProductiveDay,
    averageFocusTime,
  ];
}

class TimeOfDayProductivityEntity extends Equatable {
  final String timeOfDay;
  final double productivityScore;
  final int tasksCompleted;

  const TimeOfDayProductivityEntity({
    required this.timeOfDay,
    required this.productivityScore,
    required this.tasksCompleted,
  });

  @override
  List<Object?> get props => [timeOfDay, productivityScore, tasksCompleted];
}

class DayOfWeekProductivityEntity extends Equatable {
  final String dayOfWeek;
  final double productivityScore;

  const DayOfWeekProductivityEntity({
    required this.dayOfWeek,
    required this.productivityScore,
  });

  @override
  List<Object?> get props => [dayOfWeek, productivityScore];
}
