import 'package:equatable/equatable.dart';

class MoodAnalyticsEntity extends Equatable {
  final List<DailyMoodEntity> moodEntries;
  final double averageMood;
  final String mostCommonMood;
  final String moodTrend;
  final List<MoodCorrelationEntity> correlations;

  const MoodAnalyticsEntity({
    required this.moodEntries,
    required this.averageMood,
    required this.mostCommonMood,
    required this.moodTrend,
    required this.correlations,
  });

  @override
  List<Object?> get props => [
    moodEntries,
    averageMood,
    mostCommonMood,
    moodTrend,
    correlations,
  ];
}

class DailyMoodEntity extends Equatable {
  final DateTime date;
  final String mood;
  final int moodScore;

  const DailyMoodEntity({
    required this.date,
    required this.mood,
    required this.moodScore,
  });

  @override
  List<Object?> get props => [date, mood, moodScore];
}

class MoodCorrelationEntity extends Equatable {
  final String factor;
  final double correlationStrength;
  final String correlationType;

  const MoodCorrelationEntity({
    required this.factor,
    required this.correlationStrength,
    required this.correlationType,
  });

  @override
  List<Object?> get props => [factor, correlationStrength, correlationType];
}
