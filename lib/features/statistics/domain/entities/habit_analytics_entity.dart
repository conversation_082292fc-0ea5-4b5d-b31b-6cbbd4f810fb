import 'package:equatable/equatable.dart';

class HabitAnalyticsEntity extends Equatable {
  final double overallCompletionRate;
  final List<HabitStreakEntity> streaks;
  final List<HabitCompletionByDayEntity> completionByDay;
  final String mostConsistentHabit;
  final String habitNeedingImprovement;

  const HabitAnalyticsEntity({
    required this.overallCompletionRate,
    required this.streaks,
    required this.completionByDay,
    required this.mostConsistentHabit,
    required this.habitNeedingImprovement,
  });

  @override
  List<Object?> get props => [
    overallCompletionRate,
    streaks,
    completionByDay,
    mostConsistentHabit,
    habitNeedingImprovement,
  ];
}

class HabitStreakEntity extends Equatable {
  final String name;
  final int currentStreak;
  final int longestStreak;
  final double completionRate;

  const HabitStreakEntity({
    required this.name,
    required this.currentStreak,
    required this.longestStreak,
    required this.completionRate,
  });

  @override
  List<Object?> get props => [
    name,
    currentStreak,
    longestStreak,
    completionRate,
  ];
}

class HabitCompletionByDayEntity extends Equatable {
  final String day;
  final double completionPercentage;

  const HabitCompletionByDayEntity({
    required this.day,
    required this.completionPercentage,
  });

  @override
  List<Object?> get props => [day, completionPercentage];
}
