import 'package:equatable/equatable.dart';

class TaskCompletionStatsEntity extends Equatable {
  final double overallCompletionRate;
  final double onTimeCompletionRate;
  final List<TaskCompletionByDayEntity> completionByDay;
  final List<TaskCompletionByPriorityEntity> completionByPriority;
  final double averageTasksPerDay;
  final String mostProductiveDay;

  const TaskCompletionStatsEntity({
    required this.overallCompletionRate,
    required this.onTimeCompletionRate,
    required this.completionByDay,
    required this.completionByPriority,
    required this.averageTasksPerDay,
    required this.mostProductiveDay,
  });

  @override
  List<Object?> get props => [
    overallCompletionRate,
    onTimeCompletionRate,
    completionByDay,
    completionByPriority,
    averageTasksPerDay,
    mostProductiveDay,
  ];
}

class TaskCompletionByDayEntity extends Equatable {
  final DateTime day;
  final int completed;
  final int due;
  final double completionRate;

  const TaskCompletionByDayEntity({
    required this.day,
    required this.completed,
    required this.due,
    required this.completionRate,
  });

  @override
  List<Object?> get props => [day, completed, due, completionRate];
}

class TaskCompletionByPriorityEntity extends Equatable {
  final String priority;
  final int completed;
  final int due;
  final double completionRate;

  const TaskCompletionByPriorityEntity({
    required this.priority,
    required this.completed,
    required this.due,
    required this.completionRate,
  });

  @override
  List<Object?> get props => [priority, completed, due, completionRate];
}
