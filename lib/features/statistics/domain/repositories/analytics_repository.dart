import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/user_progress_entity.dart';
import '../entities/habit_analytics_entity.dart';
import '../entities/productivity_analytics_entity.dart';
import '../entities/mood_analytics_entity.dart';
import '../entities/weekly_stats_entity.dart';
import '../entities/task_completion_stats_entity.dart';

enum AnalyticsPeriod { day, week, month, year }

abstract class AnalyticsRepository {
  /// Get user progress analytics for the specified period
  Future<Either<Failure, UserProgressEntity>> getUserProgress(
    AnalyticsPeriod period,
  );

  /// Get detailed habit analytics for the specified period
  Future<Either<Failure, HabitAnalyticsEntity>> getHabitAnalytics(
    AnalyticsPeriod period,
  );

  /// Get productivity patterns and insights for the specified period
  Future<Either<Failure, ProductivityAnalyticsEntity>> getProductivityAnalytics(
    AnalyticsPeriod period,
  );

  /// Get mood tracking analytics and patterns for the specified period
  Future<Either<Failure, MoodAnalyticsEntity>> getMoodAnalytics(
    AnalyticsPeriod period,
  );

  /// Get aggregated weekly statistics
  Future<Either<Failure, WeeklyStatsEntity>> getWeeklyStats();

  /// Get task completion analytics for the specified period
  Future<Either<Failure, TaskCompletionStatsEntity>> getTaskCompletionStats(
    AnalyticsPeriod? period,
  );

  /// Get personalized insights and recommendations
  Future<Either<Failure, Map<String, dynamic>>> getPersonalizedInsights();

  /// Get habit correlations with mood and productivity
  Future<Either<Failure, MoodAnalyticsEntity>> getHabitCorrelations(
    AnalyticsPeriod period,
  );

  /// Get current streak milestones for habits
  Future<Either<Failure, Map<String, dynamic>>> getStreakMilestones();

  /// Get AI-powered lifestyle improvement report
  Future<Either<Failure, Map<String, dynamic>>> getAIImprovementReport(
    int? days,
  );

  /// Generate comprehensive progress report
  Future<Either<Failure, UserProgressEntity>> generateProgressReport(
    AnalyticsPeriod period,
  );

  /// Generate detailed habit report
  Future<Either<Failure, HabitAnalyticsEntity>> generateHabitReport(
    AnalyticsPeriod period,
  );

  /// Generate productivity report
  Future<Either<Failure, ProductivityAnalyticsEntity>>
  generateProductivityReport(AnalyticsPeriod period);

  /// Generate comprehensive analytics report
  Future<Either<Failure, Map<String, dynamic>>> generateComprehensiveReport();

  /// Record a mood entry
  Future<Either<Failure, void>> recordMoodEntry(String mood);

  /// Record a completed focus session
  Future<Either<Failure, void>> recordFocusSession(int minutes);

  /// Update daily progress with XP, badges, focus time, and podcast listening
  Future<Either<Failure, void>> updateDailyProgress({
    int? xpGained,
    String? badgeEarned,
    int? focusMinutes,
    bool? podcastListened,
  });

  /// Update comprehensive user progress including skill plans and achievements
  Future<Either<Failure, UserProgressEntity>> updateUserProgress({
    int? activeSkillPlans,
    int? completedSteps,
    int? totalSteps,
    int? xpGained,
    List<String>? badgesEarned,
  });
}
