import 'dart:typed_data';
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';

/// Repository interface for user management operations
abstract class UserRepository {
  /// Download user data as PDF
  /// Returns Either [Failure] or [Uint8List] containing PDF data
  Future<Either<Failure, Uint8List>> downloadUserDataPdf();

  /// Delete user account and all associated data
  /// Returns Either [Failure] or [bool] indicating success
  Future<Either<Failure, bool>> deleteAccount();
}
