import 'dart:typed_data';
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/user_repository.dart';

/// Use case for downloading user data as PDF
class DownloadUserDataPdfUseCase {
  final UserRepository repository;

  const DownloadUserDataPdfUseCase(this.repository);

  /// Execute the use case
  Future<Either<Failure, Uint8List>> call() async {
    return await repository.downloadUserDataPdf();
  }
}
