import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/user_repository.dart';

/// Use case for deleting user account and all associated data
class DeleteAccountUseCase {
  final UserRepository repository;

  const DeleteAccountUseCase(this.repository);

  /// Execute the use case
  Future<Either<Failure, bool>> call() async {
    return await repository.deleteAccount();
  }
}
