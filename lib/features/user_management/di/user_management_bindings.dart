import 'package:get/get.dart';
import '../../core/data/api/api_client.dart';
import '../../../core/network/network_info.dart';
import '../../../core/services/secure_storage_service.dart';
import '../data/datasources/user_remote_data_source.dart';
import '../data/datasources/user_remote_data_source_impl.dart';
import '../data/repositories/user_repository_impl.dart';
import '../domain/repositories/user_repository.dart';
import '../domain/usecases/delete_account_usecase.dart';
import '../domain/usecases/request_data_export_usecase.dart';
import '../presentation/controllers/user_management_controller.dart';

/// Dependency injection bindings for user management feature
class UserManagementBindings extends Bindings {
  @override
  void dependencies() {
    // Data sources
    Get.lazyPut<UserRemoteDataSource>(
      () => UserRemoteDataSourceImpl(
        Get.find<ApiClient>(),
        Get.find<SecureStorageService>(),
      ),
    );

    // Repositories
    Get.lazyPut<UserRepository>(
      () => UserRepositoryImpl(
        remoteDataSource: Get.find<UserRemoteDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
      ),
    );

    // Use cases
    Get.lazyPut<DownloadUserDataPdfUseCase>(
      () => DownloadUserDataPdfUseCase(Get.find<UserRepository>()),
    );

    Get.lazyPut<DeleteAccountUseCase>(
      () => DeleteAccountUseCase(Get.find<UserRepository>()),
    );

    // Controllers
    Get.lazyPut<UserManagementController>(
      () => UserManagementController(
        downloadUserDataPdfUseCase: Get.find<DownloadUserDataPdfUseCase>(),
        deleteAccountUseCase: Get.find<DeleteAccountUseCase>(),
      ),
    );
  }
}
