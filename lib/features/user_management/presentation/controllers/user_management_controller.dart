import 'dart:io';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import '../../domain/usecases/delete_account_usecase.dart';
import '../../domain/usecases/request_data_export_usecase.dart';

/// Controller for user management operations
class UserManagementController extends GetxController {
  final DownloadUserDataPdfUseCase _downloadUserDataPdfUseCase;
  final DeleteAccountUseCase _deleteAccountUseCase;

  UserManagementController({
    required DownloadUserDataPdfUseCase downloadUserDataPdfUseCase,
    required DeleteAccountUseCase deleteAccountUseCase,
  }) : _downloadUserDataPdfUseCase = downloadUserDataPdfUseCase,
       _deleteAccountUseCase = deleteAccountUseCase;

  // Reactive variables
  final RxBool _isRequestingExport = false.obs;
  final RxBool _isDeletingAccount = false.obs;

  // Getters
  bool get isRequestingExport => _isRequestingExport.value;
  bool get isDeletingAccount => _isDeletingAccount.value;

  /// Download user data as PDF
  Future<bool> downloadUserDataPdf() async {
    _isRequestingExport.value = true;

    try {
      final result = await _downloadUserDataPdfUseCase();
      return result.fold(
        (failure) {
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Get.theme.colorScheme.error,
            colorText: Get.theme.colorScheme.onError,
          );
          return false;
        },
        (pdfData) async {
          // Save PDF to Downloads folder
          try {
            final directory = await getDownloadsDirectory();
            if (directory != null) {
              final fileName =
                  'user_data_${DateTime.now().millisecondsSinceEpoch}.pdf';
              final file = File('${directory.path}/$fileName');
              await file.writeAsBytes(pdfData);

              Get.snackbar(
                'Success',
                'PDF downloaded successfully to Downloads folder',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Get.theme.colorScheme.primary,
                colorText: Get.theme.colorScheme.onPrimary,
              );
              return true;
            } else {
              Get.snackbar(
                'Error',
                'Could not access Downloads folder',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Get.theme.colorScheme.error,
                colorText: Get.theme.colorScheme.onError,
              );
              return false;
            }
          } catch (e) {
            Get.snackbar(
              'Error',
              'Failed to save PDF: $e',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Get.theme.colorScheme.error,
              colorText: Get.theme.colorScheme.onError,
            );
            return false;
          }
        },
      );
    } finally {
      _isRequestingExport.value = false;
    }
  }

  /// Delete user account
  Future<bool> deleteAccount() async {
    _isDeletingAccount.value = true;

    try {
      final result = await _deleteAccountUseCase();
      return result.fold(
        (failure) {
          Get.snackbar(
            'Error',
            failure.message,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        },
        (success) {
          if (success) {
            Get.snackbar(
              'Success',
              'Account deletion request sent. You will be contacted via email.',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
          return success;
        },
      );
    } finally {
      _isDeletingAccount.value = false;
    }
  }
}
