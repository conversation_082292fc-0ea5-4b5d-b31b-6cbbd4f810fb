import 'dart:typed_data';
import 'package:dio/dio.dart';
import '../../../../core/constants/api_constants.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/services/secure_storage_service.dart';
import '../../../core/data/api/api_client.dart';
import 'user_remote_data_source.dart';

/// Implementation of UserRemoteDataSource using API client
class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  final ApiClient _apiClient;
  final SecureStorageService _secureStorageService;

  UserRemoteDataSourceImpl(this._apiClient, this._secureStorageService);

  @override
  Future<Uint8List> downloadUserDataPdf() async {
    try {
      final token = await _secureStorageService.getAccessToken();
      if (token == null) {
        throw AuthException(message: 'No access token found', statusCode: 401);
      }

      final dio = Dio();
      final response = await dio.get(
        '${ApiConstants.baseUrl}/users/data/export/pdf',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
          responseType: ResponseType.bytes,
        ),
      );

      if (response.statusCode == 200) {
        return Uint8List.fromList(response.data);
      } else {
        throw ServerException(
          message: 'Failed to download PDF: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw AuthException(message: 'Unauthorized', statusCode: 401);
      }
      throw ServerException(
        message: 'Failed to download user data PDF: ${e.message}',
      );
    } catch (e) {
      throw ServerException(message: 'Failed to download user data PDF: $e');
    }
  }

  @override
  Future<bool> deleteAccount() async {
    try {
      final result = await _apiClient.delete<bool>(
        endpoint: '/users/delete-account',
        fromData: (data) => data['success'] as bool? ?? true,
      );
      return result;
    } catch (e) {
      throw ServerException(message: 'Failed to delete account');
    }
  }
}
