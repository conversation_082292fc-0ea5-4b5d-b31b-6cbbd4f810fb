import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/theme_entity.dart';
import '../entities/language_entity.dart';

/// Repository interface for settings operations
abstract class SettingsRepository {
  /// Get theme preferences for user
  Future<Either<Failure, ThemeEntity>> getThemePreferences();

  /// Update theme preferences
  Future<Either<Failure, ThemeEntity>> updateThemePreferences({
    required String themeMode,
  });

  /// Get language preferences for user
  Future<Either<Failure, LanguageEntity>> getLanguagePreferences();

  /// Update language preferences
  Future<Either<Failure, LanguageEntity>> updateLanguagePreferences({
    required String languageCode,
    required String countryCode,
  });

  /// Submit feedback
  Future<Either<Failure, bool>> submitFeedback({
    required String type,
    required String title,
    required String description,
    String? priority,
    String? platform,
    String? appVersion,
  });

  /// Rate app
  Future<Either<Failure, bool>> rateApp();

  /// Open help/FAQ
  Future<Either<Failure, bool>> openHelp();

  /// Open terms of service
  Future<Either<Failure, String>> getTermsOfService();

  /// Open privacy policy
  Future<Either<Failure, String>> getPrivacyPolicy();

  /// Get about us content
  Future<Either<Failure, String>> getAboutUs();
}
