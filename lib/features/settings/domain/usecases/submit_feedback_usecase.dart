import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/settings_repository.dart';

/// Use case for submitting feedback
class SubmitFeedbackUseCase implements UseCase<bool, SubmitFeedbackParams> {
  final SettingsRepository repository;

  const SubmitFeedbackUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(SubmitFeedbackParams params) async {
    return await repository.submitFeedback(
      type: params.type,
      title: params.title,
      description: params.description,
      priority: params.priority,
      platform: params.platform,
      appVersion: params.appVersion,
    );
  }
}

/// Parameters for SubmitFeedbackUseCase
class SubmitFeedbackParams extends Equatable {
  final String type;
  final String title;
  final String description;
  final String? priority;
  final String? platform;
  final String? appVersion;

  const SubmitFeedbackParams({
    required this.type,
    required this.title,
    required this.description,
    this.priority,
    this.platform,
    this.appVersion,
  });

  @override
  List<Object?> get props => [
    type,
    title,
    description,
    priority,
    platform,
    appVersion,
  ];
}
