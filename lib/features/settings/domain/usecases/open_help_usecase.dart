import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/settings_repository.dart';

/// Use case for opening help/FAQ
class OpenHelpUseCase implements UseCase<bool, NoParams> {
  final SettingsRepository repository;

  const OpenHelpUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    return await repository.openHelp();
  }
}
