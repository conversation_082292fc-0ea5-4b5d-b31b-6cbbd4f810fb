import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/theme_entity.dart';
import '../repositories/settings_repository.dart';

/// Use case for updating theme preferences
class UpdateThemePreferencesUseCase
    implements UseCase<ThemeEntity, UpdateThemeParams> {
  final SettingsRepository repository;

  const UpdateThemePreferencesUseCase(this.repository);

  @override
  Future<Either<Failure, ThemeEntity>> call(UpdateThemeParams params) async {
    return await repository.updateThemePreferences(themeMode: params.themeMode);
  }
}

/// Parameters for UpdateThemePreferencesUseCase
class UpdateThemeParams extends Equatable {
  final String themeMode;

  const UpdateThemeParams({required this.themeMode});

  @override
  List<Object?> get props => [themeMode];
}
