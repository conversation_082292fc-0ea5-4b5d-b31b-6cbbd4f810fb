import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/settings_repository.dart';

/// Use case for getting terms of service
class GetTermsOfServiceUseCase implements UseCase<String, NoParams> {
  final SettingsRepository repository;

  const GetTermsOfServiceUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(NoParams params) async {
    return await repository.getTermsOfService();
  }
}
