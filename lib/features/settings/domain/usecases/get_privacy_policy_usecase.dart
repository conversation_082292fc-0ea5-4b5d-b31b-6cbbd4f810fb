import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/settings_repository.dart';

/// Use case for getting privacy policy
class GetPrivacyPolicyUseCase implements UseCase<String, NoParams> {
  final SettingsRepository repository;

  const GetPrivacyPolicyUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(NoParams params) async {
    return await repository.getPrivacyPolicy();
  }
}
