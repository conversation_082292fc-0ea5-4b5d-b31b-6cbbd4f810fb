import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/theme_entity.dart';
import '../repositories/settings_repository.dart';

/// Use case for getting theme preferences
class GetThemePreferencesUseCase implements UseCase<ThemeEntity, NoParams> {
  final SettingsRepository repository;

  const GetThemePreferencesUseCase(this.repository);

  @override
  Future<Either<Failure, ThemeEntity>> call(NoParams params) async {
    return await repository.getThemePreferences();
  }
}
