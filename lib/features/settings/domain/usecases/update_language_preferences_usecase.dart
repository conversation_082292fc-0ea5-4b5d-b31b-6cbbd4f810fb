import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/language_entity.dart';
import '../repositories/settings_repository.dart';

/// Use case for updating language preferences
class UpdateLanguagePreferencesUseCase
    implements UseCase<LanguageEntity, UpdateLanguageParams> {
  final SettingsRepository repository;

  const UpdateLanguagePreferencesUseCase(this.repository);

  @override
  Future<Either<Failure, LanguageEntity>> call(
    UpdateLanguageParams params,
  ) async {
    return await repository.updateLanguagePreferences(
      languageCode: params.languageCode,
      countryCode: params.countryCode,
    );
  }
}

/// Parameters for UpdateLanguagePreferencesUseCase
class UpdateLanguageParams extends Equatable {
  final String languageCode;
  final String countryCode;

  const UpdateLanguageParams({
    required this.languageCode,
    required this.countryCode,
  });

  @override
  List<Object?> get props => [languageCode, countryCode];
}
