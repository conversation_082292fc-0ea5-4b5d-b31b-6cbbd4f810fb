import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/settings_repository.dart';
import 'package:dartz/dartz.dart';

class GetAboutUsUseCase implements UseCase<String, NoParams> {
  final SettingsRepository repository;

  const GetAboutUsUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(NoParams params) async {
    return await repository.getAboutUs();
  }
}
