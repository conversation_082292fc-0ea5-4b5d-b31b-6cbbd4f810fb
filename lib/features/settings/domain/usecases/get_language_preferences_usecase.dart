import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/language_entity.dart';
import '../repositories/settings_repository.dart';

/// Use case for getting language preferences
class GetLanguagePreferencesUseCase
    implements UseCase<LanguageEntity, NoParams> {
  final SettingsRepository repository;

  const GetLanguagePreferencesUseCase(this.repository);

  @override
  Future<Either<Failure, LanguageEntity>> call(NoParams params) async {
    return await repository.getLanguagePreferences();
  }
}
