import 'package:equatable/equatable.dart';

/// Domain entity for language preferences
class LanguageEntity extends Equatable {
  final String userId;
  final String languageCode; // 'en', 'ar', etc.
  final String countryCode; // 'US', 'SA', etc.
  final DateTime updatedAt;

  const LanguageEntity({
    required this.userId,
    required this.languageCode,
    required this.countryCode,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [userId, languageCode, countryCode, updatedAt];

  /// Create a copy with updated fields
  LanguageEntity copyWith({
    String? userId,
    String? languageCode,
    String? countryCode,
    DateTime? updatedAt,
  }) {
    return LanguageEntity(
      userId: userId ?? this.userId,
      languageCode: languageCode ?? this.languageCode,
      countryCode: countryCode ?? this.countryCode,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
