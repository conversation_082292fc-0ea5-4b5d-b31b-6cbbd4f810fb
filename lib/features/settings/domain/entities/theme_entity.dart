import 'package:equatable/equatable.dart';

/// Domain entity representing theme preferences
class ThemeEntity extends Equatable {
  final String userId;
  final String themeMode; // 'light', 'dark', 'system'
  final DateTime updatedAt;

  const ThemeEntity({
    required this.userId,
    required this.themeMode,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [userId, themeMode, updatedAt];

  /// Create a copy with updated fields
  ThemeEntity copyWith({
    String? userId,
    String? themeMode,
    DateTime? updatedAt,
  }) {
    return ThemeEntity(
      userId: userId ?? this.userId,
      themeMode: themeMode ?? this.themeMode,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
