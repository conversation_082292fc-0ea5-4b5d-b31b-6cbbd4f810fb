import '../../../../core/error/exceptions.dart';
import '../../../core/data/api/api_client.dart';
import '../models/theme_model.dart';
import '../models/language_model.dart';
import '../models/feedback_model.dart';

/// Remote data source for settings operations
abstract class SettingsRemoteDataSource {
  /// Get theme preferences for user
  Future<ThemeModel> getThemePreferences();

  /// Update theme preferences
  Future<ThemeModel> updateThemePreferences({required String themeMode});

  /// Get language preferences for user
  Future<LanguageModel> getLanguagePreferences();

  /// Update language preferences
  Future<LanguageModel> updateLanguagePreferences({
    required String languageCode,
    required String countryCode,
  });

  /// Submit feedback
  Future<bool> submitFeedback(FeedbackModel feedback);

  /// Get terms of service
  Future<String> getTermsOfService();

  /// Get privacy policy
  Future<String> getPrivacyPolicy();

  /// Get about us content
  Future<String> getAboutUs();

  /// Get help articles
  Future<List<Map<String, dynamic>>> getHelpArticles();
}

/// Implementation of settings remote data source
class SettingsRemoteDataSourceImpl implements SettingsRemoteDataSource {
  final ApiClient apiClient;

  SettingsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<ThemeModel> getThemePreferences() async {
    try {
      return await apiClient.get<ThemeModel>(
        endpoint: '/users/preferences/theme',
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => ThemeModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<ThemeModel> updateThemePreferences({required String themeMode}) async {
    try {
      return await apiClient.put<ThemeModel>(
        endpoint: '/users/preferences/theme',
        data: {'theme_mode': themeMode},
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => ThemeModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<LanguageModel> getLanguagePreferences() async {
    try {
      return await apiClient.get<LanguageModel>(
        endpoint: '/users/preferences/language',
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => LanguageModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<LanguageModel> updateLanguagePreferences({
    required String languageCode,
    required String countryCode,
  }) async {
    try {
      return await apiClient.put<LanguageModel>(
        endpoint: '/users/preferences/language',
        data: {'language_code': languageCode, 'country_code': countryCode},
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => LanguageModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<bool> submitFeedback(FeedbackModel feedback) async {
    try {
      await apiClient.post<Map<String, dynamic>>(
        endpoint: '/feedback',
        data: feedback.toJson(),
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => data,
      );
      return true;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<String> getTermsOfService() async {
    try {
      final response = await apiClient.get<Map<String, dynamic>>(
        endpoint: '/help/terms-and-conditions',
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => data,
      );

      final title = response['title'] as String? ?? 'Terms and Conditions';
      final lastUpdated = response['lastUpdated'] as String?;
      final content = response['content'] as Map<String, dynamic>?;

      if (content == null) {
        return 'Terms of Service content not available';
      }

      return _buildHtmlFromTermsContent(title, lastUpdated, content);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  String _buildHtmlFromTermsContent(
    String title,
    String? lastUpdated,
    Map<String, dynamic> content,
  ) {
    final buffer = StringBuffer();

    buffer.write('<h1>$title</h1>');
    if (lastUpdated != null) {
      buffer.write('<p><em>Last updated: $lastUpdated</em></p>');
    }

    // Introduction
    final introduction = content['introduction'] as String?;
    if (introduction != null) {
      buffer.write('<p>$introduction</p>');
    }

    // Service Description
    final serviceDesc = content['serviceDescription'] as Map<String, dynamic>?;
    if (serviceDesc != null) {
      final title = serviceDesc['title'] as String?;
      final content = serviceDesc['content'] as String?;
      if (title != null) buffer.write('<h2>$title</h2>');
      if (content != null) buffer.write('<p>$content</p>');
    }

    // User Responsibilities
    final userResp = content['userResponsibilities'] as Map<String, dynamic>?;
    if (userResp != null) {
      final title = userResp['title'] as String?;
      final items = userResp['items'] as List<dynamic>?;
      if (title != null) buffer.write('<h2>$title</h2>');
      if (items != null) {
        buffer.write('<ul>');
        for (final item in items) {
          buffer.write('<li>$item</li>');
        }
        buffer.write('</ul>');
      }
    }

    // Subscription Terms
    final subTerms = content['subscriptionTerms'] as Map<String, dynamic>?;
    if (subTerms != null) {
      final title = subTerms['title'] as String?;
      final items = subTerms['items'] as List<dynamic>?;
      if (title != null) buffer.write('<h2>$title</h2>');
      if (items != null) {
        buffer.write('<ul>');
        for (final item in items) {
          buffer.write('<li>$item</li>');
        }
        buffer.write('</ul>');
      }
    }

    // Limitation of Liability
    final limitation = content['limitation'] as Map<String, dynamic>?;
    if (limitation != null) {
      final title = limitation['title'] as String?;
      final content = limitation['content'] as String?;
      if (title != null) buffer.write('<h2>$title</h2>');
      if (content != null) buffer.write('<p>$content</p>');
    }

    // Termination
    final termination = content['termination'] as Map<String, dynamic>?;
    if (termination != null) {
      final title = termination['title'] as String?;
      final content = termination['content'] as String?;
      if (title != null) buffer.write('<h2>$title</h2>');
      if (content != null) buffer.write('<p>$content</p>');
    }

    // Contact
    final contact = content['contact'] as String?;
    if (contact != null) {
      buffer.write('<h2>Contact</h2>');
      buffer.write('<p>$contact</p>');
    }

    return buffer.toString();
  }

  @override
  Future<String> getPrivacyPolicy() async {
    try {
      final response = await apiClient.get<Map<String, dynamic>>(
        endpoint: '/help/privacy-policy',
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => data,
      );

      final title = response['title'] as String? ?? 'Privacy Policy';
      final lastUpdated = response['lastUpdated'] as String?;
      final content = response['content'] as Map<String, dynamic>?;

      if (content == null) {
        return 'Privacy Policy content not available';
      }

      return _buildHtmlFromLegalContent(title, lastUpdated, content);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  String _buildHtmlFromLegalContent(
    String title,
    String? lastUpdated,
    Map<String, dynamic> content,
  ) {
    final buffer = StringBuffer();

    buffer.write('<h1>$title</h1>');
    if (lastUpdated != null) {
      buffer.write('<p><em>Last updated: $lastUpdated</em></p>');
    }

    // Handle dynamic content structure
    content.forEach((key, value) {
      if (value is String) {
        if (key == 'introduction') {
          buffer.write('<p>$value</p>');
        } else if (key == 'contact') {
          buffer.write('<h2>Contact</h2>');
          buffer.write('<p>$value</p>');
        } else {
          buffer.write('<h2>${_formatTitle(key)}</h2>');
          buffer.write('<p>$value</p>');
        }
      } else if (value is Map<String, dynamic>) {
        final sectionTitle = value['title'] as String? ?? _formatTitle(key);
        buffer.write('<h2>$sectionTitle</h2>');

        final sectionContent = value['content'] as String?;
        if (sectionContent != null) {
          buffer.write('<p>$sectionContent</p>');
        }

        final items = value['items'] as List<dynamic>?;
        if (items != null) {
          buffer.write('<ul>');
          for (final item in items) {
            buffer.write('<li>$item</li>');
          }
          buffer.write('</ul>');
        }
      } else if (value is List<dynamic>) {
        buffer.write('<h2>${_formatTitle(key)}</h2>');
        buffer.write('<ul>');
        for (final item in value) {
          buffer.write('<li>$item</li>');
        }
        buffer.write('</ul>');
      }
    });

    return buffer.toString();
  }

  String _formatTitle(String key) {
    return key
        .replaceAllMapped(RegExp(r'(?=[A-Z])'), (match) => ' ')
        .split(' ')
        .where((word) => word.isNotEmpty)
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  @override
  Future<String> getAboutUs() async {
    try {
      final response = await apiClient.get<Map<String, dynamic>>(
        endpoint: '/help/about-us',
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => data,
      );

      final title = response['title'] as String? ?? 'About Us';
      final lastUpdated = response['lastUpdated'] as String?;
      final content = response['content'] as Map<String, dynamic>?;

      if (content == null) {
        return 'About Us content not available';
      }

      return _buildHtmlFromLegalContent(title, lastUpdated, content);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getHelpArticles() async {
    try {
      final response = await apiClient.get<Map<String, dynamic>>(
        endpoint: '/help/articles',
        headers: {'Content-Type': 'application/json'},
        fromData: (data) => data,
      );
      final List<dynamic> articles = response['data'];
      return articles.cast<Map<String, dynamic>>();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
