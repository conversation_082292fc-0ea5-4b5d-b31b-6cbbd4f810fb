import 'package:equatable/equatable.dart';

/// Data model for feedback submission
class FeedbackModel extends Equatable {
  final String type;
  final String title;
  final String description;
  final String? priority;
  final String? platform;
  final String? appVersion;

  const FeedbackModel({
    required this.type,
    required this.title,
    required this.description,
    this.priority,
    this.platform,
    this.appVersion,
  });

  /// Create FeedbackModel from JSON
  factory FeedbackModel.fromJson(Map<String, dynamic> json) {
    return FeedbackModel(
      type: json['type'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      priority: json['priority'] as String?,
      platform: json['platform'] as String?,
      appVersion: json['app_version'] as String?,
    );
  }

  /// Convert FeedbackModel to JSON for API request
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'type': type,
      'title': title,
      'description': description,
    };

    if (priority != null) data['priority'] = priority;
    if (platform != null) data['platform'] = platform;
    if (appVersion != null) data['appVersion'] = appVersion;

    return data;
  }

  @override
  List<Object?> get props => [
    type,
    title,
    description,
    priority,
    platform,
    appVersion,
  ];
}
