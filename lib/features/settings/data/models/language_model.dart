import '../../domain/entities/language_entity.dart';

/// Data model for language with serialization capabilities
class LanguageModel extends LanguageEntity {
  const LanguageModel({
    required super.userId,
    required super.languageCode,
    required super.countryCode,
    required super.updatedAt,
  });

  /// Create LanguageModel from JSON
  factory LanguageModel.fromJson(Map<String, dynamic> json) {
    return LanguageModel(
      userId: json['user_id'] as String,
      languageCode: json['language_code'] as String,
      countryCode: json['country_code'] as String,
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert LanguageModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'language_code': languageCode,
      'country_code': countryCode,
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create LanguageModel from domain entity
  factory LanguageModel.fromEntity(LanguageEntity entity) {
    return LanguageModel(
      userId: entity.userId,
      languageCode: entity.languageCode,
      countryCode: entity.countryCode,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to domain entity
  LanguageEntity toEntity() {
    return LanguageEntity(
      userId: userId,
      languageCode: languageCode,
      countryCode: countryCode,
      updatedAt: updatedAt,
    );
  }
}
