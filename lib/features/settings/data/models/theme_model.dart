import '../../domain/entities/theme_entity.dart';

/// Data model for theme with serialization capabilities
class ThemeModel extends ThemeEntity {
  const ThemeModel({
    required super.userId,
    required super.themeMode,
    required super.updatedAt,
  });

  /// Create ThemeModel from JSON
  factory ThemeModel.fromJson(Map<String, dynamic> json) {
    return ThemeModel(
      userId: json['user_id'] as String,
      themeMode: json['theme_mode'] as String,
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert ThemeModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'theme_mode': themeMode,
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create ThemeModel from domain entity
  factory ThemeModel.fromEntity(ThemeEntity entity) {
    return ThemeModel(
      userId: entity.userId,
      themeMode: entity.themeMode,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to domain entity
  ThemeEntity toEntity() {
    return ThemeEntity(
      userId: userId,
      themeMode: themeMode,
      updatedAt: updatedAt,
    );
  }
}
