import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/services/theme_service.dart';
import '../../domain/entities/language_entity.dart';
import '../../domain/usecases/get_language_preferences_usecase.dart';
import '../../domain/usecases/update_language_preferences_usecase.dart';
import '../../domain/usecases/submit_feedback_usecase.dart';
import '../../domain/usecases/get_terms_of_service_usecase.dart';
import '../../domain/usecases/get_privacy_policy_usecase.dart';
import '../../domain/usecases/open_help_usecase.dart';
import '../../domain/usecases/rate_app_usecase.dart';

/// Controller for handling settings operations using GetX
class SettingsController extends GetxController {
  final ThemeService _themeService;
  final GetLanguagePreferencesUseCase getLanguagePreferencesUseCase;
  final UpdateLanguagePreferencesUseCase updateLanguagePreferencesUseCase;
  final SubmitFeedbackUseCase submitFeedbackUseCase;
  final GetTermsOfServiceUseCase getTermsOfServiceUseCase;
  final GetPrivacyPolicyUseCase getPrivacyPolicyUseCase;
  final OpenHelpUseCase openHelpUseCase;
  final RateAppUseCase rateAppUseCase;

  SettingsController({
    required ThemeService themeService,
    required this.getLanguagePreferencesUseCase,
    required this.updateLanguagePreferencesUseCase,
    required this.submitFeedbackUseCase,
    required this.getTermsOfServiceUseCase,
    required this.getPrivacyPolicyUseCase,
    required this.openHelpUseCase,
    required this.rateAppUseCase,
  }) : _themeService = themeService;

  // Observable state variables
  final Rx<LanguageEntity?> _languagePreferences = Rx<LanguageEntity?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _currentLanguageCode = 'en'.obs;
  final RxString _currentCountryCode = 'US'.obs;

  // Getters for the state
  LanguageEntity? get languagePreferences => _languagePreferences.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  String get currentThemeMode => _themeService.themeModeString;
  bool get isDarkMode => _themeService.isDarkMode;
  String get currentLanguageCode => _currentLanguageCode.value;
  String get currentCountryCode => _currentCountryCode.value;

  /// Supported languages
  static const Map<String, Map<String, String>> supportedLanguages = {
    'en': {'name': 'English', 'country': 'US'},
    'ar': {'name': 'العربية', 'country': 'SA'},
  };

  /// Get display name for current language
  String get currentLanguageDisplayName {
    final language = supportedLanguages[currentLanguageCode];
    return language?['name'] ?? 'English';
  }

  /// Toggle between languages (for quick switching)
  void toggleLanguage() {
    final newLanguageCode = currentLanguageCode == 'en' ? 'ar' : 'en';
    final newCountryCode =
        supportedLanguages[newLanguageCode]?['country'] ?? 'US';
    updateLanguage(newLanguageCode, newCountryCode);
  }

  @override
  void onInit() {
    super.onInit();
    loadSettings();
  }

  /// Load all settings preferences
  Future<void> loadSettings() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      await loadLanguagePreferences();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update theme preferences (local only)
  Future<void> updateThemeMode(String themeMode) async {
    try {
      await _themeService.updateThemeMode(themeMode);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update theme',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> toggleThemeMode() async {
    try {
      await _themeService.toggleTheme();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update theme',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Load language preferences
  Future<void> loadLanguagePreferences() async {
    final result = await getLanguagePreferencesUseCase(NoParams());
    result.fold(
      (failure) {
        _errorMessage.value = _mapFailureToMessage(failure);
        // Set default language if failed to load
        _currentLanguageCode.value = 'en';
        _currentCountryCode.value = 'US';
      },
      (languageEntity) {
        _languagePreferences.value = languageEntity;
        _currentLanguageCode.value = languageEntity.languageCode;
        _currentCountryCode.value = languageEntity.countryCode;
      },
    );
  }

  /// Update language preferences and app locale
  Future<void> updateLanguage(String languageCode, String countryCode) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    // Update local state immediately for UI responsiveness
    _currentLanguageCode.value = languageCode;
    _currentCountryCode.value = countryCode;

    // Update GetX locale for the app
    final newLocale = Locale(languageCode, countryCode);
    Get.updateLocale(newLocale);

    final params = UpdateLanguageParams(
      languageCode: languageCode,
      countryCode: countryCode,
    );
    final result = await updateLanguagePreferencesUseCase(params);

    result.fold(
      (failure) {
        _errorMessage.value = _mapFailureToMessage(failure);
        Get.snackbar(
          'error'.tr,
          'error_occurred'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      },
      (languageEntity) {
        _languagePreferences.value = languageEntity;
        Get.snackbar(
          'success'.tr,
          'language_updated'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );

    _isLoading.value = false;
  }

  /// Submit feedback
  Future<void> submitFeedback({
    required String type,
    required String title,
    required String description,
    String? priority,
    String? platform,
    String? appVersion,
  }) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final params = SubmitFeedbackParams(
      type: type,
      title: title,
      description: description,
      priority: priority,
      platform: platform,
      appVersion: appVersion,
    );

    final result = await submitFeedbackUseCase(params);

    result.fold(
      (failure) {
        _errorMessage.value = _mapFailureToMessage(failure);
        Get.snackbar(
          'Error',
          'Failed to submit feedback: ${_errorMessage.value}',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
      (success) {
        Get.back(); // Close feedback screen
        Get.snackbar(
          'Success',
          'Feedback submitted successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );

    _isLoading.value = false;
  }

  /// Open help/FAQ
  Future<void> openHelp() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final result = await openHelpUseCase(NoParams());

    result.fold(
      (failure) {
        _errorMessage.value = _mapFailureToMessage(failure);
        Get.snackbar(
          'Error',
          'Failed to load help: ${_errorMessage.value}',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
      (success) {
        // Navigate to help screen
        Get.toNamed('/help');
      },
    );

    _isLoading.value = false;
  }

  /// Rate the app
  Future<void> rateApp() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final result = await rateAppUseCase(NoParams());

    result.fold(
      (failure) {
        _errorMessage.value = _mapFailureToMessage(failure);
        Get.snackbar(
          'Error',
          'Failed to open app store: ${_errorMessage.value}',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
      (success) {
        Get.snackbar(
          'Success',
          'Thank you for rating our app!',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );

    _isLoading.value = false;
  }

  /// Get terms of service
  Future<String?> getTermsOfService() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final result = await getTermsOfServiceUseCase(NoParams());

    _isLoading.value = false;

    return result.fold((failure) {
      _errorMessage.value = _mapFailureToMessage(failure);
      Get.snackbar(
        'Error',
        'Failed to load terms of service: ${_errorMessage.value}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }, (content) => content);
  }

  /// Get privacy policy
  Future<String?> getPrivacyPolicy() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    final result = await getPrivacyPolicyUseCase(NoParams());

    _isLoading.value = false;

    return result.fold((failure) {
      _errorMessage.value = _mapFailureToMessage(failure);
      Get.snackbar(
        'Error',
        'Failed to load privacy policy: ${_errorMessage.value}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }, (content) => content);
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'Server error occurred. Please try again later.';
      case NetworkFailure:
        return 'No internet connection. Please check your network.';
      case AuthFailure:
        return 'Authentication failed. Please login again.';
      default:
        return 'An unexpected error occurred.';
    }
  }
}
