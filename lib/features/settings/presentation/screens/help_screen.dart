import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../help/presentation/controllers/help_controller.dart';
import '../../../../core/routes/app_routes.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final helpController = Get.find<HelpController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              onChanged: helpController.searchArticles,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 16,
              ),
              decoration: InputDecoration(
                hintText: 'Search help articles...',
                hintStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Theme.of(
                  context,
                ).colorScheme.surfaceVariant.withOpacity(0.3),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 14,
                ),
              ),
            ),
          ),

          // Content
          Expanded(
            child: Obx(() {
              if (helpController.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (helpController.error.isNotEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading help articles',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        helpController.error,
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: helpController.loadFAQs,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              }

              // Show search results if available
              if (helpController.searchResults.isNotEmpty) {
                return _buildSearchResults(context, helpController);
              }

              // Show FAQ categories
              return _buildFAQCategories(context, helpController);
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(BuildContext context, HelpController controller) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Search Results (${controller.searchResults.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: controller.clearSearch,
                      child: const Text('Clear'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ...controller.searchResults.map(
                  (article) => _buildArticleCard(context, article),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildFAQCategories(BuildContext context, HelpController controller) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Frequently Asked Questions',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                if (controller.gettingStartedArticles.isNotEmpty)
                  _buildFAQCategory(
                    context,
                    icon: Icons.rocket_launch,
                    title: 'Getting Started',
                    description: 'Learn how to use Power Up effectively',
                    articles: controller.gettingStartedArticles,
                  ),

                if (controller.accountArticles.isNotEmpty)
                  _buildFAQCategory(
                    context,
                    icon: Icons.person,
                    title: 'Account & Profile',
                    description: 'Manage your account settings',
                    articles: controller.accountArticles,
                  ),

                if (controller.featuresArticles.isNotEmpty)
                  _buildFAQCategory(
                    context,
                    icon: Icons.trending_up,
                    title: 'Features & Tools',
                    description: 'Make the most of our features',
                    articles: controller.featuresArticles,
                  ),

                if (controller.technicalArticles.isNotEmpty)
                  _buildFAQCategory(
                    context,
                    icon: Icons.settings,
                    title: 'Technical Support',
                    description: 'Troubleshoot common issues',
                    articles: controller.technicalArticles,
                  ),

                if (controller.billingArticles.isNotEmpty)
                  _buildFAQCategory(
                    context,
                    icon: Icons.payment,
                    title: 'Billing & Subscriptions',
                    description: 'Questions about payments',
                    articles: controller.billingArticles,
                  ),

                // If no articles in specific categories, show all
                if (controller.gettingStartedArticles.isEmpty &&
                    controller.accountArticles.isEmpty &&
                    controller.featuresArticles.isEmpty &&
                    controller.technicalArticles.isEmpty &&
                    controller.billingArticles.isEmpty &&
                    controller.faqArticles.isNotEmpty)
                  _buildFAQCategory(
                    context,
                    icon: Icons.help_outline,
                    title: 'Help Articles',
                    description: 'All available help articles',
                    articles: controller.faqArticles,
                  ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Additional Resources
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Additional Resources',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                _buildResourceCard(
                  icon: Icons.feedback_outlined,
                  title: 'Send Feedback',
                  subtitle: 'Tell us what you think',
                  onTap: () => Get.toNamed('/feedback'),
                ),

                _buildResourceCard(
                  icon: Icons.article,
                  title: 'User Guide',
                  subtitle: 'Comprehensive documentation',
                  onTap: () {
                    // TODO: Navigate to user guide
                    Get.snackbar(
                      'Coming Soon',
                      'User guide will be available soon',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  },
                ),

                _buildResourceCard(
                  icon: Icons.forum,
                  title: 'Community Forum',
                  subtitle: 'Connect with other users',
                  onTap: () {
                    // TODO: Navigate to community
                    Get.snackbar(
                      'Coming Soon',
                      'Community forum will be available soon',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildFAQCategory(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required List<dynamic> articles,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: EdgeInsets.zero,
        expandedCrossAxisAlignment: CrossAxisAlignment.start,
        maintainState: true,
        shape: const Border(),
        collapsedShape: const Border(),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.green.shade600, size: 20),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(description),
        children:
            articles.map<Widget>((article) {
              return _buildArticleListItem(context, article);
            }).toList(),
      ),
    );
  }

  Widget _buildArticleListItem(BuildContext context, dynamic article) {
    return ListTile(
      contentPadding: const EdgeInsets.only(left: 72, right: 16),
      title: Text(
        article.title,
        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      ),
      subtitle: Text(
        article.description,
        style: const TextStyle(fontSize: 12),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 14),
      onTap: () {
        _showArticleDialog(context, article);
      },
    );
  }

  Widget _buildArticleCard(BuildContext context, dynamic article) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(
          article.title,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          article.description,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          _showArticleDialog(context, article);
        },
      ),
    );
  }

  Widget _buildResourceCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.orange.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.orange.shade600, size: 20),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.open_in_new, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showArticleDialog(BuildContext context, dynamic article) {
    Get.toNamed(AppRoutes.helpArticleDetail, arguments: article);
  }
}
