import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_html/flutter_html.dart';
import '../controllers/settings_controller.dart';

class LegalDocumentScreen extends StatefulWidget {
  const LegalDocumentScreen({super.key});

  @override
  State<LegalDocumentScreen> createState() => _LegalDocumentScreenState();
}

class _LegalDocumentScreenState extends State<LegalDocumentScreen> {
  String? content;
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadDocument();
  }

  Future<void> _loadDocument() async {
    setState(() {
      isLoading = true;
      error = null;
    });

    final settingsController = Get.find<SettingsController>();
    final documentType = Get.arguments as String? ?? 'terms';

    try {
      String? documentContent;
      if (documentType == 'terms') {
        documentContent = await settingsController.getTermsOfService();
      } else if (documentType == 'privacy') {
        documentContent = await settingsController.getPrivacyPolicy();
      }

      setState(() {
        content = documentContent;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = 'Failed to load document';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Simple back button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.arrow_back),
                    style: IconButton.styleFrom(
                      backgroundColor:
                          Theme.of(context).colorScheme.primaryContainer,
                      foregroundColor:
                          Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),

            // Document content
            Expanded(child: _buildContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text(
              error!,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDocument,
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (content == null || content!.isEmpty) {
      return const Center(
        child: Text(
          'No content available',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Html(
        data: content!,
        style: {
          "html": Style(
            fontSize: FontSize(14),
            lineHeight: LineHeight(1.6),
            color: Theme.of(context).colorScheme.onSurface,
          ),
          "h1": Style(
            fontSize: FontSize(28),
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
            margin: Margins.only(bottom: 24),
          ),
          "h2": Style(
            fontSize: FontSize(20),
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
            margin: Margins.only(top: 32, bottom: 16),
          ),
          "h3": Style(
            fontSize: FontSize(18),
            fontWeight: FontWeight.w500,
            color: Theme.of(context).colorScheme.onSurface,
            margin: Margins.only(top: 24, bottom: 12),
          ),
          "p": Style(
            fontSize: FontSize(16),
            lineHeight: LineHeight(1.7),
            margin: Margins.only(bottom: 16),
            color: Theme.of(context).colorScheme.onSurface,
          ),
          "em": Style(
            fontStyle: FontStyle.italic,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
            fontSize: FontSize(14),
          ),
          "ul": Style(margin: Margins.only(bottom: 24, left: 16)),
          "li": Style(
            fontSize: FontSize(16),
            lineHeight: LineHeight(1.6),
            margin: Margins.only(bottom: 8),
            color: Theme.of(context).colorScheme.onSurface,
          ),
          "strong": Style(fontWeight: FontWeight.w600),
          "a": Style(
            color: Theme.of(context).colorScheme.primary,
            textDecoration: TextDecoration.underline,
          ),
        },
      ),
    );
  }
}
