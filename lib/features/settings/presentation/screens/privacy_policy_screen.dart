import 'package:flutter/material.dart';

/// A clear and concise Privacy Policy screen for the app.
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(title: const Text('Privacy Policy'), centerTitle: true),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Privacy Matters',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'We are committed to protecting your personal data and being transparent about how we use it. This Privacy Policy explains what information we collect, how it is used, and your choices regarding your data.',
                  style: theme.textTheme.bodyLarge,
                ),
                const SizedBox(height: 24),
                Text(
                  '1. Data Collection',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'We collect only the data necessary to provide and improve the app experience, such as account information, usage analytics, and preferences. Sensitive data is never sold or shared with third parties.',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                Text(
                  '2. Data Usage',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Your data is used to personalize features, provide AI-powered suggestions, and improve app performance. You can opt out of personalized features and analytics sharing in Privacy Settings.',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                Text(
                  '3. Data Security',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'All data is transmitted securely over HTTPS. Authentication tokens are securely stored and never exposed. We do not log or display sensitive information in the app.',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                Text(
                  '4. Your Choices',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You have full control over your data sharing preferences. Visit Privacy Settings to manage your options or contact support for data deletion requests.',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                Text(
                  '5. Your Rights & Consent',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You have the right to access, correct, or delete your personal data at any time. You may also withdraw your consent for data processing (such as analytics or personalized AI features) in the app’s Privacy Settings. Requests for data access or deletion can be made directly in the app or by contacting support.',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 24),
                Text(
                  'For more details about your rights under GDPR, CCPA, and other privacy laws, please review our full privacy policy on our website or contact our support team.',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
