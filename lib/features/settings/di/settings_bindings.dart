import 'package:get/get.dart';
import '../../core/data/api/api_client.dart';
import '../../../core/services/theme_service.dart';
import '../data/datasources/settings_remote_data_source.dart';
import '../data/repositories/settings_repository_impl.dart';
import '../domain/repositories/settings_repository.dart';
import '../domain/usecases/get_language_preferences_usecase.dart';
import '../domain/usecases/update_language_preferences_usecase.dart';
import '../domain/usecases/submit_feedback_usecase.dart';
import '../domain/usecases/get_terms_of_service_usecase.dart';
import '../domain/usecases/get_privacy_policy_usecase.dart';
import '../domain/usecases/open_help_usecase.dart';
import '../domain/usecases/rate_app_usecase.dart';
import '../presentation/controllers/settings_controller.dart';
import '../../app_constants/presentation/controllers/app_constants_controller.dart';

/// Dependency injection binding for settings module
class SettingsBindings extends Bindings {
  @override
  void dependencies() {
    // Data sources
    Get.lazyPut<SettingsRemoteDataSource>(
      () => SettingsRemoteDataSourceImpl(apiClient: Get.find<ApiClient>()),
    );

    // Repositories
    Get.lazyPut<SettingsRepository>(
      () => SettingsRepositoryImpl(
        remoteDataSource: Get.find<SettingsRemoteDataSource>(),
      ),
    );

    // Use cases
    Get.lazyPut(
      () => GetLanguagePreferencesUseCase(Get.find<SettingsRepository>()),
    );
    Get.lazyPut(
      () => UpdateLanguagePreferencesUseCase(Get.find<SettingsRepository>()),
    );
    Get.lazyPut(() => SubmitFeedbackUseCase(Get.find<SettingsRepository>()));
    Get.lazyPut(() => GetTermsOfServiceUseCase(Get.find<SettingsRepository>()));
    Get.lazyPut(() => GetPrivacyPolicyUseCase(Get.find<SettingsRepository>()));
    Get.lazyPut(() => OpenHelpUseCase(Get.find<SettingsRepository>()));
    Get.lazyPut(() => RateAppUseCase(Get.find<SettingsRepository>()));

    // Controllers
    Get.lazyPut(
      () => SettingsController(
        themeService: Get.find<ThemeService>(),
        getLanguagePreferencesUseCase:
            Get.find<GetLanguagePreferencesUseCase>(),
        updateLanguagePreferencesUseCase:
            Get.find<UpdateLanguagePreferencesUseCase>(),
        submitFeedbackUseCase: Get.find<SubmitFeedbackUseCase>(),
        getTermsOfServiceUseCase: Get.find<GetTermsOfServiceUseCase>(),
        getPrivacyPolicyUseCase: Get.find<GetPrivacyPolicyUseCase>(),
        openHelpUseCase: Get.find<OpenHelpUseCase>(),
        rateAppUseCase: Get.find<RateAppUseCase>(),
      ),
    );

    // App Constants Controller (ensure it's available in settings)
    Get.lazyPut(() => AppConstantsController(appConstantsService: Get.find()));
  }
}
