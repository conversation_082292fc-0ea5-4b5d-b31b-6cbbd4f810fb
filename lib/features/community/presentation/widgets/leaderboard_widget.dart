import 'package:flutter/material.dart';
import '../../domain/entities/leaderboard_entry_entity.dart';

class LeaderboardWidget extends StatelessWidget {
  final List<LeaderboardEntryEntity> entries;
  final bool isLoading;

  const LeaderboardWidget({
    super.key,
    required this.entries,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (entries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.leaderboard_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No leaderboard data yet',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: entries.length,
      itemBuilder: (context, index) {
        final entry = entries[index];
        return _buildLeaderboardItem(context, entry);
      },
    );
  }

  Widget _buildLeaderboardItem(
    BuildContext context,
    LeaderboardEntryEntity entry,
  ) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Rank badge
            _buildRankBadge(context, entry.rank),
            const SizedBox(width: 12),

            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundImage:
                  entry.avatarUrl != null
                      ? NetworkImage(entry.avatarUrl!)
                      : null,
              child:
                  entry.avatarUrl == null
                      ? Text(
                        entry.userName.isNotEmpty
                            ? entry.userName[0].toUpperCase()
                            : '?',
                        style: theme.textTheme.titleMedium,
                      )
                      : null,
            ),
            const SizedBox(width: 12),

            // User info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry.userName,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (entry.additionalData != null &&
                      entry.additionalData!.containsKey('level'))
                    Text(
                      'Level ${entry.additionalData!['level']}',
                      style: theme.textTheme.bodySmall,
                    ),
                ],
              ),
            ),

            // Score
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  entry.score.toStringAsFixed(0),
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getRankColor(context, entry.rank),
                  ),
                ),
                Text('points', style: theme.textTheme.bodySmall),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRankBadge(BuildContext context, int rank) {
    final theme = Theme.of(context);
    Color backgroundColor;
    Color textColor;
    Widget? icon;

    switch (rank) {
      case 1:
        backgroundColor = const Color(0xFFFFD700); // Gold
        textColor = Colors.black;
        icon = const Icon(Icons.emoji_events, color: Colors.black, size: 16);
        break;
      case 2:
        backgroundColor = const Color(0xFFC0C0C0); // Silver
        textColor = Colors.black;
        icon = const Icon(Icons.emoji_events, color: Colors.black, size: 16);
        break;
      case 3:
        backgroundColor = const Color(0xFFCD7F32); // Bronze
        textColor = Colors.white;
        icon = const Icon(Icons.emoji_events, color: Colors.white, size: 16);
        break;
      default:
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurfaceVariant;
    }

    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(color: backgroundColor, shape: BoxShape.circle),
      child: Center(
        child:
            rank <= 3
                ? icon
                : Text(
                  rank.toString(),
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
      ),
    );
  }

  Color _getRankColor(BuildContext context, int rank) {
    final theme = Theme.of(context);
    switch (rank) {
      case 1:
        return const Color(0xFFFFD700); // Gold
      case 2:
        return const Color(0xFFC0C0C0); // Silver
      case 3:
        return const Color(0xFFCD7F32); // Bronze
      default:
        return theme.colorScheme.primary;
    }
  }
}
