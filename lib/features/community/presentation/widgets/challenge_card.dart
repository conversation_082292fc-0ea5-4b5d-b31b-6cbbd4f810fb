import 'package:flutter/material.dart';
import '../../domain/entities/challenge_entity.dart';

class ChallengeCard extends StatelessWidget {
  final ChallengeEntity challenge;
  final VoidCallback? onTap;
  final VoidCallback? onJoin;
  final VoidCallback? onLeave;

  const ChallengeCard({
    super.key,
    required this.challenge,
    this.onTap,
    this.onJoin,
    this.onLeave,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      challenge.name,
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),
              const SizedBox(height: 8),

              // Description
              Text(
                challenge.description,
                style: theme.textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Progress bar (if user has joined)
              if (challenge.joined && challenge.userProgress != null)
                _buildProgressBar(context),

              // Challenge info row
              Row(
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${challenge.participantCount} participants',
                    style: theme.textTheme.bodySmall,
                  ),
                  const Spacer(),
                  _buildDateInfo(context),
                ],
              ),
              const SizedBox(height: 12),

              // Action button
              _buildActionButton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final theme = Theme.of(context);
    String status;
    Color backgroundColor;
    Color textColor;

    if (challenge.isPending) {
      status = 'Upcoming';
      backgroundColor = theme.colorScheme.secondary.withValues(alpha: 0.1);
      textColor = theme.colorScheme.secondary;
    } else if (challenge.isActive) {
      status = 'Active';
      backgroundColor = theme.colorScheme.primary.withValues(alpha: 0.1);
      textColor = theme.colorScheme.primary;
    } else {
      status = 'Ended';
      backgroundColor = theme.colorScheme.surfaceContainerHighest;
      textColor = theme.colorScheme.onSurfaceVariant;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: theme.textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildProgressBar(BuildContext context) {
    final theme = Theme.of(context);
    final progress = challenge.userProgress ?? 0.0;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Your Progress', style: theme.textTheme.bodySmall),
            Text(
              '${(progress * 100).toInt()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.colorScheme.surfaceContainerHighest,
          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildDateInfo(BuildContext context) {
    final theme = Theme.of(context);
    final now = DateTime.now();

    String dateText;
    if (challenge.isPending) {
      final daysUntilStart = challenge.startDate.difference(now).inDays;
      dateText = 'Starts in $daysUntilStart days';
    } else if (challenge.isActive) {
      final daysRemaining = challenge.endDate.difference(now).inDays;
      dateText = '$daysRemaining days left';
    } else {
      dateText = 'Ended';
    }

    return Text(
      dateText,
      style: theme.textTheme.bodySmall?.copyWith(
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    if (challenge.hasEnded) {
      return const SizedBox(
        width: double.infinity,
        child: OutlinedButton(onPressed: null, child: Text('Challenge Ended')),
      );
    }

    if (challenge.joined) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: onTap, // Navigate to challenge detail instead of leaving
          child: const Text('View Challenge'),
        ),
      );
    } else {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: challenge.isPending ? null : onJoin,
          child: Text(
            challenge.isPending ? 'Not Started Yet' : 'Join Challenge',
          ),
        ),
      );
    }
  }
}
