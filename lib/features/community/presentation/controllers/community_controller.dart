import 'package:get/get.dart';
import '../../../../core/presentation/controllers/base_controller.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/challenge_entity.dart';
import '../../domain/entities/leaderboard_entry_entity.dart';
import '../../domain/entities/chat_message_entity.dart';
import '../../domain/usecases/get_challenges_usecase.dart';
import '../../domain/usecases/get_challenge_by_id_usecase.dart';
import '../../domain/usecases/create_challenge_usecase.dart';
import '../../domain/usecases/update_challenge_usecase.dart';
import '../../domain/usecases/delete_challenge_usecase.dart';
import '../../domain/usecases/join_challenge_usecase.dart';
import '../../domain/usecases/leave_challenge_usecase.dart';
import '../../domain/usecases/update_challenge_progress_usecase.dart';
import '../../domain/usecases/get_leaderboard_usecase.dart';
import '../../domain/usecases/send_direct_message_usecase.dart';
import '../../domain/usecases/send_group_message_usecase.dart';
import '../../domain/usecases/send_challenge_message_usecase.dart';
import '../../domain/usecases/get_user_conversations_usecase.dart';
import '../../domain/usecases/get_direct_messages_usecase.dart';
import '../../domain/usecases/get_group_messages_usecase.dart';
import '../../domain/usecases/get_challenge_messages_usecase.dart';
import '../../domain/usecases/mark_message_as_read_usecase.dart';

class CommunityController extends BaseController {
  final GetChallengesUseCase getChallengesUseCase;
  final GetChallengeByIdUseCase getChallengeByIdUseCase;
  final CreateChallengeUseCase createChallengeUseCase;
  final UpdateChallengeUseCase updateChallengeUseCase;
  final DeleteChallengeUseCase deleteChallengeUseCase;
  final JoinChallengeUseCase joinChallengeUseCase;
  final LeaveChallengeUseCase leaveChallengeUseCase;
  final UpdateChallengeProgressUseCase updateChallengeProgressUseCase;
  final GetLeaderboardUseCase getLeaderboardUseCase;
  final SendDirectMessageUseCase sendDirectMessageUseCase;
  final SendGroupMessageUseCase sendGroupMessageUseCase;
  final SendChallengeMessageUseCase sendChallengeMessageUseCase;
  final GetUserConversationsUseCase getUserConversationsUseCase;
  final GetDirectMessagesUseCase getDirectMessagesUseCase;
  final GetGroupMessagesUseCase getGroupMessagesUseCase;
  final GetChallengeMessagesUseCase getChallengeMessagesUseCase;
  final MarkMessageAsReadUseCase markMessageAsReadUseCase;

  CommunityController({
    required this.getChallengesUseCase,
    required this.getChallengeByIdUseCase,
    required this.createChallengeUseCase,
    required this.updateChallengeUseCase,
    required this.deleteChallengeUseCase,
    required this.joinChallengeUseCase,
    required this.leaveChallengeUseCase,
    required this.updateChallengeProgressUseCase,
    required this.getLeaderboardUseCase,
    required this.sendDirectMessageUseCase,
    required this.sendGroupMessageUseCase,
    required this.sendChallengeMessageUseCase,
    required this.getUserConversationsUseCase,
    required this.getDirectMessagesUseCase,
    required this.getGroupMessagesUseCase,
    required this.getChallengeMessagesUseCase,
    required this.markMessageAsReadUseCase,
  });

  // Observable state
  final RxList<ChallengeEntity> challenges = <ChallengeEntity>[].obs;
  final Rx<ChallengeEntity?> selectedChallenge = Rx<ChallengeEntity?>(null);
  final RxList<LeaderboardEntryEntity> leaderboard =
      <LeaderboardEntryEntity>[].obs;
  final RxList<ChatMessageEntity> conversations = <ChatMessageEntity>[].obs;
  final RxList<ChatMessageEntity> messages = <ChatMessageEntity>[].obs;
  final RxString currentFilter = 'all'.obs;

  @override
  void onInit() {
    super.onInit();
    // Only load challenges if not already loaded
    if (challenges.isEmpty) {
      loadChallenges();
    }
  }

  /// Load challenges with optional filter
  Future<void> loadChallenges({String? filter}) async {
    setLoading(true);
    final result = await getChallengesUseCase(
      GetChallengesParams(filter: filter ?? currentFilter.value),
    );
    result.fold((failure) => handleError(failure), (challengeList) {
      challenges.value = challengeList;
      if (filter != null) currentFilter.value = filter;
    });
    setLoading(false);
  }

  /// Load a specific challenge by ID
  Future<void> loadChallengeById(String id) async {
    setLoading(true);
    final result = await getChallengeByIdUseCase(
      GetChallengeByIdParams(challengeId: id),
    );
    result.fold(
      (failure) => handleError(failure),
      (challenge) => selectedChallenge.value = challenge,
    );
    setLoading(false);
  }

  /// Create a new challenge
  Future<void> createChallenge({
    required String name,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> rules,
  }) async {
    setLoading(true);
    final result = await createChallengeUseCase(
      CreateChallengeParams(
        name: name,
        description: description,
        startDate: startDate,
        endDate: endDate,
        rules: rules,
      ),
    );
    result.fold((failure) => handleError(failure), (challenge) {
      challenges.add(challenge);
      showSnackbar('Success', 'Challenge created successfully');
    });
    setLoading(false);
  }

  /// Update an existing challenge
  Future<void> updateChallenge({
    required String id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? rules,
  }) async {
    setLoading(true);
    final result = await updateChallengeUseCase(
      UpdateChallengeParams(
        id: id,
        name: name,
        description: description,
        startDate: startDate,
        endDate: endDate,
        rules: rules,
      ),
    );
    result.fold((failure) => handleError(failure), (updatedChallenge) {
      // Update the challenge in the list by replacing the entire list value
      challenges.value =
          challenges.map((challenge) {
            if (challenge.id == id) {
              return updatedChallenge;
            }
            return challenge;
          }).toList();

      // Update selected challenge if it's the same one
      if (selectedChallenge.value?.id == id) {
        selectedChallenge.value = updatedChallenge;
      }
      Get.back();
      showSnackbar('Success', 'Challenge updated successfully');
    });
    setLoading(false);
  }

  /// Delete a challenge
  Future<void> deleteChallenge(String id) async {
    setLoading(true);
    final result = await deleteChallengeUseCase(DeleteChallengeParams(id: id));
    result.fold((failure) => handleError(failure), (_) {
      challenges.removeWhere((c) => c.id == id);
      if (selectedChallenge.value?.id == id) {
        selectedChallenge.value = null;
      }
      showSnackbar('Success', 'Challenge deleted successfully');
    });
    setLoading(false);
  }

  /// Join a challenge
  Future<void> joinChallenge(String challengeId) async {
    setLoading(true);
    final result = await joinChallengeUseCase(
      JoinChallengeParams(challengeId: challengeId),
    );
    result.fold((failure) => handleError(failure), (joined) {
      if (joined) {
        // Update the challenge in the list by replacing the entire list value
        challenges.value =
            challenges.map((challenge) {
              if (challenge.id == challengeId) {
                return challenge.copyWith(joined: true);
              }
              return challenge;
            }).toList();

        // Update selected challenge if it's the same one
        if (selectedChallenge.value?.id == challengeId) {
          selectedChallenge.value = selectedChallenge.value?.copyWith(
            joined: true,
          );
        }
        showSnackbar('Success', 'Successfully joined the challenge');
      }
    });
    setLoading(false);
  }

  /// Leave a challenge
  Future<void> leaveChallenge(String challengeId) async {
    setLoading(true);
    final result = await leaveChallengeUseCase(
      LeaveChallengeParams(challengeId: challengeId),
    );
    result.fold((failure) => handleError(failure), (_) {
      // Update the challenge in the list by replacing the entire list value
      challenges.value =
          challenges.map((challenge) {
            if (challenge.id == challengeId) {
              return challenge.copyWith(joined: false);
            }
            return challenge;
          }).toList();

      // Update selected challenge if it's the same one
      if (selectedChallenge.value?.id == challengeId) {
        selectedChallenge.value = selectedChallenge.value?.copyWith(
          joined: false,
        );
      }
      showSnackbar('Success', 'Left the challenge successfully');
    });
    setLoading(false);
  }

  /// Update challenge progress
  Future<void> updateChallengeProgress(
    String challengeId,
    double progress,
  ) async {
    final result = await updateChallengeProgressUseCase(
      UpdateChallengeProgressParams(
        challengeId: challengeId,
        progress: progress,
      ),
    );
    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', 'Failed to update progress. Please try again.');
      },
      (updatedProgress) {
        // Update the challenge in the list by replacing the entire list value
        challenges.value =
            challenges.map((challenge) {
              if (challenge.id == challengeId) {
                return challenge.copyWith(userProgress: updatedProgress);
              }
              return challenge;
            }).toList();

        // Update selected challenge if it's the same one
        if (selectedChallenge.value?.id == challengeId) {
          selectedChallenge.value = selectedChallenge.value?.copyWith(
            userProgress: updatedProgress,
          );
        }
      },
    );
  }

  /// Load leaderboard for a challenge
  Future<void> loadLeaderboard(String challengeId) async {
    setLoading(true);
    final result = await getLeaderboardUseCase(
      GetLeaderboardParams(challengeId: challengeId),
    );
    result.fold(
      (failure) => handleError(failure),
      (leaderboardList) => leaderboard.value = leaderboardList,
    );
    setLoading(false);
  }

  /// Send a direct message
  Future<void> sendDirectMessage({
    required String recipientId,
    required String content,
    List<String>? attachments,
  }) async {
    final result = await sendDirectMessageUseCase(
      SendDirectMessageParams(
        recipientId: recipientId,
        content: content,
        attachments: attachments,
      ),
    );
    result.fold(
      (failure) => handleError(failure),
      (_) => showSnackbar('Success', 'Message sent successfully'),
    );
  }

  /// Send a group message
  Future<void> sendGroupMessage({
    required String groupId,
    required String content,
    List<String>? attachments,
  }) async {
    final result = await sendGroupMessageUseCase(
      SendGroupMessageParams(
        groupId: groupId,
        content: content,
        attachments: attachments,
      ),
    );
    result.fold(
      (failure) => handleError(failure),
      (_) => showSnackbar('Success', 'Message sent successfully'),
    );
  }

  /// Send a challenge message
  Future<void> sendChallengeMessage({
    required String challengeId,
    required String content,
    List<String>? attachments,
  }) async {
    final result = await sendChallengeMessageUseCase(
      SendChallengeMessageParams(
        challengeId: challengeId,
        content: content,
        attachments: attachments,
      ),
    );
    result.fold(
      (failure) => handleError(failure),
      (_) => showSnackbar('Success', 'Message sent successfully'),
    );
  }

  /// Load user conversations
  Future<void> loadUserConversations() async {
    setLoading(true);
    final result = await getUserConversationsUseCase(NoParams());
    result.fold(
      (failure) => handleError(failure),
      (conversationList) => conversations.value = conversationList,
    );
    setLoading(false);
  }

  /// Load direct messages
  Future<void> loadDirectMessages(String recipientId) async {
    setLoading(true);
    final result = await getDirectMessagesUseCase(
      GetDirectMessagesParams(recipientId: recipientId),
    );
    result.fold(
      (failure) => handleError(failure),
      (messageList) => messages.value = messageList,
    );
    setLoading(false);
  }

  /// Load group messages
  Future<void> loadGroupMessages(String groupId) async {
    setLoading(true);
    final result = await getGroupMessagesUseCase(
      GetGroupMessagesParams(groupId: groupId),
    );
    result.fold(
      (failure) => handleError(failure),
      (messageList) => messages.value = messageList,
    );
    setLoading(false);
  }

  /// Load challenge messages
  Future<void> loadChallengeMessages(String challengeId) async {
    setLoading(true);
    final result = await getChallengeMessagesUseCase(
      GetChallengeMessagesParams(challengeId: challengeId),
    );
    result.fold(
      (failure) => handleError(failure),
      (messageList) => messages.value = messageList,
    );
    setLoading(false);
  }

  /// Mark message as read
  Future<void> markMessageAsRead(String messageId) async {
    final result = await markMessageAsReadUseCase(
      MarkMessageAsReadParams(messageId: messageId),
    );
    result.fold((failure) => handleError(failure), (_) {
      // Update the message in the list
      final index = messages.indexWhere((m) => m.id == messageId);
      if (index != -1) {
        messages[index] = messages[index].copyWith(isRead: true);
      }
    });
  }

  /// Filter challenges
  void filterChallenges(String filter) {
    loadChallenges(filter: filter);
  }

  /// Refresh challenges
  Future<void> refreshChallenges() async {
    await loadChallenges();
  }

  /// Clear selected challenge
  void clearSelectedChallenge() {
    selectedChallenge.value = null;
  }
}
