import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../controllers/community_controller.dart';
import '../widgets/leaderboard_widget.dart';
import '../../../../core/services/auth_service.dart';

class ChallengeDetailPage extends StatefulWidget {
  const ChallengeDetailPage({super.key});

  @override
  State<ChallengeDetailPage> createState() => _ChallengeDetailPageState();
}

class _ChallengeDetailPageState extends State<ChallengeDetailPage>
    with TickerProviderStateMixin {
  CommunityController get controller => Get.find<CommunityController>();
  String get challengeId => Get.parameters['id'] ?? '';
  bool _initialized = false;
  late TabController _tabController;

  // Local progress state for smooth slider interaction
  final RxDouble _localProgress = 0.0.obs;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _initializeData() {
    if (!_initialized && challengeId.isNotEmpty) {
      _initialized = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.loadChallengeById(challengeId);
        controller.loadLeaderboard(challengeId);
        controller.loadChallengeMessages(challengeId);
      });
    }
  }

  void _updateProgress(double value) {
    // Update local state only - no database calls
    _localProgress.value = value;

    // Cancel any pending timer since we're not saving to database
    _debounceTimer?.cancel();

    controller.updateChallengeProgress(challengeId, value);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        final challenge = controller.selectedChallenge.value;

        if (controller.isLoading && challenge == null) {
          return const Center(child: CircularProgressIndicator());
        }

        if (challenge == null) {
          return const Center(child: Text('Challenge not found'));
        }

        return Stack(
          children: [
            Column(
              children: [
                _buildModernHeader(context, challenge),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildOverviewTab(context, challenge),
                      _buildLeaderboardTab(context),
                      _buildChatTab(context),
                    ],
                  ),
                ),
              ],
            ),
            // Floating Tab Bar
            Positioned(
              top: MediaQuery.of(context).padding.top + 240,
              left: 16,
              right: 16,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                child: _buildFloatingTabBar(context),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildModernMenuButton(BuildContext context) {
    final theme = Theme.of(context);
    final challenge = controller.selectedChallenge.value;

    // Get current user ID from AuthService
    final authService = Get.find<AuthService>();
    final currentUserId = authService.currentUserId;

    // Check if current user is the creator of the challenge
    final isCreator = challenge?.creatorId == currentUserId;

    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            _showEditDialog(context, challenge);
            break;
          case 'delete':
            if (challenge != null) {
              _showDeleteConfirmation(context, challenge.id);
            }
            break;
          case 'share':
            // Implement share functionality
            break;
        }
      },
      icon: Icon(Icons.more_vert, color: theme.colorScheme.onPrimary),
      style: IconButton.styleFrom(
        backgroundColor: theme.colorScheme.onPrimary.withValues(alpha: 0.1),
        padding: const EdgeInsets.all(8),
        minimumSize: const Size(40, 40),
      ),
      itemBuilder: (context) {
        List<PopupMenuItem<String>> items = [];

        // Only show edit and delete options to the challenge creator
        if (isCreator) {
          items.addAll([
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit_outlined),
                title: Text('Edit'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete_outline),
                title: Text('Delete'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ]);
        }

        // Share option is available to everyone
        items.add(
          const PopupMenuItem(
            value: 'share',
            child: ListTile(
              leading: Icon(Icons.share_outlined),
              title: Text('Share'),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        );

        return items;
      },
    );
  }

  Widget _buildModernHeader(BuildContext context, challenge) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(alpha: 0.8),
            theme.colorScheme.secondary.withValues(alpha: 0.6),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // App Bar Section
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.arrow_back_ios_new,
                        color: theme.colorScheme.onPrimary,
                      ),
                      style: IconButton.styleFrom(
                        padding: const EdgeInsets.all(8),
                        minimumSize: const Size(40, 40),
                      ),
                    ),
                  ),
                  const Spacer(),
                  _buildModernMenuButton(context),
                ],
              ),
            ),

            // Hero Content Section
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.onPrimary.withValues(
                            alpha: 0.2,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          challenge.isActive
                              ? 'Active'
                              : challenge.hasEnded
                              ? 'Ended'
                              : 'Upcoming',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    challenge.name,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    challenge.description,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.9),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      _buildStatChip(
                        context,
                        Icons.people_outline,
                        '${challenge.participantCount}',
                        'participants',
                      ),
                      const SizedBox(width: 12),
                      _buildStatChip(
                        context,
                        Icons.calendar_today_outlined,
                        _getDaysRemaining(challenge),
                        'days left',
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip(
    BuildContext context,
    IconData icon,
    String value,
    String label,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.onPrimary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: theme.colorScheme.onPrimary),
          const SizedBox(width: 6),
          Text(
            '$value $label',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _getDaysRemaining(challenge) {
    final now = DateTime.now();
    final endDate = challenge.endDate;
    final difference = endDate.difference(now).inDays;

    if (difference < 0) return '0';
    return difference.toString();
  }

  Widget _buildOverviewTab(BuildContext context, challenge) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(
        16,
        80,
        16,
        16,
      ), // Adjusted top padding for floating tab bar
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress card (if joined)
          if (challenge.joined) ...[
            _buildProgressCard(context, challenge),
            const SizedBox(height: 16),
          ],

          // Challenge details
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Challenge Details',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildDetailRow(
                  Icons.calendar_today_outlined,
                  'Start Date',
                  challenge.startDate.toString().split(' ')[0],
                ),
                _buildDetailRow(
                  Icons.event_outlined,
                  'End Date',
                  challenge.endDate.toString().split(' ')[0],
                ),
                _buildDetailRow(
                  Icons.people_outline,
                  'Participants',
                  '${challenge.participantCount}',
                ),
                _buildDetailRow(
                  Icons.person_outline,
                  'Creator',
                  challenge.creatorId.length > 20
                      ? '${challenge.creatorId.substring(0, 20)}...'
                      : challenge.creatorId,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Rules and goals
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Goals & Rules',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                if (challenge.rules.goals.isNotEmpty) ...[
                  _buildSectionHeader(context, Icons.flag_outlined, 'Goals'),
                  const SizedBox(height: 8),
                  ...challenge.rules.goals.map(
                    (goal) => _buildListItem(context, goal),
                  ),
                  const SizedBox(height: 16),
                ],
                if (challenge.rules.requirements.isNotEmpty) ...[
                  _buildSectionHeader(
                    context,
                    Icons.checklist_outlined,
                    'Requirements',
                  ),
                  const SizedBox(height: 8),
                  ...challenge.rules.requirements.map(
                    (req) => _buildListItem(context, req),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Action button
          _buildActionButton(context, challenge),
        ],
      ),
    );
  }

  Widget _buildProgressCard(BuildContext context, challenge) {
    final theme = Theme.of(context);
    final serverProgress = challenge.userProgress ?? 0.0;

    // Initialize local progress if not set or challenge changed
    if (_localProgress.value == 0.0 && serverProgress > 0.0) {
      _localProgress.value = serverProgress;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() {
        final progress = _localProgress.value;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Your Progress',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${(progress * 100).toInt()}%',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Interactive Slider Progress Bar
            Column(
              children: [
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 8,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 12,
                      elevation: 4,
                    ),
                    overlayShape: const RoundSliderOverlayShape(
                      overlayRadius: 20,
                    ),
                    activeTrackColor: theme.colorScheme.primary,
                    inactiveTrackColor:
                        theme.colorScheme.surfaceContainerHighest,
                    thumbColor: theme.colorScheme.primary,
                    overlayColor: theme.colorScheme.primary.withValues(
                      alpha: 0.1,
                    ),
                    valueIndicatorShape:
                        const PaddleSliderValueIndicatorShape(),
                    valueIndicatorColor: theme.colorScheme.primary,
                    valueIndicatorTextStyle: theme.textTheme.bodySmall
                        ?.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  child: Slider(
                    value: progress,
                    min: 0.0,
                    max: 1.0,
                    divisions: 20, // 5% increments
                    label: '${(progress * 100).toInt()}%',
                    onChanged: (value) {
                      _updateProgress(value);
                    },
                  ),
                ),

                // Progress Labels
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '0%',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Drag to preview',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '100%',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Achievement Milestones
            if (progress > 0) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withValues(
                    alpha: 0.3,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.primary.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getProgressIcon(progress),
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _getProgressMessage(progress),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    if (progress >= 1.0) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.celebration_outlined,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        );
      }),
    );
  }

  IconData _getProgressIcon(double progress) {
    if (progress >= 1.0) return Icons.emoji_events_outlined;
    if (progress >= 0.75) return Icons.trending_up_outlined;
    if (progress >= 0.5) return Icons.local_fire_department_outlined;
    if (progress >= 0.25) return Icons.speed_outlined;
    return Icons.play_circle_outline;
  }

  String _getProgressMessage(double progress) {
    if (progress >= 1.0) return 'Congratulations! Challenge completed! 🎉';
    if (progress >= 0.75) return 'Amazing! You\'re almost there! 🚀';
    if (progress >= 0.5) return 'Great job! You\'re halfway through! 🔥';
    if (progress >= 0.25) return 'Good start! Keep up the momentum! ⚡';
    return 'You\'ve started your journey! 💪';
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 18,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    IconData icon,
    String title,
  ) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(icon, size: 20, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildListItem(BuildContext context, String text) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(text, style: theme.textTheme.bodyMedium)),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, challenge) {
    final theme = Theme.of(context);

    if (challenge.hasEnded) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy_outlined,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 8),
            Text(
              'Challenge Ended',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    if (challenge.joined) {
      return Obx(() {
        final serverProgress = challenge.userProgress ?? 0.0;
        final hasChanges = _localProgress.value != serverProgress;

        return Column(
          children: [
            // Save Progress Button (only show if there are unsaved changes)
            if (hasChanges) ...[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () async {
                    await controller.updateChallengeProgress(
                      challenge.id,
                      _localProgress.value,
                    );
                    // Optionally show a success message
                    Get.snackbar(
                      'Success',
                      'Progress updated successfully!',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: theme.colorScheme.primary,
                      colorText: theme.colorScheme.onPrimary,
                      duration: const Duration(seconds: 2),
                    );
                  },
                  icon: const Icon(Icons.save_outlined),
                  label: Text(
                    'Save Progress (${(_localProgress.value * 100).toInt()}%)',
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Leave Challenge Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => controller.leaveChallenge(challenge.id),
                icon: const Icon(Icons.exit_to_app_outlined),
                label: const Text('Leave Challenge'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: theme.colorScheme.error),
                  foregroundColor: theme.colorScheme.error,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            ),
          ],
        );
      });
    } else {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed:
              challenge.isPending
                  ? null
                  : () => controller.joinChallenge(challenge.id),
          icon: Icon(
            challenge.isPending
                ? Icons.schedule_outlined
                : Icons.add_circle_outline,
          ),
          label: Text(
            challenge.isPending ? 'Challenge Not Started' : 'Join Challenge',
          ),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: challenge.isPending ? 0 : 2,
          ),
        ),
      );
    }
  }

  Widget _buildLeaderboardTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 80,
      ), // Adjusted top padding for floating tab bar
      child: Obx(
        () => LeaderboardWidget(
          entries: controller.leaderboard,
          isLoading: controller.isLoading,
        ),
      ),
    );
  }

  Widget _buildChatTab(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 80,
      ), // Adjusted top padding for floating tab bar
      child: Column(
        children: [
          Expanded(
            child: Obx(() {
              final messages = controller.messages;
              if (messages.isEmpty) {
                return const Center(
                  child: Text('No messages yet. Start the conversation!'),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: messages.length,
                itemBuilder: (context, index) {
                  final message = messages[index];
                  return ListTile(
                    leading: CircleAvatar(
                      child: Text(message.senderName[0].toUpperCase()),
                    ),
                    title: Text(message.senderName),
                    subtitle: Text(message.content),
                    trailing: Text(
                      message.timestamp
                          .toString()
                          .split(' ')[1]
                          .substring(0, 5),
                    ),
                  );
                },
              );
            }),
          ),
          _buildMessageInput(context),
        ],
      ),
    );
  }

  Widget _buildMessageInput(BuildContext context) {
    final messageController = TextEditingController();
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
              child: TextField(
                controller: messageController,
                decoration: InputDecoration(
                  hintText: 'Type a message...',
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  hintStyle: TextStyle(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (message) {
                  if (message.trim().isNotEmpty) {
                    controller.sendChallengeMessage(
                      challengeId: challengeId,
                      content: message.trim(),
                    );
                    messageController.clear();
                  }
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(24),
            ),
            child: IconButton(
              onPressed: () {
                final message = messageController.text.trim();
                if (message.isNotEmpty) {
                  controller.sendChallengeMessage(
                    challengeId: challengeId,
                    content: message,
                  );
                  messageController.clear();
                }
              },
              icon: Icon(
                Icons.send_rounded,
                color: theme.colorScheme.onPrimary,
              ),
              style: IconButton.styleFrom(padding: const EdgeInsets.all(12)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingTabBar(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.primary.withValues(alpha: 0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.all(4),
        dividerColor: Colors.transparent,
        labelColor: theme.colorScheme.onPrimary,
        unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
        labelStyle: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 13,
        ),
        unselectedLabelStyle: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 13,
        ),
        splashFactory: NoSplash.splashFactory,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Leaderboard'),
          Tab(text: 'Chat'),
        ],
      ),
    );
  }

  void _showEditDialog(BuildContext context, challenge) {
    final theme = Theme.of(context);
    final nameController = TextEditingController(text: challenge.name);
    final descriptionController = TextEditingController(
      text: challenge.description,
    );

    // Convert DateTime to reactive variables
    final startDate = Rx<DateTime>(challenge.startDate);
    final endDate = Rx<DateTime>(challenge.endDate);

    Get.bottomSheet(
      Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.1),
                  ),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Edit Challenge',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Obx(
                    () =>
                        controller.isLoading
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : IconButton(
                              onPressed:
                                  () => _saveChallenge(
                                    challenge.id,
                                    nameController.text,
                                    descriptionController.text,
                                    startDate.value,
                                    endDate.value,
                                    challenge.rules,
                                  ),
                              icon: const Icon(Icons.save_outlined),
                              style: IconButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: theme.colorScheme.onPrimary,
                              ),
                            ),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Challenge Name
                    Text(
                      'Challenge Name',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        hintText: 'Enter challenge name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: theme.colorScheme.surfaceContainerHighest,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Description
                    Text(
                      'Description',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: descriptionController,
                      maxLines: 4,
                      decoration: InputDecoration(
                        hintText: 'Describe your challenge...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: theme.colorScheme.surfaceContainerHighest,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Start Date
                    Text(
                      'Start Date',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(
                      () => InkWell(
                        onTap:
                            () => _selectDate(
                              context,
                              startDate,
                              'Select Start Date',
                            ),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: theme.colorScheme.outline.withValues(
                                alpha: 0.3,
                              ),
                            ),
                            borderRadius: BorderRadius.circular(12),
                            color: theme.colorScheme.surfaceContainerHighest,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today_outlined,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                '${startDate.value.day}/${startDate.value.month}/${startDate.value.year}',
                                style: theme.textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // End Date
                    Text(
                      'End Date',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(
                      () => InkWell(
                        onTap:
                            () => _selectDate(
                              context,
                              endDate,
                              'Select End Date',
                            ),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: theme.colorScheme.outline.withValues(
                                alpha: 0.3,
                              ),
                            ),
                            borderRadius: BorderRadius.circular(12),
                            color: theme.colorScheme.surfaceContainerHighest,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today_outlined,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                '${endDate.value.day}/${endDate.value.month}/${endDate.value.year}',
                                style: theme.textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: Obx(
                        () => ElevatedButton(
                          onPressed:
                              controller.isLoading
                                  ? null
                                  : () => _saveChallenge(
                                    challenge.id,
                                    nameController.text,
                                    descriptionController.text,
                                    startDate.value,
                                    endDate.value,
                                    challenge.rules,
                                  ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child:
                              controller.isLoading
                                  ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                  : const Text(
                                    'Save Changes',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      enableDrag: true,
    );
  }

  Future<void> _selectDate(
    BuildContext context,
    Rx<DateTime> dateObs,
    String title,
  ) async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: dateObs.value,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: title,
    );

    if (selectedDate != null) {
      dateObs.value = selectedDate;
    }
  }

  Future<void> _saveChallenge(
    String id,
    String name,
    String description,
    DateTime startDate,
    DateTime endDate,
    dynamic rules,
  ) async {
    // Validation
    if (name.trim().isEmpty) {
      Get.snackbar(
        'Validation Error',
        'Challenge name cannot be empty',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    if (description.trim().isEmpty) {
      Get.snackbar(
        'Validation Error',
        'Challenge description cannot be empty',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    if (endDate.isBefore(startDate)) {
      Get.snackbar(
        'Validation Error',
        'End date must be after start date',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    // Convert rules to Map<String, dynamic> if needed
    Map<String, dynamic> rulesMap;
    if (rules is Map<String, dynamic>) {
      rulesMap = rules;
    } else {
      // If rules is a ChallengeRules object, convert it
      rulesMap = {
        'goals': rules.goals ?? [],
        'requirements': rules.requirements ?? [],
        'rewards': rules.rewards ?? {},
      };
    }

    // Call the controller method
    await controller.updateChallenge(
      id: id,
      name: name.trim(),
      description: description.trim(),
      startDate: startDate,
      endDate: endDate,
      rules: rulesMap,
    );
  }

  void _showDeleteConfirmation(BuildContext context, String challengeId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Challenge'),
            content: const Text(
              'Are you sure you want to delete this challenge? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  controller.deleteChallenge(challengeId);
                  Get.back();
                  Get.back(); // Go back to challenges list
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
