import 'package:get/get.dart';
import 'package:power_up/features/community/community.dart';

class CommunityRoutes {
  static const String community = '/community';
  static const String challenges = '/community/challenges';
  static const String challengeDetail = '/community/challenge/:id';

  static List<GetPage> pages = [
    GetPage(
      name: community,
      page: () => const CommunityScreen(),
      binding: CommunityBindings(),
    ),
    GetPage(
      name: challenges,
      page: () => const ChallengeListPage(),
      binding: CommunityBindings(),
    ),
    GetPage(
      name: challengeDetail,
      page: () => const ChallengeDetailPage(),
      binding: CommunityBindings(),
    ),
  ];
}
