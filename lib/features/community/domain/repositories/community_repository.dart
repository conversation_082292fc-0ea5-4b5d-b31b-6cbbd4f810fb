import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/challenge_entity.dart';
import '../entities/leaderboard_entry_entity.dart';
import '../entities/chat_message_entity.dart';

/// Repository interface for community-related operations
abstract class CommunityRepository {
  // Challenge operations
  Future<Either<Failure, List<ChallengeEntity>>> getChallenges({
    String? filter,
  });
  Future<Either<Failure, ChallengeEntity>> getChallengeById(String id);
  Future<Either<Failure, ChallengeEntity>> createChallenge({
    required String name,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> rules,
  });
  Future<Either<Failure, ChallengeEntity>> updateChallenge({
    required String id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? rules,
  });
  Future<Either<Failure, void>> deleteChallenge(String id);
  Future<Either<Failure, bool>> joinChallenge(String challengeId);
  Future<Either<Failure, void>> leaveChallenge(String challengeId);
  Future<Either<Failure, double>> updateChallengeProgress({
    required String challengeId,
    required double progress,
  });

  // Leaderboard operations
  Future<Either<Failure, List<LeaderboardEntryEntity>>> getLeaderboard(
    String challengeId,
  );

  // Messaging operations
  Future<Either<Failure, void>> sendDirectMessage({
    required String recipientId,
    required String content,
    List<String>? attachments,
  });
  Future<Either<Failure, void>> sendGroupMessage({
    required String groupId,
    required String content,
    List<String>? attachments,
  });
  Future<Either<Failure, void>> sendChallengeMessage({
    required String challengeId,
    required String content,
    List<String>? attachments,
  });
  Future<Either<Failure, List<ChatMessageEntity>>> getUserConversations();
  Future<Either<Failure, List<ChatMessageEntity>>> getDirectMessages(
    String recipientId,
  );
  Future<Either<Failure, List<ChatMessageEntity>>> getGroupMessages(
    String groupId,
  );
  Future<Either<Failure, List<ChatMessageEntity>>> getChallengeMessages(
    String challengeId,
  );
  Future<Either<Failure, void>> markMessageAsRead(String messageId);
}
