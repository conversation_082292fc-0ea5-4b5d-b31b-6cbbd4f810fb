import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import '../entities/chat_message_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for getting messages in a challenge
class GetChallengeMessagesUseCase
    implements UseCase<List<ChatMessageEntity>, GetChallengeMessagesParams> {
  final CommunityRepository repository;

  GetChallengeMessagesUseCase(this.repository);

  @override
  Future<Either<Failure, List<ChatMessageEntity>>> call(
    GetChallengeMessagesParams params,
  ) async {
    return repository.getChallengeMessages(params.challengeId);
  }
}

/// Parameters for GetChallengeMessagesUseCase
class GetChallengeMessagesParams extends Equatable {
  final String challengeId;

  const GetChallengeMessagesParams({required this.challengeId});

  @override
  List<Object?> get props => [challengeId];
}
