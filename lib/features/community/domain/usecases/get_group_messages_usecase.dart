import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/chat_message_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for getting group messages
class GetGroupMessagesUseCase
    implements UseCase<List<ChatMessageEntity>, GetGroupMessagesParams> {
  final CommunityRepository repository;

  GetGroupMessagesUseCase(this.repository);

  @override
  Future<Either<Failure, List<ChatMessageEntity>>> call(
    GetGroupMessagesParams params,
  ) async {
    return repository.getGroupMessages(params.groupId);
  }
}

/// Parameters for GetGroupMessagesUseCase
class GetGroupMessagesParams extends Equatable {
  final String groupId;

  const GetGroupMessagesParams({required this.groupId});

  @override
  List<Object> get props => [groupId];
}
