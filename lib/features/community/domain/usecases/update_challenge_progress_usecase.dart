import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for updating challenge progress
class UpdateChallengeProgressUseCase
    implements UseCase<double, UpdateChallengeProgressParams> {
  final CommunityRepository repository;

  UpdateChallengeProgressUseCase(this.repository);

  @override
  Future<Either<Failure, double>> call(
    UpdateChallengeProgressParams params,
  ) async {
    return repository.updateChallengeProgress(
      challengeId: params.challengeId,
      progress: params.progress,
    );
  }
}

/// Parameters for UpdateChallengeProgressUseCase
class UpdateChallengeProgressParams extends Equatable {
  final String challengeId;
  final double progress;

  const UpdateChallengeProgressParams({
    required this.challengeId,
    required this.progress,
  });

  @override
  List<Object> get props => [challengeId, progress];
}
