import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/chat_message_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for getting user conversations
class GetUserConversationsUseCase
    implements UseCase<List<ChatMessageEntity>, NoParams> {
  final CommunityRepository repository;

  GetUserConversationsUseCase(this.repository);

  @override
  Future<Either<Failure, List<ChatMessageEntity>>> call(NoParams params) async {
    return repository.getUserConversations();
  }
}
