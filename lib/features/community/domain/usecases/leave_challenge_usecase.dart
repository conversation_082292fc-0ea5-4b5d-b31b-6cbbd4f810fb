import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for leaving a challenge
class LeaveChallengeUseCase implements UseCase<void, LeaveChallengeParams> {
  final CommunityRepository repository;

  LeaveChallengeUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(LeaveChallengeParams params) async {
    return repository.leaveChallenge(params.challengeId);
  }
}

/// Parameters for LeaveChallengeUseCase
class LeaveChallengeParams extends Equatable {
  final String challengeId;

  const LeaveChallengeParams({required this.challengeId});

  @override
  List<Object> get props => [challengeId];
}
