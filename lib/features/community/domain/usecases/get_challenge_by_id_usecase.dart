import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import '../entities/challenge_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for getting a specific challenge by ID
class GetChallengeByIdUseCase
    implements UseCase<ChallengeEntity, GetChallengeByIdParams> {
  final CommunityRepository repository;

  GetChallengeByIdUseCase(this.repository);

  @override
  Future<Either<Failure, ChallengeEntity>> call(
    GetChallengeByIdParams params,
  ) async {
    return repository.getChallengeById(params.challengeId);
  }
}

/// Parameters for GetChallengeByIdUseCase
class GetChallengeByIdParams extends Equatable {
  final String challengeId;

  const GetChallengeByIdParams({required this.challengeId});

  @override
  List<Object?> get props => [challengeId];
}
