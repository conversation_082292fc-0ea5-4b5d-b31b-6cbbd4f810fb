import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for deleting a challenge
class DeleteChallengeUseCase implements UseCase<void, DeleteChallengeParams> {
  final CommunityRepository repository;

  DeleteChallengeUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteChallengeParams params) async {
    return repository.deleteChallenge(params.id);
  }
}

/// Parameters for DeleteChallengeUseCase
class DeleteChallengeParams extends Equatable {
  final String id;

  const DeleteChallengeParams({required this.id});

  @override
  List<Object> get props => [id];
}
