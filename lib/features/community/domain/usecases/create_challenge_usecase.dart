import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/challenge_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for creating a new challenge
class CreateChallengeUseCase
    implements UseCase<ChallengeEntity, CreateChallengeParams> {
  final CommunityRepository repository;

  CreateChallengeUseCase(this.repository);

  @override
  Future<Either<Failure, ChallengeEntity>> call(
    CreateChallengeParams params,
  ) async {
    return repository.createChallenge(
      name: params.name,
      description: params.description,
      startDate: params.startDate,
      endDate: params.endDate,
      rules: params.rules,
    );
  }
}

/// Parameters for CreateChallengeUseCase
class CreateChallengeParams extends Equatable {
  final String name;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic> rules;

  const CreateChallengeParams({
    required this.name,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.rules,
  });

  @override
  List<Object> get props => [name, description, startDate, endDate, rules];
}
