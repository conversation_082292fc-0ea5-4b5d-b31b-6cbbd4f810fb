import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for sending a direct message
class SendDirectMessageUseCase
    implements UseCase<void, SendDirectMessageParams> {
  final CommunityRepository repository;

  SendDirectMessageUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(SendDirectMessageParams params) async {
    return repository.sendDirectMessage(
      recipientId: params.recipientId,
      content: params.content,
      attachments: params.attachments,
    );
  }
}

/// Parameters for SendDirectMessageUseCase
class SendDirectMessageParams extends Equatable {
  final String recipientId;
  final String content;
  final List<String>? attachments;

  const SendDirectMessageParams({
    required this.recipientId,
    required this.content,
    this.attachments,
  });

  @override
  List<Object?> get props => [recipientId, content, attachments];
}
