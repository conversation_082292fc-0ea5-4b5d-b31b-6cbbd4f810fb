import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/challenge_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for updating a challenge
class UpdateChallengeUseCase
    implements UseCase<ChallengeEntity, UpdateChallengeParams> {
  final CommunityRepository repository;

  UpdateChallengeUseCase(this.repository);

  @override
  Future<Either<Failure, ChallengeEntity>> call(
    UpdateChallengeParams params,
  ) async {
    return repository.updateChallenge(
      id: params.id,
      name: params.name,
      description: params.description,
      startDate: params.startDate,
      endDate: params.endDate,
      rules: params.rules,
    );
  }
}

/// Parameters for UpdateChallengeUseCase
class UpdateChallengeParams extends Equatable {
  final String id;
  final String? name;
  final String? description;
  final DateTime? startDate;
  final DateTime? endDate;
  final Map<String, dynamic>? rules;

  const UpdateChallengeParams({
    required this.id,
    this.name,
    this.description,
    this.startDate,
    this.endDate,
    this.rules,
  });

  @override
  List<Object?> get props => [id, name, description, startDate, endDate, rules];
}
