import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import '../entities/leaderboard_entry_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for retrieving challenge leaderboard
class GetLeaderboardUseCase
    implements UseCase<List<LeaderboardEntryEntity>, GetLeaderboardParams> {
  final CommunityRepository repository;

  GetLeaderboardUseCase(this.repository);

  @override
  Future<Either<Failure, List<LeaderboardEntryEntity>>> call(
    GetLeaderboardParams params,
  ) async {
    return repository.getLeaderboard(params.challengeId);
  }
}

/// Parameters for GetLeaderboardUseCase
class GetLeaderboardParams extends Equatable {
  final String challengeId;

  const GetLeaderboardParams({required this.challengeId});

  @override
  List<Object?> get props => [challengeId];
}
