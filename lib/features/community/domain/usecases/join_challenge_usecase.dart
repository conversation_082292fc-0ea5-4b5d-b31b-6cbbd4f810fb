import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for joining a challenge
class JoinChallengeUseCase implements UseCase<bool, JoinChallengeParams> {
  final CommunityRepository repository;

  JoinChallengeUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(JoinChallengeParams params) async {
    return repository.joinChallenge(params.challengeId);
  }
}

/// Parameters for JoinChallengeUseCase
class JoinChallengeParams extends Equatable {
  final String challengeId;

  const JoinChallengeParams({required this.challengeId});

  @override
  List<Object?> get props => [challengeId];
}
