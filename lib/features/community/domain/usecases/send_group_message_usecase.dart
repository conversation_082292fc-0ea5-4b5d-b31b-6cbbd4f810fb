import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for sending a group message
class SendGroupMessageUseCase implements UseCase<void, SendGroupMessageParams> {
  final CommunityRepository repository;

  SendGroupMessageUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(SendGroupMessageParams params) async {
    return repository.sendGroupMessage(
      groupId: params.groupId,
      content: params.content,
      attachments: params.attachments,
    );
  }
}

/// Parameters for SendGroupMessageUseCase
class SendGroupMessageParams extends Equatable {
  final String groupId;
  final String content;
  final List<String>? attachments;

  const SendGroupMessageParams({
    required this.groupId,
    required this.content,
    this.attachments,
  });

  @override
  List<Object?> get props => [groupId, content, attachments];
}
