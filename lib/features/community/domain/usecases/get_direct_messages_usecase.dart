import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/chat_message_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for getting direct messages
class GetDirectMessagesUseCase
    implements UseCase<List<ChatMessageEntity>, GetDirectMessagesParams> {
  final CommunityRepository repository;

  GetDirectMessagesUseCase(this.repository);

  @override
  Future<Either<Failure, List<ChatMessageEntity>>> call(
    GetDirectMessagesParams params,
  ) async {
    return repository.getDirectMessages(params.recipientId);
  }
}

/// Parameters for GetDirectMessagesUseCase
class GetDirectMessagesParams extends Equatable {
  final String recipientId;

  const GetDirectMessagesParams({required this.recipientId});

  @override
  List<Object> get props => [recipientId];
}
