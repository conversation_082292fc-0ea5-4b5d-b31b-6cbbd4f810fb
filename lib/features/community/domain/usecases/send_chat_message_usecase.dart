import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for sending a chat message in a challenge
class SendChatMessageUseCase implements UseCase<void, SendChatMessageParams> {
  final CommunityRepository repository;

  SendChatMessageUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(SendChatMessageParams params) async {
    switch (params.messageType) {
      case MessageType.direct:
        return repository.sendDirectMessage(
          recipientId: params.recipientId!,
          content: params.content,
          attachments: params.attachments,
        );
      case MessageType.group:
        return repository.sendGroupMessage(
          groupId: params.groupId!,
          content: params.content,
          attachments: params.attachments,
        );
      case MessageType.challenge:
        return repository.sendChallengeMessage(
          challengeId: params.challengeId!,
          content: params.content,
          attachments: params.attachments,
        );
    }
  }
}

/// Type of message to send
enum MessageType { direct, group, challenge }

/// Parameters for SendChatMessageUseCase
class SendChatMessageParams extends Equatable {
  final MessageType messageType;
  final String content;
  final String? recipientId; // For direct messages
  final String? groupId; // For group messages
  final String? challengeId; // For challenge messages
  final List<String>? attachments;

  const SendChatMessageParams({
    required this.messageType,
    required this.content,
    this.recipientId,
    this.groupId,
    this.challengeId,
    this.attachments,
  });

  @override
  List<Object?> get props => [
    messageType,
    content,
    recipientId,
    groupId,
    challengeId,
    attachments,
  ];
}
