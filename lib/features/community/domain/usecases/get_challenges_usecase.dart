import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import '../entities/challenge_entity.dart';
import '../repositories/community_repository.dart';

/// Use case for retrieving challenges
class GetChallengesUseCase
    implements UseCase<List<ChallengeEntity>, GetChallengesParams> {
  final CommunityRepository repository;

  GetChallengesUseCase(this.repository);

  @override
  Future<Either<Failure, List<ChallengeEntity>>> call(
    GetChallengesParams params,
  ) async {
    return repository.getChallenges(filter: params.filter);
  }
}

/// Parameters for GetChallengesUseCase
class GetChallengesParams extends Equatable {
  final String? filter; // 'all', 'active', etc.

  const GetChallengesParams({this.filter});

  @override
  List<Object?> get props => [filter];
}
