import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for sending a challenge message
class SendChallengeMessageUseCase
    implements UseCase<void, SendChallengeMessageParams> {
  final CommunityRepository repository;

  SendChallengeMessageUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(SendChallengeMessageParams params) async {
    return repository.sendChallengeMessage(
      challengeId: params.challengeId,
      content: params.content,
      attachments: params.attachments,
    );
  }
}

/// Parameters for SendChallengeMessageUseCase
class SendChallengeMessageParams extends Equatable {
  final String challengeId;
  final String content;
  final List<String>? attachments;

  const SendChallengeMessageParams({
    required this.challengeId,
    required this.content,
    this.attachments,
  });

  @override
  List<Object?> get props => [challengeId, content, attachments];
}
