import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/community_repository.dart';

/// Use case for marking a message as read
class MarkMessageAsReadUseCase
    implements UseCase<void, MarkMessageAsReadParams> {
  final CommunityRepository repository;

  MarkMessageAsReadUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(MarkMessageAsReadParams params) async {
    return repository.markMessageAsRead(params.messageId);
  }
}

/// Parameters for MarkMessageAsReadUseCase
class MarkMessageAsReadParams extends Equatable {
  final String messageId;

  const MarkMessageAsReadParams({required this.messageId});

  @override
  List<Object> get props => [messageId];
}
