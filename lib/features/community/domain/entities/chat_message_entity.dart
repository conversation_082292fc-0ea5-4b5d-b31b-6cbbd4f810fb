import 'package:equatable/equatable.dart';

/// Type of chat message (direct, group, challenge)
enum MessageType { direct, group, challenge }

/// Domain entity for a chat message in the community feature
class ChatMessageEntity extends Equatable {
  final String id;
  final String senderId;
  final String senderName;
  final String content;
  final DateTime timestamp;
  final MessageType type;
  final String? recipientId; // For direct messages
  final String? groupId; // For group messages
  final String? challengeId; // For challenge messages
  final List<String> attachments;
  final bool isRead;
  final String? senderAvatarUrl;

  const ChatMessageEntity({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.content,
    required this.timestamp,
    required this.type,
    this.recipientId,
    this.groupId,
    this.challengeId,
    this.attachments = const [],
    required this.isRead,
    this.senderAvatarUrl,
  });

  @override
  List<Object?> get props => [
    id,
    senderId,
    senderName,
    content,
    timestamp,
    type,
    recipientId,
    groupId,
    challengeId,
    attachments,
    isRead,
    senderAvatarUrl,
  ];

  /// Creates a copy of this ChatMessageEntity with the given fields replaced with the new values
  ChatMessageEntity copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? content,
    DateTime? timestamp,
    MessageType? type,
    String? recipientId,
    String? groupId,
    String? challengeId,
    List<String>? attachments,
    bool? isRead,
    String? senderAvatarUrl,
  }) {
    return ChatMessageEntity(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      recipientId: recipientId ?? this.recipientId,
      groupId: groupId ?? this.groupId,
      challengeId: challengeId ?? this.challengeId,
      attachments: attachments ?? this.attachments,
      isRead: isRead ?? this.isRead,
      senderAvatarUrl: senderAvatarUrl ?? this.senderAvatarUrl,
    );
  }

  /// Returns true if this is a direct message
  bool get isDirectMessage => type == MessageType.direct;

  /// Returns true if this is a group message
  bool get isGroupMessage => type == MessageType.group;

  /// Returns true if this is a challenge message
  bool get isChallengeMessage => type == MessageType.challenge;
}
