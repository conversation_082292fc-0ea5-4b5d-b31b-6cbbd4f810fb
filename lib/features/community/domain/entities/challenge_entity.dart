import 'package:equatable/equatable.dart';

/// Represents the rules and configuration for a challenge
class ChallengeRules extends Equatable {
  final List<String> goals;
  final List<String> requirements;
  final Map<String, dynamic> rewards;

  const ChallengeRules({
    required this.goals,
    required this.requirements,
    required this.rewards,
  });

  @override
  List<Object?> get props => [goals, requirements, rewards];

  ChallengeRules copyWith({
    List<String>? goals,
    List<String>? requirements,
    Map<String, dynamic>? rewards,
  }) {
    return ChallengeRules(
      goals: goals ?? this.goals,
      requirements: requirements ?? this.requirements,
      rewards: rewards ?? this.rewards,
    );
  }
}

/// Domain entity for a community challenge
class ChallengeEntity extends Equatable {
  final String id;
  final String name;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final ChallengeRules rules;
  final String creatorId;
  final int participantCount;
  final bool joined;
  final double? userProgress;

  const ChallengeEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.rules,
    required this.creatorId,
    required this.participantCount,
    required this.joined,
    this.userProgress,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    startDate,
    endDate,
    rules,
    creatorId,
    participantCount,
    joined,
    userProgress,
  ];

  /// Creates a copy of this ChallengeEntity with the given fields replaced with the new values
  ChallengeEntity copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    ChallengeRules? rules,
    String? creatorId,
    int? participantCount,
    bool? joined,
    double? userProgress,
  }) {
    return ChallengeEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      rules: rules ?? this.rules,
      creatorId: creatorId ?? this.creatorId,
      participantCount: participantCount ?? this.participantCount,
      joined: joined ?? this.joined,
      userProgress: userProgress ?? this.userProgress,
    );
  }

  /// Returns true if the challenge is currently active
  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  /// Returns true if the challenge has ended
  bool get hasEnded {
    return DateTime.now().isAfter(endDate);
  }

  /// Returns true if the challenge hasn't started yet
  bool get isPending {
    return DateTime.now().isBefore(startDate);
  }
}
