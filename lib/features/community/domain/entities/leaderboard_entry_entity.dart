import 'package:equatable/equatable.dart';

/// Domain entity for a leaderboard entry representing a user's ranking in a challenge
class LeaderboardEntryEntity extends Equatable {
  final String userId;
  final String userName;
  final double score;
  final int rank;
  final String? avatarUrl;
  final Map<String, dynamic>? additionalData;

  const LeaderboardEntryEntity({
    required this.userId,
    required this.userName,
    required this.score,
    required this.rank,
    this.avatarUrl,
    this.additionalData,
  });

  @override
  List<Object?> get props => [
    userId,
    userName,
    score,
    rank,
    avatarUrl,
    additionalData,
  ];

  /// Creates a copy of this LeaderboardEntryEntity with the given fields replaced with the new values
  LeaderboardEntryEntity copyWith({
    String? userId,
    String? userName,
    double? score,
    int? rank,
    String? avatarUrl,
    Map<String, dynamic>? additionalData,
  }) {
    return LeaderboardEntryEntity(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      score: score ?? this.score,
      rank: rank ?? this.rank,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}
