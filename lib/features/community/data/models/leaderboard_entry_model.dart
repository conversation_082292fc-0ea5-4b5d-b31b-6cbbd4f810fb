import '../../domain/entities/leaderboard_entry_entity.dart';

class LeaderboardEntryModel extends LeaderboardEntryEntity {
  const LeaderboardEntryModel({
    required super.userId,
    required super.userName,
    required super.score,
    required super.rank,
    super.avatarUrl,
    super.additionalData,
  });

  factory LeaderboardEntryModel.fromJson(Map<String, dynamic> json) {
    return LeaderboardEntryModel(
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      score: (json['score'] ?? 0).toDouble(),
      rank: json['rank'] ?? 0,
      avatarUrl: json['avatarUrl'],
      additionalData:
          json['additionalData'] != null
              ? Map<String, dynamic>.from(json['additionalData'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'score': score,
      'rank': rank,
      'avatarUrl': avatarUrl,
      'additionalData': additionalData,
    };
  }

  factory LeaderboardEntryModel.fromEntity(LeaderboardEntryEntity entity) {
    return LeaderboardEntryModel(
      userId: entity.userId,
      userName: entity.userName,
      score: entity.score,
      rank: entity.rank,
      avatarUrl: entity.avatarUrl,
      additionalData: entity.additionalData,
    );
  }
}
