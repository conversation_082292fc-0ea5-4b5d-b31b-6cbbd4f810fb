import '../../domain/entities/challenge_entity.dart';

class ChallengeRulesModel extends ChallengeRules {
  const ChallengeRulesModel({
    required super.goals,
    required super.requirements,
    required super.rewards,
  });

  factory ChallengeRulesModel.fromJson(Map<String, dynamic> json) {
    return ChallengeRulesModel(
      goals: List<String>.from(json['goals'] ?? []),
      requirements: List<String>.from(json['requirements'] ?? []),
      rewards: Map<String, dynamic>.from(json['rewards'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {'goals': goals, 'requirements': requirements, 'rewards': rewards};
  }

  factory ChallengeRulesModel.fromEntity(ChallengeRules entity) {
    return ChallengeRulesModel(
      goals: entity.goals,
      requirements: entity.requirements,
      rewards: entity.rewards,
    );
  }
}

class ChallengeModel extends ChallengeEntity {
  const ChallengeModel({
    required super.id,
    required super.name,
    required super.description,
    required super.startDate,
    required super.endDate,
    required super.rules,
    required super.creatorId,
    required super.participantCount,
    required super.joined,
    super.userProgress,
  });

  factory ChallengeModel.fromJson(
    Map<String, dynamic> json, {
    String? currentUserId,
  }) {
    // Parse participants array to get count and check if user is joined
    final List<dynamic> participants = json['participants'] ?? [];
    final participantCount = participants.length;

    // Check if current user is in participants list
    bool joined = false;
    if (currentUserId != null) {
      joined = participants.any(
        (participant) =>
            participant['id'] == currentUserId ||
            participant['firebaseUid'] == currentUserId,
      );
    }

    // For now, assume the first participant is the creator if no explicit creatorId is provided
    final creatorId =
        json['creatorId'] ??
        (participants.isNotEmpty ? participants.first['id'] ?? '' : '');

    return ChallengeModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      rules: ChallengeRulesModel.fromJson(json['rules'] ?? {}),
      creatorId: creatorId,
      participantCount: participantCount,
      joined: joined,
      userProgress:
          json['progress'] != null
              ? (json['progress'] as Map<String, dynamic>).values.isNotEmpty
                  ? (json['progress'] as Map<String, dynamic>)
                      .values
                      .first['currentProgress']
                  : null
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'rules': (rules as ChallengeRulesModel).toJson(),
      'creatorId': creatorId,
      'participantCount': participantCount,
      'joined': joined,
      'userProgress': userProgress,
    };
  }

  factory ChallengeModel.fromEntity(ChallengeEntity entity) {
    return ChallengeModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      startDate: entity.startDate,
      endDate: entity.endDate,
      rules: ChallengeRulesModel.fromEntity(entity.rules),
      creatorId: entity.creatorId,
      participantCount: entity.participantCount,
      joined: entity.joined,
      userProgress: entity.userProgress,
    );
  }
}
