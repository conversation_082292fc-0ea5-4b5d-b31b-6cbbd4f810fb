import '../../domain/entities/chat_message_entity.dart';

class ChatMessageModel extends ChatMessageEntity {
  const ChatMessageModel({
    required super.id,
    required super.senderId,
    required super.senderName,
    required super.content,
    required super.timestamp,
    required super.type,
    super.recipientId,
    super.groupId,
    super.challengeId,
    super.attachments = const [],
    required super.isRead,
    super.senderAvatarUrl,
  });

  factory ChatMessageModel.fromJson(Map<String, dynamic> json) {
    return ChatMessageModel(
      id: json['id'] ?? '',
      senderId: json['senderId'] ?? '',
      senderName: json['senderName'] ?? '',
      content: json['content'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      type: _messageTypeFromString(json['type']),
      recipientId: json['recipientId'],
      groupId: json['groupId'],
      challengeId: json['challengeId'],
      attachments: List<String>.from(json['attachments'] ?? []),
      isRead: json['isRead'] ?? false,
      senderAvatarUrl: json['senderAvatarUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'type': _messageTypeToString(type),
      'recipientId': recipientId,
      'groupId': groupId,
      'challengeId': challengeId,
      'attachments': attachments,
      'isRead': isRead,
      'senderAvatarUrl': senderAvatarUrl,
    };
  }

  factory ChatMessageModel.fromEntity(ChatMessageEntity entity) {
    return ChatMessageModel(
      id: entity.id,
      senderId: entity.senderId,
      senderName: entity.senderName,
      content: entity.content,
      timestamp: entity.timestamp,
      type: entity.type,
      recipientId: entity.recipientId,
      groupId: entity.groupId,
      challengeId: entity.challengeId,
      attachments: entity.attachments,
      isRead: entity.isRead,
      senderAvatarUrl: entity.senderAvatarUrl,
    );
  }

  static MessageType _messageTypeFromString(String? type) {
    switch (type?.toLowerCase()) {
      case 'direct':
        return MessageType.direct;
      case 'group':
        return MessageType.group;
      case 'challenge':
        return MessageType.challenge;
      default:
        return MessageType.direct;
    }
  }

  static String _messageTypeToString(MessageType type) {
    switch (type) {
      case MessageType.direct:
        return 'direct';
      case MessageType.group:
        return 'group';
      case MessageType.challenge:
        return 'challenge';
    }
  }
}
