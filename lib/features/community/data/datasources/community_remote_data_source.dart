import '../models/challenge_model.dart';
import '../models/chat_message_model.dart';
import '../models/leaderboard_entry_model.dart';

/// Remote data source for community-related operations
abstract class CommunityRemoteDataSource {
  // Challenge operations
  Future<List<ChallengeModel>> getChallenges({String? filter});
  Future<ChallengeModel> getChallengeById(String id);
  Future<ChallengeModel> createChallenge({
    required String name,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> rules,
  });
  Future<ChallengeModel> updateChallenge({
    required String id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? rules,
  });
  Future<void> deleteChallenge(String id);
  Future<bool> joinChallenge(String challengeId);
  Future<void> leaveChallenge(String challengeId);
  Future<double> updateChallengeProgress({
    required String challengeId,
    required double progress,
  });

  // Leaderboard operations
  Future<List<LeaderboardEntryModel>> getLeaderboard(String challengeId);

  // Messaging operations
  Future<void> sendDirectMessage({
    required String recipientId,
    required String content,
    List<String>? attachments,
  });
  Future<void> sendGroupMessage({
    required String groupId,
    required String content,
    List<String>? attachments,
  });
  Future<void> sendChallengeMessage({
    required String challengeId,
    required String content,
    List<String>? attachments,
  });
  Future<List<ChatMessageModel>> getUserConversations();
  Future<List<ChatMessageModel>> getDirectMessages(String recipientId);
  Future<List<ChatMessageModel>> getGroupMessages(String groupId);
  Future<List<ChatMessageModel>> getChallengeMessages(String challengeId);
  Future<void> markMessageAsRead(String messageId);
}
