import 'package:dio/dio.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/services/auth_service.dart';
import '../models/challenge_model.dart';
import '../models/chat_message_model.dart';
import '../models/leaderboard_entry_model.dart';
import 'community_remote_data_source.dart';

/// Implementation of remote data source for community operations
class CommunityRemoteDataSourceImpl implements CommunityRemoteDataSource {
  final Dio dio;
  final AuthService _authService;

  CommunityRemoteDataSourceImpl({
    required this.dio,
    required AuthService authService,
  }) : _authService = authService;

  @override
  Future<List<ChallengeModel>> getChallenges({String? filter}) async {
    try {
      final response = await dio.get(
        '/challenges',
        queryParameters: filter != null ? {'filter': filter} : null,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data ?? response.data;
        final currentUserId = _authService.currentUserId;

        return data
            .map(
              (json) =>
                  ChallengeModel.fromJson(json, currentUserId: currentUserId),
            )
            .toList();
      } else {
        throw ServerException(message: 'Failed to get challenges');
      }
    } on DioException catch (e) {
      throw ServerException(
        message: e.response?.data?['message'] ?? 'Network error',
      );
    }
  }

  // /// Provides mock data for testing when API is not available
  // List<ChallengeModel> _getMockChallenges(String? filter) {
  //   final mockChallenges = [
  //     ChallengeModel(
  //       id: '1',
  //       name: '30-Day Fitness Challenge',
  //       description: 'Complete 30 minutes of exercise daily for 30 days',
  //       startDate: DateTime.now().add(const Duration(days: 1)),
  //       endDate: DateTime.now().add(const Duration(days: 31)),
  //       rules: const ChallengeRulesModel(
  //         goals: ['Exercise 30 minutes daily', 'Track your progress'],
  //         requirements: ['Daily check-in', 'Photo evidence'],
  //         rewards: {'xp': 500, 'badge': 'Fitness Master'},
  //       ),
  //       creatorId: 'user123',
  //       participantCount: 45,
  //       joined: false,
  //       userProgress: null,
  //     ),
  //     ChallengeModel(
  //       id: '2',
  //       name: 'Reading Marathon',
  //       description: 'Read one book per week for 4 weeks',
  //       startDate: DateTime.now().subtract(const Duration(days: 5)),
  //       endDate: DateTime.now().add(const Duration(days: 23)),
  //       rules: const ChallengeRulesModel(
  //         goals: ['Read 1 book per week', 'Write a review'],
  //         requirements: ['Upload book cover', 'Share insights'],
  //         rewards: {'xp': 300, 'badge': 'Bookworm'},
  //       ),
  //       creatorId: 'user456',
  //       participantCount: 28,
  //       joined: true,
  //       userProgress: 0.6,
  //     ),
  //     ChallengeModel(
  //       id: '3',
  //       name: 'Meditation Streak',
  //       description: 'Meditate for 10 minutes daily for 21 days',
  //       startDate: DateTime.now().subtract(const Duration(days: 25)),
  //       endDate: DateTime.now().subtract(const Duration(days: 4)),
  //       rules: const ChallengeRulesModel(
  //         goals: ['Daily meditation', 'Mindfulness practice'],
  //         requirements: ['Use meditation app', 'Log sessions'],
  //         rewards: {'xp': 250, 'badge': 'Zen Master'},
  //       ),
  //       creatorId: 'user789',
  //       participantCount: 67,
  //       joined: true,
  //       userProgress: 1.0,
  //     ),
  //     ChallengeModel(
  //       id: '4',
  //       name: 'Healthy Cooking Challenge',
  //       description: 'Cook a healthy meal at home every day for 2 weeks',
  //       startDate: DateTime.now().add(const Duration(days: 3)),
  //       endDate: DateTime.now().add(const Duration(days: 17)),
  //       rules: const ChallengeRulesModel(
  //         goals: ['Cook healthy meals', 'Try new recipes'],
  //         requirements: ['Photo of meal', 'Share recipe'],
  //         rewards: {'xp': 400, 'badge': 'Master Chef'},
  //       ),
  //       creatorId: 'user012',
  //       participantCount: 19,
  //       joined: false,
  //       userProgress: null,
  //     ),
  //   ];

  //   // Apply filter if provided
  //   if (filter == null || filter == 'all') {
  //     return mockChallenges;
  //   }

  //   return mockChallenges.where((challenge) {
  //     switch (filter) {
  //       case 'active':
  //         return challenge.isActive;
  //       case 'joined':
  //         return challenge.joined;
  //       case 'completed':
  //         return challenge.hasEnded && challenge.joined;
  //       default:
  //         return true;
  //     }
  //   }).toList();
  // }

  @override
  Future<ChallengeModel> getChallengeById(String id) async {
    try {
      final response = await dio.get('/challenges/$id');

      if (response.statusCode == 200) {
        final currentUserId = _authService.currentUserId;
        return ChallengeModel.fromJson(
          response.data,
          currentUserId: currentUserId,
        );
      } else {
        throw ServerException(message: 'Failed to get challenge');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<ChallengeModel> createChallenge({
    required String name,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> rules,
  }) async {
    try {
      final response = await dio.post(
        '/challenges',
        data: {
          'name': name,
          'description': description,
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
          'rules': rules,
        },
      );

      if (response.statusCode == 201) {
        final currentUserId = _authService.currentUserId;
        return ChallengeModel.fromJson(
          response.data,
          currentUserId: currentUserId,
        );
      } else {
        throw ServerException(message: 'Failed to create challenge');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<ChallengeModel> updateChallenge({
    required String id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? rules,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (startDate != null) data['startDate'] = startDate.toIso8601String();
      if (endDate != null) data['endDate'] = endDate.toIso8601String();
      if (rules != null) data['rules'] = rules;

      final response = await dio.put('/challenges/$id', data: data);

      if (response.statusCode == 200) {
        final currentUserId = _authService.currentUserId;
        return ChallengeModel.fromJson(
          response.data,
          currentUserId: currentUserId,
        );
      } else {
        throw ServerException(message: 'Failed to update challenge');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<void> deleteChallenge(String id) async {
    try {
      final response = await dio.delete('/challenges/$id');

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw ServerException(message: 'Failed to delete challenge');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<bool> joinChallenge(String challengeId) async {
    try {
      final response = await dio.post('/challenges/$challengeId/join');

      if (response.statusCode == 200) {
        return response.data['joined'] ?? true;
      } else {
        throw ServerException(message: 'Failed to join challenge');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<void> leaveChallenge(String challengeId) async {
    try {
      final response = await dio.post('/challenges/$challengeId/leave');

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw ServerException(message: 'Failed to leave challenge');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<double> updateChallengeProgress({
    required String challengeId,
    required double progress,
  }) async {
    try {
      final response = await dio.post(
        '/challenges/$challengeId/progress',
        data: {'progress': progress},
      );

      if (response.statusCode == 200) {
        return (response.data['progress'] ?? progress).toDouble();
      } else {
        throw ServerException(message: 'Failed to update challenge progress');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<List<LeaderboardEntryModel>> getLeaderboard(String challengeId) async {
    try {
      final response = await dio.get('/challenges/$challengeId/leaderboard');

      if (response.statusCode == 200) {
        final List<dynamic> data =
            response.data['leaderboard'] ?? response.data;
        return data
            .map((json) => LeaderboardEntryModel.fromJson(json))
            .toList();
      } else {
        throw ServerException(message: 'Failed to get leaderboard');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<void> sendDirectMessage({
    required String recipientId,
    required String content,
    List<String>? attachments,
  }) async {
    try {
      final response = await dio.post(
        '/messaging/direct',
        data: {
          'recipientId': recipientId,
          'content': content,
          if (attachments != null) 'attachments': attachments,
        },
      );

      if (response.statusCode != 201) {
        throw ServerException(message: 'Failed to send direct message');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<void> sendGroupMessage({
    required String groupId,
    required String content,
    List<String>? attachments,
  }) async {
    try {
      final response = await dio.post(
        '/messaging/group',
        data: {
          'groupId': groupId,
          'content': content,
          if (attachments != null) 'attachments': attachments,
        },
      );

      if (response.statusCode != 201) {
        throw ServerException(message: 'Failed to send group message');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<void> sendChallengeMessage({
    required String challengeId,
    required String content,
    List<String>? attachments,
  }) async {
    try {
      final response = await dio.post(
        '/messaging/challenge',
        data: {
          'challengeId': challengeId,
          'content': content,
          if (attachments != null) 'attachments': attachments,
        },
      );

      if (response.statusCode != 201) {
        throw ServerException(message: 'Failed to send challenge message');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<List<ChatMessageModel>> getUserConversations() async {
    try {
      final response = await dio.get('/messaging/conversations');

      if (response.statusCode == 200) {
        final List<dynamic> data =
            response.data['conversations'] ?? response.data;
        return data.map((json) => ChatMessageModel.fromJson(json)).toList();
      } else {
        throw ServerException(message: 'Failed to get conversations');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<List<ChatMessageModel>> getDirectMessages(String recipientId) async {
    try {
      final response = await dio.get('/messaging/direct/$recipientId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['messages'] ?? response.data;
        return data.map((json) => ChatMessageModel.fromJson(json)).toList();
      } else {
        throw ServerException(message: 'Failed to get direct messages');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<List<ChatMessageModel>> getGroupMessages(String groupId) async {
    try {
      final response = await dio.get('/messaging/group/$groupId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['messages'] ?? response.data;
        return data.map((json) => ChatMessageModel.fromJson(json)).toList();
      } else {
        throw ServerException(message: 'Failed to get group messages');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<List<ChatMessageModel>> getChallengeMessages(
    String challengeId,
  ) async {
    try {
      final response = await dio.get('/messaging/challenge/$challengeId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['messages'] ?? response.data;
        return data.map((json) => ChatMessageModel.fromJson(json)).toList();
      } else {
        throw ServerException(message: 'Failed to get challenge messages');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }

  @override
  Future<void> markMessageAsRead(String messageId) async {
    try {
      final response = await dio.put('/messaging/read/$messageId');

      if (response.statusCode != 200) {
        throw ServerException(message: 'Failed to mark message as read');
      }
    } on DioException catch (e) {
      throw ServerException(message: e.message ?? 'Network error');
    }
  }
}
