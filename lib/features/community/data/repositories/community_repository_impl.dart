import 'package:dartz/dartz.dart';
import '../../domain/entities/challenge_entity.dart';
import '../../domain/entities/leaderboard_entry_entity.dart';
import '../../domain/entities/chat_message_entity.dart';
import '../../domain/repositories/community_repository.dart';
import '../datasources/community_remote_data_source.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/network_info.dart';

/// Implementation of CommunityRepository
class CommunityRepositoryImpl implements CommunityRepository {
  final CommunityRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  CommunityRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<ChallengeEntity>>> getChallenges({
    String? filter,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final challenges = await remoteDataSource.getChallenges(filter: filter);
        return Right(challenges);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ChallengeEntity>> getChallengeById(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final challenge = await remoteDataSource.getChallengeById(id);
        return Right(challenge);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ChallengeEntity>> createChallenge({
    required String name,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    required Map<String, dynamic> rules,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final challenge = await remoteDataSource.createChallenge(
          name: name,
          description: description,
          startDate: startDate,
          endDate: endDate,
          rules: rules,
        );
        return Right(challenge);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, ChallengeEntity>> updateChallenge({
    required String id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? rules,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final challenge = await remoteDataSource.updateChallenge(
          id: id,
          name: name,
          description: description,
          startDate: startDate,
          endDate: endDate,
          rules: rules,
        );
        return Right(challenge);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteChallenge(String id) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteChallenge(id);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, bool>> joinChallenge(String challengeId) async {
    if (await networkInfo.isConnected) {
      try {
        final joined = await remoteDataSource.joinChallenge(challengeId);
        return Right(joined);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> leaveChallenge(String challengeId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.leaveChallenge(challengeId);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, double>> updateChallengeProgress({
    required String challengeId,
    required double progress,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedProgress = await remoteDataSource.updateChallengeProgress(
          challengeId: challengeId,
          progress: progress,
        );
        return Right(updatedProgress);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<LeaderboardEntryEntity>>> getLeaderboard(
    String challengeId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final leaderboard = await remoteDataSource.getLeaderboard(challengeId);
        return Right(leaderboard);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> sendDirectMessage({
    required String recipientId,
    required String content,
    List<String>? attachments,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.sendDirectMessage(
          recipientId: recipientId,
          content: content,
          attachments: attachments,
        );
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> sendGroupMessage({
    required String groupId,
    required String content,
    List<String>? attachments,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.sendGroupMessage(
          groupId: groupId,
          content: content,
          attachments: attachments,
        );
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> sendChallengeMessage({
    required String challengeId,
    required String content,
    List<String>? attachments,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.sendChallengeMessage(
          challengeId: challengeId,
          content: content,
          attachments: attachments,
        );
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ChatMessageEntity>>>
  getUserConversations() async {
    if (await networkInfo.isConnected) {
      try {
        final conversations = await remoteDataSource.getUserConversations();
        return Right(conversations);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ChatMessageEntity>>> getDirectMessages(
    String recipientId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final messages = await remoteDataSource.getDirectMessages(recipientId);
        return Right(messages);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ChatMessageEntity>>> getGroupMessages(
    String groupId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final messages = await remoteDataSource.getGroupMessages(groupId);
        return Right(messages);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<ChatMessageEntity>>> getChallengeMessages(
    String challengeId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final messages = await remoteDataSource.getChallengeMessages(
          challengeId,
        );
        return Right(messages);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> markMessageAsRead(String messageId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.markMessageAsRead(messageId);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(UnexpectedFailure(message: e.toString()));
      }
    } else {
      return const Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
