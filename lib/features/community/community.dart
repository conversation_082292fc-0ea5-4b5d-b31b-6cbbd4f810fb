// Data layer exports
export 'data/models/challenge_model.dart';
export 'data/models/chat_message_model.dart';
export 'data/models/leaderboard_entry_model.dart';
export 'data/datasources/community_remote_data_source.dart';
export 'data/datasources/community_remote_data_source_impl.dart';
export 'data/repositories/community_repository_impl.dart';

// Domain layer exports
export 'domain/entities/challenge_entity.dart';
export 'domain/entities/chat_message_entity.dart';
export 'domain/entities/leaderboard_entry_entity.dart';
export 'domain/repositories/community_repository.dart';
export 'domain/usecases/get_challenges_usecase.dart';
export 'domain/usecases/get_challenge_by_id_usecase.dart';
export 'domain/usecases/create_challenge_usecase.dart';
export 'domain/usecases/update_challenge_usecase.dart';
export 'domain/usecases/delete_challenge_usecase.dart';
export 'domain/usecases/join_challenge_usecase.dart';
export 'domain/usecases/leave_challenge_usecase.dart';
export 'domain/usecases/update_challenge_progress_usecase.dart';
export 'domain/usecases/get_leaderboard_usecase.dart';
export 'domain/usecases/send_direct_message_usecase.dart';
export 'domain/usecases/send_group_message_usecase.dart';
export 'domain/usecases/send_challenge_message_usecase.dart';
export 'domain/usecases/get_user_conversations_usecase.dart';
export 'domain/usecases/get_direct_messages_usecase.dart';
export 'domain/usecases/get_group_messages_usecase.dart';
export 'domain/usecases/get_challenge_messages_usecase.dart';
export 'domain/usecases/mark_message_as_read_usecase.dart';

// Presentation layer exports
export 'presentation/controllers/community_controller.dart';
export 'presentation/pages/challenge_list_page.dart';
export 'presentation/pages/challenge_detail_page.dart';
export 'presentation/widgets/challenge_card.dart';
export 'presentation/widgets/leaderboard_widget.dart';
export 'presentation/screens/community_screen.dart';

// DI and routes
export 'di/community_bindings.dart';
export 'routes/community_routes.dart';
