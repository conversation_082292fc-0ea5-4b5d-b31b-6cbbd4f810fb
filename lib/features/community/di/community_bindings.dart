import 'package:get/get.dart';
import 'package:dio/dio.dart';
import 'package:power_up/core/services/auth_service.dart';
import '../data/datasources/community_remote_data_source.dart';
import '../data/datasources/community_remote_data_source_impl.dart';
import '../data/repositories/community_repository_impl.dart';
import '../domain/repositories/community_repository.dart';
import '../domain/usecases/get_challenges_usecase.dart';
import '../domain/usecases/get_challenge_by_id_usecase.dart';
import '../domain/usecases/create_challenge_usecase.dart';
import '../domain/usecases/update_challenge_usecase.dart';
import '../domain/usecases/delete_challenge_usecase.dart';
import '../domain/usecases/join_challenge_usecase.dart';
import '../domain/usecases/leave_challenge_usecase.dart';
import '../domain/usecases/update_challenge_progress_usecase.dart';
import '../domain/usecases/get_leaderboard_usecase.dart';
import '../domain/usecases/send_direct_message_usecase.dart';
import '../domain/usecases/send_group_message_usecase.dart';
import '../domain/usecases/send_challenge_message_usecase.dart';
import '../domain/usecases/get_user_conversations_usecase.dart';
import '../domain/usecases/get_direct_messages_usecase.dart';
import '../domain/usecases/get_group_messages_usecase.dart';
import '../domain/usecases/get_challenge_messages_usecase.dart';
import '../domain/usecases/mark_message_as_read_usecase.dart';
import '../presentation/controllers/community_controller.dart';
import '../../../core/network/network_info.dart';

class CommunityBindings extends Bindings {
  @override
  void dependencies() {
    // Data sources
    Get.lazyPut<CommunityRemoteDataSource>(
      () => CommunityRemoteDataSourceImpl(
        dio: Get.find<Dio>(),
        authService: Get.find<AuthService>(),
      ),
    );

    // Repository
    Get.lazyPut<CommunityRepository>(
      () => CommunityRepositoryImpl(
        remoteDataSource: Get.find<CommunityRemoteDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
      ),
    );

    // Use cases
    Get.lazyPut(() => GetChallengesUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(() => GetChallengeByIdUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(() => CreateChallengeUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(() => UpdateChallengeUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(() => DeleteChallengeUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(() => JoinChallengeUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(() => LeaveChallengeUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(
      () => UpdateChallengeProgressUseCase(Get.find<CommunityRepository>()),
    );
    Get.lazyPut(() => GetLeaderboardUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(
      () => SendDirectMessageUseCase(Get.find<CommunityRepository>()),
    );
    Get.lazyPut(() => SendGroupMessageUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(
      () => SendChallengeMessageUseCase(Get.find<CommunityRepository>()),
    );
    Get.lazyPut(
      () => GetUserConversationsUseCase(Get.find<CommunityRepository>()),
    );
    Get.lazyPut(
      () => GetDirectMessagesUseCase(Get.find<CommunityRepository>()),
    );
    Get.lazyPut(() => GetGroupMessagesUseCase(Get.find<CommunityRepository>()));
    Get.lazyPut(
      () => GetChallengeMessagesUseCase(Get.find<CommunityRepository>()),
    );
    Get.lazyPut(
      () => MarkMessageAsReadUseCase(Get.find<CommunityRepository>()),
    );

    // Controllers
    if (!Get.isRegistered<CommunityController>()) {
      Get.put(
        CommunityController(
          getChallengesUseCase: Get.find<GetChallengesUseCase>(),
          getChallengeByIdUseCase: Get.find<GetChallengeByIdUseCase>(),
          createChallengeUseCase: Get.find<CreateChallengeUseCase>(),
          updateChallengeUseCase: Get.find<UpdateChallengeUseCase>(),
          deleteChallengeUseCase: Get.find<DeleteChallengeUseCase>(),
          joinChallengeUseCase: Get.find<JoinChallengeUseCase>(),
          leaveChallengeUseCase: Get.find<LeaveChallengeUseCase>(),
          updateChallengeProgressUseCase:
              Get.find<UpdateChallengeProgressUseCase>(),
          getLeaderboardUseCase: Get.find<GetLeaderboardUseCase>(),
          sendDirectMessageUseCase: Get.find<SendDirectMessageUseCase>(),
          sendGroupMessageUseCase: Get.find<SendGroupMessageUseCase>(),
          sendChallengeMessageUseCase: Get.find<SendChallengeMessageUseCase>(),
          getUserConversationsUseCase: Get.find<GetUserConversationsUseCase>(),
          getDirectMessagesUseCase: Get.find<GetDirectMessagesUseCase>(),
          getGroupMessagesUseCase: Get.find<GetGroupMessagesUseCase>(),
          getChallengeMessagesUseCase: Get.find<GetChallengeMessagesUseCase>(),
          markMessageAsReadUseCase: Get.find<MarkMessageAsReadUseCase>(),
        ),
        permanent: true,
      );
    }
  }
}
