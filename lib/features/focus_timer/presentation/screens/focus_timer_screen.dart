import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/focus_timer/presentation/controllers/focus_timer_controller.dart';
import 'package:power_up/features/focus_timer/domain/entities/focus_session_entity.dart';
import 'package:power_up/features/core/presentation/screens/main_layout_screen.dart';

class FocusTimerScreen extends GetView<FocusTimerController> {
  const FocusTimerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return MainLayoutScreen(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Header Section
            Text(
              'Focus Timer',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Obx(
              () => Text(
                _getSessionDescription(),
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 24),

            // Timer Display
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.4,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Circular Progress Indicator
                    Obx(
                      () => SizedBox(
                        width: 200,
                        height: 200,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            CircularProgressIndicator(
                              value: controller.progress,
                              strokeWidth: 8,
                              backgroundColor: theme.colorScheme.primary
                                  .withValues(alpha: 0.2),
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getSessionColor(theme),
                              ),
                            ),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Time Display
                                Text(
                                  controller.formattedTime,
                                  style: theme.textTheme.headlineLarge
                                      ?.copyWith(
                                        fontSize: 32,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                ),
                                const SizedBox(height: 4),
                                // Session Type
                                Text(
                                  _getSessionName(),
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: _getSessionColor(theme),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Control Buttons
            Column(
              children: [
                // Play/Pause Button
                Obx(
                  () => ElevatedButton(
                    onPressed: _handlePlayPause,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 48,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(_getPlayPauseIcon()),
                        const SizedBox(width: 8),
                        Text(
                          _getPlayPauseText(),
                          style: const TextStyle(fontSize: 18),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Reset Button
                OutlinedButton(
                  onPressed: controller.reset,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: theme.colorScheme.primary,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: 8),
                      Text('Reset'),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Statistics
            Obx(
              () => Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      theme,
                      'Completed',
                      '${controller.completedPomodoros}',
                      Icons.check_circle,
                    ),
                    _buildStatItem(
                      theme,
                      'Session',
                      '${controller.completedPomodoros + 1}',
                      Icons.timer,
                    ),
                    _buildStatItem(
                      theme,
                      'Today',
                      '${controller.completedPomodoros}', // TODO: Track daily total
                      Icons.today,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Ambient Sounds Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.music_note, color: theme.colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        'Ambient Sounds',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Obx(
                        () => Switch(
                          value: controller.isAmbientSoundEnabled,
                          onChanged: (_) => controller.toggleAmbientSound(),
                        ),
                      ),
                    ],
                  ),

                  // Sound Selection (only show when enabled)
                  Obx(
                    () =>
                        controller.isAmbientSoundEnabled
                            ? Column(
                              children: [
                                const SizedBox(height: 12),
                                // Sound Options
                                SizedBox(
                                  height: 35,
                                  child: ListView.separated(
                                    scrollDirection: Axis.horizontal,
                                    itemCount:
                                        controller.availableSounds.length,
                                    separatorBuilder:
                                        (_, __) => const SizedBox(width: 8),
                                    itemBuilder: (context, index) {
                                      final sound =
                                          controller.availableSounds[index];
                                      final isSelected =
                                          sound.id ==
                                          controller.selectedSoundId;

                                      return FilterChip(
                                        label: Text(
                                          sound.name,
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                        selected: isSelected,
                                        onSelected:
                                            (_) => controller
                                                .selectAmbientSound(sound.id),
                                        backgroundColor:
                                            theme.colorScheme.surface,
                                        selectedColor: theme.colorScheme.primary
                                            .withValues(alpha: 0.2),
                                        checkmarkColor:
                                            theme.colorScheme.primary,
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            )
                            : const SizedBox.shrink(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Settings Button
            TextButton.icon(
              onPressed: _showSettingsDialog,
              icon: const Icon(Icons.settings),
              label: const Text('Timer Settings'),
            ),

            const SizedBox(height: 24), // Bottom padding for scroll
          ],
        ),
      ),
    );
  }

  String _getSessionDescription() {
    switch (controller.currentSession) {
      case SessionType.work:
        return 'Time to focus and be productive';
      case SessionType.shortBreak:
        return 'Take a short break and recharge';
      case SessionType.longBreak:
        return 'Enjoy your well-deserved long break';
    }
  }

  String _getSessionName() {
    switch (controller.currentSession) {
      case SessionType.work:
        return 'Work Session';
      case SessionType.shortBreak:
        return 'Short Break';
      case SessionType.longBreak:
        return 'Long Break';
    }
  }

  Color _getSessionColor(ThemeData theme) {
    switch (controller.currentSession) {
      case SessionType.work:
        return theme.colorScheme.primary;
      case SessionType.shortBreak:
        return Colors.green;
      case SessionType.longBreak:
        return Colors.blue;
    }
  }

  IconData _getPlayPauseIcon() {
    if (controller.isRunning) {
      return Icons.pause;
    } else {
      return Icons.play_arrow;
    }
  }

  String _getPlayPauseText() {
    if (controller.isRunning) {
      return 'Pause';
    } else if (controller.isPaused) {
      return 'Resume';
    } else {
      return 'Start';
    }
  }

  void _handlePlayPause() {
    if (controller.isRunning) {
      controller.pause();
    } else if (controller.isPaused) {
      controller.resume();
    } else {
      controller.start();
    }
  }

  Widget _buildStatItem(
    ThemeData theme,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: theme.colorScheme.primary, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }

  void _showSettingsDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Timer Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDurationSetting(
              'Work Duration',
              controller.workDuration,
              (value) => controller.updateWorkDuration(value),
            ),
            _buildDurationSetting(
              'Short Break',
              controller.shortBreakDuration,
              (value) => controller.updateShortBreakDuration(value),
            ),
            _buildDurationSetting(
              'Long Break',
              controller.longBreakDuration,
              (value) => controller.updateLongBreakDuration(value),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Close')),
        ],
      ),
    );
  }

  Widget _buildDurationSetting(
    String label,
    int currentValue,
    Function(int) onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            children: [
              IconButton(
                onPressed:
                    currentValue > 1 ? () => onChanged(currentValue - 1) : null,
                icon: const Icon(Icons.remove),
              ),
              SizedBox(
                width: 40,
                child: Text(
                  '$currentValue',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              IconButton(
                onPressed:
                    currentValue < 60
                        ? () => onChanged(currentValue + 1)
                        : null,
                icon: const Icon(Icons.add),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
