import 'dart:async';
import 'package:get/get.dart';
import 'package:power_up/features/focus_timer/domain/entities/focus_session_entity.dart';
import 'package:power_up/features/focus_timer/domain/entities/ambient_sound_entity.dart';
import 'package:power_up/features/focus_timer/domain/services/ambient_audio_service.dart';
import 'package:power_up/features/focus_timer/domain/services/voice_nudges_service.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';
import 'package:power_up/core/utils/logger.dart';

/// Controller for managing Pomodoro-style focus timer with ambient sounds
class FocusTimerController extends GetxController {
  final AmbientAudioService? _ambientAudioService;
  final VoiceNudgesService? _voiceNudgesService;
  final AIReportRepository? _aiReportRepository;

  // Timer state
  Timer? _timer;
  final RxBool _isRunning = false.obs;
  final RxBool _isPaused = false.obs;
  final RxInt _timeLeft = (25 * 60).obs; // Default 25 minutes in seconds
  final Rx<SessionType> _currentSession = SessionType.work.obs;
  final RxInt _completedPomodoros = 0.obs;

  // Configuration
  final RxInt _workDuration = 25.obs; // minutes
  final RxInt _shortBreakDuration = 5.obs; // minutes
  final RxInt _longBreakDuration = 15.obs; // minutes

  // Ambient sound state
  final RxList<AmbientSoundEntity> _availableSounds =
      <AmbientSoundEntity>[].obs;
  final RxnString _selectedSoundId = RxnString();
  final RxBool _isAmbientSoundEnabled = false.obs;
  final RxDouble _ambientVolume = 0.5.obs;

  // Voice nudges state
  final RxBool _isVoiceNudgesEnabled = true.obs;
  final RxDouble _voiceNudgesVolume = 0.7.obs;
  final RxString _voiceType = 'motivational'.obs;
  final RxBool _midwayNudgePlayed = false.obs;

  FocusTimerController({
    AmbientAudioService? ambientAudioService,
    VoiceNudgesService? voiceNudgesService,
    AIReportRepository? aiReportRepository,
  }) : _ambientAudioService = ambientAudioService,
       _voiceNudgesService = voiceNudgesService,
       _aiReportRepository = aiReportRepository;

  // Getters
  bool get isRunning => _isRunning.value;
  bool get isPaused => _isPaused.value;
  int get timeLeft => _timeLeft.value;
  SessionType get currentSession => _currentSession.value;
  int get completedPomodoros => _completedPomodoros.value;
  int get workDuration => _workDuration.value;
  int get shortBreakDuration => _shortBreakDuration.value;
  int get longBreakDuration => _longBreakDuration.value;

  // Ambient sound getters
  List<AmbientSoundEntity> get availableSounds => _availableSounds;
  String? get selectedSoundId => _selectedSoundId.value;
  bool get isAmbientSoundEnabled => _isAmbientSoundEnabled.value;
  double get ambientVolume => _ambientVolume.value;

  // Voice nudges getters
  bool get isVoiceNudgesEnabled => _isVoiceNudgesEnabled.value;
  double get voiceNudgesVolume => _voiceNudgesVolume.value;
  String get voiceType => _voiceType.value;

  /// Get the duration of the current session type in minutes
  int get currentSessionDuration {
    switch (currentSession) {
      case SessionType.work:
        return workDuration;
      case SessionType.shortBreak:
        return shortBreakDuration;
      case SessionType.longBreak:
        return longBreakDuration;
    }
  }

  /// Get formatted time as MM:SS or HH:MM:SS
  String get formattedTime {
    final hours = timeLeft ~/ 3600;
    final minutes = (timeLeft % 3600) ~/ 60;
    final seconds = timeLeft % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Get progress as a value between 0.0 and 1.0
  double get progress {
    final totalSeconds = currentSessionDuration * 60;
    if (totalSeconds == 0) return 1.0;
    return 1.0 - (timeLeft / totalSeconds);
  }

  /// Setters for time left (needed for testing)
  set timeLeft(int value) => _timeLeft.value = value;

  /// Setters for completed pomodoros (needed for testing)
  set completedPomodoros(int value) => _completedPomodoros.value = value;

  /// Setters for current session (needed for testing)
  set currentSession(SessionType value) => _currentSession.value = value;

  @override
  void onInit() {
    super.onInit();
    _timeLeft.value = workDuration * 60;
    _loadAmbientSounds();
  }

  @override
  void onClose() {
    _timer?.cancel();
    _ambientAudioService?.dispose();
    super.onClose();
  }

  /// Load available ambient sounds
  Future<void> _loadAmbientSounds() async {
    if (_ambientAudioService != null) {
      try {
        final sounds = await _ambientAudioService.getAvailableSounds();
        _availableSounds.assignAll(sounds);
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error loading ambient sounds: $e');
        }
      }
    }
  }

  void start() {
    if (_isPaused.value) {
      resume();
      return;
    }

    _isRunning.value = true;
    _isPaused.value = false;
    _midwayNudgePlayed.value = false; // Reset midway nudge flag

    // Start ambient sound if enabled
    _startAmbientSoundIfEnabled();

    // Play session start voice nudge
    playSessionStartNudge();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft.value > 0) {
        _timeLeft.value--;

        // Check for midway nudge (only during work sessions)
        if (currentSession == SessionType.work &&
            !_midwayNudgePlayed.value &&
            _timeLeft.value <= (currentSessionDuration * 60) / 2) {
          _midwayNudgePlayed.value = true;
          playMidwayNudge();
        }
      } else {
        onTimerComplete();
      }
    });
  }

  /// Pause the timer
  void pause() {
    _timer?.cancel();
    _isRunning.value = false;
    _isPaused.value = true;

    // Pause ambient sound if playing
    _pauseAmbientSoundIfPlaying();
  }

  /// Resume the timer
  void resume() {
    if (!_isPaused.value) return;

    _isRunning.value = true;
    _isPaused.value = false;

    // Resume ambient sound if enabled
    _resumeAmbientSoundIfEnabled();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft.value > 0) {
        _timeLeft.value--;

        // Check for midway nudge (only during work sessions)
        if (currentSession == SessionType.work &&
            !_midwayNudgePlayed.value &&
            _timeLeft.value <= (currentSessionDuration * 60) / 2) {
          _midwayNudgePlayed.value = true;
          playMidwayNudge();
        }
      } else {
        onTimerComplete();
      }
    });
  }

  /// Reset the timer
  void reset() {
    _timer?.cancel();
    _isRunning.value = false;
    _isPaused.value = false;
    _midwayNudgePlayed.value = false; // Reset midway nudge flag
    _timeLeft.value = currentSessionDuration * 60;

    // Stop ambient sound if playing
    _stopAmbientSoundIfPlaying();
  }

  /// Update work duration
  void updateWorkDuration(int minutes) {
    _workDuration.value = minutes;
    if (currentSession == SessionType.work && !isRunning) {
      _timeLeft.value = minutes * 60;
    }
  }

  /// Update short break duration
  void updateShortBreakDuration(int minutes) {
    _shortBreakDuration.value = minutes;
    if (currentSession == SessionType.shortBreak && !isRunning) {
      _timeLeft.value = minutes * 60;
    }
  }

  /// Update long break duration
  void updateLongBreakDuration(int minutes) {
    _longBreakDuration.value = minutes;
    if (currentSession == SessionType.longBreak && !isRunning) {
      _timeLeft.value = minutes * 60;
    }
  }

  /// Handle timer completion
  Future<void> onTimerComplete() async {
    _timer?.cancel();
    _isRunning.value = false;
    _isPaused.value = false;
    _midwayNudgePlayed.value = false; // Reset for next session

    final previousSession = currentSession;
    final sessionDurationMinutes = currentSessionDuration;

    // Switch to next session type
    switch (currentSession) {
      case SessionType.work:
        _completedPomodoros.value++;
        // Record completed work session
        await _recordFocusSession(sessionDurationMinutes);

        // After 4 pomodoros, take a long break
        if (completedPomodoros % 4 == 0) {
          _currentSession.value = SessionType.longBreak;
          _timeLeft.value = longBreakDuration * 60;
          // Play long break start nudge
          await _playVoiceNudge(() async {
            if (_voiceNudgesService != null) {
              await _voiceNudgesService.playBreakStartNudge(
                breakDurationMinutes: longBreakDuration,
                isLongBreak: true,
              );
            }
          });
        } else {
          _currentSession.value = SessionType.shortBreak;
          _timeLeft.value = shortBreakDuration * 60;
          // Play short break start nudge
          await _playVoiceNudge(() async {
            if (_voiceNudgesService != null) {
              await _voiceNudgesService.playBreakStartNudge(
                breakDurationMinutes: shortBreakDuration,
                isLongBreak: false,
              );
            }
          });
        }
        break;
      case SessionType.shortBreak:
      case SessionType.longBreak:
        _currentSession.value = SessionType.work;
        _timeLeft.value = workDuration * 60;
        // Play work session start nudge
        await _playVoiceNudge(() async {
          if (_voiceNudgesService != null) {
            await _voiceNudgesService.playBreakEndNudge(
              workDurationMinutes: workDuration,
            );
          }
        });
        break;
    }

    // Log session completion
    if (Get.isLogEnable) {
      Logger.info(
        'Session Complete! ${previousSession.name} session finished. Next: ${currentSession.name}',
      );
    }
  }

  // ===== AMBIENT SOUND METHODS =====

  /// Select an ambient sound for playback
  Future<void> selectAmbientSound(String soundId) async {
    _selectedSoundId.value = soundId;
    if (_isAmbientSoundEnabled.value && _ambientAudioService != null) {
      try {
        await _ambientAudioService.playAmbientSound(soundId);
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error playing ambient sound: $e');
        }
      }
    }
  }

  /// Toggle ambient sound on/off
  Future<void> toggleAmbientSound() async {
    _isAmbientSoundEnabled.value = !_isAmbientSoundEnabled.value;

    if (_ambientAudioService != null) {
      try {
        if (_isAmbientSoundEnabled.value && _selectedSoundId.value != null) {
          await _ambientAudioService.playAmbientSound(_selectedSoundId.value!);
        } else {
          await _ambientAudioService.stopAmbientSound();
        }
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error toggling ambient sound: $e');
        }
      }
    }
  }

  /// Set ambient sound volume
  Future<void> setAmbientVolume(double volume) async {
    _ambientVolume.value = volume.clamp(0.0, 1.0);
    if (_ambientAudioService != null) {
      try {
        await _ambientAudioService.setVolume(_ambientVolume.value);
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error setting ambient volume: $e');
        }
      }
    }
  }

  /// Start ambient sound when timer starts (if enabled)
  Future<void> _startAmbientSoundIfEnabled() async {
    if (_isAmbientSoundEnabled.value &&
        _selectedSoundId.value != null &&
        _ambientAudioService != null) {
      try {
        await _ambientAudioService.playAmbientSound(_selectedSoundId.value!);
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error starting ambient sound: $e');
        }
      }
    }
  }

  /// Pause ambient sound when timer is paused
  Future<void> _pauseAmbientSoundIfPlaying() async {
    if (_ambientAudioService != null && _ambientAudioService.isPlaying) {
      try {
        await _ambientAudioService.pauseAmbientSound();
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error pausing ambient sound: $e');
        }
      }
    }
  }

  /// Resume ambient sound when timer is resumed
  Future<void> _resumeAmbientSoundIfEnabled() async {
    if (_isAmbientSoundEnabled.value &&
        _ambientAudioService != null &&
        _ambientAudioService.isPaused) {
      try {
        await _ambientAudioService.resumeAmbientSound();
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error resuming ambient sound: $e');
        }
      }
    }
  }

  /// Stop ambient sound when timer is stopped/reset
  Future<void> _stopAmbientSoundIfPlaying() async {
    if (_ambientAudioService != null &&
        (_ambientAudioService.isPlaying || _ambientAudioService.isPaused)) {
      try {
        await _ambientAudioService.stopAmbientSound();
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error stopping ambient sound: $e');
        }
      }
    }
  }

  // ===== VOICE NUDGES METHODS =====

  /// Record completed focus session
  Future<void> _recordFocusSession(int minutes) async {
    if (_aiReportRepository != null) {
      try {
        await _aiReportRepository.recordFocusSession(minutes: minutes);
        if (Get.isLogEnable) {
          Logger.info('Focus session recorded: $minutes minutes');
        }
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error recording focus session: $e');
        }
      }
    }
  }

  /// Play voice nudge if enabled
  Future<void> _playVoiceNudge(Future<void> Function() nudgeFunction) async {
    if (_isVoiceNudgesEnabled.value && _voiceNudgesService != null) {
      try {
        await nudgeFunction();
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error playing voice nudge: $e');
        }
      }
    }
  }

  /// Toggle voice nudges on/off
  void toggleVoiceNudges() {
    _isVoiceNudgesEnabled.value = !_isVoiceNudgesEnabled.value;
  }

  /// Set voice nudges volume
  Future<void> setVoiceNudgesVolume(double volume) async {
    _voiceNudgesVolume.value = volume.clamp(0.0, 1.0);
    if (_voiceNudgesService != null) {
      try {
        await _voiceNudgesService.setVolume(_voiceNudgesVolume.value);
      } catch (e) {
        if (Get.isLogEnable) {
          Logger.error('Error setting voice nudges volume: $e');
        }
      }
    }
  }

  /// Set voice type for nudges
  void setVoiceType(String voiceType) {
    _voiceType.value = voiceType;
  }

  /// Play session start nudge
  Future<void> playSessionStartNudge() async {
    await _playVoiceNudge(() async {
      if (_voiceNudgesService != null) {
        await _voiceNudgesService.playSessionStartNudge(
          workDurationMinutes: workDuration,
          breakDurationMinutes:
              currentSession == SessionType.work
                  ? (completedPomodoros % 4 == 3
                      ? longBreakDuration
                      : shortBreakDuration)
                  : workDuration,
        );
      }
    });
  }

  /// Play midway encouragement nudge
  Future<void> playMidwayNudge() async {
    await _playVoiceNudge(() async {
      if (_voiceNudgesService != null) {
        await _voiceNudgesService.playMidwayNudge(
          remainingMinutes: (timeLeft / 60).round(),
          isWorkSession: currentSession == SessionType.work,
        );
      }
    });
  }

  /// Play session complete nudge
  Future<void> playSessionCompleteNudge() async {
    await _playVoiceNudge(() async {
      if (_voiceNudgesService != null) {
        await _voiceNudgesService.playSessionCompleteNudge(
          totalMinutes: currentSessionDuration,
          completedPomodoros: completedPomodoros,
        );
      }
    });
  }
}
