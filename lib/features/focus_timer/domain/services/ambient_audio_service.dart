import '../entities/ambient_sound_entity.dart';

/// Interface for managing ambient audio during focus sessions
abstract class AmbientAudioService {
  /// Gets the list of available ambient sounds
  Future<List<AmbientSoundEntity>> getAvailableSounds();

  /// Starts playing the specified ambient sound
  Future<void> playAmbientSound(String soundId);

  /// Stops the currently playing ambient sound
  Future<void> stopAmbientSound();

  /// Pauses the currently playing ambient sound
  Future<void> pauseAmbientSound();

  /// Resumes the paused ambient sound
  Future<void> resumeAmbientSound();

  /// Sets the volume for ambient sounds (0.0 to 1.0)
  Future<void> setVolume(double volume);

  /// Gets the current volume level
  double get currentVolume;

  /// Gets the currently playing sound ID
  String? get currentSoundId;

  /// Gets whether ambient sound is currently playing
  bool get isPlaying;

  /// Gets whether ambient sound is currently paused
  bool get isPaused;

  /// Stream of the current playback state
  Stream<bool> get playbackStateStream;

  /// Stream of the current playing sound ID
  Stream<String?> get currentSoundStream;

  /// Disposes the service and releases resources
  Future<void> dispose();
}
