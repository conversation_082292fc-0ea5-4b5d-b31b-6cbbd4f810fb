/// Interface for managing AI voice nudges during focus sessions
abstract class VoiceNudgesService {
  /// Generates and plays a voice nudge for session start
  Future<void> playSessionStartNudge({
    required int workDurationMinutes,
    required int breakDurationMinutes,
  });

  /// Generates and plays a voice nudge for session break
  Future<void> playBreakStartNudge({
    required int breakDurationMinutes,
    required bool isLongBreak,
  });

  /// Generates and plays a voice nudge for break end
  Future<void> playBreakEndNudge({required int workDurationMinutes});

  /// Generates and plays a voice nudge for session completion
  Future<void> playSessionCompleteNudge({
    required int totalMinutes,
    required int completedPomodoros,
  });

  /// Generates and plays a motivational midway nudge
  Future<void> playMidwayNudge({
    required int remainingMinutes,
    required bool isWorkSession,
  });

  /// Sets the volume for voice nudges (0.0 to 1.0)
  Future<void> setVolume(double volume);

  /// Gets the current volume level
  double get currentVolume;

  /// Gets whether voice nudges are enabled
  bool get isEnabled;

  /// Enables or disables voice nudges
  Future<void> setEnabled(bool enabled);

  /// Gets whether a voice nudge is currently playing
  bool get isPlaying;

  /// Stops the currently playing voice nudge
  Future<void> stopCurrentNudge();

  /// Stream of the current playback state
  Stream<bool> get playbackStateStream;

  /// Gets the available voice types/personalities
  Future<List<String>> getAvailableVoiceTypes();

  /// Sets the voice type/personality for nudges
  Future<void> setVoiceType(String voiceType);

  /// Gets the current voice type
  String get currentVoiceType;

  /// Disposes the service and releases resources
  Future<void> dispose();
}
