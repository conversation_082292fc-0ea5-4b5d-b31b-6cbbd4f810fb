/// Represents a voice nudge with its properties
class VoiceNudgeEntity {
  final String id;
  final String type;
  final String script;
  final String voiceType;
  final Duration duration;
  final DateTime createdAt;

  const VoiceNudgeEntity({
    required this.id,
    required this.type,
    required this.script,
    required this.voiceType,
    required this.duration,
    required this.createdAt,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VoiceNudgeEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          type == other.type &&
          script == other.script &&
          voiceType == other.voiceType &&
          duration == other.duration;

  @override
  int get hashCode =>
      id.hashCode ^
      type.hashCode ^
      script.hashCode ^
      voiceType.hashCode ^
      duration.hashCode;

  @override
  String toString() {
    return 'VoiceNudgeEntity{id: $id, type: $type, script: $script, '
        'voiceType: $voiceType, duration: $duration}';
  }
}

/// Voice nudge types
class VoiceNudgeType {
  static const String sessionStart = 'session_start';
  static const String breakStart = 'break_start';
  static const String breakEnd = 'break_end';
  static const String sessionComplete = 'session_complete';
  static const String midway = 'midway';
  static const String encouragement = 'encouragement';
}

/// Available voice types/personalities
class VoicePersonality {
  static const String motivational = 'motivational';
  static const String calm = 'calm';
  static const String energetic = 'energetic';
  static const String professional = 'professional';
  static const String friendly = 'friendly';
}
