import 'package:equatable/equatable.dart';

/// Entity representing an ambient sound for focus sessions
class AmbientSoundEntity extends Equatable {
  /// Unique identifier for the ambient sound
  final String id;

  /// Display name of the ambient sound
  final String name;

  /// Description of the ambient sound
  final String description;

  /// Asset path or URL to the audio file
  final String audioPath;

  /// Duration of the audio file in seconds (if known)
  final int? duration;

  /// Whether this sound is currently selected/active
  final bool isSelected;

  /// Category of the ambient sound (e.g., nature, white noise, etc.)
  final AmbientSoundCategory category;

  const AmbientSoundEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.audioPath,
    this.duration,
    this.isSelected = false,
    required this.category,
  });

  /// Creates a copy of this entity with updated values
  AmbientSoundEntity copyWith({
    String? id,
    String? name,
    String? description,
    String? audioPath,
    int? duration,
    bool? isSelected,
    AmbientSoundCategory? category,
  }) {
    return AmbientSoundEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      audioPath: audioPath ?? this.audioPath,
      duration: duration ?? this.duration,
      isSelected: isSelected ?? this.isSelected,
      category: category ?? this.category,
    );
  }

  /// Returns the formatted duration if available
  String get formattedDuration {
    if (duration == null) return 'Unknown';
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    audioPath,
    duration,
    isSelected,
    category,
  ];
}

/// Categories for ambient sounds
enum AmbientSoundCategory {
  nature('Nature'),
  whiteNoise('White Noise'),
  brownNoise('Brown Noise'),
  pinkNoise('Pink Noise'),
  urban('Urban'),
  instrumental('Instrumental'),
  binaural('Binaural Beats');

  const AmbientSoundCategory(this.displayName);

  final String displayName;
}
