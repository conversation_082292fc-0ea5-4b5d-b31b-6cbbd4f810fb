import 'package:equatable/equatable.dart';

/// Session types for focus timer
enum SessionType { work, shortBreak, longBreak }

/// Domain entity for a focus session
class FocusSessionEntity extends Equatable {
  final String id;
  final String startTime;
  final String? endTime;
  final int plannedDuration; // in minutes
  final int? actualDuration; // in minutes
  final SessionType type;
  final bool completedSuccessfully;
  final String createdAt;

  const FocusSessionEntity({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.plannedDuration,
    this.actualDuration,
    required this.type,
    required this.completedSuccessfully,
    required this.createdAt,
  });

  /// Creates a copy of this FocusSessionEntity with the given fields replaced
  FocusSessionEntity copyWith({
    String? id,
    String? startTime,
    String? endTime,
    int? plannedDuration,
    int? actualDuration,
    SessionType? type,
    bool? completedSuccessfully,
    String? createdAt,
  }) {
    return FocusSessionEntity(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      plannedDuration: plannedDuration ?? this.plannedDuration,
      actualDuration: actualDuration ?? this.actualDuration,
      type: type ?? this.type,
      completedSuccessfully:
          completedSuccessfully ?? this.completedSuccessfully,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get duration in minutes (actual if available, otherwise planned)
  int get durationInMinutes => actualDuration ?? plannedDuration;

  /// Check if this is a work session
  bool get isWorkSession => type == SessionType.work;

  /// Check if this is a break session
  bool get isBreakSession =>
      type == SessionType.shortBreak || type == SessionType.longBreak;

  @override
  List<Object?> get props => [
    id,
    startTime,
    endTime,
    plannedDuration,
    actualDuration,
    type,
    completedSuccessfully,
    createdAt,
  ];
}
