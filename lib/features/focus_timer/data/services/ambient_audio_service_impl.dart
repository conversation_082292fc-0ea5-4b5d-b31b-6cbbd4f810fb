import 'dart:async';

import 'package:just_audio/just_audio.dart';

import '../../domain/entities/ambient_sound_entity.dart';
import '../../domain/services/ambient_audio_service.dart';
import '../models/ambient_sound_model.dart';

/// Implementation of AmbientAudioService using just_audio
class AmbientAudioServiceImpl implements AmbientAudioService {
  final AudioPlayer _audioPlayer;
  final StreamController<bool> _playbackStateController;
  final StreamController<String?> _currentSoundController;

  List<AmbientSoundEntity> _availableSounds = [];
  String? _currentSoundId;
  double _currentVolume = 0.5;

  AmbientAudioServiceImpl({AudioPlayer? audioPlayer})
    : _audioPlayer = audioPlayer ?? AudioPlayer(),
      _playbackStateController = StreamController<bool>.broadcast(),
      _currentSoundController = StreamController<String?>.broadcast() {
    _initializeService();
  }

  void _initializeService() {
    // Load default sounds
    _availableSounds = AmbientSoundModel.getDefaultSounds();

    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((state) {
      _playbackStateController.add(state.playing);
    });

    // Set initial volume
    _audioPlayer.setVolume(_currentVolume);

    // Set looping mode for ambient sounds
    _audioPlayer.setLoopMode(LoopMode.one);
  }

  @override
  Future<List<AmbientSoundEntity>> getAvailableSounds() async {
    return _availableSounds;
  }

  @override
  Future<void> playAmbientSound(String soundId) async {
    try {
      // Find the sound
      final sound = _availableSounds.firstWhere(
        (s) => s.id == soundId,
        orElse: () => throw Exception('Sound not found: $soundId'),
      );

      // Stop current sound if playing
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }

      // Load and play the new sound
      await _audioPlayer.setAsset(sound.audioPath);
      await _audioPlayer.play();

      _currentSoundId = soundId;
      _currentSoundController.add(soundId);
    } catch (e) {
      throw Exception('Failed to play ambient sound: $e');
    }
  }

  @override
  Future<void> stopAmbientSound() async {
    try {
      await _audioPlayer.stop();
      _currentSoundId = null;
      _currentSoundController.add(null);
    } catch (e) {
      throw Exception('Failed to stop ambient sound: $e');
    }
  }

  @override
  Future<void> pauseAmbientSound() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      throw Exception('Failed to pause ambient sound: $e');
    }
  }

  @override
  Future<void> resumeAmbientSound() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      throw Exception('Failed to resume ambient sound: $e');
    }
  }

  @override
  Future<void> setVolume(double volume) async {
    try {
      _currentVolume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(_currentVolume);
    } catch (e) {
      throw Exception('Failed to set volume: $e');
    }
  }

  @override
  double get currentVolume => _currentVolume;

  @override
  String? get currentSoundId => _currentSoundId;

  @override
  bool get isPlaying => _audioPlayer.playing;

  @override
  bool get isPaused =>
      _audioPlayer.processingState == ProcessingState.ready &&
      !_audioPlayer.playing;

  @override
  Stream<bool> get playbackStateStream => _playbackStateController.stream;

  @override
  Stream<String?> get currentSoundStream => _currentSoundController.stream;

  @override
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _playbackStateController.close();
    await _currentSoundController.close();
  }
}
