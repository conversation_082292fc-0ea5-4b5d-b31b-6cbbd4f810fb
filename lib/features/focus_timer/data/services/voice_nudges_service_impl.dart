import 'dart:async';
import 'dart:math';

import 'package:just_audio/just_audio.dart';
import 'package:power_up/features/core/data/api/api_client.dart';
import 'package:power_up/core/error/exceptions.dart';

import '../../domain/entities/voice_nudge_entity.dart';
import '../../domain/services/voice_nudges_service.dart';

/// Implementation of VoiceNudgesService with AI generation and audio playback
class VoiceNudgesServiceImpl implements VoiceNudgesService {
  final ApiClient _apiClient;
  final AudioPlayer _audioPlayer;
  final StreamController<bool> _playbackStateController;

  bool _isEnabled;
  double _currentVolume;
  String _currentVoiceType;

  static const List<String> _availableVoiceTypes = [
    VoicePersonality.motivational,
    VoicePersonality.calm,
    VoicePersonality.energetic,
    VoicePersonality.professional,
    VoicePersonality.friendly,
  ];

  VoiceNudgesServiceImpl({
    required ApiClient apiClient,
    AudioPlayer? audioPlayer,
    bool enabled = true,
    double volume = 0.7,
    String voiceType = VoicePersonality.motivational,
  }) : _apiClient = apiClient,
       _audioPlayer = audioPlayer ?? AudioPlayer(),
       _playbackStateController = StreamController<bool>.broadcast(),
       _isEnabled = enabled,
       _currentVolume = volume,
       _currentVoiceType = voiceType {
    _initializeService();
  }

  void _initializeService() {
    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((state) {
      _playbackStateController.add(state.playing);
    });

    // Set initial volume
    _audioPlayer.setVolume(_currentVolume);
  }

  @override
  Future<void> playSessionStartNudge({
    required int workDurationMinutes,
    required int breakDurationMinutes,
  }) async {
    if (!_isEnabled) return;

    final script = _generateSessionStartScript(
      workDurationMinutes,
      breakDurationMinutes,
    );
    await _generateAndPlayNudge(
      script: script,
      type: VoiceNudgeType.sessionStart,
    );
  }

  @override
  Future<void> playBreakStartNudge({
    required int breakDurationMinutes,
    required bool isLongBreak,
  }) async {
    if (!_isEnabled) return;

    final script = _generateBreakStartScript(breakDurationMinutes, isLongBreak);
    await _generateAndPlayNudge(
      script: script,
      type: VoiceNudgeType.breakStart,
    );
  }

  @override
  Future<void> playBreakEndNudge({required int workDurationMinutes}) async {
    if (!_isEnabled) return;

    final script = _generateBreakEndScript(workDurationMinutes);
    await _generateAndPlayNudge(script: script, type: VoiceNudgeType.breakEnd);
  }

  @override
  Future<void> playSessionCompleteNudge({
    required int totalMinutes,
    required int completedPomodoros,
  }) async {
    if (!_isEnabled) return;

    final script = _generateSessionCompleteScript(
      totalMinutes,
      completedPomodoros,
    );
    await _generateAndPlayNudge(
      script: script,
      type: VoiceNudgeType.sessionComplete,
    );
  }

  @override
  Future<void> playMidwayNudge({
    required int remainingMinutes,
    required bool isWorkSession,
  }) async {
    if (!_isEnabled) return;

    final script = _generateMidwayScript(remainingMinutes, isWorkSession);
    await _generateAndPlayNudge(script: script, type: VoiceNudgeType.midway);
  }

  /// Generate and play a voice nudge
  Future<void> _generateAndPlayNudge({
    required String script,
    required String type,
  }) async {
    try {
      // Stop any currently playing nudge
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }

      // Generate voice audio via API
      final audioUrl = await _generateVoiceAudio(script, type);

      // Load and play the audio
      await _audioPlayer.setUrl(audioUrl);
      await _audioPlayer.play();
    } catch (e) {
      throw Exception('Failed to generate and play voice nudge: $e');
    }
  }

  /// Generate voice audio from script using backend API
  Future<String> _generateVoiceAudio(String script, String type) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        endpoint: '/voice/generate-nudge',
        data: {
          'script': script,
          'type': type,
          'voiceType': _currentVoiceType,
          'speed': 1.0,
          'emotion': _getEmotionForType(type),
        },
        fromData: (data) => data as Map<String, dynamic>,
      );

      return response['audioUrl'] as String;
    } on ServerException catch (e) {
      throw Exception('Failed to generate voice audio: ${e.message}');
    }
  }

  /// Get appropriate emotion for nudge type
  String _getEmotionForType(String type) {
    switch (type) {
      case VoiceNudgeType.sessionStart:
        return 'encouraging';
      case VoiceNudgeType.breakStart:
        return 'relaxed';
      case VoiceNudgeType.breakEnd:
        return 'motivating';
      case VoiceNudgeType.sessionComplete:
        return 'celebrating';
      case VoiceNudgeType.midway:
        return 'supportive';
      default:
        return 'neutral';
    }
  }

  /// Generate script for session start
  String _generateSessionStartScript(int workMinutes, int breakMinutes) {
    final scripts = [
      "Let's begin this $workMinutes-minute focus session! Remember, you'll have a $breakMinutes-minute break afterwards. You've got this!",
      "Time to dive deep! This $workMinutes-minute session is your chance to make real progress. Stay focused and trust the process.",
      "Starting your $workMinutes-minute focus block now. Eliminate distractions and give your full attention to what matters most.",
      "Here we go! $workMinutes minutes of pure focus ahead. Your $breakMinutes-minute break is waiting for you on the other side.",
    ];
    return _getRandomScript(scripts);
  }

  /// Generate script for break start
  String _generateBreakStartScript(int breakMinutes, bool isLongBreak) {
    final breakType = isLongBreak ? 'long' : 'short';
    final scripts = [
      "Great work! Time for a $breakMinutes-minute $breakType break. Step away, stretch, and recharge.",
      "You've earned this $breakMinutes-minute break! Use this time to refresh your mind and body.",
      "Break time! These $breakMinutes minutes are yours. Move around, breathe deeply, and prepare for your next session.",
      "Excellent focus! Enjoy this $breakMinutes-minute $breakType break. Your brain needs this recovery time.",
    ];
    return _getRandomScript(scripts);
  }

  /// Generate script for break end
  String _generateBreakEndScript(int workMinutes) {
    final scripts = [
      "Break's over! Ready for another $workMinutes minutes of focused work? Let's maintain that momentum.",
      "Time to get back to work! This next $workMinutes-minute session will bring you closer to your goals.",
      "Hope you're feeling refreshed! Let's tackle the next $workMinutes minutes with renewed energy.",
      "Back to focus mode! Use the energy from your break to power through these next $workMinutes minutes.",
    ];
    return _getRandomScript(scripts);
  }

  /// Generate script for session completion
  String _generateSessionCompleteScript(
    int totalMinutes,
    int completedPomodoros,
  ) {
    final scripts = [
      "Congratulations! You've completed $completedPomodoros pomodoros in $totalMinutes minutes. That's some serious focus!",
      "Session complete! $totalMinutes minutes of productive work with $completedPomodoros focused blocks. Well done!",
      "Amazing work! You just finished $completedPomodoros pomodoros. $totalMinutes minutes of pure productivity!",
      "That's a wrap! $completedPomodoros pomodoros completed in $totalMinutes minutes. You should be proud of this achievement!",
    ];
    return _getRandomScript(scripts);
  }

  /// Generate script for midway encouragement
  String _generateMidwayScript(int remainingMinutes, bool isWorkSession) {
    if (isWorkSession) {
      final scripts = [
        "You're doing great! $remainingMinutes minutes to go. Stay in the zone!",
        "Halfway there! Keep that concentration strong for $remainingMinutes more minutes.",
        "Excellent focus! Push through these final $remainingMinutes minutes.",
        "You're in the flow! $remainingMinutes minutes left in this session.",
      ];
      return _getRandomScript(scripts);
    } else {
      final scripts = [
        "Enjoy your break! $remainingMinutes minutes left to recharge.",
        "Keep relaxing! $remainingMinutes more minutes of well-deserved rest.",
        "Break time is precious! Make the most of these $remainingMinutes minutes.",
        "You're recharging well! $remainingMinutes minutes left in your break.",
      ];
      return _getRandomScript(scripts);
    }
  }

  /// Get a random script from the list
  String _getRandomScript(List<String> scripts) {
    final random = Random();
    return scripts[random.nextInt(scripts.length)];
  }

  @override
  Future<void> setVolume(double volume) async {
    _currentVolume = volume.clamp(0.0, 1.0);
    await _audioPlayer.setVolume(_currentVolume);
  }

  @override
  double get currentVolume => _currentVolume;

  @override
  bool get isEnabled => _isEnabled;

  @override
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
    if (!enabled && _audioPlayer.playing) {
      await _audioPlayer.stop();
    }
  }

  @override
  bool get isPlaying => _audioPlayer.playing;

  @override
  Future<void> stopCurrentNudge() async {
    if (_audioPlayer.playing) {
      await _audioPlayer.stop();
    }
  }

  @override
  Stream<bool> get playbackStateStream => _playbackStateController.stream;

  @override
  Future<List<String>> getAvailableVoiceTypes() async {
    return List.from(_availableVoiceTypes);
  }

  @override
  Future<void> setVoiceType(String voiceType) async {
    if (_availableVoiceTypes.contains(voiceType)) {
      _currentVoiceType = voiceType;
    }
  }

  @override
  String get currentVoiceType => _currentVoiceType;

  @override
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _playbackStateController.close();
  }
}
