import '../../domain/entities/ambient_sound_entity.dart';

/// Data model for ambient sounds, extends the entity for data layer operations
class AmbientSoundModel extends AmbientSoundEntity {
  const AmbientSoundModel({
    required super.id,
    required super.name,
    required super.description,
    required super.audioPath,
    super.duration,
    super.isSelected,
    required super.category,
  });

  /// Creates an AmbientSoundModel from JSON
  factory AmbientSoundModel.fromJson(Map<String, dynamic> json) {
    return AmbientSoundModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      audioPath: json['audioPath'] as String,
      duration: json['duration'] as int?,
      isSelected: json['isSelected'] as bool? ?? false,
      category: AmbientSoundCategory.values.firstWhere(
        (category) => category.name == json['category'],
        orElse: () => AmbientSoundCategory.nature,
      ),
    );
  }

  /// Converts this model to JSON
  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'audioPath': audioPath,
      'duration': duration,
      'isSelected': isSelected,
      'category': category.name,
    };
  }

  /// Creates an AmbientSoundModel from an AmbientSoundEntity
  factory AmbientSoundModel.fromEntity(AmbientSoundEntity entity) {
    return AmbientSoundModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      audioPath: entity.audioPath,
      duration: entity.duration,
      isSelected: entity.isSelected,
      category: entity.category,
    );
  }

  /// Creates a copy with updated values
  @override
  AmbientSoundModel copyWith({
    String? id,
    String? name,
    String? description,
    String? audioPath,
    int? duration,
    bool? isSelected,
    AmbientSoundCategory? category,
  }) {
    return AmbientSoundModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      audioPath: audioPath ?? this.audioPath,
      duration: duration ?? this.duration,
      isSelected: isSelected ?? this.isSelected,
      category: category ?? this.category,
    );
  }

  /// Gets the default ambient sounds available in the app
  static List<AmbientSoundModel> getDefaultSounds() {
    return [
      const AmbientSoundModel(
        id: 'rain',
        name: 'Rain',
        description: 'Gentle rainfall sounds for deep concentration',
        audioPath: 'assets/audio/ambient/rain.mp3',
        duration: 600, // 10 minutes
        category: AmbientSoundCategory.nature,
      ),
      const AmbientSoundModel(
        id: 'forest',
        name: 'Forest',
        description: 'Birds chirping and leaves rustling in a peaceful forest',
        audioPath: 'assets/audio/ambient/forest.mp3',
        duration: 600,
        category: AmbientSoundCategory.nature,
      ),
      const AmbientSoundModel(
        id: 'ocean',
        name: 'Ocean Waves',
        description: 'Rhythmic ocean waves for relaxation',
        audioPath: 'assets/audio/ambient/ocean.mp3',
        duration: 600,
        category: AmbientSoundCategory.nature,
      ),
      const AmbientSoundModel(
        id: 'white_noise',
        name: 'White Noise',
        description: 'Pure white noise for blocking distractions',
        audioPath: 'assets/audio/ambient/white_noise.mp3',
        duration: 600,
        category: AmbientSoundCategory.whiteNoise,
      ),
      const AmbientSoundModel(
        id: 'brown_noise',
        name: 'Brown Noise',
        description: 'Deep, rumbling brown noise for focus',
        audioPath: 'assets/audio/ambient/brown_noise.mp3',
        duration: 600,
        category: AmbientSoundCategory.brownNoise,
      ),
      const AmbientSoundModel(
        id: 'cafe',
        name: 'Coffee Shop',
        description: 'Ambient coffee shop sounds',
        audioPath: 'assets/audio/ambient/cafe.mp3',
        duration: 600,
        category: AmbientSoundCategory.urban,
      ),
    ];
  }
}
