import 'package:get/get.dart';
import 'package:power_up/features/focus_timer/presentation/controllers/focus_timer_controller.dart';
import 'package:power_up/features/focus_timer/domain/services/ambient_audio_service.dart';
import 'package:power_up/features/focus_timer/data/services/ambient_audio_service_impl.dart';
import 'package:power_up/features/focus_timer/domain/services/voice_nudges_service.dart';
import 'package:power_up/features/focus_timer/data/services/voice_nudges_service_impl.dart';
import 'package:power_up/features/core/data/api/api_client.dart';

/// Bindings for focus timer feature
class FocusTimerBindings implements Bindings {
  @override
  void dependencies() {
    // Services
    Get.lazyPut<AmbientAudioService>(() => AmbientAudioServiceImpl());
    Get.lazyPut<VoiceNudgesService>(
      () => VoiceNudgesServiceImpl(apiClient: Get.find<ApiClient>()),
    );

    // Controller
    Get.lazyPut<FocusTimerController>(
      () => FocusTimerController(
        ambientAudioService: Get.find<AmbientAudioService>(),
        voiceNudgesService: Get.find<VoiceNudgesService>(),
      ),
    );
  }
}
