import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/mood_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting mood analytics
class GetMoodAnalyticsUseCase
    implements UseCase<MoodAnalyticsEntity, GetMoodAnalyticsParams> {
  final AIReportRepository repository;

  GetMoodAnalyticsUseCase(this.repository);

  @override
  Future<Either<Failure, MoodAnalyticsEntity>> call(
    GetMoodAnalyticsParams params,
  ) async {
    return repository.getMoodAnalytics(period: params.period);
  }
}

/// Parameters for GetMoodAnalyticsUseCase
class GetMoodAnalyticsParams extends Equatable {
  final String period; // week, month, year

  const GetMoodAnalyticsParams({required this.period});

  @override
  List<Object> get props => [period];
}
