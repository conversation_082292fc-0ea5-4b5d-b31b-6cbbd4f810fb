import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/ai_reports/domain/entities/ai_improvement_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting AI improvement report
class GetAIImprovementReportUseCase
    implements
        UseCase<AIImprovementReportEntity, GetAIImprovementReportParams> {
  final AIReportRepository repository;

  GetAIImprovementReportUseCase(this.repository);

  @override
  Future<Either<Failure, AIImprovementReportEntity>> call(
    GetAIImprovementReportParams params,
  ) async {
    return await repository.getAIImprovementReport(days: params.days);
  }
}

/// Parameters for GetAIImprovementReportUseCase
class GetAIImprovementReportParams extends Equatable {
  final int days;

  const GetAIImprovementReportParams({this.days = 30});

  @override
  List<Object> get props => [days];
}
