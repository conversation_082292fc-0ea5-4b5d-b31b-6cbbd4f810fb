import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for recording user mood entries
class RecordMoodEntryUseCase implements UseCase<void, RecordMoodEntryParams> {
  final AIReportRepository repository;

  const RecordMoodEntryUseCase({required this.repository});

  @override
  Future<Either<Failure, void>> call(RecordMoodEntryParams params) async {
    return await repository.recordMoodEntry(mood: params.mood);
  }
}

class RecordMoodEntryParams extends Equatable {
  final String mood;

  const RecordMoodEntryParams({required this.mood});

  @override
  List<Object?> get props => [mood];
}
