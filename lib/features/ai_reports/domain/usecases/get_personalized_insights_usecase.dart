import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/personalized_insights_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting personalized insights and recommendations
class GetPersonalizedInsightsUseCase
    implements UseCase<PersonalizedInsightsEntity, NoParams> {
  final AIReportRepository repository;

  GetPersonalizedInsightsUseCase(this.repository);

  @override
  Future<Either<Failure, PersonalizedInsightsEntity>> call(
    NoParams params,
  ) async {
    return repository.getPersonalizedInsights();
  }
}
