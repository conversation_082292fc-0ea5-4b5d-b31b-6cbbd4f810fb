import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/habit_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting habit analytics
class GetHabitAnalyticsUseCase
    implements UseCase<HabitAnalyticsEntity, GetHabitAnalyticsParams> {
  final AIReportRepository repository;

  GetHabitAnalyticsUseCase(this.repository);

  @override
  Future<Either<Failure, HabitAnalyticsEntity>> call(
    GetHabitAnalyticsParams params,
  ) async {
    return repository.getHabitAnalytics(period: params.period);
  }
}

/// Parameters for GetHabitAnalyticsUseCase
class GetHabitAnalyticsParams extends Equatable {
  final String period; // week, month, year

  const GetHabitAnalyticsParams({required this.period});

  @override
  List<Object> get props => [period];
}
