import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting coaching suggestions
class GetCoachingSuggestionsUseCase
    implements UseCase<List<CoachingSuggestionEntity>, NoParams> {
  final AIReportRepository repository;

  GetCoachingSuggestionsUseCase(this.repository);

  @override
  Future<Either<Failure, List<CoachingSuggestionEntity>>> call(
    NoParams params,
  ) async {
    return repository.getCoachingSuggestions();
  }
}
