import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/progress_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting weekly progress report
class GetWeeklyProgressReportUseCase
    implements UseCase<ProgressReportEntity, NoParams> {
  final AIReportRepository repository;

  GetWeeklyProgressReportUseCase(this.repository);

  @override
  Future<Either<Failure, ProgressReportEntity>> call(NoParams params) async {
    return repository.getWeeklyProgressReport();
  }
}
