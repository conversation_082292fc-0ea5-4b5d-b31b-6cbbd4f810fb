import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/weekly_stats_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting weekly statistics
class GetWeeklyStatsUseCase implements UseCase<WeeklyStatsEntity, NoParams> {
  final AIReportRepository repository;

  GetWeeklyStatsUseCase(this.repository);

  @override
  Future<Either<Failure, WeeklyStatsEntity>> call(NoParams params) async {
    return repository.getWeeklyStats();
  }
}
