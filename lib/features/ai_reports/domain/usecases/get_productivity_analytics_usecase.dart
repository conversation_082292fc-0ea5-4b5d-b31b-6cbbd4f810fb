import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/productivity_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting productivity analytics
class GetProductivityAnalyticsUseCase
    implements
        UseCase<ProductivityAnalyticsEntity, GetProductivityAnalyticsParams> {
  final AIReportRepository repository;

  GetProductivityAnalyticsUseCase(this.repository);

  @override
  Future<Either<Failure, ProductivityAnalyticsEntity>> call(
    GetProductivityAnalyticsParams params,
  ) async {
    return repository.getProductivityAnalytics(period: params.period);
  }
}

/// Parameters for GetProductivityAnalyticsUseCase
class GetProductivityAnalyticsParams extends Equatable {
  final String period; // week, month

  const GetProductivityAnalyticsParams({required this.period});

  @override
  List<Object> get props => [period];
}
