import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/progress_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Use case for getting user progress analytics
class GetUserProgressUseCase implements UseCase<ProgressReportEntity, GetUserProgressParams> {
  final AIReportRepository repository;

  GetUserProgressUseCase(this.repository);

  @override
  Future<Either<Failure, ProgressReportEntity>> call(GetUserProgressParams params) async {
    return repository.getUserProgress(period: params.period);
  }
}

/// Parameters for GetUserProgressUseCase
class GetUserProgressParams extends Equatable {
  final String period; // day, week, month, year

  const GetUserProgressParams({
    required this.period,
  });

  @override
  List<Object> get props => [period];
}
