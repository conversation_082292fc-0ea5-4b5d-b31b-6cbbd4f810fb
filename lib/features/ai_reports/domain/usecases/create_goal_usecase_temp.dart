import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/ai_reports/domain/entities/goal_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/goal_repository.dart';

/// Use case for creating a new goal
class CreateGoalUseCase implements UseCase<GoalEntity, CreateGoalParams> {
  final GoalRepository repository;

  CreateGoalUseCase(this.repository);

  // Valid categories for goals
  static const _validCategories = [
    'personal',
    'professional',
    'health',
    'financial',
    'education',
    'relationship',
    'hobby',
  ];

  // Valid priorities for goals
  static const _validPriorities = ['low', 'medium', 'high', 'critical'];

  @override
  Future<Either<Failure, GoalEntity>> call(CreateGoalParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    return await repository.createGoal(
      title: params.title,
      description: params.description,
      category: params.category,
      priority: params.priority,
      targetDate: params.targetDate,
    );
  }

  ValidationFailure? _validateParams(CreateGoalParams params) {
    if (params.title.trim().isEmpty) {
      return const ValidationFailure(message: 'Title cannot be empty');
    }

    if (params.description.trim().isEmpty) {
      return const ValidationFailure(message: 'Description cannot be empty');
    }

    if (!_validCategories.contains(params.category)) {
      return const ValidationFailure(message: 'Invalid category');
    }

    if (!_validPriorities.contains(params.priority)) {
      return const ValidationFailure(message: 'Invalid priority');
    }

    if (params.targetDate.isBefore(DateTime.now())) {
      return const ValidationFailure(
        message: 'Target date must be in the future',
      );
    }

    return null;
  }
}

/// Parameters for creating a goal
class CreateGoalParams extends Equatable {
  final String title;
  final String description;
  final String category;
  final String priority;
  final DateTime targetDate;

  const CreateGoalParams({
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.targetDate,
  });

  @override
  List<Object?> get props => [
    title,
    description,
    category,
    priority,
    targetDate,
  ];
}
