import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/goal_entity.dart';
import '../repositories/goal_repository.dart';

class CreateGoalUseCase implements UseCase<GoalEntity, CreateGoalParams> {
  final GoalRepository repository;

  CreateGoalUseCase(this.repository);

  @override
  Future<Either<Failure, GoalEntity>> call(CreateGoalParams params) async {
    // Validate input parameters
    final validationFailure = _validateParams(params);
    if (validationFailure != null) {
      return Left(validationFailure);
    }

    return await repository.createGoal(
      title: params.title,
      description: params.description,
      category: params.category,
      priority: params.priority,
      targetDate: params.targetDate,
    );
  }

  ValidationFailure? _validateParams(CreateGoalParams params) {
    if (params.title.trim().isEmpty) {
      return const ValidationFailure(message: 'Title cannot be empty');
    }

    if (params.description.trim().isEmpty) {
      return const ValidationFailure(message: 'Description cannot be empty');
    }

    if (params.targetDate.isBefore(DateTime.now())) {
      return const ValidationFailure(
        message: 'Target date must be in the future',
      );
    }

    // Validate category
    const validCategories = [
      'health',
      'career',
      'personal',
      'education',
      'finance',
    ];
    if (!validCategories.contains(params.category)) {
      return const ValidationFailure(message: 'Invalid category');
    }

    // Validate priority
    const validPriorities = ['low', 'medium', 'high'];
    if (!validPriorities.contains(params.priority)) {
      return const ValidationFailure(message: 'Invalid priority');
    }

    return null;
  }
}

class CreateGoalParams extends Equatable {
  final String title;
  final String description;
  final String category;
  final String priority;
  final DateTime targetDate;

  const CreateGoalParams({
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.targetDate,
  });

  @override
  List<Object> get props => [
    title,
    description,
    category,
    priority,
    targetDate,
  ];
}
