import 'package:equatable/equatable.dart';

/// Entity for AI improvement report
class AIImprovementReportEntity extends Equatable {
  final int overallScore;
  final List<MetricEntity> metrics;
  final List<ImprovementAreaEntity> improvements;
  final List<PersonalityInsightEntity> personalityInsights;
  final List<String> strengths;
  final List<String> criticalAreas;
  final int projectedScore;
  final String analysisDateRange;
  final DateTime generatedAt;
  final String motivationalMessage;
  final bool aiEnhanced;

  const AIImprovementReportEntity({
    required this.overallScore,
    required this.metrics,
    required this.improvements,
    required this.personalityInsights,
    required this.strengths,
    required this.criticalAreas,
    required this.projectedScore,
    required this.analysisDateRange,
    required this.generatedAt,
    required this.motivationalMessage,
    required this.aiEnhanced,
  });

  @override
  List<Object?> get props => [
        overallScore,
        metrics,
        improvements,
        personalityInsights,
        strengths,
        criticalAreas,
        projectedScore,
        analysisDateRange,
        generatedAt,
        motivationalMessage,
        aiEnhanced,
      ];
}

/// Entity for metric data
class MetricEntity extends Equatable {
  final String name;
  final int currentScore;
  final int targetScore;
  final String trend;
  final double changePercentage;

  const MetricEntity({
    required this.name,
    required this.currentScore,
    required this.targetScore,
    required this.trend,
    required this.changePercentage,
  });

  @override
  List<Object?> get props => [
        name,
        currentScore,
        targetScore,
        trend,
        changePercentage,
      ];
}

/// Entity for improvement area
class ImprovementAreaEntity extends Equatable {
  final String area;
  final int currentScore;
  final int improvementPotential;
  final List<ActionEntity> actions;
  final String evidence;

  const ImprovementAreaEntity({
    required this.area,
    required this.currentScore,
    required this.improvementPotential,
    required this.actions,
    required this.evidence,
  });

  @override
  List<Object?> get props => [
        area,
        currentScore,
        improvementPotential,
        actions,
        evidence,
      ];
}

/// Entity for action item
class ActionEntity extends Equatable {
  final String title;
  final String description;
  final String impact;
  final String difficulty;
  final int timeToResults;
  final String category;

  const ActionEntity({
    required this.title,
    required this.description,
    required this.impact,
    required this.difficulty,
    required this.timeToResults,
    required this.category,
  });

  @override
  List<Object?> get props => [
        title,
        description,
        impact,
        difficulty,
        timeToResults,
        category,
      ];
}

/// Entity for personality insight
class PersonalityInsightEntity extends Equatable {
  final String insight;
  final String category;
  final int confidence;

  const PersonalityInsightEntity({
    required this.insight,
    required this.category,
    required this.confidence,
  });

  @override
  List<Object?> get props => [
        insight,
        category,
        confidence,
      ];
}
