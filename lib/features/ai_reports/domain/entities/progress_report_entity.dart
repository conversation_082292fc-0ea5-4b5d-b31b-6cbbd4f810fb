import 'package:equatable/equatable.dart';

class ProgressReportEntity extends Equatable {
  final double overallScore;
  final List<DailyProgressEntity> dailyProgress;
  final int totalTasksCompleted;
  final int totalHabitsCompleted;
  final double changeFromPreviousPeriod;

  const ProgressReportEntity({
    required this.overallScore,
    required this.dailyProgress,
    required this.totalTasksCompleted,
    required this.totalHabitsCompleted,
    required this.changeFromPreviousPeriod,
  });

  @override
  List<Object?> get props => [
    overallScore,
    dailyProgress,
    totalTasksCompleted,
    totalHabitsCompleted,
    changeFromPreviousPeriod,
  ];
}

class DailyProgressEntity extends Equatable {
  final DateTime date;
  final double score;
  final int tasksCompleted;
  final int habitsCompleted;
  final double learningProgress;

  const DailyProgressEntity({
    required this.date,
    required this.score,
    required this.tasksCompleted,
    required this.habitsCompleted,
    required this.learningProgress,
  });

  @override
  List<Object?> get props => [
    date,
    score,
    tasksCompleted,
    habitsCompleted,
    learningProgress,
  ];
}
