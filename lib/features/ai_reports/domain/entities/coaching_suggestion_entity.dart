import 'package:equatable/equatable.dart';

class CoachingSuggestionEntity extends Equatable {
  final String id;
  final String suggestionText;
  final String? relatedHabitId;
  final String? relatedTaskId;
  final String impactArea;
  final String priority; // low, medium, high
  final String type; // insight, recommendation, tip
  final DateTime createdAt;

  const CoachingSuggestionEntity({
    required this.id,
    required this.suggestionText,
    this.relatedHabitId,
    this.relatedTaskId,
    required this.impactArea,
    required this.priority,
    required this.type,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [
    id,
    suggestionText,
    relatedHabitId,
    relatedTaskId,
    impactArea,
    priority,
    type,
    createdAt,
  ];
}
