import 'package:equatable/equatable.dart';

class WeeklyStatsEntity extends Equatable {
  final double totalFocusTime;
  final double? avgDailyConsistency;
  final double? habitCompletionRate;
  final List<String> topPerformingDays;

  const WeeklyStatsEntity({
    required this.totalFocusTime,
    this.avgDailyConsistency,
    this.habitCompletionRate,
    required this.topPerformingDays,
  });

  @override
  List<Object?> get props => [
    totalFocusTime,
    avgDailyConsistency,
    habitCompletionRate,
    topPerformingDays,
  ];
}
