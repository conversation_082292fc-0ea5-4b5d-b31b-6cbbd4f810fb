import 'package:equatable/equatable.dart';

/// Domain entity representing a user goal
class GoalEntity extends Equatable {
  final String id;
  final String title;
  final String description;
  final String category;
  final String priority;
  final DateTime targetDate;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double? progress;

  const GoalEntity({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.targetDate,
    required this.isCompleted,
    required this.createdAt,
    required this.updatedAt,
    this.progress,
  });

  /// Get progress as a percentage (0.0 to 1.0)
  double get progressPercentage => progress ?? 0.0;

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    category,
    priority,
    targetDate,
    isCompleted,
    createdAt,
    updatedAt,
    progress,
  ];
}
