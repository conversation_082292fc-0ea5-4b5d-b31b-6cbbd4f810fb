import 'package:equatable/equatable.dart';

class TaskCompletionStatsEntity extends Equatable {
  final double overallCompletionRate;
  final double onTimeCompletionRate;
  final List<TaskCompletionByDayEntity> completionByDay;
  final List<TaskCompletionByPriorityEntity> completionByPriority;
  final double averageTasksPerDay;
  final String mostProductiveDay;

  const TaskCompletionStatsEntity({
    required this.overallCompletionRate,
    required this.onTimeCompletionRate,
    required this.completionByDay,
    required this.completionByPriority,
    required this.averageTasksPerDay,
    required this.mostProductiveDay,
  });

  @override
  List<Object?> get props => [
    overallCompletionRate,
    onTimeCompletionRate,
    completionByDay,
    completionByPriority,
    averageTasksPerDay,
    mostProductiveDay,
  ];
}

class TaskCompletionByDayEntity extends Equatable {
  final String dayOfWeek;
  final double completionRate;
  final int tasksCompleted;

  const TaskCompletionByDayEntity({
    required this.dayOfWeek,
    required this.completionRate,
    required this.tasksCompleted,
  });

  @override
  List<Object?> get props => [dayOfWeek, completionRate, tasksCompleted];
}

class TaskCompletionByPriorityEntity extends Equatable {
  final String priority; // low, medium, high
  final double completionRate;
  final int tasksCompleted;

  const TaskCompletionByPriorityEntity({
    required this.priority,
    required this.completionRate,
    required this.tasksCompleted,
  });

  @override
  List<Object?> get props => [priority, completionRate, tasksCompleted];
}
