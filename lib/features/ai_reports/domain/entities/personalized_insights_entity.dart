import 'package:equatable/equatable.dart';

class PersonalizedInsightsEntity extends Equatable {
  final List<InsightEntity> insights;
  final List<RecommendationEntity> recommendations;
  final List<String> strengths;
  final List<String> improvementAreas;

  const PersonalizedInsightsEntity({
    required this.insights,
    required this.recommendations,
    required this.strengths,
    required this.improvementAreas,
  });

  @override
  List<Object?> get props => [
    insights,
    recommendations,
    strengths,
    improvementAreas,
  ];
}

class InsightEntity extends Equatable {
  final String title;
  final String description;
  final String category; // productivity, habits, mood, etc.
  final String priority; // low, medium, high

  const InsightEntity({
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
  });

  @override
  List<Object?> get props => [title, description, category, priority];
}

class RecommendationEntity extends Equatable {
  final String text;
  final String impactArea;
  final String priority; // low, medium, high

  const RecommendationEntity({
    required this.text,
    required this.impactArea,
    required this.priority,
  });

  @override
  List<Object?> get props => [text, impactArea, priority];
}
