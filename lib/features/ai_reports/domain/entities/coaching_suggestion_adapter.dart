import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/ai_improvement_report_entity.dart';

/// Adapter to convert ActionEntity to CoachingSuggestionEntity for backward compatibility
class CoachingSuggestionAdapter {
  static CoachingSuggestionEntity fromAction(ActionEntity action) {
    return CoachingSuggestionEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      suggestionText: action.description,
      relatedHabitId: null, // Not available in ActionEntity
      relatedTaskId: null, // Not available in ActionEntity
      impactArea: action.category,
      priority: action.impact, // Using impact as priority
      type: 'recommendation',
      createdAt: DateTime.now(),
    );
  }

  static List<CoachingSuggestionEntity> fromActions(List<ActionEntity> actions) {
    return actions.map((action) => fromAction(action)).toList();
  }
}
