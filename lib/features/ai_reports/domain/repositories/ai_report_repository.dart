import 'package:dartz/dartz.dart';
import 'package:power_up/core/domain/repositories/repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/ai_reports/domain/entities/progress_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/habit_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/productivity_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/mood_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/personalized_insights_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/weekly_stats_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/task_completion_stats_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/ai_improvement_report_entity.dart';

/// Repository interface for AI reports and analytics
abstract class AIReportRepository extends Repository {
  /// Get user progress analytics for a specified period
  /// Returns Either [Failure] or [ProgressReportEntity]
  Future<Either<Failure, ProgressReportEntity>> getUserProgress({
    required String period, // day, week, month, year
  });

  /// Get detailed habit analytics for a specified period
  /// Returns Either [Failure] or [HabitAnalyticsEntity]
  Future<Either<Failure, HabitAnalyticsEntity>> getHabitAnalytics({
    required String period, // week, month, year
  });

  /// Get productivity analytics for a specified period
  /// Returns Either [Failure] or [ProductivityAnalyticsEntity]
  Future<Either<Failure, ProductivityAnalyticsEntity>>
  getProductivityAnalytics({
    required String period, // week, month
  });

  /// Get mood analytics for a specified period
  /// Returns Either [Failure] or [MoodAnalyticsEntity]
  Future<Either<Failure, MoodAnalyticsEntity>> getMoodAnalytics({
    required String period, // week, month, year
  });

  /// Get personalized insights and recommendations
  /// Returns Either [Failure] or [PersonalizedInsightsEntity]
  Future<Either<Failure, PersonalizedInsightsEntity>> getPersonalizedInsights();

  /// Get aggregated weekly statistics
  /// Returns Either [Failure] or [WeeklyStatsEntity]
  Future<Either<Failure, WeeklyStatsEntity>> getWeeklyStats();

  /// Get habit correlations with mood and productivity
  /// Returns Either [Failure] or [MoodAnalyticsEntity]
  Future<Either<Failure, MoodAnalyticsEntity>> getHabitCorrelations({
    required String period,
  });

  /// Get current streak milestones for habits
  /// Returns Either [Failure] or [PersonalizedInsightsEntity]
  Future<Either<Failure, PersonalizedInsightsEntity>> getStreakMilestones();

  /// Get task completion analytics
  /// Returns Either [Failure] or [TaskCompletionStatsEntity]
  Future<Either<Failure, TaskCompletionStatsEntity>> getTaskCompletionStats({
    String? period, // week, month (optional)
  });

  /// Generate a weekly progress report
  /// Returns Either [Failure] or [ProgressReportEntity]
  Future<Either<Failure, ProgressReportEntity>> getWeeklyProgressReport();

  /// Get coaching suggestions based on user data
  /// Returns Either [Failure] or a list of [CoachingSuggestionEntity]
  Future<Either<Failure, List<CoachingSuggestionEntity>>>
  getCoachingSuggestions();

  /// Record a mood entry
  /// Returns Either [Failure] or void on success
  Future<Either<Failure, void>> recordMoodEntry({
    required String mood, // great, good, neutral, bad, terrible
  });

  /// Record a completed focus session
  /// Returns Either [Failure] or void on success
  Future<Either<Failure, void>> recordFocusSession({required int minutes});

  /// Get AI improvement report with personalized recommendations
  /// Returns Either [Failure] or [AIImprovementReportEntity]
  Future<Either<Failure, AIImprovementReportEntity>> getAIImprovementReport({
    int days = 30, // Number of days to analyze (7-365)
  });
}
