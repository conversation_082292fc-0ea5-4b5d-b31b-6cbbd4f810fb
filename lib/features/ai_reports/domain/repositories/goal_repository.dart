import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/ai_reports/domain/entities/goal_entity.dart';

/// Repository interface for goal operations
abstract class GoalRepository {
  /// Create a new goal
  Future<Either<Failure, GoalEntity>> createGoal({
    required String title,
    required String description,
    required String category,
    required String priority,
    required DateTime targetDate,
  });

  /// Get all user goals
  Future<Either<Failure, List<GoalEntity>>> getGoals();

  /// Get a specific goal by ID
  Future<Either<Failure, GoalEntity>> getGoalById(String id);

  /// Update an existing goal
  Future<Either<Failure, GoalEntity>> updateGoal({
    required String id,
    String? title,
    String? description,
    String? category,
    String? priority,
    DateTime? targetDate,
    double? progress,
  });

  /// Delete a goal
  Future<Either<Failure, void>> deleteGoal(String id);

  /// Mark goal as completed
  Future<Either<Failure, GoalEntity>> markGoalComplete(String id);

  /// Get active goals (not completed)
  Future<Either<Failure, List<GoalEntity>>> getActiveGoals();
}
