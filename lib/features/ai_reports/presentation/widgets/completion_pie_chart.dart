import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

/// Widget for displaying task and habit completion as a pie chart
class CompletionPieChart extends StatelessWidget {
  final Map<String, int> data;

  const CompletionPieChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final tasksCompleted = data['tasks'] ?? 0;
    final habitsCompleted = data['habits'] ?? 0;
    final total = tasksCompleted + habitsCompleted;

    if (total == 0) {
      return _buildEmptyState(context);
    }

    return Row(
      children: [
        // Pie Chart
        Expanded(
          flex: 2,
          child: SizedBox(
            height: 150,
            child: PieChart(
              PieChartData(
                sections: _buildSections(
                  theme,
                  tasksCompleted,
                  habitsCompleted,
                ),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
                pieTouchData: PieTouchData(
                  touchCallback: (FlTouchEvent event, pieTouchResponse) {
                    // Handle touch events if needed
                  },
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 20),
        // Legend
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLegendItem(
                context,
                color: theme.colorScheme.primary,
                label: 'Tasks',
                value: tasksCompleted,
                percentage:
                    total > 0 ? (tasksCompleted / total * 100).round() : 0,
              ),
              const SizedBox(height: 12),
              _buildLegendItem(
                context,
                color: theme.colorScheme.secondary,
                label: 'Habits',
                value: habitsCompleted,
                percentage:
                    total > 0 ? (habitsCompleted / total * 100).round() : 0,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  'Total: $total',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<PieChartSectionData> _buildSections(
    ThemeData theme,
    int tasksCompleted,
    int habitsCompleted,
  ) {
    final total = tasksCompleted + habitsCompleted;

    return [
      PieChartSectionData(
        color: theme.colorScheme.primary,
        value: tasksCompleted.toDouble(),
        title: total > 0 ? '${(tasksCompleted / total * 100).round()}%' : '0%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        titlePositionPercentageOffset: 0.6,
      ),
      PieChartSectionData(
        color: theme.colorScheme.secondary,
        value: habitsCompleted.toDouble(),
        title: total > 0 ? '${(habitsCompleted / total * 100).round()}%' : '0%',
        radius: 50,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        titlePositionPercentageOffset: 0.6,
      ),
    ];
  }

  Widget _buildLegendItem(
    BuildContext context, {
    required Color color,
    required String label,
    required int value,
    required int percentage,
  }) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$value ($percentage%)',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 150,
      decoration: BoxDecoration(
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.pie_chart_outline,
              size: 48,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 8),
            Text(
              'No completion data available',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
