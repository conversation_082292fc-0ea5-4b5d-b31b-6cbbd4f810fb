import 'package:flutter/material.dart';

/// Widget for displaying weekly overview data
class WeeklyOverviewCard extends StatelessWidget {
  final Map<String, dynamic> weeklyData;

  const WeeklyOverviewCard({super.key, required this.weeklyData});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.insights,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Weekly Overview',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Stats grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              children: [
                _buildStatItem(
                  context,
                  title: 'Focus Time',
                  value:
                      '${weeklyData['totalFocusTime']?.toStringAsFixed(0) ?? '0'}m',
                  icon: Icons.timer,
                  color: Colors.orange,
                ),
                _buildStatItem(
                  context,
                  title: 'Daily Consistency',
                  value:
                      '${weeklyData['avgDailyConsistency']?.toStringAsFixed(1) ?? '0.0'}%',
                  icon: Icons.trending_up,
                  color: Colors.green,
                ),
                _buildStatItem(
                  context,
                  title: 'Habit Completion',
                  value:
                      '${weeklyData['habitCompletionRate']?.toStringAsFixed(1) ?? '0.0'}%',
                  icon: Icons.repeat,
                  color: theme.colorScheme.secondary,
                ),
                _buildStatItem(
                  context,
                  title: 'Top Days',
                  value:
                      (weeklyData['topPerformingDays'] as List<String>?)?.length
                          .toString() ??
                      '0',
                  icon: Icons.star,
                  color: theme.colorScheme.primary,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Most productive day
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.star, color: theme.colorScheme.primary, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Most Productive Day: ',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    weeklyData['mostProductiveDay']?.toString() ?? 'N/A',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
