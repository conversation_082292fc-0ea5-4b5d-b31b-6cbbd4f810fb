import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/domain/entities/goal_entity.dart';
import 'package:power_up/features/ai_reports/presentation/controllers/ai_reports_controller.dart';

/// A widget for displaying and managing user goals
class GoalListWidget extends StatelessWidget {
  const GoalListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AiReportsController>();

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.track_changes,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Your Goals',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Obx(
                  () => Chip(
                    label: Text('${controller.goals.length} goals'),
                    backgroundColor: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.1),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Filter tabs
            DefaultTabController(
              length: 3,
              child: Column(
                children: [
                  TabBar(
                    labelColor: Theme.of(context).primaryColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: Theme.of(context).primaryColor,
                    tabs: const [
                      Tab(text: 'Active'),
                      Tab(text: 'Completed'),
                      Tab(text: 'All'),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 400,
                    child: TabBarView(
                      children: [
                        _buildGoalsList(
                          controller,
                          controller.getActiveGoals(),
                        ),
                        _buildGoalsList(
                          controller,
                          controller.getCompletedGoals(),
                        ),
                        _buildGoalsList(controller, controller.goals),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsList(
    AiReportsController controller,
    List<GoalEntity> goals,
  ) {
    return Obx(() {
      if (goals.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.flag_outlined, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'No goals found',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
              SizedBox(height: 8),
              Text(
                'Create your first goal to get started!',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: goals.length,
        itemBuilder: (context, index) {
          final goal = goals[index];
          return _buildGoalCard(goal, controller);
        },
      );
    });
  }

  Widget _buildGoalCard(GoalEntity goal, AiReportsController controller) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    goal.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildPriorityChip(goal.priority),
              ],
            ),
            const SizedBox(height: 8),

            Text(
              goal.description,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                _buildCategoryChip(goal.category),
                const SizedBox(width: 8),
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Target: ${goal.targetDate.day}/${goal.targetDate.month}/${goal.targetDate.year}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Progress bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progress',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    Text(
                      '${(goal.progressPercentage * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _getProgressColor(goal.progressPercentage),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: goal.progressPercentage,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getProgressColor(goal.progressPercentage),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (!goal.isCompleted) ...[
                  TextButton.icon(
                    onPressed:
                        () => _showUpdateProgressDialog(goal, controller),
                    icon: const Icon(Icons.trending_up, size: 16),
                    label: const Text('Update Progress'),
                  ),
                  const SizedBox(width: 8),
                ],
                TextButton.icon(
                  onPressed: () => _showGoalDetails(goal),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('View'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;

    switch (priority) {
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_double_arrow_up;
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.orange;
        icon = Icons.keyboard_arrow_up;
    }

    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            priority.toUpperCase(),
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color.withValues(alpha: 0.3)),
    );
  }

  Widget _buildCategoryChip(String category) {
    IconData icon;

    switch (category) {
      case 'health':
        icon = Icons.favorite;
        break;
      case 'career':
        icon = Icons.work;
        break;
      case 'personal':
        icon = Icons.person;
        break;
      case 'education':
        icon = Icons.school;
        break;
      case 'finance':
        icon = Icons.attach_money;
        break;
      default:
        icon = Icons.flag;
    }

    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14),
          const SizedBox(width: 4),
          Text(category.toUpperCase(), style: const TextStyle(fontSize: 12)),
        ],
      ),
      backgroundColor: Colors.grey[100],
    );
  }

  Color _getProgressColor(double progress) {
    if (progress < 0.3) return Colors.red;
    if (progress < 0.7) return Colors.orange;
    return Colors.green;
  }

  void _showUpdateProgressDialog(
    GoalEntity goal,
    AiReportsController controller,
  ) {
    double newProgress = goal.progressPercentage;

    Get.dialog(
      AlertDialog(
        title: Text('Update Progress: ${goal.title}'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Current Progress: ${(goal.progressPercentage * 100).toInt()}%',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: newProgress,
                  min: 0.0,
                  max: 1.0,
                  divisions: 100,
                  label: '${(newProgress * 100).toInt()}%',
                  onChanged: (value) {
                    setState(() {
                      newProgress = value;
                    });
                  },
                ),
                Text(
                  'New Progress: ${(newProgress * 100).toInt()}%',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement update progress in controller
              Get.back();
              Get.snackbar(
                'Success',
                'Goal progress updated!',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showGoalDetails(GoalEntity goal) {
    Get.dialog(
      AlertDialog(
        title: Text(goal.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Description:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(goal.description),
              const SizedBox(height: 16),

              Text(
                'Category:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(goal.category.toUpperCase()),
              const SizedBox(height: 16),

              Text(
                'Priority:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(goal.priority.toUpperCase()),
              const SizedBox(height: 16),

              Text(
                'Target Date:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '${goal.targetDate.day}/${goal.targetDate.month}/${goal.targetDate.year}',
              ),
              const SizedBox(height: 16),

              Text(
                'Progress:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text('${(goal.progressPercentage * 100).toInt()}%'),
              const SizedBox(height: 8),

              LinearProgressIndicator(
                value: goal.progressPercentage,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getProgressColor(goal.progressPercentage),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Close')),
        ],
      ),
    );
  }
}
