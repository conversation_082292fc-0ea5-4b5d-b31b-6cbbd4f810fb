import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/core/routes/app_routes.dart';

/// A widget that displays coaching suggestions related to a specific task
class TaskCoachingSuggestionsWidget extends StatelessWidget {
  final List<CoachingSuggestionEntity> suggestions;
  final String taskId;

  const TaskCoachingSuggestionsWidget({
    super.key,
    required this.suggestions,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Filter suggestions related to this task
    final taskSuggestions =
        suggestions.where((s) => s.relatedTaskId == taskId).toList();

    if (taskSuggestions.isEmpty) {
      return const SizedBox.shrink(); // Don't show if no related suggestions
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // AI Advice Disclaimer
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          child: Text(
            '* AI-generated suggestions. Not a substitute for professional advice.',
            style: theme.textTheme.bodySmall?.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.grey[600],
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.purple.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.psychology, color: theme.primaryColor),
                  const SizedBox(width: 8),
                  Text(
                    'Task Insights',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // List of coaching suggestions for this task
              ...taskSuggestions.map(
                (suggestion) => _buildSuggestionTile(suggestion, theme),
              ),

              // Link to all coaching suggestions
              Center(
                child: TextButton.icon(
                  onPressed: () => Get.toNamed(AppRoutes.coachingSuggestions),
                  icon: const Icon(Icons.view_list, size: 16),
                  label: const Text('View all coaching suggestions'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSuggestionTile(
    CoachingSuggestionEntity suggestion,
    ThemeData theme,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.lightbulb_outline, size: 16, color: theme.primaryColor),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              suggestion.suggestionText,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
