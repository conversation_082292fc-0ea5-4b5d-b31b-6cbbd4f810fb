import 'package:flutter/material.dart';

/// A widget for tracking user mood with visual selection interface
class MoodTrackerWidget extends StatelessWidget {
  final String? selectedMood;
  final ValueChanged<String> onMoodSelected;

  const MoodTrackerWidget({
    super.key,
    required this.selectedMood,
    required this.onMoodSelected,
  });

  static const Map<String, Map<String, String>> _moodOptions = {
    'great': {'emoji': '😄', 'label': 'Great'},
    'good': {'emoji': '😊', 'label': 'Good'},
    'neutral': {'emoji': '😐', 'label': 'Neutral'},
    'bad': {'emoji': '😟', 'label': 'Bad'},
    'terrible': {'emoji': '😢', 'label': 'Terrible'},
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'How are you feeling today?',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children:
                _moodOptions.entries.map((entry) {
                  final moodKey = entry.key;
                  final moodData = entry.value;
                  final isSelected = selectedMood == moodKey;

                  return _buildMoodOption(
                    context,
                    moodKey,
                    moodData['emoji']!,
                    moodData['label']!,
                    isSelected,
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMoodOption(
    BuildContext context,
    String moodKey,
    String emoji,
    String label,
    bool isSelected,
  ) {
    return Column(
      children: [
        InkWell(
          onTap: () => onMoodSelected(moodKey),
          borderRadius: BorderRadius.circular(30),
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color:
                  isSelected
                      ? Theme.of(context).primaryColor.withValues(alpha: 0.2)
                      : Colors.grey.withValues(alpha: 0.1),
              border: Border.all(
                color:
                    isSelected
                        ? Theme.of(context).primaryColor
                        : Colors.grey.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Center(
              child: Text(emoji, style: const TextStyle(fontSize: 28)),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color:
                isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).textTheme.bodySmall?.color,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
