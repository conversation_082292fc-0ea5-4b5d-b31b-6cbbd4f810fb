import 'package:flutter/material.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/coaching_suggestion_card.dart';

/// A widget that displays a list of coaching suggestions grouped by priority
class CoachingSuggestionsSection extends StatefulWidget {
  final List<CoachingSuggestionEntity> suggestions;
  final String title;
  final String emptyMessage;

  const CoachingSuggestionsSection({
    super.key,
    required this.suggestions,
    this.title = 'Coaching Suggestions',
    this.emptyMessage = 'No coaching suggestions available',
  });

  @override
  State<CoachingSuggestionsSection> createState() =>
      _CoachingSuggestionsSectionState();
}

class _CoachingSuggestionsSectionState
    extends State<CoachingSuggestionsSection> {
  String? _expandedId;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (widget.suggestions.isEmpty) {
      return _buildEmptyState(theme);
    }

    // Group suggestions by priority
    final Map<String, List<CoachingSuggestionEntity>> groupedSuggestions = {
      'high': [],
      'medium': [],
      'low': [],
    };

    for (final suggestion in widget.suggestions) {
      if (groupedSuggestions.containsKey(suggestion.priority)) {
        groupedSuggestions[suggestion.priority]!.add(suggestion);
      } else {
        groupedSuggestions['medium']!.add(
          suggestion,
        ); // Default to medium if unknown priority
      }
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.psychology, color: theme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  widget.title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // High priority suggestions
          if (groupedSuggestions['high']!.isNotEmpty)
            _buildPrioritySection(
              'High Priority',
              groupedSuggestions['high']!,
              theme,
              Colors.red.shade50,
            ),

          // Medium priority suggestions
          if (groupedSuggestions['medium']!.isNotEmpty)
            _buildPrioritySection(
              'Recommended',
              groupedSuggestions['medium']!,
              theme,
              Colors.orange.shade50,
            ),

          // Low priority suggestions
          if (groupedSuggestions['low']!.isNotEmpty)
            _buildPrioritySection(
              'Suggestions',
              groupedSuggestions['low']!,
              theme,
              Colors.blue.shade50,
            ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildPrioritySection(
    String title,
    List<CoachingSuggestionEntity> suggestions,
    ThemeData theme,
    Color backgroundColor,
  ) {
    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.primaryColor,
              ),
            ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: suggestions.length,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemBuilder: (context, index) {
            final suggestion = suggestions[index];
            return CoachingSuggestionCard(
              suggestion: suggestion,
              isExpanded: _expandedId == suggestion.id,
              onTap: () {
                setState(() {
                  if (_expandedId == suggestion.id) {
                    _expandedId = null;
                  } else {
                    _expandedId = suggestion.id;
                  }
                });
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(Icons.psychology, size: 48, color: theme.disabledColor),
          const SizedBox(height: 16),
          Text(
            widget.emptyMessage,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.disabledColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Complete more activities to get personalized coaching suggestions',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.disabledColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
