import 'package:flutter/material.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';

/// A card widget displaying a single coaching suggestion
class CoachingSuggestionCard extends StatelessWidget {
  final CoachingSuggestionEntity suggestion;
  final VoidCallback? onTap;
  final bool isExpanded;

  const CoachingSuggestionCard({
    super.key,
    required this.suggestion,
    this.onTap,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Map priority to colors
    final priorityColors = {
      'high': Colors.red.shade100,
      'medium': Colors.orange.shade100,
      'low': Colors.blue.shade100,
    };

    // Map type to icons
    final typeIcons = {
      'insight': Icons.lightbulb_outline,
      'recommendation': Icons.stars,
      'tip': Icons.tips_and_updates,
    };

    final backgroundColor =
        priorityColors[suggestion.priority] ?? Colors.grey.shade100;
    final icon = typeIcons[suggestion.type] ?? Icons.assistant;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: backgroundColor, width: 2),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Suggestion icon based on type
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: theme.primaryColor),
                  ),
                  const SizedBox(width: 12),

                  // Suggestion text
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          suggestion.suggestionText,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isExpanded) ...[
                          const SizedBox(height: 8),
                          Text(
                            'Impact area: ${suggestion.impactArea.toUpperCase()}',
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),

              // Additional details when expanded
              if (isExpanded) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Chip(
                      label: Text(
                        suggestion.priority.toUpperCase(),
                        style: TextStyle(
                          color: theme.primaryColor,
                          fontSize: 12,
                        ),
                      ),
                      backgroundColor: backgroundColor,
                      padding: EdgeInsets.zero,
                    ),
                    Text(
                      suggestion.type.toUpperCase(),
                      style: theme.textTheme.bodySmall,
                    ),
                  ],
                ),

                // Related item reference if available
                if (suggestion.relatedHabitId != null ||
                    suggestion.relatedTaskId != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      suggestion.relatedHabitId != null
                          ? 'Related to a habit'
                          : 'Related to a task',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
