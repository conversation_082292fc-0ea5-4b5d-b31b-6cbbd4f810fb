import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/presentation/controllers/ai_reports_controller.dart';

/// An integrated mood tracker widget that connects to the AI reports controller
class IntegratedMoodTrackerWidget extends StatelessWidget {
  const IntegratedMoodTrackerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AiReportsController>();

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.sentiment_satisfied_alt,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'How are you feeling today?',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              final moodOptions = controller.getMoodOptions();
              return Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    moodOptions.map((option) {
                      final isSelected =
                          controller.currentMood == option['value'];
                      return GestureDetector(
                        onTap:
                            controller.isMoodRecording
                                ? null
                                : () =>
                                    controller.recordMoodEntry(option['value']),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? (option['color'] as Color).withValues(
                                      alpha: 0.2,
                                    )
                                    : Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? option['color'] as Color
                                      : Colors.grey.withValues(alpha: 0.3),
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: Text(
                            option['label'] as String,
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? option['color'] as Color
                                      : Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.color,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
              );
            }),
            const SizedBox(height: 12),
            Obx(() {
              if (controller.isMoodRecording) {
                return const Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 8),
                    Text('Recording mood...'),
                  ],
                );
              }
              if (controller.currentMood.isNotEmpty) {
                return Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).primaryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Mood recorded for today',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 12,
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }
}
