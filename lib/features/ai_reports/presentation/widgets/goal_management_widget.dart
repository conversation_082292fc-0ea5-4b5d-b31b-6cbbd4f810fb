import 'package:flutter/material.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/goal_input_widget.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/goal_list_widget.dart';

/// A comprehensive goal management widget that combines goal creation and listing
class GoalManagementWidget extends StatelessWidget {
  const GoalManagementWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TabBar(
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Theme.of(context).primaryColor,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              tabs: const [
                Tab(icon: Icon(Icons.add_circle_outline), text: 'Create Goal'),
                Tab(icon: Icon(Icons.list_alt), text: 'My Goals'),
              ],
            ),
          ),
          const Expanded(
            child: TabBarView(
              children: [
                SingleChildScrollView(child: GoalInputWidget()),
                GoalListWidget(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
