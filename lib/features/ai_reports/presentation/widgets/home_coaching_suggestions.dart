import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/core/routes/app_routes.dart';

/// A compact widget for displaying high-priority coaching suggestions on the home screen
class HomeSuggestionCard extends StatelessWidget {
  final CoachingSuggestionEntity suggestion;

  const HomeSuggestionCard({super.key, required this.suggestion});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Map type to icons
    final typeIcons = {
      'insight': Icons.lightbulb_outline,
      'recommendation': Icons.stars,
      'tip': Icons.tips_and_updates,
    };

    final icon = typeIcons[suggestion.type] ?? Icons.psychology;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: theme.primaryColor.withValues(alpha: 0.2)),
      ),
      child: Ink<PERSON>ell(
        onTap: () => Get.toNamed(AppRoutes.coachingSuggestions),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: theme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: theme.primaryColor),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.psychology,
                          size: 16,
                          color: theme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Coaching Insight',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      suggestion.suggestionText,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}

/// Widget that displays a section of coaching suggestions on the home screen
class HomeCoachingSuggestionsSection extends StatelessWidget {
  final List<CoachingSuggestionEntity> suggestions;
  final int maxSuggestions;

  const HomeCoachingSuggestionsSection({
    super.key,
    required this.suggestions,
    this.maxSuggestions = 2,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (suggestions.isEmpty) {
      return const SizedBox.shrink(); // Don't show anything if no suggestions
    }

    // Filter to show only high priority or top suggestions
    List<CoachingSuggestionEntity> displaySuggestions =
        suggestions.where((s) => s.priority == 'high').toList();

    // If no high priority, take any priority
    if (displaySuggestions.isEmpty) {
      displaySuggestions = suggestions;
    }

    // Limit to max suggestions
    final limitedSuggestions = displaySuggestions.take(maxSuggestions).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // AI Advice Disclaimer
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          child: Text(
            '* AI-generated suggestions. Not a substitute for professional advice.',
            style: theme.textTheme.bodySmall?.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.grey[600],
            ),
          ),
        ),
        // Section header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(Icons.psychology, color: theme.primaryColor, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Coaching For You',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              TextButton(
                onPressed: () => Get.toNamed(AppRoutes.coachingSuggestions),
                child: const Text('View All'),
              ),
            ],
          ),
        ),

        // Suggestions
        ...limitedSuggestions.map(
          (suggestion) => HomeSuggestionCard(suggestion: suggestion),
        ),

        // "More" indicator if there are more suggestions
        if (suggestions.length > maxSuggestions)
          Center(
            child: TextButton.icon(
              onPressed: () => Get.toNamed(AppRoutes.coachingSuggestions),
              icon: const Icon(Icons.more_horiz),
              label: Text(
                '${suggestions.length - maxSuggestions} more suggestions',
                style: theme.textTheme.bodySmall,
              ),
            ),
          ),
      ],
    );
  }
}
