import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/presentation/controllers/ai_reports_controller.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/progress_summary_card.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/insights_section.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/productivity_chart.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/completion_pie_chart.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/weekly_overview_card.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/tips_section.dart';

/// Screen for displaying progress reports with data visualization
class ProgressReportScreen extends StatefulWidget {
  const ProgressReportScreen({super.key});

  @override
  State<ProgressReportScreen> createState() => _ProgressReportScreenState();
}

class _ProgressReportScreenState extends State<ProgressReportScreen> {
  final AiReportsController controller = Get.find<AiReportsController>();

  @override
  void initState() {
    super.initState();
    // Load data if not already loaded
    if (controller.progressReport == null) {
      controller.loadInitialData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Progress Report',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Period selector
          Obx(
            () => DropdownButton<String>(
              value: controller.selectedPeriod,
              icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
              dropdownColor: Theme.of(context).primaryColor,
              underline: Container(),
              items:
                  ['week', 'month', 'year']
                      .map(
                        (period) => DropdownMenuItem(
                          value: period,
                          child: Text(
                            period.capitalizeFirst!,
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                      )
                      .toList(),
              onChanged: (period) {
                if (period != null) {
                  controller.changePeriod(period);
                }
              },
            ),
          ),
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshData(),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading && controller.progressReport == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text(
                  'Loading your progress report...',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        if (controller.errorMessage.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  controller.errorMessage,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16, color: Colors.red),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    controller.clearError();
                    controller.refreshData();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (controller.progressReport == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.analytics_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No progress data available',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Progress Summary Card
                ProgressSummaryCard(
                  progressReport: controller.progressReport!,
                  motivationalMessage:
                      controller.aiImprovementReport?.motivationalMessage,
                ),
                const SizedBox(height: 16),

                // Weekly Overview
                WeeklyOverviewCard(weeklyData: controller.getWeeklyOverview()),
                const SizedBox(height: 16),

                // Data Visualization Section
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Progress Trends',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 16),

                        // Productivity Trend Chart
                        SizedBox(
                          height: 250,
                          child: ProductivityChart(
                            data: controller.getProductivityTrendData(),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Task/Habit Completion Pie Chart
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'Completion Overview',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.w600),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          height: 200,
                          child: CompletionPieChart(
                            data: controller.getTaskHabitCompletionData(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // AI Improvement Report Section
                if (controller.aiImprovementReport != null) ...[
                  // Strengths Section
                  if (controller.aiImprovementReport!.strengths.isNotEmpty)
                    InsightsSection(
                      insights: controller.aiImprovementReport!.strengths,
                    ),
                  const SizedBox(height: 16),

                  // Critical Areas Section
                  if (controller.aiImprovementReport!.criticalAreas.isNotEmpty)
                    TipsSection(
                      tips: controller.aiImprovementReport!.criticalAreas,
                    ),
                ],
                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      }),
    );
  }
}
