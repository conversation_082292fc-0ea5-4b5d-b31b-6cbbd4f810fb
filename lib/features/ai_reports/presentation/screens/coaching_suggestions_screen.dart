import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:power_up/features/ai_reports/presentation/controllers/ai_reports_controller.dart';
import 'package:power_up/features/ai_reports/presentation/widgets/coaching_suggestions_section.dart';

/// A screen that displays all coaching suggestions
class CoachingSuggestionsScreen extends StatelessWidget {
  const CoachingSuggestionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final controller = Get.find<AiReportsController>();

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Your Coaching Insights',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
        ),
        backgroundColor: theme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Obx(() {
        final isLoading = controller.isLoading;
        final suggestions = controller.coachingSuggestions;
        final errorMessage = controller.errorMessage;

        if (isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (errorMessage.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  color: theme.colorScheme.error,
                  size: 60,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error Loading Suggestions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  errorMessage,
                  style: theme.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: controller.loadCoachingSuggestions,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Try Again'),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Description text
              Text(
                'Personalized coaching suggestions based on your habits, tasks, and progress.',
                style: theme.textTheme.bodyLarge,
              ),
              const SizedBox(height: 16),

              // Disclaimer
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade100),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: theme.primaryColor),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'These suggestions are AI-generated and not a substitute for professional advice.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Coaching suggestions section
              CoachingSuggestionsSection(
                suggestions: suggestions,
                title: 'Personalized Coaching',
                emptyMessage: 'No coaching suggestions yet',
              ),
            ],
          ),
        );
      }),
    );
  }
}
