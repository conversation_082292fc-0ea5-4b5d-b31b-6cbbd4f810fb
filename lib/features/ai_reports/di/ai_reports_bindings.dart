import 'package:get/get.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/ai_reports/data/repositories/ai_report_repository_impl.dart';
import 'package:power_up/features/ai_reports/data/repositories/goal_repository_impl.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_local_data_source.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_local_data_source_impl.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_remote_data_source.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_remote_data_source_impl.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_local_data_source.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_local_data_source_impl.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_remote_data_source.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_remote_data_source_impl.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';
import 'package:power_up/features/ai_reports/domain/repositories/goal_repository.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_ai_improvement_report_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_weekly_progress_report_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_weekly_stats_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/get_user_progress_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/record_mood_entry_usecase.dart';
import 'package:power_up/features/ai_reports/domain/usecases/create_goal_usecase.dart';
import 'package:power_up/features/ai_reports/presentation/controllers/ai_reports_controller.dart';

/// Dependency injection for AI Reports feature
class AiReportsBindings extends Bindings {
  @override
  void dependencies() {
    // Data sources
    Get.lazyPut<AiReportRemoteDataSource>(
      () => AiReportRemoteDataSourceImpl(apiClient: Get.find()),
    );

    Get.lazyPut<AiReportLocalDataSource>(
      () => AiReportLocalDataSourceImpl(
        storageService: Get.find<StorageService>(),
      ),
    );

    // Goal data sources
    Get.lazyPut<GoalRemoteDataSource>(
      () => GoalRemoteDataSourceImpl(Get.find()),
    );

    Get.lazyPut<GoalLocalDataSource>(
      () => GoalLocalDataSourceImpl(Get.find<StorageService>()),
    );

    // Repositories
    Get.lazyPut<AIReportRepository>(
      () => AiReportRepositoryImpl(
        remoteDataSource: Get.find<AiReportRemoteDataSource>(),
        localDataSource: Get.find<AiReportLocalDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
      ),
    );

    Get.lazyPut<GoalRepository>(
      () => GoalRepositoryImpl(
        remoteDataSource: Get.find<GoalRemoteDataSource>(),
        localDataSource: Get.find<GoalLocalDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
      ),
    );

    // Use cases
    Get.lazyPut(
      () => GetWeeklyProgressReportUseCase(Get.find<AIReportRepository>()),
    );

    Get.lazyPut(
      () => GetAIImprovementReportUseCase(Get.find<AIReportRepository>()),
    );

    Get.lazyPut(() => GetWeeklyStatsUseCase(Get.find<AIReportRepository>()));

    Get.lazyPut(() => GetUserProgressUseCase(Get.find<AIReportRepository>()));

    Get.lazyPut(
      () => RecordMoodEntryUseCase(repository: Get.find<AIReportRepository>()),
    );

    Get.lazyPut(() => CreateGoalUseCase(Get.find<GoalRepository>()));

    // Controller
    Get.lazyPut(
      () => AiReportsController(
        getWeeklyProgressReportUseCase:
            Get.find<GetWeeklyProgressReportUseCase>(),
        getAIImprovementReportUseCase:
            Get.find<GetAIImprovementReportUseCase>(),
        getWeeklyStatsUseCase: Get.find<GetWeeklyStatsUseCase>(),
        getUserProgressUseCase: Get.find<GetUserProgressUseCase>(),
        recordMoodEntryUseCase: Get.find<RecordMoodEntryUseCase>(),
        createGoalUseCase: Get.find<CreateGoalUseCase>(),
      ),
    );
  }
}
