import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_local_data_source.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_remote_data_source.dart';
import 'package:power_up/features/ai_reports/data/models/goal_model.dart';
import 'package:power_up/features/ai_reports/domain/entities/goal_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/goal_repository.dart';

/// Implementation of the goal repository
class GoalRepositoryImpl implements GoalRepository {
  final GoalRemoteDataSource _remoteDataSource;
  final GoalLocalDataSource _localDataSource;
  final NetworkInfo _networkInfo;

  GoalRepositoryImpl({
    required GoalRemoteDataSource remoteDataSource,
    required GoalLocalDataSource localDataSource,
    required NetworkInfo networkInfo,
  }) : _remoteDataSource = remoteDataSource,
       _localDataSource = localDataSource,
       _networkInfo = networkInfo;

  @override
  Future<Either<Failure, GoalEntity>> createGoal({
    required String title,
    required String description,
    required String category,
    required String priority,
    required DateTime targetDate,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final goal = await _remoteDataSource.createGoal(
          title: title,
          description: description,
          category: category,
          priority: priority,
          targetDate: targetDate,
        );

        // Cache the created goal
        await _localDataSource.cacheGoal(goal);
        return Right(goal);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: $e'));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<GoalEntity>>> getGoals() async {
    if (await _networkInfo.isConnected) {
      try {
        final goals = await _remoteDataSource.getGoals();

        // Cache the goals
        await _localDataSource.cacheGoals(goals);
        return Right(goals);
      } on ServerException catch (e) {
        // Fallback to cached data on server error
        try {
          final cachedGoals = await _localDataSource.getCachedGoals();
          return Right(cachedGoals);
        } on CacheException {
          return Left(ServerFailure(message: e.message));
        }
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: $e'));
      }
    } else {
      // Offline - get from cache
      try {
        final cachedGoals = await _localDataSource.getCachedGoals();
        return Right(cachedGoals);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, GoalEntity>> getGoalById(String id) async {
    if (await _networkInfo.isConnected) {
      try {
        final goal = await _remoteDataSource.getGoalById(id);

        // Cache the goal
        await _localDataSource.cacheGoal(goal);
        return Right(goal);
      } on ServerException catch (e) {
        // Fallback to cached data on server error
        try {
          final cachedGoal = await _localDataSource.getCachedGoalById(id);
          if (cachedGoal != null) {
            return Right(cachedGoal);
          } else {
            return Left(ServerFailure(message: e.message));
          }
        } on CacheException {
          return Left(ServerFailure(message: e.message));
        }
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: $e'));
      }
    } else {
      // Offline - get from cache
      try {
        final cachedGoal = await _localDataSource.getCachedGoalById(id);
        if (cachedGoal != null) {
          return Right(cachedGoal);
        } else {
          return Left(CacheFailure(message: 'Goal not found in cache'));
        }
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, GoalEntity>> updateGoal({
    required String id,
    String? title,
    String? description,
    String? category,
    String? priority,
    DateTime? targetDate,
    double? progress,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final updatedGoal = await _remoteDataSource.updateGoal(
          id: id,
          title: title,
          description: description,
          category: category,
          priority: priority,
          targetDate: targetDate,
          progress: progress,
        );

        // Update cached goal
        await _localDataSource.updateCachedGoal(updatedGoal);
        return Right(updatedGoal);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: $e'));
      }
    } else {
      // Offline - update cache only
      try {
        final cachedGoal = await _localDataSource.getCachedGoalById(id);
        if (cachedGoal != null) {
          final updatedGoal = GoalModel.fromEntity(cachedGoal).copyWith(
            title: title,
            description: description,
            category: category,
            priority: priority,
            targetDate: targetDate,
            progress: progress,
            updatedAt: DateTime.now(),
          );

          await _localDataSource.updateCachedGoal(updatedGoal);
          return Right(updatedGoal);
        } else {
          return Left(CacheFailure(message: 'Goal not found in cache'));
        }
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, void>> deleteGoal(String id) async {
    if (await _networkInfo.isConnected) {
      try {
        await _remoteDataSource.deleteGoal(id);

        // Remove from cache
        await _localDataSource.deleteCachedGoal(id);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: $e'));
      }
    } else {
      // Offline - remove from cache only
      try {
        await _localDataSource.deleteCachedGoal(id);
        return const Right(null);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, GoalEntity>> markGoalComplete(String id) async {
    if (await _networkInfo.isConnected) {
      try {
        final completedGoal = await _remoteDataSource.markGoalComplete(id);

        // Update cached goal
        await _localDataSource.updateCachedGoal(completedGoal);
        return Right(completedGoal);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: $e'));
      }
    } else {
      // Offline - update cache only
      try {
        final cachedGoal = await _localDataSource.getCachedGoalById(id);
        if (cachedGoal != null) {
          final completedGoal = GoalModel.fromEntity(cachedGoal).copyWith(
            isCompleted: true,
            progress: 100.0,
            updatedAt: DateTime.now(),
          );

          await _localDataSource.updateCachedGoal(completedGoal);
          return Right(completedGoal);
        } else {
          return Left(CacheFailure(message: 'Goal not found in cache'));
        }
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<GoalEntity>>> getActiveGoals() async {
    if (await _networkInfo.isConnected) {
      try {
        final activeGoals = await _remoteDataSource.getActiveGoals();

        // Note: We don't cache just active goals separately to avoid cache inconsistency
        // Instead, we rely on the full goals cache
        return Right(activeGoals);
      } on ServerException catch (e) {
        // Fallback to cached data on server error
        try {
          final cachedActiveGoals =
              await _localDataSource.getCachedActiveGoals();
          return Right(cachedActiveGoals);
        } on CacheException {
          return Left(ServerFailure(message: e.message));
        }
      } catch (e) {
        return Left(ServerFailure(message: 'Unexpected error: $e'));
      }
    } else {
      // Offline - get from cache
      try {
        final cachedActiveGoals = await _localDataSource.getCachedActiveGoals();
        return Right(cachedActiveGoals);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }
}
