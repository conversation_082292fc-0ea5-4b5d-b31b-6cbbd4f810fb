import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_local_data_source.dart';
import 'package:power_up/features/ai_reports/data/datasources/ai_report_remote_data_source.dart';
import 'package:power_up/features/ai_reports/domain/entities/progress_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/coaching_suggestion_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/habit_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/productivity_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/mood_analytics_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/personalized_insights_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/weekly_stats_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/task_completion_stats_entity.dart';
import 'package:power_up/features/ai_reports/domain/entities/ai_improvement_report_entity.dart';
import 'package:power_up/features/ai_reports/domain/repositories/ai_report_repository.dart';

/// Implementation of the AIReportRepository
class AiReportRepositoryImpl implements AIReportRepository {
  final AiReportRemoteDataSource remoteDataSource;
  final AiReportLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  AiReportRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<void> init() async {
    await localDataSource.init();
  }

  @override
  Future<void> clearData() async {
    await localDataSource.clear();
  }

  @override
  Future<Either<Failure, ProgressReportEntity>> getUserProgress({
    required String period,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteProgress = await remoteDataSource.getUserProgress(
          period: period,
        );

        // Cache the remote data
        await localDataSource.cacheUserProgress(remoteProgress, period);

        return Right(remoteProgress);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedProgress = await localDataSource.getCachedUserProgress(
            period,
          );
          if (cachedProgress != null) {
            return Right(cachedProgress);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedProgress = await localDataSource.getCachedUserProgress(
          period,
        );
        if (cachedProgress != null) {
          return Right(cachedProgress);
        }
        return const Left(
          CacheFailure(message: 'No cached user progress available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, HabitAnalyticsEntity>> getHabitAnalytics({
    required String period,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteAnalytics = await remoteDataSource.getHabitAnalytics(
          period: period,
        );

        // Cache the remote data
        await localDataSource.cacheHabitAnalytics(remoteAnalytics, period);

        return Right(remoteAnalytics);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedAnalytics = await localDataSource.getCachedHabitAnalytics(
            period,
          );
          if (cachedAnalytics != null) {
            return Right(cachedAnalytics);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedAnalytics = await localDataSource.getCachedHabitAnalytics(
          period,
        );
        if (cachedAnalytics != null) {
          return Right(cachedAnalytics);
        }
        return const Left(
          CacheFailure(message: 'No cached habit analytics available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, ProductivityAnalyticsEntity>>
  getProductivityAnalytics({required String period}) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteAnalytics = await remoteDataSource.getProductivityAnalytics(
          period: period,
        );

        // Cache the remote data
        await localDataSource.cacheProductivityAnalytics(
          remoteAnalytics,
          period,
        );

        return Right(remoteAnalytics);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedAnalytics = await localDataSource
              .getCachedProductivityAnalytics(period);
          if (cachedAnalytics != null) {
            return Right(cachedAnalytics);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedAnalytics = await localDataSource
            .getCachedProductivityAnalytics(period);
        if (cachedAnalytics != null) {
          return Right(cachedAnalytics);
        }
        return const Left(
          CacheFailure(message: 'No cached productivity analytics available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, MoodAnalyticsEntity>> getMoodAnalytics({
    required String period,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteAnalytics = await remoteDataSource.getMoodAnalytics(
          period: period,
        );

        // Cache the remote data
        await localDataSource.cacheMoodAnalytics(remoteAnalytics, period);

        return Right(remoteAnalytics);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedAnalytics = await localDataSource.getCachedMoodAnalytics(
            period,
          );
          if (cachedAnalytics != null) {
            return Right(cachedAnalytics);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedAnalytics = await localDataSource.getCachedMoodAnalytics(
          period,
        );
        if (cachedAnalytics != null) {
          return Right(cachedAnalytics);
        }
        return const Left(
          CacheFailure(message: 'No cached mood analytics available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, PersonalizedInsightsEntity>>
  getPersonalizedInsights() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteInsights = await remoteDataSource.getPersonalizedInsights();

        // Cache the remote data
        await localDataSource.cachePersonalizedInsights(remoteInsights);

        return Right(remoteInsights);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedInsights =
              await localDataSource.getCachedPersonalizedInsights();
          if (cachedInsights != null) {
            return Right(cachedInsights);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedInsights =
            await localDataSource.getCachedPersonalizedInsights();
        if (cachedInsights != null) {
          return Right(cachedInsights);
        }
        return const Left(
          CacheFailure(message: 'No cached personalized insights available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, WeeklyStatsEntity>> getWeeklyStats() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteStats = await remoteDataSource.getWeeklyStats();

        // Cache the remote data
        await localDataSource.cacheWeeklyStats(remoteStats);

        return Right(remoteStats);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedStats = await localDataSource.getCachedWeeklyStats();
          if (cachedStats != null) {
            return Right(cachedStats);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedStats = await localDataSource.getCachedWeeklyStats();
        if (cachedStats != null) {
          return Right(cachedStats);
        }
        return const Left(
          CacheFailure(message: 'No cached weekly stats available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, MoodAnalyticsEntity>> getHabitCorrelations({
    required String period,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteCorrelations = await remoteDataSource.getHabitCorrelations(
          period: period,
        );

        // Cache the remote data
        await localDataSource.cacheHabitCorrelations(
          remoteCorrelations,
          period,
        );

        return Right(remoteCorrelations);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedCorrelations = await localDataSource
              .getCachedHabitCorrelations(period);
          if (cachedCorrelations != null) {
            return Right(cachedCorrelations);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedCorrelations = await localDataSource
            .getCachedHabitCorrelations(period);
        if (cachedCorrelations != null) {
          return Right(cachedCorrelations);
        }
        return const Left(
          CacheFailure(message: 'No cached habit correlations available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, PersonalizedInsightsEntity>>
  getStreakMilestones() async {
    // Note: This uses the same endpoint as personalized insights but could be separate
    return await getPersonalizedInsights();
  }

  @override
  Future<Either<Failure, TaskCompletionStatsEntity>> getTaskCompletionStats({
    String? period,
  }) async {
    // Note: This functionality might be implemented later or use productivity analytics
    // For now, return a placeholder implementation
    return const Left(
      CacheFailure(message: 'Task completion stats not yet implemented'),
    );
  }

  @override
  Future<Either<Failure, ProgressReportEntity>>
  getWeeklyProgressReport() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteReport = await remoteDataSource.getWeeklyProgressReport();

        // Cache the remote data
        await localDataSource.cacheWeeklyProgressReport(remoteReport);

        return Right(remoteReport);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedReport =
              await localDataSource.getCachedWeeklyProgressReport();
          if (cachedReport != null) {
            return Right(cachedReport);
          }
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final cachedReport =
            await localDataSource.getCachedWeeklyProgressReport();
        if (cachedReport != null) {
          return Right(cachedReport);
        }
        return const Left(
          CacheFailure(message: 'No cached weekly progress report available'),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<CoachingSuggestionEntity>>>
  getCoachingSuggestions() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteSuggestions =
            await remoteDataSource.getCoachingSuggestions();

        // Cache the remote data
        await localDataSource.cacheCoachingSuggestions(remoteSuggestions);

        return Right(remoteSuggestions);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedSuggestions =
              await localDataSource.getCachedCoachingSuggestions();
          return Right(cachedSuggestions);
        } on CacheException {
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        }
      }
    } else {
      try {
        final cachedSuggestions =
            await localDataSource.getCachedCoachingSuggestions();
        return Right(cachedSuggestions);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, void>> recordMoodEntry({required String mood}) async {
    if (await networkInfo.isConnected) {
      try {
        // Note: This would need to be implemented in the remote data source
        // For now, this is a placeholder
        return const Right(null);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      // Store locally for sync later when online
      return const Left(
        NetworkFailure(message: 'Cannot record mood entry while offline'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> recordFocusSession({
    required int minutes,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.recordFocusSession(minutes: minutes);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      // Store locally for sync later when online
      return const Left(
        NetworkFailure(message: 'Cannot record focus session while offline'),
      );
    }
  }

  @override
  Future<Either<Failure, AIImprovementReportEntity>> getAIImprovementReport({
    int days = 30,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final report = await remoteDataSource.getAIImprovementReport(
          days: days,
        );
        await localDataSource.cacheAIImprovementReport(report, days);
        return Right(report);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        final cachedReport = await localDataSource.getCachedAIImprovementReport(
          days,
        );
        if (cachedReport != null) {
          return Right(cachedReport);
        } else {
          return const Left(
            CacheFailure(message: 'No cached AI improvement report available'),
          );
        }
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }
}
