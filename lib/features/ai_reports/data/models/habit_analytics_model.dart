import 'package:power_up/features/ai_reports/domain/entities/habit_analytics_entity.dart';

/// Model for habit streak
class HabitStreakModel extends HabitStreakEntity {
  const HabitStreakModel({
    required super.name,
    required super.currentStreak,
    required super.longestStreak,
    required super.completionRate,
  });

  factory HabitStreakModel.fromJson(Map<String, dynamic> json) {
    return HabitStreakModel(
      name: json['name'] as String,
      currentStreak: (json['currentStreak'] as num).toInt(),
      longestStreak: (json['longestStreak'] as num).toInt(),
      completionRate: (json['completionRate'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'completionRate': completionRate,
    };
  }

  factory HabitStreakModel.fromEntity(HabitStreakEntity entity) {
    return HabitStreakModel(
      name: entity.name,
      currentStreak: entity.currentStreak,
      longestStreak: entity.longestStreak,
      completionRate: entity.completionRate,
    );
  }
}

/// Model for habit completion by day
class HabitCompletionByDayModel extends HabitCompletionByDayEntity {
  const HabitCompletionByDayModel({
    required super.day,
    required super.completionPercentage,
  });

  factory HabitCompletionByDayModel.fromJson(Map<String, dynamic> json) {
    return HabitCompletionByDayModel(
      day: json['day'] as String,
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'day': day, 'completionPercentage': completionPercentage};
  }

  factory HabitCompletionByDayModel.fromEntity(
    HabitCompletionByDayEntity entity,
  ) {
    return HabitCompletionByDayModel(
      day: entity.day,
      completionPercentage: entity.completionPercentage,
    );
  }
}

/// Model for habit analytics
class HabitAnalyticsModel extends HabitAnalyticsEntity {
  const HabitAnalyticsModel({
    required super.overallCompletionRate,
    required super.streaks,
    required super.completionByDay,
    required super.mostConsistentHabit,
    required super.habitNeedingImprovement,
  });

  factory HabitAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return HabitAnalyticsModel(
      overallCompletionRate: (json['overallCompletionRate'] as num).toDouble(),
      streaks:
          (json['streaks'] as List<dynamic>)
              .map(
                (item) =>
                    HabitStreakModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
      completionByDay:
          (json['completionByDay'] as List<dynamic>)
              .map(
                (item) => HabitCompletionByDayModel.fromJson(
                  item as Map<String, dynamic>,
                ),
              )
              .toList(),
      mostConsistentHabit: json['mostConsistentHabit'] as String,
      habitNeedingImprovement: json['habitNeedingImprovement'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overallCompletionRate': overallCompletionRate,
      'streaks':
          streaks
              .map((item) => HabitStreakModel.fromEntity(item).toJson())
              .toList(),
      'completionByDay':
          completionByDay
              .map(
                (item) => HabitCompletionByDayModel.fromEntity(item).toJson(),
              )
              .toList(),
      'mostConsistentHabit': mostConsistentHabit,
      'habitNeedingImprovement': habitNeedingImprovement,
    };
  }

  factory HabitAnalyticsModel.fromEntity(HabitAnalyticsEntity entity) {
    return HabitAnalyticsModel(
      overallCompletionRate: entity.overallCompletionRate,
      streaks: entity.streaks,
      completionByDay: entity.completionByDay,
      mostConsistentHabit: entity.mostConsistentHabit,
      habitNeedingImprovement: entity.habitNeedingImprovement,
    );
  }
}
