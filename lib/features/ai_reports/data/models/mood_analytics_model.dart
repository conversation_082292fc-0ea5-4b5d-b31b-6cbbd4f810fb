import 'package:power_up/features/ai_reports/domain/entities/mood_analytics_entity.dart';

/// Model for daily mood
class DailyMoodModel extends DailyMoodEntity {
  const DailyMoodModel({
    required super.date,
    required super.mood,
    required super.moodScore,
  });

  factory DailyMoodModel.fromJson(Map<String, dynamic> json) {
    return DailyMoodModel(
      date: DateTime.parse(json['date'] as String),
      mood: json['mood'] as String,
      moodScore: (json['moodScore'] as num).toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'mood': mood,
      'moodScore': moodScore,
    };
  }

  factory DailyMoodModel.fromEntity(DailyMoodEntity entity) {
    return DailyMoodModel(
      date: entity.date,
      mood: entity.mood,
      moodScore: entity.moodScore,
    );
  }
}

/// Model for mood correlation
class MoodCorrelationModel extends MoodCorrelationEntity {
  const MoodCorrelationModel({
    required super.factor,
    required super.correlationStrength,
    required super.correlationType,
  });

  factory MoodCorrelationModel.fromJson(Map<String, dynamic> json) {
    return MoodCorrelationModel(
      factor: json['factor'] as String,
      correlationStrength: (json['correlationStrength'] as num).toDouble(),
      correlationType: json['correlationType'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'factor': factor,
      'correlationStrength': correlationStrength,
      'correlationType': correlationType,
    };
  }

  factory MoodCorrelationModel.fromEntity(MoodCorrelationEntity entity) {
    return MoodCorrelationModel(
      factor: entity.factor,
      correlationStrength: entity.correlationStrength,
      correlationType: entity.correlationType,
    );
  }
}

/// Model for mood analytics
class MoodAnalyticsModel extends MoodAnalyticsEntity {
  const MoodAnalyticsModel({
    required super.moodEntries,
    required super.averageMood,
    required super.mostCommonMood,
    required super.moodTrend,
    required super.correlations,
  });

  factory MoodAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return MoodAnalyticsModel(
      moodEntries:
          (json['moodEntries'] as List<dynamic>)
              .map(
                (item) => DailyMoodModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
      averageMood: (json['averageMood'] as num).toDouble(),
      mostCommonMood: json['mostCommonMood'] as String,
      moodTrend: json['moodTrend'] as String,
      correlations:
          (json['correlations'] as List<dynamic>)
              .map(
                (item) =>
                    MoodCorrelationModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'moodEntries':
          moodEntries
              .map((item) => DailyMoodModel.fromEntity(item).toJson())
              .toList(),
      'averageMood': averageMood,
      'mostCommonMood': mostCommonMood,
      'moodTrend': moodTrend,
      'correlations':
          correlations
              .map((item) => MoodCorrelationModel.fromEntity(item).toJson())
              .toList(),
    };
  }

  factory MoodAnalyticsModel.fromEntity(MoodAnalyticsEntity entity) {
    return MoodAnalyticsModel(
      moodEntries: entity.moodEntries,
      averageMood: entity.averageMood,
      mostCommonMood: entity.mostCommonMood,
      moodTrend: entity.moodTrend,
      correlations: entity.correlations,
    );
  }
}
