import 'package:power_up/features/ai_reports/domain/entities/personalized_insights_entity.dart';

/// Model for insight
class InsightModel extends InsightEntity {
  const InsightModel({
    required super.title,
    required super.description,
    required super.category,
    required super.priority,
  });

  factory InsightModel.fromJson(Map<String, dynamic> json) {
    return InsightModel(
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      priority: json['priority'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'category': category,
      'priority': priority,
    };
  }

  factory InsightModel.fromEntity(InsightEntity entity) {
    return InsightModel(
      title: entity.title,
      description: entity.description,
      category: entity.category,
      priority: entity.priority,
    );
  }
}

/// Model for recommendation
class RecommendationModel extends RecommendationEntity {
  const RecommendationModel({
    required super.text,
    required super.impactArea,
    required super.priority,
  });

  factory RecommendationModel.from<PERSON>son(Map<String, dynamic> json) {
    return RecommendationModel(
      text: json['text'] as String,
      impactArea: json['impactArea'] as String,
      priority: json['priority'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'text': text, 'impactArea': impactArea, 'priority': priority};
  }

  factory RecommendationModel.fromEntity(RecommendationEntity entity) {
    return RecommendationModel(
      text: entity.text,
      impactArea: entity.impactArea,
      priority: entity.priority,
    );
  }
}

/// Model for personalized insights
class PersonalizedInsightsModel extends PersonalizedInsightsEntity {
  const PersonalizedInsightsModel({
    required super.insights,
    required super.recommendations,
    required super.strengths,
    required super.improvementAreas,
  });

  factory PersonalizedInsightsModel.fromJson(Map<String, dynamic> json) {
    return PersonalizedInsightsModel(
      insights:
          (json['insights'] as List<dynamic>)
              .map(
                (item) => InsightModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
      recommendations:
          (json['recommendations'] as List<dynamic>)
              .map(
                (item) =>
                    RecommendationModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
      strengths: (json['strengths'] as List<dynamic>).cast<String>(),
      improvementAreas:
          (json['improvementAreas'] as List<dynamic>).cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'insights':
          insights
              .map((item) => InsightModel.fromEntity(item).toJson())
              .toList(),
      'recommendations':
          recommendations
              .map((item) => RecommendationModel.fromEntity(item).toJson())
              .toList(),
      'strengths': strengths,
      'improvementAreas': improvementAreas,
    };
  }

  factory PersonalizedInsightsModel.fromEntity(
    PersonalizedInsightsEntity entity,
  ) {
    return PersonalizedInsightsModel(
      insights: entity.insights,
      recommendations: entity.recommendations,
      strengths: entity.strengths,
      improvementAreas: entity.improvementAreas,
    );
  }
}
