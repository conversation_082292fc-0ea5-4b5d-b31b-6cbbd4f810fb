import 'package:power_up/features/ai_reports/domain/entities/weekly_stats_entity.dart';

/// Model for weekly stats
class WeeklyStatsModel extends WeeklyStatsEntity {
  const WeeklyStatsModel({
    required super.totalFocusTime,
    super.avgDailyConsistency,
    super.habitCompletionRate,
    required super.topPerformingDays,
  });

  factory WeeklyStatsModel.fromJson(Map<String, dynamic> json) {
    return WeeklyStatsModel(
      totalFocusTime: (json['totalFocusTime'] as num).toDouble(),
      avgDailyConsistency: (json['avgDailyConsistency'] as num?)?.toDouble(),
      habitCompletionRate: (json['habitCompletionRate'] as num?)?.toDouble(),
      topPerformingDays:
          (json['topPerformingDays'] as List<dynamic>).cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalFocusTime': totalFocusTime,
      'avgDailyConsistency': avgDailyConsistency,
      'habitCompletionRate': habitCompletionRate,
      'topPerformingDays': topPerformingDays,
    };
  }

  factory WeeklyStatsModel.fromEntity(WeeklyStatsEntity entity) {
    return WeeklyStatsModel(
      totalFocusTime: entity.totalFocusTime,
      avgDailyConsistency: entity.avgDailyConsistency,
      habitCompletionRate: entity.habitCompletionRate,
      topPerformingDays: entity.topPerformingDays,
    );
  }
}
