import 'package:power_up/features/ai_reports/domain/entities/progress_report_entity.dart';

/// Model for daily progress
class DailyProgressModel extends DailyProgressEntity {
  const DailyProgressModel({
    required super.date,
    required super.score,
    required super.tasksCompleted,
    required super.habitsCompleted,
    required super.learningProgress,
  });

  factory DailyProgressModel.fromJson(Map<String, dynamic> json) {
    return DailyProgressModel(
      date: DateTime.parse(json['date'] as String),
      score: (json['score'] as num).toDouble(),
      tasksCompleted: (json['tasksCompleted'] as num).toInt(),
      habitsCompleted: (json['habitsCompleted'] as num).toInt(),
      learningProgress: (json['learningProgress'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'score': score,
      'tasksCompleted': tasksCompleted,
      'habitsCompleted': habitsCompleted,
      'learningProgress': learningProgress,
    };
  }

  factory DailyProgressModel.fromEntity(DailyProgressEntity entity) {
    return DailyProgressModel(
      date: entity.date,
      score: entity.score,
      tasksCompleted: entity.tasksCompleted,
      habitsCompleted: entity.habitsCompleted,
      learningProgress: entity.learningProgress,
    );
  }
}

/// Model for progress report
class ProgressReportModel extends ProgressReportEntity {
  const ProgressReportModel({
    required super.overallScore,
    required super.dailyProgress,
    required super.totalTasksCompleted,
    required super.totalHabitsCompleted,
    required super.changeFromPreviousPeriod,
  });

  factory ProgressReportModel.fromJson(Map<String, dynamic> json) {
    return ProgressReportModel(
      overallScore: (json['overallScore'] as num?)?.toDouble() ?? 0,
      dailyProgress:
          (json['dailyProgress'] as List<dynamic>)
              .map(
                (item) =>
                    DailyProgressModel.fromJson(item as Map<String, dynamic>),
              )
              .toList(),
      totalTasksCompleted: (json['totalTasksCompleted'] as num).toInt(),
      totalHabitsCompleted: (json['totalHabitsCompleted'] as num).toInt(),
      changeFromPreviousPeriod:
          (json['changeFromPreviousPeriod'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overallScore': overallScore,
      'dailyProgress':
          dailyProgress
              .map((item) => DailyProgressModel.fromEntity(item).toJson())
              .toList(),
      'totalTasksCompleted': totalTasksCompleted,
      'totalHabitsCompleted': totalHabitsCompleted,
      'changeFromPreviousPeriod': changeFromPreviousPeriod,
    };
  }

  factory ProgressReportModel.fromEntity(ProgressReportEntity entity) {
    return ProgressReportModel(
      overallScore: entity.overallScore,
      dailyProgress: entity.dailyProgress,
      totalTasksCompleted: entity.totalTasksCompleted,
      totalHabitsCompleted: entity.totalHabitsCompleted,
      changeFromPreviousPeriod: entity.changeFromPreviousPeriod,
    );
  }
}
