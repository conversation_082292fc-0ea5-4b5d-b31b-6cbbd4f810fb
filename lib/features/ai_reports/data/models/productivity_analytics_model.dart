import 'package:power_up/features/ai_reports/domain/entities/productivity_analytics_entity.dart';

/// Model for time of day productivity
class TimeOfDayProductivityModel extends TimeOfDayProductivityEntity {
  const TimeOfDayProductivityModel({
    required super.timeOfDay,
    required super.productivityScore,
    required super.tasksCompleted,
  });

  factory TimeOfDayProductivityModel.fromJson(Map<String, dynamic> json) {
    return TimeOfDayProductivityModel(
      timeOfDay: json['timeOfDay'] as String,
      productivityScore: (json['productivityScore'] as num).toDouble(),
      tasksCompleted: (json['tasksCompleted'] as num).toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timeOfDay': timeOfDay,
      'productivityScore': productivityScore,
      'tasksCompleted': tasksCompleted,
    };
  }

  factory TimeOfDayProductivityModel.fromEntity(
    TimeOfDayProductivityEntity entity,
  ) {
    return TimeOfDayProductivityModel(
      timeOfDay: entity.timeOfDay,
      productivityScore: entity.productivityScore,
      tasksCompleted: entity.tasksCompleted,
    );
  }
}

/// Model for day of week productivity
class DayOfWeekProductivityModel extends DayOfWeekProductivityEntity {
  const DayOfWeekProductivityModel({
    required super.dayOfWeek,
    required super.productivityScore,
  });

  factory DayOfWeekProductivityModel.fromJson(Map<String, dynamic> json) {
    return DayOfWeekProductivityModel(
      dayOfWeek: json['dayOfWeek'] as String,
      productivityScore: (json['productivityScore'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'dayOfWeek': dayOfWeek, 'productivityScore': productivityScore};
  }

  factory DayOfWeekProductivityModel.fromEntity(
    DayOfWeekProductivityEntity entity,
  ) {
    return DayOfWeekProductivityModel(
      dayOfWeek: entity.dayOfWeek,
      productivityScore: entity.productivityScore,
    );
  }
}

/// Model for productivity analytics
class ProductivityAnalyticsModel extends ProductivityAnalyticsEntity {
  const ProductivityAnalyticsModel({
    required super.overallProductivity,
    required super.productivityByTimeOfDay,
    required super.productivityByDayOfWeek,
    required super.mostProductiveTime,
    required super.mostProductiveDay,
    required super.averageFocusTime,
  });

  factory ProductivityAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return ProductivityAnalyticsModel(
      overallProductivity: (json['overallProductivity'] as num).toDouble(),
      productivityByTimeOfDay:
          (json['productivityByTimeOfDay'] as List<dynamic>)
              .map(
                (item) => TimeOfDayProductivityModel.fromJson(
                  item as Map<String, dynamic>,
                ),
              )
              .toList(),
      productivityByDayOfWeek:
          (json['productivityByDayOfWeek'] as List<dynamic>)
              .map(
                (item) => DayOfWeekProductivityModel.fromJson(
                  item as Map<String, dynamic>,
                ),
              )
              .toList(),
      mostProductiveTime: json['mostProductiveTime'] as String,
      mostProductiveDay: json['mostProductiveDay'] as String,
      averageFocusTime: (json['averageFocusTime'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overallProductivity': overallProductivity,
      'productivityByTimeOfDay':
          productivityByTimeOfDay
              .map(
                (item) => TimeOfDayProductivityModel.fromEntity(item).toJson(),
              )
              .toList(),
      'productivityByDayOfWeek':
          productivityByDayOfWeek
              .map(
                (item) => DayOfWeekProductivityModel.fromEntity(item).toJson(),
              )
              .toList(),
      'mostProductiveTime': mostProductiveTime,
      'mostProductiveDay': mostProductiveDay,
      'averageFocusTime': averageFocusTime,
    };
  }

  factory ProductivityAnalyticsModel.fromEntity(
    ProductivityAnalyticsEntity entity,
  ) {
    return ProductivityAnalyticsModel(
      overallProductivity: entity.overallProductivity,
      productivityByTimeOfDay: entity.productivityByTimeOfDay,
      productivityByDayOfWeek: entity.productivityByDayOfWeek,
      mostProductiveTime: entity.mostProductiveTime,
      mostProductiveDay: entity.mostProductiveDay,
      averageFocusTime: entity.averageFocusTime,
    );
  }
}
