import 'package:power_up/features/ai_reports/domain/entities/ai_improvement_report_entity.dart';

/// Model for AI improvement report
class AIImprovementReportModel extends AIImprovementReportEntity {
  const AIImprovementReportModel({
    required super.overallScore,
    required super.metrics,
    required super.improvements,
    required super.personalityInsights,
    required super.strengths,
    required super.criticalAreas,
    required super.projectedScore,
    required super.analysisDateRange,
    required super.generatedAt,
    required super.motivationalMessage,
    required super.aiEnhanced,
  });

  factory AIImprovementReportModel.fromJson(Map<String, dynamic> json) {
    return AIImprovementReportModel(
      overallScore: (json['overallScore'] as num).toInt(),
      metrics: (json['metrics'] as List)
          .map((e) => MetricModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      improvements: (json['improvements'] as List)
          .map((e) => ImprovementAreaModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      personalityInsights: (json['personalityInsights'] as List)
          .map((e) => PersonalityInsightModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      strengths: (json['strengths'] as List).cast<String>(),
      criticalAreas: (json['criticalAreas'] as List).cast<String>(),
      projectedScore: (json['projectedScore'] as num).toInt(),
      analysisDateRange: json['analysisDateRange'] as String,
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      motivationalMessage: json['motivationalMessage'] as String,
      aiEnhanced: json['aiEnhanced'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'overallScore': overallScore,
      'metrics': metrics.map((e) => (e as MetricModel).toJson()).toList(),
      'improvements': improvements.map((e) => (e as ImprovementAreaModel).toJson()).toList(),
      'personalityInsights': personalityInsights.map((e) => (e as PersonalityInsightModel).toJson()).toList(),
      'strengths': strengths,
      'criticalAreas': criticalAreas,
      'projectedScore': projectedScore,
      'analysisDateRange': analysisDateRange,
      'generatedAt': generatedAt.toIso8601String(),
      'motivationalMessage': motivationalMessage,
      'aiEnhanced': aiEnhanced,
    };
  }

  factory AIImprovementReportModel.fromEntity(AIImprovementReportEntity entity) {
    return AIImprovementReportModel(
      overallScore: entity.overallScore,
      metrics: entity.metrics,
      improvements: entity.improvements,
      personalityInsights: entity.personalityInsights,
      strengths: entity.strengths,
      criticalAreas: entity.criticalAreas,
      projectedScore: entity.projectedScore,
      analysisDateRange: entity.analysisDateRange,
      generatedAt: entity.generatedAt,
      motivationalMessage: entity.motivationalMessage,
      aiEnhanced: entity.aiEnhanced,
    );
  }
}

/// Model for metric data
class MetricModel extends MetricEntity {
  const MetricModel({
    required super.name,
    required super.currentScore,
    required super.targetScore,
    required super.trend,
    required super.changePercentage,
  });

  factory MetricModel.fromJson(Map<String, dynamic> json) {
    return MetricModel(
      name: json['name'] as String,
      currentScore: (json['currentScore'] as num).toInt(),
      targetScore: (json['targetScore'] as num).toInt(),
      trend: json['trend'] as String,
      changePercentage: (json['changePercentage'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'currentScore': currentScore,
      'targetScore': targetScore,
      'trend': trend,
      'changePercentage': changePercentage,
    };
  }
}

/// Model for improvement area
class ImprovementAreaModel extends ImprovementAreaEntity {
  const ImprovementAreaModel({
    required super.area,
    required super.currentScore,
    required super.improvementPotential,
    required super.actions,
    required super.evidence,
  });

  factory ImprovementAreaModel.fromJson(Map<String, dynamic> json) {
    return ImprovementAreaModel(
      area: json['area'] as String,
      currentScore: (json['currentScore'] as num).toInt(),
      improvementPotential: (json['improvementPotential'] as num).toInt(),
      actions: (json['actions'] as List)
          .map((e) => ActionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      evidence: json['evidence'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'area': area,
      'currentScore': currentScore,
      'improvementPotential': improvementPotential,
      'actions': actions.map((e) => (e as ActionModel).toJson()).toList(),
      'evidence': evidence,
    };
  }
}

/// Model for action item
class ActionModel extends ActionEntity {
  const ActionModel({
    required super.title,
    required super.description,
    required super.impact,
    required super.difficulty,
    required super.timeToResults,
    required super.category,
  });

  factory ActionModel.fromJson(Map<String, dynamic> json) {
    return ActionModel(
      title: json['title'] as String,
      description: json['description'] as String,
      impact: json['impact'] as String,
      difficulty: json['difficulty'] as String,
      timeToResults: (json['timeToResults'] as num).toInt(),
      category: json['category'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'impact': impact,
      'difficulty': difficulty,
      'timeToResults': timeToResults,
      'category': category,
    };
  }
}

/// Model for personality insight
class PersonalityInsightModel extends PersonalityInsightEntity {
  const PersonalityInsightModel({
    required super.insight,
    required super.category,
    required super.confidence,
  });

  factory PersonalityInsightModel.fromJson(Map<String, dynamic> json) {
    return PersonalityInsightModel(
      insight: json['insight'] as String,
      category: json['category'] as String,
      confidence: (json['confidence'] as num).toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'insight': insight,
      'category': category,
      'confidence': confidence,
    };
  }
}
