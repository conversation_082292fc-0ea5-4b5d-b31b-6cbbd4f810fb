import 'package:power_up/features/ai_reports/domain/entities/goal_entity.dart';

/// Model class for serializing and deserializing goal data
class GoalModel extends GoalEntity {
  const GoalModel({
    required String id,
    required String title,
    required String description,
    required String category,
    required String priority,
    required DateTime targetDate,
    required bool isCompleted,
    required DateTime createdAt,
    required DateTime updatedAt,
    double? progress,
  }) : super(
         id: id,
         title: title,
         description: description,
         category: category,
         priority: priority,
         targetDate: targetDate,
         isCompleted: isCompleted,
         createdAt: createdAt,
         updatedAt: updatedAt,
         progress: progress,
       );

  /// Create GoalModel from JSON
  factory GoalModel.fromJson(Map<String, dynamic> json) {
    return GoalModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      priority: json['priority'] as String,
      targetDate: DateTime.parse(json['target_date'] as String),
      isCompleted: json['is_completed'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// Convert GoalModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'priority': priority,
      'target_date': targetDate.toIso8601String(),
      'is_completed': isCompleted,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'progress': progress,
    };
  }

  /// Create GoalModel from GoalEntity
  factory GoalModel.fromEntity(GoalEntity entity) {
    return GoalModel(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      category: entity.category,
      priority: entity.priority,
      targetDate: entity.targetDate,
      isCompleted: entity.isCompleted,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      progress: entity.progress,
    );
  }

  /// Create a copy with updated fields
  GoalModel copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? priority,
    DateTime? targetDate,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? progress,
  }) {
    return GoalModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      targetDate: targetDate ?? this.targetDate,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      progress: progress ?? this.progress,
    );
  }
}
