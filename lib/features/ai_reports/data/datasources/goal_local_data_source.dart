import 'package:power_up/features/ai_reports/data/models/goal_model.dart';

/// Local data source interface for goals
abstract class GoalLocalDataSource {
  /// Cache a goal locally
  Future<void> cacheGoal(GoalModel goal);

  /// Cache multiple goals locally
  Future<void> cacheGoals(List<GoalModel> goals);

  /// Get all cached goals
  Future<List<GoalModel>> getCachedGoals();

  /// Get a cached goal by ID
  Future<GoalModel?> getCachedGoalById(String id);

  /// Update a cached goal
  Future<void> updateCachedGoal(GoalModel goal);

  /// Delete a cached goal
  Future<void> deleteCachedGoal(String id);

  /// Clear all cached goals
  Future<void> clearGoals();

  /// Get active goals from cache
  Future<List<GoalModel>> getCachedActiveGoals();
}
