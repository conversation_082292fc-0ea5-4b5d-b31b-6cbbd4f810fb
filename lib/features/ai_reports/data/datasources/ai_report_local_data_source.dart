import 'package:power_up/core/data/local_datasource.dart';
import 'package:power_up/features/ai_reports/data/models/progress_report_model.dart';
import 'package:power_up/features/ai_reports/data/models/coaching_suggestion_model.dart';
import 'package:power_up/features/ai_reports/data/models/habit_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/productivity_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/mood_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/personalized_insights_model.dart';
import 'package:power_up/features/ai_reports/data/models/weekly_stats_model.dart';
import 'package:power_up/features/ai_reports/data/models/ai_improvement_report_model.dart';

/// Definition for the local data source for AI reports
abstract class AiReportLocalDataSource extends LocalDataSource {
  /// Cache weekly progress report to local storage
  Future<void> cacheWeeklyProgressReport(ProgressReportModel report);

  /// Get cached weekly progress report from local storage
  Future<ProgressReportModel?> getCachedWeeklyProgressReport();

  /// Cache coaching suggestions to local storage
  Future<void> cacheCoachingSuggestions(
    List<CoachingSuggestionModel> suggestions,
  );

  /// Get cached coaching suggestions from local storage
  Future<List<CoachingSuggestionModel>> getCachedCoachingSuggestions();

  /// Cache user progress analytics to local storage
  Future<void> cacheUserProgress(ProgressReportModel progress, String period);

  /// Get cached user progress analytics from local storage
  Future<ProgressReportModel?> getCachedUserProgress(String period);

  /// Cache habit analytics to local storage
  Future<void> cacheHabitAnalytics(
    HabitAnalyticsModel analytics,
    String period,
  );

  /// Get cached habit analytics from local storage
  Future<HabitAnalyticsModel?> getCachedHabitAnalytics(String period);

  /// Cache productivity analytics to local storage
  Future<void> cacheProductivityAnalytics(
    ProductivityAnalyticsModel analytics,
    String period,
  );

  /// Get cached productivity analytics from local storage
  Future<ProductivityAnalyticsModel?> getCachedProductivityAnalytics(
    String period,
  );

  /// Cache mood analytics to local storage
  Future<void> cacheMoodAnalytics(MoodAnalyticsModel analytics, String period);

  /// Get cached mood analytics from local storage
  Future<MoodAnalyticsModel?> getCachedMoodAnalytics(String period);

  /// Cache personalized insights to local storage
  Future<void> cachePersonalizedInsights(PersonalizedInsightsModel insights);

  /// Get cached personalized insights from local storage
  Future<PersonalizedInsightsModel?> getCachedPersonalizedInsights();

  /// Cache weekly stats to local storage
  Future<void> cacheWeeklyStats(WeeklyStatsModel stats);

  /// Get cached weekly stats from local storage
  Future<WeeklyStatsModel?> getCachedWeeklyStats();

  /// Cache habit correlations to local storage
  Future<void> cacheHabitCorrelations(
    MoodAnalyticsModel correlations,
    String period,
  );

  /// Get cached habit correlations from local storage
  Future<MoodAnalyticsModel?> getCachedHabitCorrelations(String period);

  /// Cache AI improvement report to local storage
  Future<void> cacheAIImprovementReport(
    AIImprovementReportModel report,
    int days,
  );

  /// Get cached AI improvement report from local storage
  Future<AIImprovementReportModel?> getCachedAIImprovementReport(int days);

  /// Clear all cached AI reports data
  Future<void> clearAllCache();
}
