import 'dart:convert';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_local_data_source.dart';
import 'package:power_up/features/ai_reports/data/models/goal_model.dart';

/// Implementation of the local data source for goals
class GoalLocalDataSourceImpl implements GoalLocalDataSource {
  final StorageService _storageService;
  static const String _goalsKey = 'cached_goals';

  GoalLocalDataSourceImpl(this._storageService);

  @override
  Future<void> cacheGoal(GoalModel goal) async {
    try {
      final cachedGoals = await getCachedGoals();
      final existingIndex = cachedGoals.indexWhere((g) => g.id == goal.id);

      if (existingIndex != -1) {
        cachedGoals[existingIndex] = goal;
      } else {
        cachedGoals.add(goal);
      }

      await _saveGoals(cachedGoals);
    } catch (e) {
      throw CacheException(message: 'Failed to cache goal: $e');
    }
  }

  @override
  Future<void> cacheGoals(List<GoalModel> goals) async {
    try {
      await _saveGoals(goals);
    } catch (e) {
      throw CacheException(message: 'Failed to cache goals: $e');
    }
  }

  @override
  Future<List<GoalModel>> getCachedGoals() async {
    try {
      final goalsJson = _storageService.getData<String>(_goalsKey);
      if (goalsJson == null) return [];

      final List<dynamic> decodedGoals = jsonDecode(goalsJson);
      return decodedGoals.map((json) => GoalModel.fromJson(json)).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached goals: $e');
    }
  }

  @override
  Future<GoalModel?> getCachedGoalById(String id) async {
    try {
      final goals = await getCachedGoals();
      return goals.where((goal) => goal.id == id).firstOrNull;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached goal: $e');
    }
  }

  @override
  Future<void> updateCachedGoal(GoalModel goal) async {
    try {
      final cachedGoals = await getCachedGoals();
      final existingIndex = cachedGoals.indexWhere((g) => g.id == goal.id);

      if (existingIndex != -1) {
        cachedGoals[existingIndex] = goal;
        await _saveGoals(cachedGoals);
      } else {
        throw CacheException(message: 'Goal not found in cache');
      }
    } catch (e) {
      throw CacheException(message: 'Failed to update cached goal: $e');
    }
  }

  @override
  Future<void> deleteCachedGoal(String id) async {
    try {
      final cachedGoals = await getCachedGoals();
      cachedGoals.removeWhere((goal) => goal.id == id);
      await _saveGoals(cachedGoals);
    } catch (e) {
      throw CacheException(message: 'Failed to delete cached goal: $e');
    }
  }

  @override
  Future<void> clearGoals() async {
    try {
      await _storageService.removeData(_goalsKey);
    } catch (e) {
      throw CacheException(message: 'Failed to clear goals: $e');
    }
  }

  @override
  Future<List<GoalModel>> getCachedActiveGoals() async {
    try {
      final goals = await getCachedGoals();
      return goals.where((goal) => !goal.isCompleted).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached active goals: $e');
    }
  }

  /// Save goals to storage
  Future<void> _saveGoals(List<GoalModel> goals) async {
    final goalsJson = jsonEncode(goals.map((goal) => goal.toJson()).toList());
    await _storageService.setData(_goalsKey, goalsJson);
  }
}
