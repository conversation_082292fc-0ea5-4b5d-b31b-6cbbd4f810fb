import 'package:power_up/features/ai_reports/data/models/goal_model.dart';

/// Remote data source interface for goals
abstract class GoalRemoteDataSource {
  /// Create a new goal on the server
  Future<GoalModel> createGoal({
    required String title,
    required String description,
    required String category,
    required String priority,
    required DateTime targetDate,
  });

  /// Get all user goals from the server
  Future<List<GoalModel>> getGoals();

  /// Get a specific goal by ID from the server
  Future<GoalModel> getGoalById(String id);

  /// Update an existing goal on the server
  Future<GoalModel> updateGoal({
    required String id,
    String? title,
    String? description,
    String? category,
    String? priority,
    DateTime? targetDate,
    bool? isCompleted,
    double? progress,
  });

  /// Delete a goal from the server
  Future<void> deleteGoal(String id);

  /// Mark goal as completed on the server
  Future<GoalModel> markGoalComplete(String id);

  /// Get active goals from the server
  Future<List<GoalModel>> getActiveGoals();
}
