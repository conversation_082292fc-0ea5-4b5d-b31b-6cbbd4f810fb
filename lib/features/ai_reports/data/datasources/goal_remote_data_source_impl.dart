import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/ai_reports/data/datasources/goal_remote_data_source.dart';
import 'package:power_up/features/ai_reports/data/models/goal_model.dart';
import 'package:power_up/features/core/data/api/api_client.dart';

/// Implementation of the remote data source for goals
class GoalRemoteDataSourceImpl implements GoalRemoteDataSource {
  final ApiClient _apiClient;

  GoalRemoteDataSourceImpl(this._apiClient);

  @override
  Future<GoalModel> createGoal({
    required String title,
    required String description,
    required String category,
    required String priority,
    required DateTime targetDate,
  }) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        endpoint: '/goals',
        fromData: (data) => data as Map<String, dynamic>,
        data: {
          'title': title,
          'description': description,
          'category': category,
          'priority': priority,
          'target_date': targetDate.toIso8601String(),
        },
      );

      return GoalModel.fromJson(response['goal']);
    } catch (e) {
      throw ServerException(message: 'Failed to create goal: $e');
    }
  }

  @override
  Future<List<GoalModel>> getGoals() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        endpoint: '/goals',
        fromData: (data) => data as Map<String, dynamic>,
      );

      final List<dynamic> goalsJson = response['goals'] as List<dynamic>;
      return goalsJson.map((json) => GoalModel.fromJson(json)).toList();
    } catch (e) {
      throw ServerException(message: 'Failed to get goals: $e');
    }
  }

  @override
  Future<GoalModel> getGoalById(String id) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        endpoint: '/goals/$id',
        fromData: (data) => data as Map<String, dynamic>,
      );

      return GoalModel.fromJson(response['goal']);
    } catch (e) {
      throw ServerException(message: 'Failed to get goal: $e');
    }
  }

  @override
  Future<GoalModel> updateGoal({
    required String id,
    String? title,
    String? description,
    String? category,
    String? priority,
    DateTime? targetDate,
    bool? isCompleted,
    double? progress,
  }) async {
    try {
      final Map<String, dynamic> data = {};

      if (title != null) data['title'] = title;
      if (description != null) data['description'] = description;
      if (category != null) data['category'] = category;
      if (priority != null) data['priority'] = priority;
      if (targetDate != null) {
        data['target_date'] = targetDate.toIso8601String();
      }
      if (isCompleted != null) data['is_completed'] = isCompleted;
      if (progress != null) data['progress'] = progress;

      final response = await _apiClient.put<Map<String, dynamic>>(
        endpoint: '/goals/$id',
        fromData: (data) => data as Map<String, dynamic>,
        data: data,
      );

      return GoalModel.fromJson(response['goal']);
    } catch (e) {
      throw ServerException(message: 'Failed to update goal: $e');
    }
  }

  @override
  Future<void> deleteGoal(String id) async {
    try {
      await _apiClient.delete<Map<String, dynamic>>(
        endpoint: '/goals/$id',
        fromData: (data) => data as Map<String, dynamic>,
      );
    } catch (e) {
      throw ServerException(message: 'Failed to delete goal: $e');
    }
  }

  @override
  Future<GoalModel> markGoalComplete(String id) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        endpoint: '/goals/$id/complete',
        fromData: (data) => data as Map<String, dynamic>,
        data: {'is_completed': true, 'progress': 100.0},
      );

      return GoalModel.fromJson(response['goal']);
    } catch (e) {
      throw ServerException(message: 'Failed to mark goal as complete: $e');
    }
  }

  @override
  Future<List<GoalModel>> getActiveGoals() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        endpoint: '/goals?filter=active',
        fromData: (data) => data as Map<String, dynamic>,
      );

      final List<dynamic> goalsJson = response['goals'] as List<dynamic>;
      return goalsJson.map((json) => GoalModel.fromJson(json)).toList();
    } catch (e) {
      throw ServerException(message: 'Failed to get active goals: $e');
    }
  }
}
