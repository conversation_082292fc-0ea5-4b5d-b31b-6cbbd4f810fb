import 'package:power_up/core/data/remote_datasource.dart';
import 'package:power_up/features/ai_reports/data/models/progress_report_model.dart';
import 'package:power_up/features/ai_reports/data/models/coaching_suggestion_model.dart';
import 'package:power_up/features/ai_reports/data/models/habit_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/productivity_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/mood_analytics_model.dart';
import 'package:power_up/features/ai_reports/data/models/personalized_insights_model.dart';
import 'package:power_up/features/ai_reports/data/models/weekly_stats_model.dart';
import 'package:power_up/features/ai_reports/data/models/ai_improvement_report_model.dart';

/// Definition for the remote data source for AI reports
abstract class AiReportRemoteDataSource extends RemoteDataSource {
  /// Get weekly progress report from backend
  Future<ProgressReportModel> getWeeklyProgressReport();

  /// Get coaching suggestions from backend
  Future<List<CoachingSuggestionModel>> getCoachingSuggestions();

  /// Get user progress analytics for a specified period
  Future<ProgressReportModel> getUserProgress({required String period});

  /// Get detailed habit analytics for a specified period
  Future<HabitAnalyticsModel> getHabitAnalytics({required String period});

  /// Get productivity analytics for a specified period
  Future<ProductivityAnalyticsModel> getProductivityAnalytics({
    required String period,
  });

  /// Get mood analytics for a specified period
  Future<MoodAnalyticsModel> getMoodAnalytics({required String period});

  /// Get personalized insights and recommendations
  Future<PersonalizedInsightsModel> getPersonalizedInsights();

  /// Get aggregated weekly statistics
  Future<WeeklyStatsModel> getWeeklyStats();

  /// Get habit correlations with mood and productivity
  Future<MoodAnalyticsModel> getHabitCorrelations({required String period});

  /// Record a mood entry
  Future<void> recordMoodEntry({required String mood});

  /// Record a completed focus session
  Future<void> recordFocusSession({required int minutes});

  /// Get AI improvement report
  Future<AIImprovementReportModel> getAIImprovementReport({int days = 30});
}
