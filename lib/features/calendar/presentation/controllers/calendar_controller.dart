import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:power_up/core/presentation/controllers/base_controller.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';
import 'package:power_up/features/calendar/domain/usecases/get_calendar_events_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/get_events_for_date_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/create_calendar_event_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/update_calendar_event_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/delete_calendar_event_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/sync_calendar_usecase.dart';
import 'package:power_up/features/tasks/domain/usecases/create_task_usecase.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';

/// Controller for the Calendar feature
class Calendar<PERSON>ontroller extends BaseController {
  final GetCalendarEventsUseCase _getCalendarEventsUseCase;
  final GetEventsForDateUseCase _getEventsForDateUseCase;
  final CreateCalendarEventUseCase _createCalendarEventUseCase;
  final UpdateCalendarEventUseCase _updateCalendarEventUseCase;
  final DeleteCalendarEventUseCase _deleteCalendarEventUseCase;
  final SyncCalendarUseCase _syncCalendarUseCase;

  // Observable state
  final RxList<CalendarEventEntity> events = <CalendarEventEntity>[].obs;
  final RxList<CalendarEventEntity> selectedDateEvents =
      <CalendarEventEntity>[].obs;
  final Rx<DateTime?> selectedDay = Rx<DateTime?>(DateTime.now());
  final Rx<DateTime> focusedDay = DateTime.now().obs;
  final Rx<CalendarFormat> calendarFormat = CalendarFormat.month.obs;
  final RxBool isSyncing = false.obs;

  // Form controllers for creating events
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final Rx<DateTime> selectedStartTime = DateTime.now().obs;
  final Rx<DateTime> selectedEndTime =
      DateTime.now().add(const Duration(hours: 1)).obs;
  final RxString selectedColor = '#2196F3'.obs; // Default blue color

  // Form state for event creation/editing
  final Rx<CalendarEventType> selectedEventType = CalendarEventType.custom.obs;
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final Rx<TimeOfDay> selectedTime = TimeOfDay.now().obs;

  /// Callback to notify UI when a new event is created (for synergy)
  void Function(CalendarEventEntity event)? onEventCreated;

  // Add a reference to CreateTaskUseCase for synergy
  CreateTaskUseCase? createTaskUseCaseForSynergy;

  CalendarController({
    required GetCalendarEventsUseCase getCalendarEventsUseCase,
    required GetEventsForDateUseCase getEventsForDateUseCase,
    required CreateCalendarEventUseCase createCalendarEventUseCase,
    required UpdateCalendarEventUseCase updateCalendarEventUseCase,
    required DeleteCalendarEventUseCase deleteCalendarEventUseCase,
    required SyncCalendarUseCase syncCalendarUseCase,
  }) : _getCalendarEventsUseCase = getCalendarEventsUseCase,
       _getEventsForDateUseCase = getEventsForDateUseCase,
       _createCalendarEventUseCase = createCalendarEventUseCase,
       _updateCalendarEventUseCase = updateCalendarEventUseCase,
       _deleteCalendarEventUseCase = deleteCalendarEventUseCase,
       _syncCalendarUseCase = syncCalendarUseCase;

  @override
  void onInit() {
    super.onInit();
    loadCalendarEvents();
    loadEventsForSelectedDate();
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    super.onClose();
  }

  /// Load calendar events for the current month
  Future<void> loadCalendarEvents() async {
    setLoading(true);

    final startOfMonth = DateTime(
      focusedDay.value.year,
      focusedDay.value.month,
      1,
    );
    final endOfMonth = DateTime(
      focusedDay.value.year,
      focusedDay.value.month + 1,
      0,
      23,
      59,
      59,
    );

    final result = await _getCalendarEventsUseCase(
      GetCalendarEventsParams(startDate: startOfMonth, endDate: endOfMonth),
    );

    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message);
      },
      (calendarEvents) {
        events.assignAll(calendarEvents);
      },
    );

    setLoading(false);
  }

  /// Load events for the selected date
  Future<void> loadEventsForSelectedDate() async {
    if (selectedDay.value == null) return;

    final result = await _getEventsForDateUseCase(
      GetEventsForDateParams(date: selectedDay.value!),
    );

    result.fold(
      (failure) {
        // Don't show error for selected date events as it's not critical
        selectedDateEvents.clear();
      },
      (dayEvents) {
        selectedDateEvents.assignAll(dayEvents);
      },
    );
  }

  /// Handle day selection on calendar
  void onDaySelected(DateTime selectedDate, DateTime focusedDate) {
    selectedDay.value = selectedDate;
    focusedDay.value = focusedDate;
    loadEventsForSelectedDate();
  }

  /// Handle calendar format change
  void onFormatChanged(CalendarFormat format) {
    calendarFormat.value = format;
  }

  /// Handle page changed (month/week navigation)
  void onPageChanged(DateTime focusedDate) {
    focusedDay.value = focusedDate;
    // Reload events when month changes
    if (focusedDate.month != focusedDay.value.month ||
        focusedDate.year != focusedDay.value.year) {
      loadCalendarEvents();
    }
  }

  /// Get events for a specific day (used by table_calendar)
  List<CalendarEventEntity> getEventsForDay(DateTime day) {
    return events.where((event) => isSameDay(event.startTime, day)).toList();
  }

  /// Check if two dates are the same day
  bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Sync calendar with tasks and habits
  Future<void> syncCalendar() async {
    isSyncing.value = true;

    final result = await _syncCalendarUseCase(NoParams());

    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Sync Failed', failure.message, isError: true);
      },
      (success) {
        showSnackbar('Success', 'Calendar synced successfully');
        // Reload events after sync
        loadCalendarEvents();
        loadEventsForSelectedDate();
      },
    );

    isSyncing.value = false;
  }

  /// Create a new calendar event
  Future<bool> createEvent() async {
    if (titleController.text.trim().isEmpty) {
      showSnackbar('Error', 'Event title is required', isError: true);
      return false;
    }

    setLoading(true);

    final eventDateTime = DateTime(
      selectedDate.value.year,
      selectedDate.value.month,
      selectedDate.value.day,
      selectedTime.value.hour,
      selectedTime.value.minute,
    );

    final params = CreateCalendarEventParams(
      title: titleController.text.trim(),
      description:
          descriptionController.text.trim().isEmpty
              ? null
              : descriptionController.text.trim(),
      startTime: eventDateTime,
      endTime: eventDateTime.add(const Duration(hours: 1)),
      type: selectedEventType.value,
      color: selectedColor.value,
    );

    final result = await _createCalendarEventUseCase(params);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message, isError: true);
        setLoading(false);
        return false;
      },
      (newEvent) async {
        events.add(newEvent);
        if (selectedDay.value != null &&
            isSameDay(newEvent.startTime, selectedDay.value!)) {
          selectedDateEvents.add(newEvent);
        }
        clearForm();
        showSnackbar('Success', 'Event created successfully');
        setLoading(false);
        // --- Synergy: Notify UI to prompt for task creation ---
        if (onEventCreated != null) {
          onEventCreated!(newEvent);
        }
        return true;
      },
    );
  }

  /// Synergy: Create a task from a calendar event
  Future<bool> createTaskFromEvent(CalendarEventEntity event) async {
    if (createTaskUseCaseForSynergy == null) {
      showSnackbar('Error', 'Task creation not available', isError: true);
      return false;
    }
    // Only create for event types that make sense
    if (event.type != CalendarEventType.task &&
        event.type != CalendarEventType.custom) {
      return false;
    }
    final params = CreateTaskParams(
      title: event.title,
      description: event.description,
      dueDate: event.startTime,
      priority: TaskPriority.medium, // Default, or could be mapped from event
    );
    final result = await createTaskUseCaseForSynergy!(params);
    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', failure.message, isError: true);
        return false;
      },
      (task) {
        showSnackbar('Success', 'Task auto-created from event');
        return true;
      },
    );
  }

  /// Toggle calendar format between month and week
  void toggleCalendarFormat() {
    calendarFormat.value =
        calendarFormat.value == CalendarFormat.month
            ? CalendarFormat.week
            : CalendarFormat.month;
  }

  /// Set calendar format
  void setCalendarFormat(CalendarFormat format) {
    calendarFormat.value = format;
  }

  /// Set selected event type
  void setSelectedEventType(CalendarEventType type) {
    selectedEventType.value = type;
  }

  /// Select date for event
  Future<void> selectDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: selectedDate.value,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      selectedDate.value = picked;
    }
  }

  /// Select time for event
  Future<void> selectTime(BuildContext context) async {
    final picked = await showTimePicker(
      context: context,
      initialTime: selectedTime.value,
    );
    if (picked != null) {
      selectedTime.value = picked;
    }
  }

  /// Format selected date for display
  String formatSelectedDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Format event time for display
  String formatEventTime(CalendarEventEntity event) {
    final startTime = TimeOfDay.fromDateTime(event.startTime);
    final endTime = TimeOfDay.fromDateTime(event.endTime);

    return '${startTime.format(Get.context!)} - ${endTime.format(Get.context!)}';
  }

  /// Get upcoming events
  List<CalendarEventEntity> getUpcomingEvents() {
    final now = DateTime.now();
    return events.where((event) {
        return event.startTime.isAfter(now) && !event.isCompleted;
      }).toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  /// Get event color based on type
  Color getEventColor(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return Colors.blue;
      case CalendarEventType.habit:
        return Colors.green;
      case CalendarEventType.custom:
        return Colors.purple;
    }
  }

  /// Get event icon based on type
  IconData getEventIcon(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return Icons.task_alt;
      case CalendarEventType.habit:
        return Icons.repeat;
      case CalendarEventType.custom:
        return Icons.event;
    }
  }

  /// Clear form
  void clearForm() {
    titleController.clear();
    descriptionController.clear();
    selectedStartTime.value = DateTime.now();
    selectedEndTime.value = DateTime.now().add(const Duration(hours: 1));
    selectedColor.value = '#2196F3';
    selectedEventType.value = CalendarEventType.custom;
    selectedDate.value = DateTime.now();
    selectedTime.value = TimeOfDay.now();
  }

  /// Populate form for editing
  void populateFormForEdit(CalendarEventEntity event) {
    titleController.text = event.title;
    descriptionController.text = event.description ?? '';
    selectedEventType.value = event.type;
    selectedDate.value = event.startTime;
    selectedTime.value = TimeOfDay.fromDateTime(event.startTime);
    if (event.color != null) {
      selectedColor.value = event.color!;
    }
  }

  /// Update event
  Future<bool> updateEvent(String eventId) async {
    if (titleController.text.trim().isEmpty) {
      showSnackbar('Error', 'Event title is required', isError: true);
      return false;
    }

    // Find the event to update
    final eventIndex = events.indexWhere((e) => e.id == eventId);
    if (eventIndex == -1) {
      showSnackbar('Error', 'Event not found', isError: true);
      return false;
    }

    final existingEvent = events[eventIndex];

    // Only allow updating custom events
    if (existingEvent.type != CalendarEventType.custom) {
      showSnackbar('Error', 'Only custom events can be edited', isError: true);
      return false;
    }

    setLoading(true);

    final eventDateTime = DateTime(
      selectedDate.value.year,
      selectedDate.value.month,
      selectedDate.value.day,
      selectedTime.value.hour,
      selectedTime.value.minute,
    );

    final params = UpdateCalendarEventParams(
      id: eventId,
      title: titleController.text.trim(),
      description:
          descriptionController.text.trim().isEmpty
              ? null
              : descriptionController.text.trim(),
      startTime: eventDateTime,
      endTime: eventDateTime.add(const Duration(hours: 1)),
      color: selectedColor.value,
    );

    final result = await _updateCalendarEventUseCase(params);

    bool success = false;
    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar(
          'Error',
          'Failed to update event: ${failure.message}',
          isError: true,
        );
        success = false;
      },
      (updatedEvent) {
        // Update in the list
        events[eventIndex] = updatedEvent;

        // Update in selected date events if applicable
        final selectedDateIndex = selectedDateEvents.indexWhere(
          (e) => e.id == eventId,
        );
        if (selectedDateIndex != -1) {
          selectedDateEvents[selectedDateIndex] = updatedEvent;
        }

        showSnackbar('Success', 'Event updated successfully');

        // Refresh the calendar events to ensure consistency
        loadCalendarEvents();
        loadEventsForSelectedDate();
        success = true;
      },
    );

    clearForm();
    setLoading(false);
    return success;
  }

  /// Delete event
  Future<bool> deleteEvent(String eventId) async {
    // Find the event to check if it's deletable
    CalendarEventEntity? event;

    // Try to find in main events list first
    try {
      event = events.firstWhere((e) => e.id == eventId);
    } catch (e) {
      // If not found, try in selected date events
      try {
        event = selectedDateEvents.firstWhere((e) => e.id == eventId);
      } catch (e) {
        showSnackbar('Error', 'Event not found', isError: true);
        return false;
      }
    }

    // Only allow deletion of custom events
    if (event.type != CalendarEventType.custom) {
      showSnackbar('Error', 'Only custom events can be deleted', isError: true);
      return false;
    }

    setLoading(true);

    final result = await _deleteCalendarEventUseCase(
      DeleteCalendarEventParams(id: eventId),
    );

    bool success = false;
    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar(
          'Error',
          'Failed to delete event: ${failure.message}',
          isError: true,
        );
        success = false;
      },
      (deleteSuccess) {
        if (deleteSuccess) {
          // Remove from local state
          events.removeWhere((event) => event.id == eventId);
          selectedDateEvents.removeWhere((event) => event.id == eventId);

          showSnackbar('Success', 'Event deleted successfully');

          // Refresh the calendar events to ensure consistency
          loadCalendarEvents();
          loadEventsForSelectedDate();
          success = true;
        } else {
          showSnackbar('Error', 'Failed to delete event', isError: true);
          success = false;
        }
      },
    );

    setLoading(false);
    return success;
  }

  /// Create a calendar event from form data
  // Implementation would go here...
}
