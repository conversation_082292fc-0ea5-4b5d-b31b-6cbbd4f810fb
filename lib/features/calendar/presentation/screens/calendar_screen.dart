import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../controllers/calendar_controller.dart';
import '../../domain/entities/calendar_event_entity.dart';

class CalendarScreen extends StatelessWidget {
  const CalendarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final controller = Get.find<CalendarController>();

    // --- Synergy: Set up event callback for auto-create task prompt ---
    controller.onEventCreated = (event) async {
      // Only prompt for task or custom events
      if (event.type == CalendarEventType.task ||
          event.type == CalendarEventType.custom) {
        final shouldCreate = await showDialog<bool>(
          context: context,
          builder:
              (ctx) => AlertDialog(
                title: const Text('Auto-create Task?'),
                content: Text(
                  'Would you like to auto-create a task for the new event "${event.title}"?',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(ctx).pop(false),
                    child: const Text('No'),
                  ),
                  FilledButton(
                    onPressed: () => Navigator.of(ctx).pop(true),
                    child: const Text('Yes'),
                  ),
                ],
              ),
        );
        if (shouldCreate == true) {
          await controller.createTaskFromEvent(event);
        }
      }
    };

    return MainLayoutScreen(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Calendar',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'View and manage your schedule',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Calendar Format Toggle
                Obx(
                  () => IconButton(
                    icon: Icon(
                      controller.calendarFormat.value == CalendarFormat.month
                          ? Icons.view_week
                          : Icons.calendar_month,
                    ),
                    onPressed: () => controller.toggleCalendarFormat(),
                    tooltip: 'Toggle calendar format',
                  ),
                ),
                // Add Event Button
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () => _showCreateEventDialog(context, controller),
                  tooltip: 'Add event',
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Calendar Widget
            Obx(
              () => Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.only(bottom: 16),
                child: TableCalendar<CalendarEventEntity>(
                  firstDay: DateTime.utc(2020, 1, 1),
                  lastDay: DateTime.utc(2030, 12, 31),
                  focusedDay: controller.focusedDay.value,
                  calendarFormat: controller.calendarFormat.value,
                  eventLoader: (day) => controller.getEventsForDay(day),
                  startingDayOfWeek: StartingDayOfWeek.monday,
                  selectedDayPredicate: (day) {
                    return controller.selectedDay.value != null &&
                        isSameDay(controller.selectedDay.value!, day);
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    controller.onDaySelected(selectedDay, focusedDay);
                  },
                  onPageChanged: (focusedDay) {
                    controller.onPageChanged(focusedDay);
                  },
                  onFormatChanged: (format) {
                    controller.setCalendarFormat(format);
                  },
                  calendarStyle: CalendarStyle(
                    outsideDaysVisible: false,
                    weekendTextStyle: TextStyle(color: theme.colorScheme.error),
                    holidayTextStyle: TextStyle(color: theme.colorScheme.error),
                    selectedDecoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    todayDecoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                    ),
                    markerDecoration: BoxDecoration(
                      color: theme.colorScheme.secondary,
                      shape: BoxShape.circle,
                    ),
                    markersMaxCount: 3,
                    canMarkersOverflow: true,
                  ),
                  headerStyle: HeaderStyle(
                    formatButtonVisible: false,
                    titleCentered: true,
                    leftChevronIcon: Icon(
                      Icons.chevron_left,
                      color: theme.colorScheme.onSurface,
                    ),
                    rightChevronIcon: Icon(
                      Icons.chevron_right,
                      color: theme.colorScheme.onSurface,
                    ),
                    titleTextStyle: theme.textTheme.titleLarge!.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  daysOfWeekStyle: DaysOfWeekStyle(
                    weekdayStyle: theme.textTheme.labelLarge!,
                    weekendStyle: theme.textTheme.labelLarge!.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  Obx(() {
                    final selectedDay = controller.selectedDay.value;
                    if (selectedDay == null) {
                      return const SizedBox.shrink();
                    }

                    final events = controller.getEventsForDay(selectedDay);
                    return Column(
                      children: [
                        Text(
                          'Events for ${controller.formatSelectedDate(selectedDay)}',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (events.isEmpty)
                          Text(
                            'No events for this day',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          )
                        else
                          ...events.map(
                            (event) =>
                                _buildEventCard(context, event, controller),
                          ),
                      ],
                    );
                  }),

                  const SizedBox(height: 16),

                  // Upcoming Events
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Upcoming Events',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Obx(() {
                        if (controller.isLoading) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        final upcomingEvents = controller.getUpcomingEvents();
                        if (upcomingEvents.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.event_busy,
                                  size: 64,
                                  color: theme.colorScheme.onSurface.withValues(
                                    alpha: 0.3,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No upcoming events',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: theme.colorScheme.onSurface
                                        .withValues(alpha: 0.7),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        return Column(
                          children: [
                            ...upcomingEvents.map(
                              (event) =>
                                  _buildEventCard(context, event, controller),
                            ),
                          ],
                        );
                      }),
                      // Add bottom padding for floating tab bar
                      const SizedBox(height: 16),
                    ],
                  ),
                ],
              ),
            ),

            // Selected Day Events
          ],
        ),
      ),
    );
  }

  Widget _buildEventCard(
    BuildContext context,
    CalendarEventEntity event,
    CalendarController controller,
  ) {
    final theme = Theme.of(context);
    final eventColor = controller.getEventColor(event.type);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: eventColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Color Indicator
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: eventColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(width: 16),

          // Event Icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: eventColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              controller.getEventIcon(event.type),
              color: eventColor,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // Event Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (event.description?.isNotEmpty == true) ...[
                  const SizedBox(height: 2),
                  Text(
                    event.description!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 4),
                Text(
                  controller.formatEventTime(event),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          // Action Button - only show for custom events
          if (event.type == CalendarEventType.custom)
            PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditEventDialog(context, controller, event);
                    break;
                  case 'delete':
                    _showDeleteEventDialog(context, controller, event);
                    break;
                }
              },
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('Edit'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete),
                        title: Text('Delete'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
            )
          else
            // Show a small indicator for non-editable events
            Icon(
              Icons.lock_outline,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              size: 16,
            ),
        ],
      ),
    );
  }

  void _showCreateEventDialog(
    BuildContext context,
    CalendarController controller,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Create Event'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: controller.titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: controller.descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    Obx(
                      () => DropdownButtonFormField<CalendarEventType>(
                        value: controller.selectedEventType.value,
                        decoration: const InputDecoration(
                          labelText: 'Type',
                          border: OutlineInputBorder(),
                        ),
                        items:
                            CalendarEventType.values.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Row(
                                  children: [
                                    Icon(
                                      controller.getEventIcon(type),
                                      color: controller.getEventColor(type),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(type.name.capitalize ?? type.name),
                                  ],
                                ),
                              );
                            }).toList(),
                        onChanged: (type) {
                          if (type != null) {
                            controller.setSelectedEventType(type);
                          }
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => controller.selectDate(context),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Date',
                                border: OutlineInputBorder(),
                              ),
                              child: Obx(
                                () => Text(
                                  controller.formatSelectedDate(
                                    controller.selectedDate.value,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: InkWell(
                            onTap: () => controller.selectTime(context),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Time',
                                border: OutlineInputBorder(),
                              ),
                              child: Obx(
                                () => Text(
                                  controller.selectedTime.value.format(context),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  controller.clearForm();
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () async {
                  final success = await controller.createEvent();
                  if (success && context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Event created successfully'),
                      ),
                    );
                  }
                },
                child: const Text('Create'),
              ),
            ],
          ),
    );
  }

  void _showEditEventDialog(
    BuildContext context,
    CalendarController controller,
    CalendarEventEntity event,
  ) {
    controller.populateFormForEdit(event);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Edit Event'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: controller.titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: controller.descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    Obx(
                      () => DropdownButtonFormField<CalendarEventType>(
                        value: controller.selectedEventType.value,
                        decoration: const InputDecoration(
                          labelText: 'Type',
                          border: OutlineInputBorder(),
                        ),
                        items:
                            CalendarEventType.values.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Row(
                                  children: [
                                    Icon(
                                      controller.getEventIcon(type),
                                      color: controller.getEventColor(type),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(type.name.capitalize ?? type.name),
                                  ],
                                ),
                              );
                            }).toList(),
                        onChanged: (type) {
                          if (type != null) {
                            controller.setSelectedEventType(type);
                          }
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => controller.selectDate(context),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Date',
                                border: OutlineInputBorder(),
                              ),
                              child: Obx(
                                () => Text(
                                  controller.formatSelectedDate(
                                    controller.selectedDate.value,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: InkWell(
                            onTap: () => controller.selectTime(context),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Time',
                                border: OutlineInputBorder(),
                              ),
                              child: Obx(
                                () => Text(
                                  controller.selectedTime.value.format(context),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  controller.clearForm();
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () async {
                  final success = await controller.updateEvent(event.id);
                  if (success && context.mounted) {
                    Navigator.of(context).pop();
                  }
                },
                child: const Text('Update'),
              ),
            ],
          ),
    );
  }

  void _showDeleteEventDialog(
    BuildContext context,
    CalendarController controller,
    CalendarEventEntity event,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Event'),
            content: Text('Are you sure you want to delete "${event.title}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () async {
                  final success = await controller.deleteEvent(event.id);
                  if (success && context.mounted) {
                    Navigator.of(context).pop();
                  }
                },
                style: FilledButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
