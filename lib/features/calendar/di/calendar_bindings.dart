import 'package:get/get.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/calendar/data/datasources/calendar_local_data_source.dart';
import 'package:power_up/features/calendar/data/datasources/calendar_local_data_source_impl.dart';
import 'package:power_up/features/calendar/data/datasources/calendar_remote_data_source.dart';
import 'package:power_up/features/calendar/data/datasources/calendar_remote_data_source_impl.dart';
import 'package:power_up/features/calendar/data/repositories/calendar_repository_impl.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';
import 'package:power_up/features/calendar/domain/usecases/get_calendar_events_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/get_events_for_date_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/get_calendar_event_by_id_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/create_calendar_event_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/update_calendar_event_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/delete_calendar_event_usecase.dart';
import 'package:power_up/features/calendar/domain/usecases/sync_calendar_usecase.dart';
import 'package:power_up/features/calendar/presentation/controllers/calendar_controller.dart';
import 'package:power_up/features/core/data/api/api_client.dart';
import 'package:power_up/features/tasks/domain/usecases/create_task_usecase.dart';

/// Bindings for calendar feature
class CalendarBindings implements Bindings {
  @override
  void dependencies() {
    // Core dependencies
    final apiClient = Get.find<ApiClient>();
    final networkInfo = Get.find<NetworkInfo>();
    final storageService = Get.find<StorageService>();

    // Data sources
    Get.lazyPut<CalendarLocalDataSource>(
      () => CalendarLocalDataSourceImpl(storageService: storageService),
    );
    Get.lazyPut<CalendarRemoteDataSource>(
      () => CalendarRemoteDataSourceImpl(apiClient: apiClient),
    );

    // Repository
    Get.lazyPut<CalendarRepository>(
      () => CalendarRepositoryImpl(
        remoteDataSource: Get.find<CalendarRemoteDataSource>(),
        localDataSource: Get.find<CalendarLocalDataSource>(),
        networkInfo: networkInfo,
      ),
    );

    // Use cases
    Get.lazyPut(() => GetCalendarEventsUseCase(Get.find<CalendarRepository>()));
    Get.lazyPut(() => GetEventsForDateUseCase(Get.find<CalendarRepository>()));
    Get.lazyPut(
      () => GetCalendarEventByIdUseCase(Get.find<CalendarRepository>()),
    );
    Get.lazyPut(
      () => CreateCalendarEventUseCase(Get.find<CalendarRepository>()),
    );
    Get.lazyPut(
      () => UpdateCalendarEventUseCase(Get.find<CalendarRepository>()),
    );
    Get.lazyPut(
      () => DeleteCalendarEventUseCase(Get.find<CalendarRepository>()),
    );
    Get.lazyPut(() => SyncCalendarUseCase(Get.find<CalendarRepository>()));

    // Controller
    Get.lazyPut<CalendarController>(() {
      final controller = CalendarController(
        getCalendarEventsUseCase: Get.find<GetCalendarEventsUseCase>(),
        getEventsForDateUseCase: Get.find<GetEventsForDateUseCase>(),
        createCalendarEventUseCase: Get.find<CreateCalendarEventUseCase>(),
        updateCalendarEventUseCase: Get.find<UpdateCalendarEventUseCase>(),
        deleteCalendarEventUseCase: Get.find<DeleteCalendarEventUseCase>(),
        syncCalendarUseCase: Get.find<SyncCalendarUseCase>(),
      );
      // Inject CreateTaskUseCase for synergy
      controller.createTaskUseCaseForSynergy = Get.find<CreateTaskUseCase>();
      return controller;
    });
  }
}
