import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/calendar/data/datasources/calendar_local_data_source.dart';
import 'package:power_up/features/calendar/data/datasources/calendar_remote_data_source.dart';
import 'package:power_up/features/calendar/data/models/calendar_event_model.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Implementation of the CalendarRepository
class CalendarRepositoryImpl implements CalendarRepository {
  final CalendarRemoteDataSource remoteDataSource;
  final CalendarLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  CalendarRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<void> init() async {
    await localDataSource.init();
  }

  @override
  Future<void> clearData() async {
    await localDataSource.clear();
  }

  @override
  Future<Either<Failure, List<CalendarEventEntity>>> getEventsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // Get events from remote data source (already includes calendar, task events)
        final remoteEvents = await remoteDataSource.getEventsForDateRange(
          startDate: startDate,
          endDate: endDate,
        );

        // Save to local cache
        await localDataSource.saveEvents(remoteEvents);

        return Right(remoteEvents);
      } on ServerException {
        // Fall back to local data if server fails
        try {
          final localEvents = await localDataSource.getEventsForDateRange(
            startDate: startDate,
            endDate: endDate,
          );
          return Right(localEvents);
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final localEvents = await localDataSource.getEventsForDateRange(
          startDate: startDate,
          endDate: endDate,
        );
        return Right(localEvents);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, List<CalendarEventEntity>>> getEventsForDate(
    DateTime date,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        // Get events from remote data source (already includes all event types)
        final remoteEvents = await remoteDataSource.getEventsForDate(date);

        // Save to local cache
        await localDataSource.saveEvents(remoteEvents);

        return Right(remoteEvents);
      } on ServerException {
        // Fall back to local data if server fails
        try {
          final localEvents = await localDataSource.getEventsForDate(date);
          return Right(localEvents);
        } on CacheException catch (cacheE) {
          return Left(CacheFailure(message: cacheE.message));
        }
      }
    } else {
      try {
        final localEvents = await localDataSource.getEventsForDate(date);
        return Right(localEvents);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, CalendarEventEntity>> getEventById(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final event = await remoteDataSource.getEventById(id);

        // Save to local cache
        await localDataSource.saveEvent(event);

        return Right(event);
      } on ServerException catch (e) {
        // Fall back to local data if server fails
        try {
          final localEvent = await localDataSource.getEventById(id);
          if (localEvent != null) {
            return Right(localEvent);
          } else {
            return Left(
              const CacheFailure(message: 'Event not found in cache'),
            );
          }
        } on CacheException {
          return Left(
            ServerFailure(message: e.message, statusCode: e.statusCode),
          );
        }
      }
    } else {
      try {
        final localEvent = await localDataSource.getEventById(id);
        if (localEvent != null) {
          return Right(localEvent);
        } else {
          return Left(
            const CacheFailure(message: 'Event not found in local storage'),
          );
        }
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, CalendarEventEntity>> createEvent({
    required String title,
    String? description,
    required DateTime startTime,
    required DateTime endTime,
    CalendarEventType? type,
    String? color,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final event = await remoteDataSource.createEvent(
          title: title,
          description: description,
          startTime: startTime,
          endTime: endTime,
          type: type,
          color: color,
        );

        // Save to local cache
        await localDataSource.saveEvent(event);

        return Right(event);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Create a local event with temporary ID
        final newEvent = CalendarEventModel(
          id: 'local_${DateTime.now().millisecondsSinceEpoch}',
          title: title,
          description: description,
          startTime: startTime,
          endTime: endTime,
          type: type ?? CalendarEventType.custom,
          color: color,
          isCompleted: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await localDataSource.saveEvent(newEvent);
        return Right(newEvent);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, CalendarEventEntity>> updateEvent({
    required String id,
    String? title,
    String? description,
    DateTime? startTime,
    DateTime? endTime,
    String? color,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedEvent = await remoteDataSource.updateEvent(
          id: id,
          title: title,
          description: description,
          startTime: startTime,
          endTime: endTime,
          color: color,
        );

        // Update local cache
        await localDataSource.saveEvent(updatedEvent);

        return Right(updatedEvent);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Get existing event from local storage
        final existingEvent = await localDataSource.getEventById(id);

        if (existingEvent == null) {
          return const Left(CacheFailure(message: 'Event not found locally'));
        }

        // Update local event
        final updatedEvent = CalendarEventModel(
          id: existingEvent.id,
          title: title ?? existingEvent.title,
          description: description ?? existingEvent.description,
          startTime: startTime ?? existingEvent.startTime,
          endTime: endTime ?? existingEvent.endTime,
          type: existingEvent.type,
          relatedId: existingEvent.relatedId,
          color: color ?? existingEvent.color,
          isCompleted: existingEvent.isCompleted,
          createdAt: existingEvent.createdAt,
          updatedAt: DateTime.now(),
        );

        await localDataSource.saveEvent(updatedEvent);
        return Right(updatedEvent);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, bool>> deleteEvent(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.deleteEvent(id);

        // Delete from local cache
        await localDataSource.deleteEvent(id);

        return Right(result);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Mark for deletion when back online
        await localDataSource.deleteEvent(id);
        return const Right(true);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, CalendarEventEntity>> markEventComplete(
    String id,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final completedEvent = await remoteDataSource.markEventComplete(id);

        // Update local cache
        await localDataSource.saveEvent(completedEvent);

        return Right(completedEvent);
      } on ServerException catch (e) {
        return Left(
          ServerFailure(message: e.message, statusCode: e.statusCode),
        );
      }
    } else {
      try {
        // Get existing event
        final existingEvent = await localDataSource.getEventById(id);

        if (existingEvent == null) {
          return const Left(CacheFailure(message: 'Event not found locally'));
        }

        // Mark as complete locally
        final completedEvent = existingEvent.markAsComplete();
        await localDataSource.saveEvent(
          CalendarEventModel.fromEntity(completedEvent),
        );
        return Right(completedEvent);
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, bool>> syncCalendar() async {
    if (await networkInfo.isConnected) {
      try {
        // Get the current month's events to sync
        final now = DateTime.now();
        final startOfMonth = DateTime(now.year, now.month, 1);
        final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

        // Fetch and sync all event types
        await getEventsForDateRange(
          startDate: startOfMonth,
          endDate: endOfMonth,
        );

        return const Right(true);
      } on Exception catch (e) {
        return Left(ServerFailure(message: 'Failed to sync calendar: $e'));
      }
    } else {
      return const Left(
        NetworkFailure(message: 'No internet connection for calendar sync'),
      );
    }
  }
}
