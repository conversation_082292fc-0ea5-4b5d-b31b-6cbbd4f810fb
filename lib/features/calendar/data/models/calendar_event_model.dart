import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';

/// Model for calendar events, used for serialization/deserialization
class CalendarEventModel extends CalendarEventEntity {
  const CalendarEventModel({
    required super.id,
    required super.title,
    super.description,
    required super.startTime,
    required super.endTime,
    required super.type,
    super.relatedId,
    super.color,
    required super.isCompleted,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create a CalendarEventModel from a JSON map
  factory CalendarEventModel.fromJson(Map<String, dynamic> json) {
    return CalendarEventModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      type: _parseEventType(json['type']),
      relatedId: json['relatedId'],
      color: json['color'],
      isCompleted: json['isCompleted'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Convert this model to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'type': _eventTypeToString(type),
      'relatedId': relatedId,
      'color': color,
      'isCompleted': isCompleted,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Helper method to parse event type from string
  static CalendarEventType _parseEventType(String? type) {
    switch (type) {
      case 'task':
        return CalendarEventType.task;
      case 'habit':
        return CalendarEventType.habit;
      case 'custom':
        return CalendarEventType.custom;
      default:
        return CalendarEventType.custom;
    }
  }

  /// Helper method to convert event type enum to string
  static String _eventTypeToString(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return 'task';
      case CalendarEventType.habit:
        return 'habit';
      case CalendarEventType.custom:
        return 'custom';
    }
  }

  /// Convert domain entity to a model
  factory CalendarEventModel.fromEntity(CalendarEventEntity entity) {
    return CalendarEventModel(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      startTime: entity.startTime,
      endTime: entity.endTime,
      type: entity.type,
      relatedId: entity.relatedId,
      color: entity.color,
      isCompleted: entity.isCompleted,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
