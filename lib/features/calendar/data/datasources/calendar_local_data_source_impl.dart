import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/calendar/data/datasources/calendar_local_data_source.dart';
import 'package:power_up/features/calendar/data/models/calendar_event_model.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';

/// Implementation of CalendarLocalDataSource using StorageService
class CalendarLocalDataSourceImpl implements CalendarLocalDataSource {
  static const String _eventsKey = 'calendar_events';
  late final StorageService _storageService;
  bool _isInitialized = false;

  CalendarLocalDataSourceImpl({StorageService? storageService}) {
    if (storageService != null) {
      _storageService = storageService;
      _isInitialized = true;
    }
  }

  @override
  Future<void> init() async {
    if (!_isInitialized) {
      _storageService = await StorageService.getInstance();
      _isInitialized = true;
    }
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await init();
    }
  }

  @override
  Future<List<CalendarEventModel>> getEventsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      await _ensureInitialized();
      final eventsJson =
          _storageService.getData<List<dynamic>>(_eventsKey) ?? [];
      final allEvents =
          eventsJson
              .map(
                (json) => CalendarEventModel.fromJson(
                  Map<String, dynamic>.from(json),
                ),
              )
              .toList();

      // Filter events within date range
      return allEvents.where((event) {
        return (event.startTime.isAfter(startDate) ||
                event.startTime.isAtSameMomentAs(startDate)) &&
            (event.startTime.isBefore(endDate) ||
                event.startTime.isAtSameMomentAs(endDate));
      }).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get events for date range: $e');
    }
  }

  @override
  Future<List<CalendarEventModel>> getEventsForDate(DateTime date) async {
    try {
      await _ensureInitialized();
      final eventsJson =
          _storageService.getData<List<dynamic>>(_eventsKey) ?? [];
      final allEvents =
          eventsJson
              .map(
                (json) => CalendarEventModel.fromJson(
                  Map<String, dynamic>.from(json),
                ),
              )
              .toList();

      // Filter events for specific date
      return allEvents.where((event) {
        return event.startTime.year == date.year &&
            event.startTime.month == date.month &&
            event.startTime.day == date.day;
      }).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get events for date: $e');
    }
  }

  @override
  Future<void> saveEvent(CalendarEventModel event) async {
    try {
      await _ensureInitialized();
      final eventsJson =
          _storageService.getData<List<dynamic>>(_eventsKey) ?? [];
      final events =
          eventsJson
              .map(
                (json) => CalendarEventModel.fromJson(
                  Map<String, dynamic>.from(json),
                ),
              )
              .toList();

      // Remove existing event with same ID if it exists
      events.removeWhere((e) => e.id == event.id);

      // Add the new/updated event
      events.add(event);

      // Convert back to JSON and save
      final updatedEventsJson = events.map((e) => e.toJson()).toList();
      await _storageService.setData(_eventsKey, updatedEventsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to save event: $e');
    }
  }

  @override
  Future<void> saveEvents(List<CalendarEventModel> events) async {
    try {
      await _ensureInitialized();
      final existingEventsJson =
          _storageService.getData<List<dynamic>>(_eventsKey) ?? [];
      final existingEvents =
          existingEventsJson
              .map(
                (json) => CalendarEventModel.fromJson(
                  Map<String, dynamic>.from(json),
                ),
              )
              .toList();

      // Remove existing events that match any of the new events
      final newEventIds = events.map((e) => e.id).toSet();
      existingEvents.removeWhere((e) => newEventIds.contains(e.id));

      // Add all new events
      existingEvents.addAll(events);

      // Convert to JSON and save
      final updatedEventsJson = existingEvents.map((e) => e.toJson()).toList();
      await _storageService.setData(_eventsKey, updatedEventsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to save events: $e');
    }
  }

  @override
  Future<CalendarEventModel?> getEventById(String id) async {
    try {
      await _ensureInitialized();
      final eventsJson =
          _storageService.getData<List<dynamic>>(_eventsKey) ?? [];
      final events =
          eventsJson
              .map(
                (json) => CalendarEventModel.fromJson(
                  Map<String, dynamic>.from(json),
                ),
              )
              .toList();

      try {
        return events.firstWhere((event) => event.id == id);
      } catch (e) {
        return null; // Event not found
      }
    } catch (e) {
      throw CacheException(message: 'Failed to get event by ID: $e');
    }
  }

  @override
  Future<void> deleteEvent(String id) async {
    try {
      await _ensureInitialized();
      final eventsJson =
          _storageService.getData<List<dynamic>>(_eventsKey) ?? [];
      final events =
          eventsJson
              .map(
                (json) => CalendarEventModel.fromJson(
                  Map<String, dynamic>.from(json),
                ),
              )
              .toList();

      // Remove event with matching ID
      events.removeWhere((event) => event.id == id);

      // Convert back to JSON and save
      final updatedEventsJson = events.map((e) => e.toJson()).toList();
      await _storageService.setData(_eventsKey, updatedEventsJson);
    } catch (e) {
      throw CacheException(message: 'Failed to delete event: $e');
    }
  }

  @override
  Future<void> clear() async {
    try {
      await _ensureInitialized();
      await _storageService.removeData(_eventsKey);
    } catch (e) {
      throw CacheException(message: 'Failed to clear calendar events: $e');
    }
  }
}
