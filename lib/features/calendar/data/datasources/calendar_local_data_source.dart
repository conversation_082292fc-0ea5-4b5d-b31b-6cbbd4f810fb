import 'package:power_up/features/calendar/data/models/calendar_event_model.dart';

/// Local data source for calendar operations using GetStorage
abstract class CalendarLocalDataSource {
  /// Initialize the local data source
  Future<void> init();

  /// Get calendar events for a specific date range from local storage
  Future<List<CalendarEventModel>> getEventsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get calendar events for a specific date from local storage
  Future<List<CalendarEventModel>> getEventsForDate(DateTime date);

  /// Save a calendar event to local storage
  Future<void> saveEvent(CalendarEventModel event);

  /// Save multiple calendar events to local storage
  Future<void> saveEvents(List<CalendarEventModel> events);

  /// Get a calendar event by ID from local storage
  Future<CalendarEventModel?> getEventById(String id);

  /// Delete a calendar event from local storage
  Future<void> deleteEvent(String id);

  /// Clear all calendar events from local storage
  Future<void> clear();
}
