import 'package:power_up/features/core/data/api/api_client.dart';
import 'package:power_up/core/error/exceptions.dart';
import 'package:power_up/features/tasks/data/models/task_model.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';
import 'package:power_up/features/habits/data/models/habit_model.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'calendar_remote_data_source.dart';
import '../models/calendar_event_model.dart';
import '../../domain/entities/calendar_event_entity.dart';

/// Implementation of CalendarRemoteDataSource with API integration
class CalendarRemoteDataSourceImpl implements CalendarRemoteDataSource {
  final ApiClient apiClient;

  CalendarRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<CalendarEventModel>> getEventsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // Get calendar events from the calendar endpoint
      final calendarEvents = await apiClient.get<List<CalendarEventModel>>(
        endpoint: '/calendar',
        queryParameters: {
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        },
        fromData: (data) {
          final List<dynamic> eventList = data;
          return eventList
              .map((json) => CalendarEventModel.fromJson(json))
              .toList();
        },
      );

      // Get task events separately (they might not be in calendar endpoint)
      final taskEvents = await getTaskEvents(
        startDate: startDate,
        endDate: endDate,
      );

      // Generate habit events client-side (they are based on habit schedules)
      final habitEvents = await getHabitEvents(
        startDate: startDate,
        endDate: endDate,
      );

      // Filter out habit events that might already be in calendar events to avoid duplication
      final Set<String> calendarEventIds =
          calendarEvents
              .where((event) => event.type == CalendarEventType.habit)
              .map((event) => event.relatedId ?? '')
              .where((id) => id.isNotEmpty)
              .toSet();

      final filteredHabitEvents =
          habitEvents.where((habitEvent) {
            final habitId = habitEvent.relatedId ?? '';
            return !calendarEventIds.contains(habitId);
          }).toList();

      // Combine all events
      final allEvents = <CalendarEventModel>[];
      allEvents.addAll(calendarEvents);
      allEvents.addAll(taskEvents);
      allEvents.addAll(filteredHabitEvents);

      return allEvents;
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch calendar events: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<CalendarEventModel>> getEventsForDate(DateTime date) async {
    final endDate = DateTime(date.year, date.month, date.day, 23, 59, 59);
    final startDate = DateTime(date.year, date.month, date.day, 0, 0, 0);

    return getEventsForDateRange(startDate: startDate, endDate: endDate);
  }

  @override
  Future<CalendarEventModel> getEventById(String id) async {
    try {
      return await apiClient.get<CalendarEventModel>(
        endpoint: '/calendar/$id',
        fromData: (data) => CalendarEventModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch calendar event: ${e.toString()}',
      );
    }
  }

  @override
  Future<CalendarEventModel> createEvent({
    required String title,
    String? description,
    required DateTime startTime,
    required DateTime endTime,
    CalendarEventType? type,
    String? color,
  }) async {
    try {
      final eventData = {
        'title': title,
        'description': description,
        'startTime': startTime.toIso8601String(),
        'endTime': endTime.toIso8601String(),
        'type': _eventTypeToString(type ?? CalendarEventType.custom),
        'color': color ?? '#2196F3',
      };

      return await apiClient.post<CalendarEventModel>(
        endpoint: '/calendar',
        data: eventData,
        fromData: (data) => CalendarEventModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to create calendar event: ${e.toString()}',
      );
    }
  }

  @override
  Future<CalendarEventModel> updateEvent({
    required String id,
    String? title,
    String? description,
    DateTime? startTime,
    DateTime? endTime,
    String? color,
  }) async {
    try {
      final updateData = <String, dynamic>{};

      if (title != null) updateData['title'] = title;
      if (description != null) updateData['description'] = description;
      if (startTime != null) {
        updateData['startTime'] = startTime.toIso8601String();
      }
      if (endTime != null) updateData['endTime'] = endTime.toIso8601String();
      if (color != null) updateData['color'] = color;

      return await apiClient.patch<CalendarEventModel>(
        endpoint: '/calendar/$id',
        data: updateData,
        fromData: (data) => CalendarEventModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to update calendar event: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> deleteEvent(String id) async {
    try {
      await apiClient.delete<void>(
        endpoint: '/calendar/$id',
        fromData: (_) {}, // API returns 204 No Content, so data is empty
      );
      return true;
    } catch (e) {
      throw ServerException(
        message: 'Failed to delete calendar event: ${e.toString()}',
      );
    }
  }

  @override
  Future<CalendarEventModel> markEventComplete(String id) async {
    try {
      return await apiClient.patch<CalendarEventModel>(
        endpoint: '/calendar/$id/toggle-complete',
        fromData: (data) => CalendarEventModel.fromJson(data),
      );
    } catch (e) {
      throw ServerException(
        message: 'Failed to mark calendar event as complete: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<CalendarEventModel>> getTaskEvents({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final tasks = await apiClient.get<List<TaskModel>>(
        endpoint: '/tasks',
        queryParameters: {
          'filter': 'all',
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        },
        fromData: (data) {
          final List<dynamic> taskList = data;
          return taskList.map((json) => TaskModel.fromJson(json)).toList();
        },
      );

      // Convert tasks to calendar events
      return tasks.map((task) => _taskToCalendarEvent(task)).toList();
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch task events: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<CalendarEventModel>> getHabitEvents({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final habits = await apiClient.get<List<HabitModel>>(
        endpoint: '/habits',
        fromData: (data) {
          final List<dynamic> habitList = data;
          return habitList.map((json) => HabitModel.fromJson(json)).toList();
        },
      );

      // Convert habits to calendar events for the date range
      final calendarEvents = <CalendarEventModel>[];

      for (final habit in habits) {
        final habitEvents = _habitToCalendarEvents(habit, startDate, endDate);
        calendarEvents.addAll(habitEvents);
      }

      return calendarEvents;
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch habit events: ${e.toString()}',
      );
    }
  }

  /// Helper method to convert a task to a calendar event
  CalendarEventModel _taskToCalendarEvent(TaskModel task) {
    return CalendarEventModel(
      id: 'task_${task.id}',
      title: task.title,
      description: task.description,
      startTime: task.dueDate,
      endTime: task.dueDate.add(
        const Duration(hours: 1),
      ), // Default 1-hour duration
      type: CalendarEventType.task,
      relatedId: task.id,
      color: _getTaskColor(task.priority),
      isCompleted: task.isCompleted,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    );
  }

  /// Helper method to convert a habit to calendar events for a date range
  List<CalendarEventModel> _habitToCalendarEvents(
    HabitModel habit,
    DateTime startDate,
    DateTime endDate,
  ) {
    final events = <CalendarEventModel>[];
    var currentDate = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);

    while (currentDate.isBefore(end) || currentDate.isAtSameMomentAs(end)) {
      if (_shouldHabitOccurOnDate(habit, currentDate)) {
        // Default habit time to 9:00 AM
        final habitTime = DateTime(
          currentDate.year,
          currentDate.month,
          currentDate.day,
          9,
          0,
        );

        events.add(
          CalendarEventModel(
            id:
                'habit_${habit.id}_${currentDate.toIso8601String().split('T')[0]}',
            title: habit.name,
            description: habit.description,
            startTime: habitTime,
            endTime: habitTime.add(
              const Duration(minutes: 30),
            ), // Default 30-min duration
            type: CalendarEventType.habit,
            relatedId: habit.id,
            color: '#4CAF50', // Green for habits
            isCompleted: _isHabitCompletedOnDate(habit, currentDate),
            createdAt: habit.createdAt,
            updatedAt: habit.updatedAt,
          ),
        );
      }
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return events;
  }

  /// Helper method to determine if a habit should occur on a specific date
  bool _shouldHabitOccurOnDate(HabitModel habit, DateTime date) {
    switch (habit.frequency) {
      case HabitFrequency.daily:
        return true;
      case HabitFrequency.weekdays:
        final weekday = date.weekday;
        return weekday >= 1 && weekday <= 5; // Monday to Friday
      case HabitFrequency.weekends:
        final weekday = date.weekday;
        return weekday == 6 || weekday == 7; // Saturday and Sunday
      case HabitFrequency.weekly:
        // For weekly, assume it occurs on the same day of week as created
        return date.weekday == habit.createdAt.weekday;
      case HabitFrequency.monthly:
        // For monthly, assume it occurs on the same day of month as created
        return date.day == habit.createdAt.day;
      case HabitFrequency.custom:
        // Check if the current date's weekday is in the custom days list
        return habit.customDays?.contains(date.weekday) ?? false;
    }
  }

  /// Helper method to check if habit is completed on a specific date
  bool _isHabitCompletedOnDate(HabitModel habit, DateTime date) {
    final dateKey =
        '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    return habit.completionHistory?[dateKey] ?? false;
  }

  /// Helper method to get task color based on priority
  String _getTaskColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return '#F44336'; // Red
      case TaskPriority.medium:
        return '#FF9800'; // Orange
      case TaskPriority.low:
        return '#2196F3'; // Blue
    }
  }

  /// Helper method to convert event type enum to string
  String _eventTypeToString(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return 'task';
      case CalendarEventType.habit:
        return 'habit';
      case CalendarEventType.custom:
        return 'custom';
    }
  }
}
