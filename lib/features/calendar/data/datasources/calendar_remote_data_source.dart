import 'package:power_up/features/calendar/data/models/calendar_event_model.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';

/// Remote data source for calendar operations
abstract class CalendarRemoteDataSource {
  /// Get calendar events for a specific date range from backend
  Future<List<CalendarEventModel>> getEventsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get calendar events for a specific date from backend
  Future<List<CalendarEventModel>> getEventsForDate(DateTime date);

  /// Get a specific calendar event by ID from backend
  Future<CalendarEventModel> getEventById(String id);

  /// Create a custom calendar event on backend
  Future<CalendarEventModel> createEvent({
    required String title,
    String? description,
    required DateTime startTime,
    required DateTime endTime,
    CalendarEventType? type,
    String? color,
  });

  /// Update a calendar event on backend
  Future<CalendarEventModel> updateEvent({
    required String id,
    String? title,
    String? description,
    DateTime? startTime,
    DateTime? endTime,
    String? color,
  });

  /// Delete a calendar event on backend
  Future<bool> deleteEvent(String id);

  /// Mark an event as complete on backend
  Future<CalendarEventModel> markEventComplete(String id);

  /// Get tasks from backend and convert to calendar events
  Future<List<CalendarEventModel>> getTaskEvents({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get habits from backend and convert to calendar events
  Future<List<CalendarEventModel>> getHabitEvents({
    required DateTime startDate,
    required DateTime endDate,
  });
}
