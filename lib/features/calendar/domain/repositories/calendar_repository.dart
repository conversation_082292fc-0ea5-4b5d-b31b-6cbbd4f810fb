import 'package:dartz/dartz.dart';
import 'package:power_up/core/domain/repositories/repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';

/// Repository interface for calendar operations
abstract class CalendarRepository extends Repository {
  /// Get calendar events for a specific date range
  /// Returns Either [Failure] or a list of [CalendarEventEntity]
  Future<Either<Failure, List<CalendarEventEntity>>> getEventsForDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get calendar events for a specific date
  /// Returns Either [Failure] or a list of [CalendarEventEntity]
  Future<Either<Failure, List<CalendarEventEntity>>> getEventsForDate(
    DateTime date,
  );

  /// Get a specific calendar event by ID
  /// Returns Either [Failure] or the [CalendarEventEntity]
  Future<Either<Failure, CalendarEventEntity>> getEventById(String id);

  /// Create a custom calendar event
  /// Returns Either [Failure] or the created [CalendarEventEntity]
  Future<Either<Failure, CalendarEventEntity>> createEvent({
    required String title,
    String? description,
    required DateTime startTime,
    required DateTime endTime,
    CalendarEventType? type,
    String? color,
  });

  /// Update a calendar event
  /// Returns Either [Failure] or the updated [CalendarEventEntity]
  Future<Either<Failure, CalendarEventEntity>> updateEvent({
    required String id,
    String? title,
    String? description,
    DateTime? startTime,
    DateTime? endTime,
    String? color,
  });

  /// Delete a calendar event
  /// Returns Either [Failure] or a [bool] indicating success
  Future<Either<Failure, bool>> deleteEvent(String id);

  /// Mark an event as complete
  /// Returns Either [Failure] or the updated [CalendarEventEntity]
  Future<Either<Failure, CalendarEventEntity>> markEventComplete(String id);

  /// Sync calendar with tasks and habits
  /// This method fetches tasks and habits and creates corresponding calendar events
  /// Returns Either [Failure] or a [bool] indicating success
  Future<Either<Failure, bool>> syncCalendar();
}
