import 'package:equatable/equatable.dart';

/// Types of calendar events
enum CalendarEventType { task, habit, custom }

/// Domain entity for a calendar event
class CalendarEventEntity extends Equatable {
  final String id;
  final String title;
  final String? description;
  final DateTime startTime;
  final DateTime endTime;
  final CalendarEventType type;
  final String? relatedId; // ID of the related task/habit
  final String? color; // Hex color code
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CalendarEventEntity({
    required this.id,
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    required this.type,
    this.relatedId,
    this.color,
    required this.isCompleted,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this CalendarEventEntity with the given fields replaced
  CalendarEventEntity copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? startTime,
    DateTime? endTime,
    CalendarEventType? type,
    String? relatedId,
    String? color,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CalendarEventEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      type: type ?? this.type,
      relatedId: relatedId ?? this.relatedId,
      color: color ?? this.color,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Mark event as complete
  CalendarEventEntity markAsComplete() {
    return copyWith(isCompleted: true, updatedAt: DateTime.now());
  }

  /// Check if event is overdue
  bool get isOverdue => !isCompleted && endTime.isBefore(DateTime.now());

  /// Check if event is today
  bool get isToday {
    final now = DateTime.now();
    return startTime.year == now.year &&
        startTime.month == now.month &&
        startTime.day == now.day;
  }

  /// Check if event is all day
  bool get isAllDay {
    return startTime.hour == 0 &&
        startTime.minute == 0 &&
        endTime.hour == 23 &&
        endTime.minute == 59;
  }

  /// Get duration of the event
  Duration get duration => endTime.difference(startTime);

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    startTime,
    endTime,
    type,
    relatedId,
    color,
    isCompleted,
    createdAt,
    updatedAt,
  ];
}
