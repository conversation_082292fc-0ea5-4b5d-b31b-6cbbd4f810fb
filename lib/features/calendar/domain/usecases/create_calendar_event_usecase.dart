import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Use case for creating a new calendar event
class CreateCalendarEventUseCase
    implements UseCase<CalendarEventEntity, CreateCalendarEventParams> {
  final CalendarRepository repository;

  CreateCalendarEventUseCase(this.repository);

  @override
  Future<Either<Failure, CalendarEventEntity>> call(
    CreateCalendarEventParams params,
  ) async {
    // Validate input
    if (params.title.isEmpty) {
      return const Left(
        ValidationFailure(message: 'Event title cannot be empty'),
      );
    }

    if (params.endTime.isBefore(params.startTime)) {
      return const Left(
        ValidationFailure(message: 'End time cannot be before start time'),
      );
    }

    return repository.createEvent(
      title: params.title,
      description: params.description,
      startTime: params.startTime,
      endTime: params.endTime,
      type: params.type,
      color: params.color,
    );
  }
}

/// Parameters for CreateCalendarEventUseCase
class CreateCalendarEventParams extends Equatable {
  final String title;
  final String? description;
  final DateTime startTime;
  final DateTime endTime;
  final CalendarEventType? type;
  final String? color;

  const CreateCalendarEventParams({
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    this.type,
    this.color,
  });

  @override
  List<Object?> get props => [
    title,
    description,
    startTime,
    endTime,
    type,
    color,
  ];
}
