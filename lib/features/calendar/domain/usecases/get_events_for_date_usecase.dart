import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Use case for getting calendar events for a specific date
class GetEventsForDateUseCase
    implements UseCase<List<CalendarEventEntity>, GetEventsForDateParams> {
  final CalendarRepository repository;

  GetEventsForDateUseCase(this.repository);

  @override
  Future<Either<Failure, List<CalendarEventEntity>>> call(
    GetEventsForDateParams params,
  ) async {
    return repository.getEventsForDate(params.date);
  }
}

/// Parameters for GetEventsForDateUseCase
class GetEventsForDateParams extends Equatable {
  final DateTime date;

  const GetEventsForDateParams({required this.date});

  @override
  List<Object?> get props => [date];
}
