import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Use case for updating a calendar event
class UpdateCalendarEventUseCase
    implements UseCase<CalendarEventEntity, UpdateCalendarEventParams> {
  final CalendarRepository repository;

  UpdateCalendarEventUseCase(this.repository);

  @override
  Future<Either<Failure, CalendarEventEntity>> call(
    UpdateCalendarEventParams params,
  ) async {
    return await repository.updateEvent(
      id: params.id,
      title: params.title,
      description: params.description,
      startTime: params.startTime,
      endTime: params.endTime,
      color: params.color,
    );
  }
}

/// Parameters for UpdateCalendarEventUseCase
class UpdateCalendarEventParams extends Equatable {
  final String id;
  final String? title;
  final String? description;
  final DateTime? startTime;
  final DateTime? endTime;
  final String? color;

  const UpdateCalendarEventParams({
    required this.id,
    this.title,
    this.description,
    this.startTime,
    this.endTime,
    this.color,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    startTime,
    endTime,
    color,
  ];
}
