import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Use case for deleting a calendar event
class DeleteCalendarEventUseCase
    implements UseCase<bool, DeleteCalendarEventParams> {
  final CalendarRepository repository;

  DeleteCalendarEventUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(DeleteCalendarEventParams params) async {
    return await repository.deleteEvent(params.id);
  }
}

/// Parameters for DeleteCalendarEventUseCase
class DeleteCalendarEventParams extends Equatable {
  final String id;

  const DeleteCalendarEventParams({required this.id});

  @override
  List<Object> get props => [id];
}
