import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Use case for getting a specific calendar event by ID
class GetCalendarEventByIdUseCase
    implements UseCase<CalendarEventEntity, GetCalendarEventByIdParams> {
  final CalendarRepository repository;

  GetCalendarEventByIdUseCase(this.repository);

  @override
  Future<Either<Failure, CalendarEventEntity>> call(
    GetCalendarEventByIdParams params,
  ) async {
    return await repository.getEventById(params.id);
  }
}

/// Parameters for GetCalendarEventByIdUseCase
class GetCalendarEventByIdParams extends Equatable {
  final String id;

  const GetCalendarEventByIdParams({required this.id});

  @override
  List<Object> get props => [id];
}
