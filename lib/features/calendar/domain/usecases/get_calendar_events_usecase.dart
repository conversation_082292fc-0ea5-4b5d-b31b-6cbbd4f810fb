import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/entities/calendar_event_entity.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Use case for getting calendar events for a specific date range
class GetCalendarEventsUseCase
    implements UseCase<List<CalendarEventEntity>, GetCalendarEventsParams> {
  final CalendarRepository repository;

  GetCalendarEventsUseCase(this.repository);

  @override
  Future<Either<Failure, List<CalendarEventEntity>>> call(
    GetCalendarEventsParams params,
  ) async {
    // Validate date range
    if (params.endDate.isBefore(params.startDate)) {
      return const Left(
        ValidationFailure(message: 'End date cannot be before start date'),
      );
    }

    return repository.getEventsForDateRange(
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

/// Parameters for GetCalendarEventsUseCase
class GetCalendarEventsParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;

  const GetCalendarEventsParams({
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [startDate, endDate];
}
