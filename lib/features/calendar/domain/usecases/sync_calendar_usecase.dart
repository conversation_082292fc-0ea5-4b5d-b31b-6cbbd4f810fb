import 'package:dartz/dartz.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/usecases/usecase.dart';
import 'package:power_up/features/calendar/domain/repositories/calendar_repository.dart';

/// Use case for syncing calendar with tasks and habits
class SyncCalendarUseCase implements UseCase<bool, NoParams> {
  final CalendarRepository repository;

  SyncCalendarUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(NoParams params) async {
    return repository.syncCalendar();
  }
}
