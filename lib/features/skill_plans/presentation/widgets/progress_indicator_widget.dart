import 'package:flutter/material.dart';

/// A custom progress indicator widget
class ProgressIndicatorWidget extends StatelessWidget {
  final double progress;
  final double height;
  final Color? backgroundColor;
  final Color? progressColor;
  final BorderRadius? borderRadius;

  const ProgressIndicatorWidget({
    Key? key,
    required this.progress,
    this.height = 8.0,
    this.backgroundColor,
    this.progressColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor =
        backgroundColor ?? theme.colorScheme.primary.withValues(alpha: 0.2);
    final pgColor = progressColor ?? theme.colorScheme.primary;
    final radius = borderRadius ?? BorderRadius.circular(height / 2);

    return Container(
      height: height,
      decoration: BoxDecoration(color: bgColor, borderRadius: radius),
      child: Stack(
        children: [
          // Progress fill
          FractionallySizedBox(
            widthFactor: progress / 100,
            child: Container(
              height: height,
              decoration: BoxDecoration(color: pgColor, borderRadius: radius),
            ),
          ),
        ],
      ),
    );
  }
}
