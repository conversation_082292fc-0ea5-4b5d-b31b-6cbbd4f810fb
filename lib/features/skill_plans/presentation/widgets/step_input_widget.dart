import 'package:flutter/material.dart';
import 'package:power_up/features/skill_plans/domain/entities/skill_step_entity.dart';

/// Widget for adding a new step to a custom skill plan
class StepInputWidget extends StatefulWidget {
  /// Callback for adding a new step
  final Function(SkillStepEntity) onAddStep;

  /// Current step order (for determining next order)
  final int currentStepCount;

  /// Existing step to edit (null for new step)
  final SkillStepEntity? existingStep;

  const StepInputWidget({
    Key? key,
    required this.onAddStep,
    this.currentStepCount = 0,
    this.existingStep,
  }) : super(key: key);

  @override
  State<StepInputWidget> createState() => _StepInputWidgetState();
}

class _StepInputWidgetState extends State<StepInputWidget> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _taskController = TextEditingController();
  final List<SkillStepTaskEntity> _tasks = [];
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _initializeWithExistingData();
  }

  void _initializeWithExistingData() {
    if (widget.existingStep != null) {
      _titleController.text = widget.existingStep!.title;
      _descriptionController.text = widget.existingStep!.description;
      _tasks.addAll(widget.existingStep!.tasks);
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _taskController.dispose();
    super.dispose();
  }

  void _addTask() {
    final taskDescription = _taskController.text.trim();
    if (taskDescription.isNotEmpty) {
      setState(() {
        _tasks.add(
          SkillStepTaskEntity(
            id: '', // Server will generate ID
            description: taskDescription,
          ),
        );
      });
      _taskController.clear();
    }
  }

  void _removeTask(int index) {
    setState(() {
      _tasks.removeAt(index);
    });
  }

  void _saveStep() {
    if (_formKey.currentState?.validate() ?? false) {
      final step = SkillStepEntity(
        id:
            widget.existingStep?.id ??
            '', // Preserve existing ID or use empty for new
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        order:
            widget.existingStep?.order ??
            (widget.currentStepCount + 1), // Preserve existing order
        tasks: List.from(_tasks),
      );

      widget.onAddStep(step);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxHeight: 600, maxWidth: 500),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Add New Step',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Form
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Step title
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Step Title',
                        hintText: 'Enter step title',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.title),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a step title';
                        }
                        return null;
                      },
                      maxLength: 100,
                    ),

                    const SizedBox(height: 16),

                    // Step description
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Step Description',
                        hintText: 'Describe what this step involves',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a step description';
                        }
                        return null;
                      },
                      maxLength: 500,
                    ),

                    const SizedBox(height: 16),

                    // Tasks section
                    Text(
                      'Tasks (Optional)',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Task input
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _taskController,
                            decoration: const InputDecoration(
                              labelText: 'Add Task',
                              hintText: 'Enter a task for this step',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.task_alt),
                            ),
                            onFieldSubmitted: (_) => _addTask(),
                            maxLength: 200,
                            buildCounter:
                                (
                                  context, {
                                  required currentLength,
                                  required isFocused,
                                  maxLength,
                                }) => null,
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _addTask,
                          child: const Text('Add'),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Tasks list
                    if (_tasks.isNotEmpty) ...[
                      Text(
                        'Tasks for this step:',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        constraints: const BoxConstraints(maxHeight: 150),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _tasks.length,
                          itemBuilder: (context, index) {
                            final task = _tasks[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: const Icon(Icons.task_alt),
                                title: Text(task.description),
                                trailing: IconButton(
                                  onPressed: () => _removeTask(index),
                                  icon: const Icon(Icons.delete),
                                  color: theme.colorScheme.error,
                                ),
                                dense: true,
                              ),
                            );
                          },
                        ),
                      ),
                    ] else ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: theme.dividerColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'No tasks added yet. You can add tasks to break down this step into smaller actions.',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.hintColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _saveStep,
                child: const Text('Add Step'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
