import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/utils/url_launcher.dart';
import '../controllers/skill_plans_controller.dart';
import '../../domain/entities/skill_step_entity.dart';

/// A widget to display a skill step as a card
class SkillStepCard extends StatelessWidget {
  final SkillStepEntity step;
  final int stepNumber;
  final String planId;

  const SkillStepCard({
    Key? key,
    required this.step,
    required this.stepNumber,
    required this.planId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Step header with number and completion status
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Step number circle
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color:
                        step.isCompleted
                            ? theme.colorScheme.primary
                            : theme.colorScheme.primary.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      stepNumber.toString(),
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color:
                            step.isCompleted
                                ? theme.colorScheme.onPrimary
                                : theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Step title and description
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        step.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        step.description,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Tasks section (if any)
            if (step.tasks.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Tasks',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...step.tasks.asMap().entries.map(
                (entry) => _buildTaskItem(context, entry.value, entry.key),
              ),
            ],

            // Resources section (if any)
            if (step.resources.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Resources',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...step.resources.map(
                (resource) => _buildResourceItem(context, resource),
              ),
            ],

            // Completion date (if completed)
            if (step.completedAt != null) ...[
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Completed on ${_formatDate(step.completedAt!)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.primary,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTaskItem(
    BuildContext context,
    SkillStepTaskEntity task,
    int taskIndex,
  ) {
    final theme = Theme.of(context);
    final controller = Get.find<SkillPlansController>();

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: InkWell(
        onTap: () {
          // Toggle task completion
          controller.toggleTaskCompletion(
            planId,
            step.id,
            taskIndex,
            !task.isCompleted,
          );
        },
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                task.isCompleted
                    ? Icons.check_circle
                    : Icons.radio_button_unchecked,
                color:
                    task.isCompleted ? theme.colorScheme.primary : Colors.grey,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  task.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    decoration:
                        task.isCompleted ? TextDecoration.lineThrough : null,
                    color:
                        task.isCompleted
                            ? theme.colorScheme.onSurface.withOpacity(0.6)
                            : theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  Widget _buildResourceItem(BuildContext context, dynamic resource) {
    final theme = Theme.of(context);

    // Default resource type icon
    IconData resourceIcon = Icons.link;

    // Set different icons based on resource type if available
    if (resource.type != null) {
      switch (resource.type.toString()) {
        case 'ResourceType.article':
          resourceIcon = Icons.article;
          break;
        case 'ResourceType.video':
          resourceIcon = Icons.video_library;
          break;
        case 'ResourceType.audio':
          resourceIcon = Icons.headphones;
          break;
        case 'ResourceType.book':
          resourceIcon = Icons.book;
          break;
        case 'ResourceType.exercise':
          resourceIcon = Icons.fitness_center;
          break;
        case 'ResourceType.website':
          resourceIcon = Icons.language;
          break;
        default:
          resourceIcon = Icons.link;
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: InkWell(
        onTap: () {
          // Launch the URL when tapped
          UrlLauncherUtil.launchURL(resource.url);
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Icon(resourceIcon, color: theme.colorScheme.primary, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      resource.title,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (resource.description != null &&
                        resource.description.isNotEmpty)
                      Text(
                        resource.description,
                        style: theme.textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              const Icon(Icons.open_in_new, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}
