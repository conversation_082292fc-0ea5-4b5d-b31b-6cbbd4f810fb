import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../domain/entities/skill_plan_entity.dart';

/// A widget to display skill plan metadata information
class SkillPlanMetaInfo extends StatelessWidget {
  final SkillPlanMetadata metadata;

  const SkillPlanMetaInfo({Key? key, required this.metadata}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Plan Information',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 16),

        // Info cards grid
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: [
            _buildInfoCard(
              context,
              'Category',
              _getCategoryText(metadata.category),
              _getCategoryIcon(metadata.category),
              _getCategoryColor(metadata.category),
            ),
            _buildInfoCard(
              context,
              'Difficulty',
              (metadata.difficulty?.capitalizeFirst ?? metadata.difficulty) ??
                  'Unknown',
              _getDifficultyIcon(metadata.difficulty),
              _getDifficultyColor(metadata.difficulty),
            ),
            _buildInfoCard(
              context,
              'Est. Duration',
              metadata.estimatedDuration ?? 'Not specified',
              Icons.calendar_today,
              Colors.purple,
            ),
          ],
        ),

        // Tags section
        if (metadata.tags.isNotEmpty) ...[
          const SizedBox(height: 24),

          Text(
            'Tags',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                metadata.tags
                    .map((tag) => _buildTagChip(context, tag))
                    .toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildInfoCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      width: 100,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTagChip(BuildContext context, String tag) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: theme.colorScheme.primary.withOpacity(0.3)),
      ),
      child: Text(
        tag,
        style: TextStyle(
          color: theme.colorScheme.primary,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _getCategoryText(String? category) {
    if (category == null || category.isEmpty) return 'Uncategorized';

    switch (category.toLowerCase()) {
      case 'personal':
        return 'Personal';
      case 'professional':
        return 'Professional';
      case 'health':
        return 'Health';
      case 'education':
        return 'Education';
      case 'hobby':
        return 'Hobby';
      default:
        return 'Other';
    }
  }

  IconData _getCategoryIcon(String? category) {
    if (category == null || category.isEmpty) return Icons.category;

    switch (category.toLowerCase()) {
      case 'personal':
        return Icons.person;
      case 'professional':
        return Icons.work;
      case 'health':
        return Icons.favorite;
      case 'education':
        return Icons.school;
      case 'hobby':
        return Icons.palette;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String? category) {
    if (category == null || category.isEmpty) return Colors.grey;

    switch (category.toLowerCase()) {
      case 'personal':
        return Colors.blue;
      case 'professional':
        return Colors.green;
      case 'health':
        return Colors.red;
      case 'education':
        return Colors.purple;
      case 'hobby':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  IconData _getDifficultyIcon(String? difficulty) {
    if (difficulty == null || difficulty.isEmpty) return Icons.trending_up;

    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Icons.trending_flat;
      case 'intermediate':
        return Icons.trending_up;
      case 'advanced':
        return Icons.signal_cellular_alt;
      default:
        return Icons.trending_up;
    }
  }

  Color _getDifficultyColor(String? difficulty) {
    if (difficulty == null || difficulty.isEmpty) return Colors.blue;

    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}
