import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Widget for adding and managing tags in a custom skill plan
class TagInputWidget extends StatefulWidget {
  /// Callback for adding a new tag
  final Function(String) onAddTag;

  /// Callback for removing a tag
  final Function(String) onRemoveTag;

  /// Current list of tags
  final RxList<String> tags;

  const TagInputWidget({
    Key? key,
    required this.onAddTag,
    required this.onRemoveTag,
    required this.tags,
  }) : super(key: key);

  @override
  State<TagInputWidget> createState() => _TagInputWidgetState();
}

class _TagInputWidgetState extends State<TagInputWidget> {
  final TextEditingController _tagController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _tagController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty) {
      widget.onAddTag(tag);
      _tagController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),

        // Tag input field
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _tagController,
                focusNode: _focusNode,
                decoration: const InputDecoration(
                  labelText: 'Add Tag',
                  hintText: 'Enter a tag and press Add',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.tag),
                ),
                onFieldSubmitted: (_) => _addTag(),
                maxLength: 50,
                buildCounter:
                    (
                      context, {
                      required currentLength,
                      required isFocused,
                      maxLength,
                    }) => null,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(onPressed: _addTag, child: const Text('Add')),
          ],
        ),

        const SizedBox(height: 16),

        // Tags display
        Obx(() {
          if (widget.tags.isEmpty) {
            return Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: theme.dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'No tags added yet. Add tags to help categorize your plan.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.hintColor,
                ),
                textAlign: TextAlign.center,
              ),
            );
          }

          return Wrap(
            spacing: 8.0,
            runSpacing: 8.0,
            children:
                widget.tags.map((tag) {
                  return Chip(
                    label: Text(tag),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => widget.onRemoveTag(tag),
                    backgroundColor: theme.colorScheme.primaryContainer,
                    labelStyle: TextStyle(
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  );
                }).toList(),
          );
        }),
      ],
    );
  }
}
