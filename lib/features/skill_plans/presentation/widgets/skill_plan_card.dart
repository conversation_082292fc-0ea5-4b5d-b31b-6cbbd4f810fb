import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../domain/entities/skill_plan_entity.dart';
import '../controllers/skill_plans_controller.dart';
import '../screens/update_skill_plan_screen.dart';
import 'progress_indicator_widget.dart';

/// A widget to display a skill plan as a card
class SkillPlanCard extends StatelessWidget {
  final SkillPlanEntity plan;
  final VoidCallback? onTap;

  const SkillPlanCard({Key? key, required this.plan, this.onTap})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Plan name and category badge
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      plan.name,
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildCategoryChip(theme),
                  const SizedBox(width: 8),
                  _buildActionMenu(context),
                ],
              ),

              const SizedBox(height: 8),

              // Description
              Text(
                plan.description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 16),

              // Meta data row
              Row(
                children: [
                  _buildInfoItem(
                    context,
                    Icons.access_time,
                    plan.metadata.estimatedDuration ?? 'Not specified',
                  ),
                  const SizedBox(width: 16),
                  _buildInfoItem(
                    context,
                    Icons.trending_up,
                    _getDifficultyText(plan.metadata.difficulty),
                  ),
                  const SizedBox(width: 16),
                  _buildInfoItem(
                    context,
                    Icons.stairs,
                    '${plan.steps.length} steps',
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Progress bar
              Row(
                children: [
                  Expanded(
                    child: ProgressIndicatorWidget(
                      progress: plan.progress,
                      height: 8,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    '${plan.progress.round()}%',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryChip(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getCategoryColor(plan.metadata.category).withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _getCategoryText(plan.metadata.category),
        style: TextStyle(
          color: _getCategoryColor(plan.metadata.category),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, IconData icon, String text) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: theme.colorScheme.onSurface.withOpacity(0.7),
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  String _getCategoryText(String? category) {
    if (category == null || category.isEmpty) return 'Uncategorized';

    switch (category.toLowerCase()) {
      case 'personal':
        return 'Personal';
      case 'professional':
        return 'Professional';
      case 'health':
        return 'Health';
      case 'education':
        return 'Education';
      case 'hobby':
        return 'Hobby';
      default:
        return 'Other';
    }
  }

  Color _getCategoryColor(String? category) {
    if (category == null || category.isEmpty) return Colors.grey;

    switch (category.toLowerCase()) {
      case 'personal':
        return Colors.blue;
      case 'professional':
        return Colors.green;
      case 'health':
        return Colors.red;
      case 'education':
        return Colors.purple;
      case 'hobby':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  String _getDifficultyText(String? difficulty) {
    if (difficulty == null || difficulty.isEmpty) return 'Unknown';
    return difficulty.capitalizeFirst ?? difficulty;
  }

  Widget _buildActionMenu(BuildContext context) {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            _navigateToUpdateScreen();
            break;
          case 'delete':
            _showDeleteConfirmationDialog(context);
            break;
        }
      },
      itemBuilder:
          (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
      child: const Icon(Icons.more_vert, size: 20),
    );
  }

  /// Navigate to update skill plan screen
  void _navigateToUpdateScreen() {
    Get.to(() => UpdateSkillPlanScreen(skillPlan: plan));
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmationDialog(BuildContext context) {
    final controller = Get.find<SkillPlansController>();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Skill Plan'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Are you sure you want to delete this skill plan?'),
                const SizedBox(height: 8),
                Text(
                  plan.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  'This action cannot be undone.',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Get.back(); // Close dialog first

                  final success = await controller.deleteSkillPlan(plan.id);

                  if (success) {
                    Get.snackbar('Success', 'Plan deleted successfully');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
