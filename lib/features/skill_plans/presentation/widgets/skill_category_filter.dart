import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/skill_plans_controller.dart';

/// A widget for filtering skill plans by category
class SkillCategoryFilter extends GetView<SkillPlansController> {
  const SkillCategoryFilter({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // List of available categories
    final List<Map<String, dynamic>> categories = [
      {'id': '', 'name': 'All', 'color': Colors.grey, 'icon': Icons.category},
      {
        'id': 'personal',
        'name': 'Personal',
        'color': Colors.blue,
        'icon': Icons.person,
      },
      {
        'id': 'professional',
        'name': 'Professional',
        'color': Colors.green,
        'icon': Icons.work,
      },
      {
        'id': 'health',
        'name': 'Health',
        'color': Colors.red,
        'icon': Icons.favorite,
      },
      {
        'id': 'education',
        'name': 'Education',
        'color': Colors.purple,
        'icon': Icons.school,
      },
      {
        'id': 'hobby',
        'name': 'Hobby',
        'color': Colors.amber,
        'icon': Icons.palette,
      },
      {
        'id': 'other',
        'name': 'Other',
        'color': Colors.brown,
        'icon': Icons.more_horiz,
      },
    ];

    return SizedBox(
      height: 90,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];

          return Obx(() {
            final isSelected =
                controller.categoryFilter.value == category['id'];

            return Padding(
              padding: const EdgeInsets.only(right: 12),
              child: InkWell(
                onTap: () => controller.categoryFilter.value = category['id'],
                borderRadius: BorderRadius.circular(12),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? category['color']
                                : category['color'].withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border:
                            isSelected
                                ? Border.all(color: category['color'], width: 2)
                                : null,
                      ),
                      child: Icon(
                        category['icon'],
                        color: isSelected ? Colors.white : category['color'],
                        size: 28,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      category['name'],
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                        color:
                            isSelected
                                ? category['color']
                                : theme.colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            );
          });
        },
      ),
    );
  }
}
