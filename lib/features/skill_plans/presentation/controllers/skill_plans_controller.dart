import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:power_up/core/presentation/controllers/base_controller.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/skill_plans/domain/entities/skill_plan_entity.dart';
import 'package:power_up/features/skill_plans/domain/entities/skill_step_entity.dart';
import 'package:power_up/features/skill_plans/domain/usecases/get_prebuilt_plans_usecase.dart';
import 'package:power_up/features/skill_plans/domain/usecases/create_custom_plan_usecase.dart';
import 'package:power_up/features/skill_plans/domain/usecases/update_skill_plan_progress_usecase.dart';
import 'package:power_up/features/skill_plans/domain/usecases/update_skill_plan_usecase.dart';
import 'package:power_up/features/skill_plans/domain/usecases/delete_skill_plan_usecase.dart';
import 'package:power_up/features/skill_plans/domain/usecases/mark_skill_plan_task_complete_usecase.dart';

/// Controller for skill plans feature
class SkillPlansController extends BaseController {
  // Use Cases
  final GetPrebuiltPlansUseCase _getPrebuiltPlansUseCase;
  final CreateCustomPlanUseCase _createCustomPlanUseCase;
  final UpdateSkillPlanProgressUseCase _updateSkillPlanProgressUseCase;
  final UpdateSkillPlanUseCase _updateSkillPlanUseCase;
  final DeleteSkillPlanUseCase _deleteSkillPlanUseCase;
  final MarkSkillPlanTaskCompleteUseCase _markSkillPlanTaskCompleteUseCase;

  // Reactive state
  final RxList<SkillPlanEntity> _prebuiltPlans = <SkillPlanEntity>[].obs;
  final RxList<SkillPlanEntity> _userPlans = <SkillPlanEntity>[].obs;
  final Rx<SkillPlanEntity?> _selectedPlan = Rx<SkillPlanEntity?>(null);

  // Form controllers for creating custom plans
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  // Category filter
  final RxString categoryFilter = ''.obs;

  // Custom plan form values
  final RxBool isPublic = false.obs;
  final Rx<String> selectedCategory = 'personal'.obs;
  final Rx<String> selectedDifficulty = 'beginner'.obs;
  final Rx<String> estimatedDuration =
      '2 weeks'
          .obs; // Changed from int estimatedDays to String estimatedDuration
  final RxBool useCustomDuration = false.obs;
  final TextEditingController customDurationController =
      TextEditingController();
  final RxList<String> tags = <String>[].obs;
  final RxList<SkillStepEntity> steps = <SkillStepEntity>[].obs;

  // Getters
  List<SkillPlanEntity> get prebuiltPlans => _prebuiltPlans;
  List<SkillPlanEntity> get userPlans => _userPlans;
  SkillPlanEntity? get selectedPlan => _selectedPlan.value;

  /// Constructor
  SkillPlansController({
    required GetPrebuiltPlansUseCase getPrebuiltPlansUseCase,
    required CreateCustomPlanUseCase createCustomPlanUseCase,
    required UpdateSkillPlanProgressUseCase updateSkillPlanProgressUseCase,
    required UpdateSkillPlanUseCase updateSkillPlanUseCase,
    required DeleteSkillPlanUseCase deleteSkillPlanUseCase,
    required MarkSkillPlanTaskCompleteUseCase markSkillPlanTaskCompleteUseCase,
  }) : _getPrebuiltPlansUseCase = getPrebuiltPlansUseCase,
       _createCustomPlanUseCase = createCustomPlanUseCase,
       _updateSkillPlanProgressUseCase = updateSkillPlanProgressUseCase,
       _updateSkillPlanUseCase = updateSkillPlanUseCase,
       _deleteSkillPlanUseCase = deleteSkillPlanUseCase,
       _markSkillPlanTaskCompleteUseCase = markSkillPlanTaskCompleteUseCase;

  @override
  void onInit() {
    super.onInit();
    fetchPrebuiltPlans();
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    customDurationController.dispose();
    super.onClose();
  }

  /// Fetch all pre-built plans
  Future<void> fetchPrebuiltPlans() async {
    setLoading(true);

    final result = await _getPrebuiltPlansUseCase(
      const GetPrebuiltPlansParams(),
    );
    result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', 'Failed to load pre-built skill plans.');
      },
      (plans) {
        _prebuiltPlans.assignAll(plans);
      },
    );

    setLoading(false);
  }

  /// Create a new custom plan
  Future<bool> createCustomPlan() async {
    if (nameController.text.isEmpty || descriptionController.text.isEmpty) {
      showSnackbar('Error', 'Please fill all required fields.');
      return false;
    }

    if (steps.isEmpty) {
      showSnackbar('Error', 'Please add at least one step to your plan.');
      return false;
    }

    setLoading(true);

    final metadata = SkillPlanMetadata(
      category:
          selectedCategory.value.isNotEmpty ? selectedCategory.value : null,
      difficulty:
          selectedDifficulty.value.isNotEmpty ? selectedDifficulty.value : null,
      estimatedDuration: _getEffectiveEstimatedDuration(),
      tags: tags.toList(),
    );

    final params = CreateCustomPlanParams(
      name: nameController.text,
      description: descriptionController.text,
      isPublic: isPublic.value,
      metadata: metadata,
      steps: steps,
    );

    final result = await _createCustomPlanUseCase(params);

    setLoading(false);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', 'Failed to create custom plan.');
        return false;
      },
      (plan) {
        _userPlans.add(plan);
        showSnackbar('Success', 'Custom plan created successfully.');
        clearForm();
        return true;
      },
    );
  }

  /// Update progress for a skill plan step
  Future<bool> updatePlanProgress(
    String planId,
    int stepOrder,
    bool completed,
  ) async {
    setLoading(true);

    final params = UpdateSkillPlanProgressParams(
      planId: planId,
      order: stepOrder,
      completed: completed,
    );

    final result = await _updateSkillPlanProgressUseCase(params);

    setLoading(false);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', 'Failed to update plan progress.');
        return false;
      },
      (progressResponse) {
        // Progress update was successful - no need to update local plan state
        // since the response only contains progress information, not the full plan
        if (kDebugMode) {
          print(
            'Progress updated successfully: ${progressResponse.progress}% complete, steps completed: ${progressResponse.completedSteps}',
          );
        }
        return true;
      },
    );
  }

  /// Update an existing skill plan
  Future<bool> updateSkillPlan({
    required String id,
    String? name,
    String? description,
    bool? isPublic,
    SkillPlanMetadata? metadata,
    List<SkillStepEntity>? steps,
  }) async {
    setLoading(true);

    final params = UpdateSkillPlanParams(
      id: id,
      name: name,
      description: description,
      isPublic: isPublic,
      metadata: metadata,
      steps: steps,
    );

    final result = await _updateSkillPlanUseCase(params);

    setLoading(false);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', 'Failed to update skill plan.');
        return false;
      },
      (updatedPlan) {
        // Update the plan in the appropriate list
        final prebuiltIndex = _prebuiltPlans.indexWhere((p) => p.id == id);
        if (prebuiltIndex != -1) {
          _prebuiltPlans[prebuiltIndex] = updatedPlan;
        }

        final userIndex = _userPlans.indexWhere((p) => p.id == id);
        if (userIndex != -1) {
          _userPlans[userIndex] = updatedPlan;
        }

        // Update selected plan if it's the one being updated
        if (_selectedPlan.value?.id == id) {
          _selectedPlan.value = updatedPlan;
        }

        showSnackbar('Success', 'Skill plan updated successfully.');
        Get.back(); // Navigate back after successful update
        return true;
      },
    );
  }

  /// Update a skill plan using current form data
  Future<bool> updateSkillPlanFromForm(String id) async {
    // Validate form
    if (nameController.text.trim().isEmpty) {
      showSnackbar('Error', 'Plan name cannot be empty.');
      return false;
    }

    if (steps.isEmpty) {
      showSnackbar('Error', 'Please add at least one step to your plan.');
      return false;
    }

    setLoading(true);

    final metadata = SkillPlanMetadata(
      category:
          selectedCategory.value.isNotEmpty ? selectedCategory.value : null,
      difficulty:
          selectedDifficulty.value.isNotEmpty ? selectedDifficulty.value : null,
      estimatedDuration: _getEffectiveEstimatedDuration(),
      tags: tags.toList(),
    );

    final params = UpdateSkillPlanParams(
      id: id,
      name: nameController.text.trim(),
      description: descriptionController.text.trim(),
      isPublic: isPublic.value,
      metadata: metadata,
      steps: steps.toList(),
    );

    final result = await _updateSkillPlanUseCase(params);

    setLoading(false);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', 'Failed to update skill plan.');
        return false;
      },
      (updatedPlan) {
        // Update the plan in the appropriate list
        final prebuiltIndex = _prebuiltPlans.indexWhere((p) => p.id == id);
        if (prebuiltIndex != -1) {
          _prebuiltPlans[prebuiltIndex] = updatedPlan;
        }

        final userIndex = _userPlans.indexWhere((p) => p.id == id);
        if (userIndex != -1) {
          _userPlans[userIndex] = updatedPlan;
        }

        // Update selected plan if it's the one being updated
        if (_selectedPlan.value?.id == id) {
          _selectedPlan.value = updatedPlan;
        }

        Get.back(); // Navigate back after successful update
        clearForm();
        showSnackbar('Success', 'Skill plan updated successfully.');
        return true;
      },
    );
  }

  /// Delete a skill plan
  Future<bool> deleteSkillPlan(String id) async {
    setLoading(true);

    final params = DeleteSkillPlanParams(id: id);
    final result = await _deleteSkillPlanUseCase(params);

    setLoading(false);

    return result.fold(
      (failure) {
        handleError(failure);
        showSnackbar('Error', 'Failed to delete skill plan.');
        return false;
      },
      (_) {
        // Remove the plan from the appropriate list
        _prebuiltPlans.removeWhere((p) => p.id == id);
        _userPlans.removeWhere((p) => p.id == id);

        // Clear selected plan if it's the one being deleted
        if (_selectedPlan.value?.id == id) {
          _selectedPlan.value = null;
        }

        showSnackbar('Success', 'Skill plan deleted successfully.');
        return true;
      },
    );
  }

  /// Mark a task as complete or incomplete
  Future<void> toggleTaskCompletion(
    String planId,
    String stepId,
    int taskIndex,
    bool isCompleted,
  ) async {
    try {
      setLoading(true);

      // First, mark the task as complete via API
      final params = MarkSkillPlanTaskCompleteParams(
        planId: planId,
        stepId: stepId,
        taskIndex: taskIndex,
        isComplete: isCompleted,
      );

      final result = await _markSkillPlanTaskCompleteUseCase(params);

      result.fold(
        (failure) {
          handleError(failure);
          showSnackbar('Error', 'Failed to update task completion.');
        },
        (updatedStep) async {
          // Find the current plan that contains this step
          SkillPlanEntity? currentPlan;

          // Try to find the plan in selected, prebuilt, or user plans
          if (_selectedPlan.value?.id == planId) {
            currentPlan = _selectedPlan.value;
          } else {
            try {
              currentPlan = _prebuiltPlans.firstWhere((p) => p.id == planId);
            } catch (e) {
              try {
                currentPlan = _userPlans.firstWhere((p) => p.id == planId);
              } catch (e) {
                // Plan not found in local state
                currentPlan = null;
              }
            }
          }

          if (currentPlan != null) {
            // Update the step in the current plan
            final updatedSteps =
                currentPlan.steps.map((step) {
                  if (step.id == stepId) {
                    return updatedStep;
                  }
                  return step;
                }).toList();

            // Calculate new progress based on completed tasks across all steps
            final totalTasks = updatedSteps.fold<int>(
              0,
              (sum, step) => sum + step.tasks.length,
            );

            final completedTasks = updatedSteps.fold<int>(
              0,
              (sum, step) =>
                  sum + step.tasks.where((task) => task.isCompleted).length,
            );

            final newProgress =
                totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0.0;

            // Create updated plan with new step and recalculated progress
            final updatedPlan = SkillPlanEntity(
              id: currentPlan.id,
              name: currentPlan.name,
              description: currentPlan.description,
              steps: updatedSteps,
              isPublic: currentPlan.isPublic,
              creatorId: currentPlan.creatorId,
              creator: currentPlan.creator,
              createdAt: currentPlan.createdAt,
              updatedAt: DateTime.now(),
              progress: newProgress,
              metadata: currentPlan.metadata,
            );

            // Update the local plan state with the new progress
            _updateLocalPlanState(updatedPlan);

            // Check if all tasks in the step are completed and trigger step completion
            if (isCompleted) {
              await _checkAndUpdateStepCompletion(updatedPlan, stepId);
            }
          }

          showSnackbar(
            'Success',
            isCompleted
                ? 'Task marked as complete'
                : 'Task marked as incomplete',
          );
        },
      );
    } finally {
      setLoading(false);
    }
  }

  /// Update the local plan state with the updated plan from the server
  void _updateLocalPlanState(SkillPlanEntity updatedPlan) {
    // Update in prebuilt plans list
    final prebuiltIndex = _prebuiltPlans.indexWhere(
      (p) => p.id == updatedPlan.id,
    );
    if (prebuiltIndex != -1) {
      _prebuiltPlans[prebuiltIndex] = updatedPlan;
    }

    // Update in user plans list
    final userIndex = _userPlans.indexWhere((p) => p.id == updatedPlan.id);
    if (userIndex != -1) {
      _userPlans[userIndex] = updatedPlan;
    }

    // Update selected plan if it matches
    if (_selectedPlan.value?.id == updatedPlan.id) {
      _selectedPlan.value = updatedPlan;
    }
  }

  /// Check if all tasks in a step are completed and update step progress
  Future<void> _checkAndUpdateStepCompletion(
    SkillPlanEntity updatedPlan,
    String stepId,
  ) async {
    try {
      // Find the step in the updated plan
      final step = updatedPlan.steps.firstWhere(
        (s) => s.id == stepId,
        orElse: () => throw Exception('Step not found'),
      );

      // Check if all tasks in the step are completed
      final allTasksCompleted =
          step.tasks.isNotEmpty && step.tasks.every((task) => task.isCompleted);

      // If all tasks are completed and the step itself is not marked as completed
      if (allTasksCompleted && !step.isCompleted) {
        // Send step completion update to API
        final params = UpdateSkillPlanProgressParams(
          planId: updatedPlan.id,
          order: step.order,
          completed: true,
        );

        final result = await _updateSkillPlanProgressUseCase(params);

        result.fold(
          (failure) {
            // Log error but don't show snackbar to avoid interrupting user flow
            if (kDebugMode) {
              print('Failed to update step completion: ${failure.message}');
            }
          },
          (progressResponse) {
            // Step completion progress update was successful
            if (kDebugMode) {
              print(
                'Step completion progress updated: ${progressResponse.progress}% complete, steps: ${progressResponse.completedSteps}',
              );
            }

            // Show success message for step completion
            showSnackbar(
              'Step Complete!',
              'You\'ve completed all tasks in "${step.title}"',
            );
          },
        );
      }
    } catch (e) {
      // Log error but don't interrupt user flow
      if (kDebugMode) {
        print('Error checking step completion: $e');
      }
    }
  }

  /// Add a new step to a custom plan being created
  void addStep(SkillStepEntity step) {
    steps.add(step);
  }

  /// Remove a step from a custom plan being created
  void removeStep(int index) {
    if (index >= 0 && index < steps.length) {
      steps.removeAt(index);
    }
  }

  /// Update a step at a specific index
  void updateStep(int index, SkillStepEntity updatedStep) {
    if (index >= 0 && index < steps.length) {
      steps[index] = updatedStep;
    }
  }

  /// Add a tag to the custom plan being created
  void addTag(String tag) {
    if (tag.isNotEmpty && !tags.contains(tag)) {
      tags.add(tag);
    }
  }

  /// Remove a tag from the custom plan being created
  void removeTag(String tag) {
    tags.remove(tag);
  }

  /// Set the selected plan
  void selectPlan(SkillPlanEntity plan) {
    _selectedPlan.value = plan;
  }

  /// Clear the selected plan
  void clearSelectedPlan() {
    _selectedPlan.value = null;
  }

  /// Clear the form after creating a custom plan
  void clearForm() {
    nameController.clear();
    descriptionController.clear();
    customDurationController.clear();
    isPublic.value = false;
    selectedCategory.value = 'personal';
    selectedDifficulty.value = 'beginner';
    estimatedDuration.value = '2 weeks';
    useCustomDuration.value = false;
    tags.clear();
    steps.clear();
  }

  /// Filter plans by category
  List<SkillPlanEntity> getFilteredPlans(List<SkillPlanEntity> plans) {
    if (categoryFilter.isEmpty) {
      return plans;
    }

    return plans.where((plan) {
      final planCategory =
          plan.metadata.category?.toLowerCase() ?? 'uncategorized';
      return planCategory == categoryFilter.value.toLowerCase();
    }).toList();
  }

  /// Get the effective estimated duration (custom or predefined)
  String? _getEffectiveEstimatedDuration() {
    if (useCustomDuration.value &&
        customDurationController.text.trim().isNotEmpty) {
      return customDurationController.text.trim();
    } else if (estimatedDuration.value.isNotEmpty) {
      return estimatedDuration.value;
    }
    return null;
  }

  /// Handle errors
  @override
  void handleError(Failure failure) {
    super.handleError(failure);
    // Specific error handling for skill plans if needed
  }
}
