import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../domain/entities/skill_plan_entity.dart';
import '../../domain/entities/skill_step_entity.dart';
import '../controllers/skill_plans_controller.dart';
import '../widgets/skill_step_card.dart';
import '../widgets/skill_plan_meta_info.dart';
import '../widgets/progress_indicator_widget.dart';
import 'update_skill_plan_screen.dart';

/// Screen for displaying details of a skill plan
class SkillPlanDetailScreen extends GetView<SkillPlansController> {
  const SkillPlanDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Plan Details'),
        actions: [
          // Edit button
          Obx(() {
            final plan = controller.selectedPlan;
            if (plan != null) {
              return IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _navigateToUpdateScreen(plan),
                tooltip: 'Edit Plan',
              );
            }
            return const SizedBox.shrink();
          }),

          // Delete button
          Obx(() {
            final plan = controller.selectedPlan;
            if (plan != null) {
              return IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => _showDeleteConfirmationDialog(context, plan),
                tooltip: 'Delete Plan',
              );
            }
            return const SizedBox.shrink();
          }),

          // Share button
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Share functionality
              Get.snackbar(
                'Share',
                'Sharing functionality will be implemented soon!',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            tooltip: 'Share Plan',
          ),
        ],
      ),
      body: Obx(() {
        final plan = controller.selectedPlan;

        if (plan == null) {
          return const Center(child: Text('Plan not found'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with progress
              _buildHeader(plan, theme),

              const SizedBox(height: 24),

              // Meta information section
              SkillPlanMetaInfo(metadata: plan.metadata),

              const SizedBox(height: 24),

              // Steps section
              _buildStepsSection(plan.steps, theme),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildHeader(SkillPlanEntity plan, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          plan.name,
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        Text(
          plan.description,
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(
              179,
            ), // 0.7 opacity is approximately 179 alpha
          ),
        ),

        const SizedBox(height: 16),

        // Progress bar
        Row(
          children: [
            Expanded(
              child: ProgressIndicatorWidget(
                progress: plan.progress,
                height: 20,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              '${plan.progress.round()}%',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStepsSection(List<SkillStepEntity> steps, ThemeData theme) {
    final plan = controller.selectedPlan;
    if (plan == null) {
      return const SizedBox.shrink();
    }

    if (steps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.hiking,
              size: 48,
              color: theme.colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No steps found in this plan',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Steps',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 16),

        ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: steps.length,
          itemBuilder: (context, index) {
            final step = steps[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: SkillStepCard(
                step: step,
                stepNumber: index + 1,
                planId: plan.id,
              ),
            );
          },
        ),
      ],
    );
  }

  /// Navigate to update skill plan screen
  void _navigateToUpdateScreen(SkillPlanEntity plan) {
    Get.to(() => UpdateSkillPlanScreen(skillPlan: plan));
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmationDialog(
    BuildContext context,
    SkillPlanEntity plan,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Skill Plan'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Are you sure you want to delete this skill plan?'),
                const SizedBox(height: 8),
                Text(
                  plan.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  'This action cannot be undone.',
                  style: TextStyle(color: Colors.red),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Get.back(); // Close dialog first

                  final success = await controller.deleteSkillPlan(plan.id);

                  if (success) {
                    // Navigate back to plans list since the plan is deleted
                    Get.back();
                    Get.snackbar('Success', 'Plan deleted successfully');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
