import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../controllers/skill_plans_controller.dart';
import '../widgets/skill_plan_card.dart';
import '../widgets/skill_category_filter.dart';

/// Screen for browsing skill plans
class SkillPlansScreen extends GetView<SkillPlansController> {
  const SkillPlansScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return MainLayoutScreen(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Text(
              'Skill Plans',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Build new skills with structured learning plans',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(
                  179,
                ), // 0.7 opacity is approximately 179 alpha
              ),
            ),

            const SizedBox(height: 16),

            // Category Filter
            const SkillCategoryFilter(),

            const SizedBox(height: 12),

            // Plans Section Title
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Pre-built Plans',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: () => Get.toNamed(AppRoutes.createCustomPlan),
                  icon: const Icon(Icons.add),
                  label: const Text('Create Custom'),
                ),
              ],
            ),

            const SizedBox(height: 4),

            // Plans List
            Expanded(
              child: Obx(() {
                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final plans = controller.getFilteredPlans(
                  controller.prebuiltPlans,
                );
                if (plans.isEmpty) {
                  return RefreshIndicator(
                    onRefresh: () async {
                      await controller.fetchPrebuiltPlans();
                    },
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: SizedBox(
                        height: MediaQuery.of(context).size.height * 0.5,
                        child: _buildEmptyState(theme),
                      ),
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    await controller.fetchPrebuiltPlans();
                  },
                  child: ListView.builder(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    itemCount: plans.length,
                    itemBuilder: (context, index) {
                      final plan = plans[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: SkillPlanCard(
                          plan: plan,
                          onTap: () {
                            controller.selectPlan(plan);
                            Get.toNamed(
                              '${AppRoutes.skillPlanDetail.replaceAll(':id', '')}${plan.id}',
                            );
                          },
                        ),
                      );
                    },
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.school_outlined,
            size: 64,
            color: theme.colorScheme.primary.withAlpha(
              128,
            ), // 0.5 opacity is approximately 128 alpha
          ),
          const SizedBox(height: 16),
          Text(
            'No skill plans found',
            style: theme.textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Try changing your filter or create a custom plan',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(
                179,
              ), // 0.7 opacity is approximately 179 alpha
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
