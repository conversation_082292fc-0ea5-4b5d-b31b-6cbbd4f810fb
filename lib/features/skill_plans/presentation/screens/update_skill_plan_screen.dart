import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../domain/entities/skill_plan_entity.dart';
import '../controllers/skill_plans_controller.dart';
import '../widgets/step_input_widget.dart';
import '../widgets/tag_input_widget.dart';

/// Screen for updating an existing skill plan
class UpdateSkillPlanScreen extends GetView<SkillPlansController> {
  final SkillPlanEntity skillPlan;

  const UpdateSkillPlanScreen({Key? key, required this.skillPlan})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Initialize form with existing plan data
    _initializeFormWithExistingData();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Update Plan'),
        actions: [
          TextButton.icon(
            onPressed: () async {
              final success = await controller.updateSkillPlanFromForm(
                skillPlan.id,
              );
              if (success) {
                Get.back(); // Return to previous screen
              }
            },
            icon: const Icon(Icons.save),
            label: const Text('Save'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Basic Information Section
            Text(
              'Basic Information',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // Plan name field
            TextFormField(
              controller: controller.nameController,
              decoration: const InputDecoration(
                labelText: 'Plan Name',
                hintText: 'Enter a name for your plan',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              maxLength: 100,
            ),

            const SizedBox(height: 16),

            // Plan description field
            TextFormField(
              controller: controller.descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Describe your plan',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
              maxLength: 500,
            ),

            const SizedBox(height: 24),

            // Metadata Section
            Text(
              'Plan Details',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // Category dropdown
            Obx(
              () => DropdownButtonFormField<String>(
                value: controller.selectedCategory.value,
                decoration: const InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'personal',
                    child: Text('Personal Development'),
                  ),
                  DropdownMenuItem(
                    value: 'professional',
                    child: Text('Professional Skills'),
                  ),
                  DropdownMenuItem(
                    value: 'health',
                    child: Text('Health & Wellness'),
                  ),
                  DropdownMenuItem(
                    value: 'education',
                    child: Text('Education'),
                  ),
                  DropdownMenuItem(value: 'hobby', child: Text('Hobbies')),
                  DropdownMenuItem(value: 'other', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) controller.selectedCategory.value = value;
                },
              ),
            ),

            const SizedBox(height: 16),

            // Difficulty dropdown
            Obx(
              () => DropdownButtonFormField<String>(
                value: controller.selectedDifficulty.value,
                decoration: const InputDecoration(
                  labelText: 'Difficulty Level',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.trending_up),
                ),
                items: const [
                  DropdownMenuItem(value: 'beginner', child: Text('Beginner')),
                  DropdownMenuItem(
                    value: 'intermediate',
                    child: Text('Intermediate'),
                  ),
                  DropdownMenuItem(value: 'advanced', child: Text('Advanced')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedDifficulty.value = value;
                  }
                },
              ),
            ),

            const SizedBox(height: 16),

            // Estimated duration dropdown and custom input
            Obx(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DropdownButtonFormField<String>(
                    value:
                        controller.useCustomDuration.value
                            ? 'custom'
                            : controller.estimatedDuration.value,
                    decoration: const InputDecoration(
                      labelText: 'Estimated Duration',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.schedule),
                    ),
                    items: const [
                      DropdownMenuItem(value: '1 week', child: Text('1 week')),
                      DropdownMenuItem(
                        value: '2 weeks',
                        child: Text('2 weeks'),
                      ),
                      DropdownMenuItem(
                        value: '3 weeks',
                        child: Text('3 weeks'),
                      ),
                      DropdownMenuItem(
                        value: '1 month',
                        child: Text('1 month'),
                      ),
                      DropdownMenuItem(
                        value: '2 months',
                        child: Text('2 months'),
                      ),
                      DropdownMenuItem(
                        value: '3 months',
                        child: Text('3 months'),
                      ),
                      DropdownMenuItem(
                        value: '6 months',
                        child: Text('6 months'),
                      ),
                      DropdownMenuItem(value: 'custom', child: Text('Custom')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        if (value == 'custom') {
                          controller.useCustomDuration.value = true;
                        } else {
                          controller.useCustomDuration.value = false;
                          controller.estimatedDuration.value = value;
                        }
                      }
                    },
                  ),
                  if (controller.useCustomDuration.value) ...[
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: controller.customDurationController,
                      decoration: const InputDecoration(
                        labelText: 'Custom Duration',
                        hintText: 'e.g., 4 weeks, 1.5 months, 10 days',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.edit_calendar),
                      ),
                      validator: (value) {
                        if (controller.useCustomDuration.value &&
                            (value == null || value.trim().isEmpty)) {
                          return 'Please enter a custom duration';
                        }
                        return null;
                      },
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Tags input
            TagInputWidget(
              onAddTag: controller.addTag,
              onRemoveTag: controller.removeTag,
              tags: controller.tags,
            ),

            const SizedBox(height: 16),

            // Privacy toggle
            Obx(
              () => SwitchListTile(
                title: const Text('Make this plan public'),
                subtitle: const Text(
                  'Allow other users to view and use this plan',
                ),
                value: controller.isPublic.value,
                onChanged: (value) => controller.isPublic.value = value,
                contentPadding: EdgeInsets.zero,
              ),
            ),

            const SizedBox(height: 24),

            // Steps Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Plan Steps',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showAddStepDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Step'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Steps list
            Obx(() {
              if (controller.steps.isEmpty) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 32.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.stairs_outlined,
                          size: 64,
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.5,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Add steps to your plan',
                          style: TextStyle(fontSize: 18),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Each step will guide learners through your plan',
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.steps.length,
                itemBuilder: (context, index) {
                  final step = controller.steps[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    elevation: 2,
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      title: Text(
                        step.title,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(step.description),
                          const SizedBox(height: 4),
                          Text('Tasks: ${step.tasks.length}'),
                        ],
                      ),
                      leading: CircleAvatar(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        child: Text('${index + 1}'),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit_outlined),
                            onPressed:
                                () => _showEditStepDialog(context, index, step),
                            color: theme.colorScheme.primary,
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete_outline),
                            onPressed: () => controller.removeStep(index),
                            color: theme.colorScheme.error,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            }),

            const SizedBox(height: 32),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Get.back(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () async {
                      final success = await controller.updateSkillPlanFromForm(
                        skillPlan.id,
                      );
                      if (success) {
                        Get.back(); // Return to previous screen
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                    child: Obx(
                      () =>
                          controller.isLoading
                              ? const CircularProgressIndicator(
                                color: Colors.white,
                              )
                              : const Text(
                                'Update Plan',
                                style: TextStyle(fontSize: 16),
                              ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _initializeFormWithExistingData() {
    // Set form controllers with existing data
    controller.nameController.text = skillPlan.name;
    controller.descriptionController.text = skillPlan.description;

    // Set metadata values
    controller.selectedCategory.value =
        skillPlan.metadata.category ?? 'personal';
    controller.selectedDifficulty.value =
        skillPlan.metadata.difficulty ?? 'beginner';

    // Handle estimated duration (check if it's a predefined value or custom)
    final existingDuration = skillPlan.metadata.estimatedDuration ?? '2 weeks';
    final predefinedDurations = [
      '1 week',
      '2 weeks',
      '3 weeks',
      '1 month',
      '2 months',
      '3 months',
      '6 months',
    ];

    if (predefinedDurations.contains(existingDuration)) {
      controller.useCustomDuration.value = false;
      controller.estimatedDuration.value = existingDuration;
      controller.customDurationController.clear();
    } else {
      controller.useCustomDuration.value = true;
      controller.estimatedDuration.value = '2 weeks'; // default for dropdown
      controller.customDurationController.text = existingDuration;
    }

    controller.isPublic.value = skillPlan.isPublic;

    // Set tags
    controller.tags.clear();
    controller.tags.addAll(skillPlan.metadata.tags);

    // Set steps
    controller.steps.clear();
    controller.steps.addAll(skillPlan.steps);
  }

  void _showAddStepDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: StepInputWidget(
                currentStepCount: controller.steps.length,
                onAddStep: (step) {
                  controller.addStep(step);
                  Get.back(); // Close dialog
                },
              ),
            ),
          ),
    );
  }

  void _showEditStepDialog(BuildContext context, int index, dynamic step) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: StepInputWidget(
                currentStepCount: controller.steps.length,
                existingStep: step,
                onAddStep: (updatedStep) {
                  controller.updateStep(index, updatedStep);
                  Get.back(); // Close dialog
                },
              ),
            ),
          ),
    );
  }
}
