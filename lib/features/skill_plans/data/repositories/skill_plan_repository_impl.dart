import 'package:dartz/dartz.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/skill_plan_entity.dart';
import '../../domain/entities/skill_plan_progress_entity.dart';
import '../../domain/entities/skill_step_entity.dart';
import '../../domain/repositories/skill_plan_repository.dart';
import '../datasources/skill_plan_remote_data_source.dart';
import '../models/skill_plan_model.dart';
import '../models/skill_step_model.dart';

/// Implementation of [SkillPlanRepository] that uses a remote data source
class SkillPlanRepositoryImpl implements SkillPlanRepository {
  final SkillPlanRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  /// Constructor
  SkillPlanRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<void> init() async {
    // Nothing to initialize for this repository
  }

  @override
  Future<void> clearData() async {
    // Nothing to clear for this repository
  }

  @override
  Future<Either<Failure, SkillPlanEntity>> createSkillPlan({
    required String name,
    required String description,
    required bool isPublic,
    required SkillPlanMetadata metadata,
    required List<SkillStepEntity> steps,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final model = SkillPlanModel(
          // Using a placeholder ID that will be replaced by the backend
          id: 'temp-id',
          name: name,
          description: description,
          steps: steps.map((step) => SkillStepModel.fromEntity(step)).toList(),
          isPublic: isPublic,
          // These fields will be set by the backend
          creatorId: 'current-user',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: SkillPlanMetadataModel.fromEntity(metadata),
        );

        final result = await remoteDataSource.createSkillPlan(model);
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteSkillPlan(String id) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteSkillPlan(id);
        return const Right(null);
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<SkillPlanEntity>>> getPrebuiltPlans() async {
    // Pre-built plans are just public plans created by the system
    return getSkillPlans(isPublic: true);
  }

  @override
  Future<Either<Failure, SkillPlanEntity>> getSkillPlanById(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getSkillPlanById(id);
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, List<SkillPlanEntity>>> getSkillPlans({
    bool? isPublic,
    String? creatorId,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.getSkillPlans(
          isPublic: isPublic,
          creatorId: creatorId,
        );
        return Right(result.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, SkillStepEntity>> markSkillPlanStepTaskComplete({
    required String planId,
    required String stepId,
    required int taskIndex,
    required bool isComplete,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // Mark the task complete and get the updated step
        final updatedStep = await remoteDataSource
            .markSkillPlanStepTaskComplete(
              planId,
              stepId,
              taskIndex,
              isComplete,
            );

        return Right(updatedStep.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, SkillPlanEntity>> updateSkillPlan({
    required String id,
    String? name,
    String? description,
    bool? isPublic,
    SkillPlanMetadata? metadata,
    List<SkillStepEntity>? steps,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // First, get the current state of the plan
        final currentPlanModel = await remoteDataSource.getSkillPlanById(id);

        // Create an updated model with changed fields
        final updatedModel = SkillPlanModel(
          id: id,
          name: name ?? currentPlanModel.name,
          description: description ?? currentPlanModel.description,
          isPublic: isPublic ?? currentPlanModel.isPublic,
          creatorId: currentPlanModel.creatorId,
          createdAt: currentPlanModel.createdAt,
          updatedAt: DateTime.now(),
          steps:
              steps != null
                  ? steps
                      .map((step) => SkillStepModel.fromEntity(step))
                      .toList()
                  : currentPlanModel.steps,
          metadata:
              metadata != null
                  ? SkillPlanMetadataModel.fromEntity(metadata)
                  : currentPlanModel.metadata,
          progress: currentPlanModel.progress,
        );

        final result = await remoteDataSource.updateSkillPlan(id, updatedModel);
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, SkillPlanProgressEntity>> updateSkillPlanProgress({
    required String planId,
    required int order,
    required bool completed,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final result = await remoteDataSource.updateSkillPlanProgress(
          planId,
          order,
          completed,
        );
        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }

  @override
  Future<Either<Failure, SkillStepEntity>> updateSkillPlanStep({
    required String planId,
    required String stepId,
    String? title,
    String? description,
    bool? isCompleted,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        // First, get the current state of the plan
        final currentPlanModel = await remoteDataSource.getSkillPlanById(
          planId,
        );
        final currentStep = currentPlanModel.steps.firstWhere(
          (step) => step.id == stepId,
          orElse: () => throw ServerException(message: 'Step not found'),
        );

        // Update the step with the new values
        final updatedStepModel = SkillStepModel(
          id: stepId,
          title: title ?? currentStep.title,
          description: description ?? currentStep.description,
          order: currentStep.order,
          isCompleted: isCompleted ?? currentStep.isCompleted,
          tasks: currentStep.tasks,
          resources: currentStep.resources,
          completedAt:
              isCompleted == true && !currentStep.isCompleted
                  ? DateTime.now()
                  : currentStep.completedAt,
        );

        // Call the API to update the step
        final result = await remoteDataSource.updateSkillPlanStep(
          planId,
          stepId,
          updatedStepModel,
        );

        return Right(result.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      }
    } else {
      return Left(NetworkFailure(message: 'No internet connection'));
    }
  }
}
