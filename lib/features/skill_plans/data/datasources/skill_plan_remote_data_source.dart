import '../models/skill_plan_model.dart';
import '../models/skill_plan_progress_model.dart';
import '../models/skill_step_model.dart';

/// Abstract class defining the operations for the SkillPlan remote data source
abstract class SkillPlanRemoteDataSource {
  /// Create a new skill plan
  ///
  /// [model] - The skill plan model to create
  Future<SkillPlanModel> createSkillPlan(SkillPlanModel model);

  /// Get skill plans based on filters
  ///
  /// [isPublic] - Filter for public plans
  /// [creatorId] - Filter for plans created by a specific user
  Future<List<SkillPlanModel>> getSkillPlans({
    bool? isPublic,
    String? creatorId,
  });

  /// Get a skill plan by ID
  ///
  /// [id] - The ID of the skill plan to retrieve
  Future<SkillPlanModel> getSkillPlanById(String id);

  /// Update an existing skill plan
  ///
  /// [id] - The ID of the skill plan to update
  /// [model] - The updated skill plan model
  Future<SkillPlanModel> updateSkillPlan(String id, SkillPlanModel model);

  /// Delete a skill plan
  ///
  /// [id] - The ID of the skill plan to delete
  Future<void> deleteSkillPlan(String id);

  /// Update a specific step in a skill plan
  ///
  /// [planId] - The ID of the parent skill plan
  /// [stepId] - The ID of the step to update
  /// [model] - The updated step model
  Future<SkillStepModel> updateSkillPlanStep(
    String planId,
    String stepId,
    SkillStepModel model,
  );

  /// Mark a specific task within a step as complete/incomplete
  ///
  /// [planId] - The ID of the parent skill plan
  /// [stepId] - The ID of the parent step
  /// [taskIndex] - The index of the task to update
  /// [isComplete] - Whether to mark as complete or incomplete
  Future<SkillStepModel> markSkillPlanStepTaskComplete(
    String planId,
    String stepId,
    int taskIndex,
    bool isComplete,
  );

  /// Update the overall progress of a skill plan
  ///
  /// [id] - The ID of the skill plan to update
  /// [order] - The step order number
  /// [completed] - Whether the step is completed
  Future<SkillPlanProgressModel> updateSkillPlanProgress(
    String id,
    int order,
    bool completed,
  );
}
