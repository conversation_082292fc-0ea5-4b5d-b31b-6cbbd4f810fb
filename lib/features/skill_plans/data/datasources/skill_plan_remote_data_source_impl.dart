import '../../../../core/error/exceptions.dart';
import '../../../core/data/api/api_client.dart';
import '../models/skill_plan_model.dart';
import '../models/skill_plan_progress_model.dart';
import '../models/skill_step_model.dart';
import 'skill_plan_remote_data_source.dart';

/// Implementation of [SkillPlanRemoteDataSource] using HTTP API client
class SkillPlanRemoteDataSourceImpl implements SkillPlanRemoteDataSource {
  final ApiClient apiClient;

  /// Constructor
  SkillPlanRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<SkillPlanModel> createSkillPlan(SkillPlanModel model) async {
    try {
      final response = await apiClient.post<Map<String, dynamic>>(
        endpoint: '/skill-plans',
        data: model.toCreateJson(),
        fromData: (data) => data as Map<String, dynamic>,
      );

      return SkillPlanModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to create skill plan');
    }
  }

  @override
  Future<List<SkillPlanModel>> getSkillPlans({
    bool? isPublic,
    String? creatorId,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (isPublic != null) {
        queryParams['isPublic'] = isPublic.toString();
      }
      if (creatorId != null) {
        queryParams['creatorId'] = creatorId;
      }

      final response = await apiClient.get<List<Map<String, dynamic>>>(
        endpoint: '/skill-plans',
        queryParameters: queryParams,
        fromData: (data) => (data as List).cast<Map<String, dynamic>>(),
      );

      return response
          .map((planJson) => SkillPlanModel.fromJson(planJson))
          .toList();
    } catch (e) {
      throw ServerException(message: 'Failed to fetch skill plans');
    }
  }

  @override
  Future<SkillPlanModel> getSkillPlanById(String id) async {
    try {
      final response = await apiClient.get<Map<String, dynamic>>(
        endpoint: '/skill-plans/$id',
        fromData: (data) => data as Map<String, dynamic>,
      );
      return SkillPlanModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to fetch skill plan');
    }
  }

  @override
  Future<SkillPlanModel> updateSkillPlan(
    String id,
    SkillPlanModel model,
  ) async {
    try {
      final response = await apiClient.put<Map<String, dynamic>>(
        endpoint: '/skill-plans/$id',
        data:
            model
                .toCreateJson(), // Use toCreateJson to exclude server-managed fields
        fromData: (data) => data as Map<String, dynamic>,
      );
      return SkillPlanModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to update skill plan');
    }
  }

  @override
  Future<void> deleteSkillPlan(String id) async {
    try {
      await apiClient.delete<void>(
        endpoint: '/skill-plans/$id',
        fromData: (data) => data,
      );
    } catch (e) {
      throw ServerException(message: 'Failed to delete skill plan');
    }
  }

  @override
  Future<SkillStepModel> updateSkillPlanStep(
    String planId,
    String stepId,
    SkillStepModel model,
  ) async {
    try {
      final response = await apiClient.put<Map<String, dynamic>>(
        endpoint: '/skill-plans/$planId/steps/$stepId',
        data: model.toJson(),
        fromData: (data) => data as Map<String, dynamic>,
      );
      return SkillStepModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to update skill plan step');
    }
  }

  @override
  Future<SkillStepModel> markSkillPlanStepTaskComplete(
    String planId,
    String stepId,
    int taskIndex,
    bool isComplete,
  ) async {
    try {
      // Mark the task as complete - API returns the updated step
      final response = await apiClient.put<Map<String, dynamic>>(
        endpoint:
            '/skill-plans/$planId/steps/$stepId/tasks/$taskIndex/complete',
        data: {'isComplete': isComplete},
        fromData: (data) => data as Map<String, dynamic>,
      );

      return SkillStepModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to mark task complete');
    }
  }

  @override
  Future<SkillPlanProgressModel> updateSkillPlanProgress(
    String id,
    int order,
    bool completed,
  ) async {
    try {
      final response = await apiClient.post<Map<String, dynamic>>(
        endpoint: '/skill-plans/$id/progress',
        data: {'order': order, 'completed': completed},
        fromData: (data) => data as Map<String, dynamic>,
      );
      return SkillPlanProgressModel.fromJson(response);
    } catch (e) {
      throw ServerException(message: 'Failed to update skill plan progress');
    }
  }
}
