import '../../domain/entities/skill_plan_entity.dart';
import 'skill_step_model.dart';
import 'user_model.dart';

/// Data model for skill step resource with serialization capabilities
class SkillStepResourceModel extends SkillStepResource {
  const SkillStepResourceModel({
    required super.type,
    required super.url,
    required super.title,
  });

  /// Create a SkillStepResourceModel from a JSON map
  factory SkillStepResourceModel.fromJson(Map<String, dynamic> json) {
    return SkillStepResourceModel(
      type: json['type'],
      url: json['url'],
      title: json['title'],
    );
  }

  /// Convert SkillStepResourceModel to a JSON map
  Map<String, dynamic> toJson() {
    return {'type': type, 'url': url, 'title': title};
  }

  /// Convert domain entity to a model
  factory SkillStepResourceModel.fromEntity(SkillStepResource entity) {
    return SkillStepResourceModel(
      type: entity.type,
      url: entity.url,
      title: entity.title,
    );
  }
}

/// Data model for skill plan metadata with serialization capabilities
class SkillPlanMetadataModel extends SkillPlanMetadata {
  const SkillPlanMetadataModel({
    super.category,
    super.difficulty,
    super.estimatedDuration,
    super.tags = const [],
  });

  /// Create a SkillPlanMetadataModel from a JSON map
  factory SkillPlanMetadataModel.fromJson(Map<String, dynamic> json) {
    return SkillPlanMetadataModel(
      category: json['category'],
      difficulty: json['difficulty'],
      estimatedDuration: json['estimatedDuration'],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : const [],
    );
  }

  /// Convert SkillPlanMetadataModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'difficulty': difficulty,
      'estimatedDuration': estimatedDuration,
      'tags': tags,
    };
  }

  /// Convert domain entity to a model
  factory SkillPlanMetadataModel.fromEntity(SkillPlanMetadata entity) {
    return SkillPlanMetadataModel(
      category: entity.category,
      difficulty: entity.difficulty,
      estimatedDuration: entity.estimatedDuration,
      tags: entity.tags,
    );
  }

  /// Convert model to domain entity
  SkillPlanMetadata toEntity() {
    return SkillPlanMetadata(
      category: category,
      difficulty: difficulty,
      estimatedDuration: estimatedDuration,
      tags: tags,
    );
  }
}

/// Data model for skill plan with serialization capabilities
class SkillPlanModel extends SkillPlanEntity {
  const SkillPlanModel({
    required super.id,
    required super.name,
    required super.description,
    super.steps = const [],
    required super.isPublic,
    required super.creatorId,
    super.creator,
    required super.createdAt,
    required super.updatedAt,
    super.progress = 0.0,
    required super.metadata,
  });

  /// Create a SkillPlanModel from a JSON map
  factory SkillPlanModel.fromJson(Map<String, dynamic> json) {
    List<SkillStepModel> steps = [];
    if (json['steps'] != null) {
      steps =
          (json['steps'] as List)
              .map((stepJson) => SkillStepModel.fromJson(stepJson))
              .toList();
    }

    // Handle both old and new response structures
    String creatorId;
    UserModel? creator;

    if (json['creator'] != null && json['creator'] is Map<String, dynamic>) {
      // New API response with creator object
      creator = UserModel.fromJson(json['creator']);
      creatorId = creator.id;
    } else {
      // Old API response with creatorId string
      creatorId = json['creatorId'] ?? '';
    }

    return SkillPlanModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      steps: steps,
      isPublic: json['isPublic'] ?? true,
      creatorId: creatorId,
      creator: creator,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      progress: json['progress']?.toDouble() ?? 0.0,
      metadata: SkillPlanMetadataModel.fromJson(json['metadata']),
    );
  }

  /// Convert SkillPlanModel to a JSON map for creation (excludes server-managed fields)
  Map<String, dynamic> toCreateJson() {
    return {
      'name': name,
      'description': description,
      'steps':
          steps.map((step) => (step as SkillStepModel).toCreateJson()).toList(),
      'isPublic': isPublic,
      'metadata': (metadata as SkillPlanMetadataModel).toJson(),
    };
  }

  /// Convert SkillPlanModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'steps': steps.map((step) => (step as SkillStepModel).toJson()).toList(),
      'isPublic': isPublic,
      'creatorId': creatorId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'progress': progress,
      'metadata': (metadata as SkillPlanMetadataModel).toJson(),
    };
  }

  /// Convert domain entity to a model
  factory SkillPlanModel.fromEntity(SkillPlanEntity entity) {
    return SkillPlanModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      steps:
          entity.steps.map((step) => SkillStepModel.fromEntity(step)).toList(),
      isPublic: entity.isPublic,
      creatorId: entity.creatorId,
      creator:
          entity.creator != null ? UserModel.fromEntity(entity.creator!) : null,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      progress: entity.progress,
      metadata: SkillPlanMetadataModel.fromEntity(entity.metadata),
    );
  }

  /// Convert model to domain entity
  SkillPlanEntity toEntity() {
    return SkillPlanEntity(
      id: id,
      name: name,
      description: description,
      steps: steps.map((step) => (step as SkillStepModel).toEntity()).toList(),
      isPublic: isPublic,
      creatorId: creatorId,
      creator: creator != null ? (creator as UserModel).toEntity() : null,
      createdAt: createdAt,
      updatedAt: updatedAt,
      progress: progress,
      metadata: (metadata as SkillPlanMetadataModel).toEntity(),
    );
  }
}
