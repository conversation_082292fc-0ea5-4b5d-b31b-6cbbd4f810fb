import '../../domain/entities/creator_entity.dart';

/// Data model for skill plan creator with serialization capabilities
class CreatorModel extends CreatorEntity {
  const CreatorModel({
    required super.id,
    required super.name,
    required super.email,
    super.profilePicture,
  });

  /// Create a CreatorModel from a JSON map
  factory CreatorModel.fromJson(Map<String, dynamic> json) {
    return CreatorModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      profilePicture: json['profilePicture'],
    );
  }

  /// Convert CreatorModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profilePicture': profilePicture,
    };
  }

  /// Convert domain entity to a model
  factory CreatorModel.fromEntity(CreatorEntity entity) {
    return CreatorModel(
      id: entity.id,
      name: entity.name,
      email: entity.email,
      profilePicture: entity.profilePicture,
    );
  }

  /// Convert model to domain entity
  CreatorEntity toEntity() {
    return CreatorEntity(
      id: id,
      name: name,
      email: email,
      profilePicture: profilePicture,
    );
  }
}
