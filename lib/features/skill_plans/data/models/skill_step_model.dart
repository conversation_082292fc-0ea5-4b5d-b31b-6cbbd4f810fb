import '../../domain/entities/skill_step_entity.dart';
import '../../domain/entities/skill_plan_entity.dart';

/// Data model for skill step resource with serialization capabilities
class SkillStepResourceModel extends SkillStepResource {
  const SkillStepResourceModel({
    required super.type,
    required super.url,
    required super.title,
  });

  /// Create a SkillStepResourceModel from a JSON map
  factory SkillStepResourceModel.fromJson(Map<String, dynamic> json) {
    return SkillStepResourceModel(
      type: json['type'],
      url: json['url'],
      title: json['title'],
    );
  }

  /// Convert SkillStepResourceModel to a JSON map
  Map<String, dynamic> toJson() {
    return {'type': type, 'url': url, 'title': title};
  }

  /// Convert domain entity to a model
  factory SkillStepResourceModel.fromEntity(SkillStepResource entity) {
    return SkillStepResourceModel(
      type: entity.type,
      url: entity.url,
      title: entity.title,
    );
  }

  /// Convert model to domain entity
  SkillStepResource toEntity() {
    return SkillStepResource(type: type, url: url, title: title);
  }
}

/// Data model for skill step task with serialization capabilities
class SkillStepTaskModel extends SkillStepTaskEntity {
  const SkillStepTaskModel({
    required super.id,
    required super.description,
    super.isCompleted = false,
    super.completedAt,
  });

  /// Create a SkillStepTaskModel from a JSON map
  factory SkillStepTaskModel.fromJson(Map<String, dynamic> json) {
    return SkillStepTaskModel(
      id: json['id'] ?? '', // Handle tasks without IDs gracefully
      description: json['description'],
      isCompleted: json['isCompleted'] ?? false,
      completedAt:
          json['completedAt'] != null
              ? DateTime.parse(json['completedAt'])
              : null,
    );
  }

  /// Convert SkillStepTaskModel to a JSON map for creation (excludes server-managed fields)
  Map<String, dynamic> toCreateJson() {
    return {'description': description, 'isCompleted': isCompleted};
  }

  /// Convert SkillStepTaskModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  /// Convert domain entity to a model
  factory SkillStepTaskModel.fromEntity(SkillStepTaskEntity entity) {
    return SkillStepTaskModel(
      id: entity.id,
      description: entity.description,
      isCompleted: entity.isCompleted,
      completedAt: entity.completedAt,
    );
  }

  /// Convert model to domain entity
  SkillStepTaskEntity toEntity() {
    return SkillStepTaskEntity(
      id: id,
      description: description,
      isCompleted: isCompleted,
      completedAt: completedAt,
    );
  }
}

/// Data model for skill step with serialization capabilities
class SkillStepModel extends SkillStepEntity {
  const SkillStepModel({
    required super.id,
    required super.title,
    required super.description,
    required super.order,
    super.isCompleted = false,
    super.tasks = const [],
    super.resources = const [],
    super.completedAt,
    super.skillPlanId,
  });

  /// Create a SkillStepModel from a JSON map
  factory SkillStepModel.fromJson(Map<String, dynamic> json) {
    List<SkillStepTaskModel> tasks = [];
    if (json['tasks'] != null) {
      tasks =
          (json['tasks'] as List)
              .map((taskJson) => SkillStepTaskModel.fromJson(taskJson))
              .toList();
    }

    List<SkillStepResourceModel> resources = [];
    if (json['resources'] != null) {
      resources =
          (json['resources'] as List)
              .map(
                (resourceJson) => SkillStepResourceModel.fromJson(resourceJson),
              )
              .toList();
    }

    // Extract skillPlanId from nested skillPlan object if present
    String? skillPlanId;
    if (json['skillPlan'] != null && json['skillPlan']['id'] != null) {
      skillPlanId = json['skillPlan']['id'];
    }

    return SkillStepModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      order: json['order'] ?? 1,
      isCompleted: json['isCompleted'] ?? false,
      tasks: tasks,
      resources: resources,
      completedAt:
          json['completedAt'] != null
              ? DateTime.parse(json['completedAt'])
              : null,
      skillPlanId: skillPlanId,
    );
  }

  /// Convert SkillStepModel to a JSON map for creation (excludes server-managed fields)
  Map<String, dynamic> toCreateJson() {
    return {
      'title': title,
      'description': description,
      'order': order,
      'resources':
          resources
              .map((resource) => (resource as SkillStepResourceModel).toJson())
              .toList(),
      'tasks':
          tasks
              .map((task) => (task as SkillStepTaskModel).toCreateJson())
              .toList(),
    };
  }

  /// Convert SkillStepModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'order': order,
      'isCompleted': isCompleted,
      'tasks':
          tasks.map((task) => (task as SkillStepTaskModel).toJson()).toList(),
      'resources':
          resources
              .map((resource) => (resource as SkillStepResourceModel).toJson())
              .toList(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  /// Convert domain entity to a model
  factory SkillStepModel.fromEntity(SkillStepEntity entity) {
    return SkillStepModel(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      order: entity.order,
      isCompleted: entity.isCompleted,
      tasks:
          entity.tasks
              .map((task) => SkillStepTaskModel.fromEntity(task))
              .toList(),
      resources:
          entity.resources
              .map((resource) => SkillStepResourceModel.fromEntity(resource))
              .toList(),
      completedAt: entity.completedAt,
      skillPlanId: entity.skillPlanId,
    );
  }

  /// Convert model to domain entity
  SkillStepEntity toEntity() {
    return SkillStepEntity(
      id: id,
      title: title,
      description: description,
      order: order,
      isCompleted: isCompleted,
      tasks:
          tasks.map((task) => (task as SkillStepTaskModel).toEntity()).toList(),
      resources:
          resources
              .map(
                (resource) => (resource as SkillStepResourceModel).toEntity(),
              )
              .toList(),
      completedAt: completedAt,
      skillPlanId: skillPlanId,
    );
  }
}
