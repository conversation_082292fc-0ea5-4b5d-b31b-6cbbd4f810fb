import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/skill_plan_progress_entity.dart';

part 'skill_plan_progress_model.g.dart';

/// Data model for skill plan progress response
@JsonSerializable()
class SkillPlanProgressModel extends SkillPlanProgressEntity {
  const SkillPlanProgressModel({
    required super.progress,
    required super.completedSteps,
  });

  /// Create model from JSON
  factory SkillPlanProgressModel.fromJson(Map<String, dynamic> json) =>
      _$SkillPlanProgressModelFromJson(json);

  /// Convert model to JSON
  Map<String, dynamic> toJson() => _$SkillPlanProgressModelToJson(this);

  /// Convert model to entity
  SkillPlanProgressEntity toEntity() => SkillPlanProgressEntity(
    progress: progress,
    completedSteps: completedSteps,
  );
}
