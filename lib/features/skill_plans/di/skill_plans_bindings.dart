import 'package:get/get.dart';
import 'package:power_up/features/core/data/api/api_client.dart';

import '../domain/usecases/get_prebuilt_plans_usecase.dart';
import '../domain/usecases/create_custom_plan_usecase.dart';
import '../domain/usecases/update_skill_plan_progress_usecase.dart';
import '../domain/usecases/update_skill_plan_usecase.dart';
import '../domain/usecases/delete_skill_plan_usecase.dart';
import '../domain/usecases/mark_skill_plan_task_complete_usecase.dart';
import '../domain/usecases/get_skill_plan_by_id_usecase.dart';
import '../domain/repositories/skill_plan_repository.dart';
import '../data/repositories/skill_plan_repository_impl.dart';
import '../data/datasources/skill_plan_remote_data_source.dart';
import '../data/datasources/skill_plan_remote_data_source_impl.dart';
import '../presentation/controllers/skill_plans_controller.dart';
import '../../../core/network/network_info.dart';

/// Bindings for skill plans feature
class SkillPlansBindings implements Bindings {
  @override
  void dependencies() {
    // Core dependencies are expected to be already registered
    final apiClient = Get.find<ApiClient>();
    final networkInfo = Get.find<NetworkInfo>();

    // Register data sources
    Get.lazyPut<SkillPlanRemoteDataSource>(
      () => SkillPlanRemoteDataSourceImpl(apiClient: apiClient),
    );

    // Register repositories
    Get.lazyPut<SkillPlanRepository>(
      () => SkillPlanRepositoryImpl(
        remoteDataSource: Get.find<SkillPlanRemoteDataSource>(),
        networkInfo: networkInfo,
      ),
    );

    // Register use cases
    Get.lazyPut(() => GetPrebuiltPlansUseCase(Get.find<SkillPlanRepository>()));

    Get.lazyPut(() => CreateCustomPlanUseCase(Get.find<SkillPlanRepository>()));

    Get.lazyPut(
      () => UpdateSkillPlanProgressUseCase(Get.find<SkillPlanRepository>()),
    );

    Get.lazyPut(() => UpdateSkillPlanUseCase(Get.find<SkillPlanRepository>()));

    Get.lazyPut(() => DeleteSkillPlanUseCase(Get.find<SkillPlanRepository>()));

    Get.lazyPut(
      () => MarkSkillPlanTaskCompleteUseCase(Get.find<SkillPlanRepository>()),
    );

    Get.lazyPut(() => GetSkillPlanByIdUseCase(Get.find<SkillPlanRepository>()));

    // Register controllers
    Get.lazyPut(
      () => SkillPlansController(
        getPrebuiltPlansUseCase: Get.find<GetPrebuiltPlansUseCase>(),
        createCustomPlanUseCase: Get.find<CreateCustomPlanUseCase>(),
        updateSkillPlanProgressUseCase:
            Get.find<UpdateSkillPlanProgressUseCase>(),
        updateSkillPlanUseCase: Get.find<UpdateSkillPlanUseCase>(),
        deleteSkillPlanUseCase: Get.find<DeleteSkillPlanUseCase>(),
        markSkillPlanTaskCompleteUseCase:
            Get.find<MarkSkillPlanTaskCompleteUseCase>(),
      ),
    );
  }
}
