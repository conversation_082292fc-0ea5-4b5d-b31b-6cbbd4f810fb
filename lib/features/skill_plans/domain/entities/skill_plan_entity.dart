import '../../../../core/domain/entities/entity.dart';
import 'skill_step_entity.dart';
import '../../../authentication/domain/entities/user_entity.dart';

/// Entity representing a skill building plan
class SkillPlanEntity extends Entity {
  final String id;
  final String name;
  final String description;
  final List<SkillStepEntity> steps;
  final bool isPublic;
  final String creatorId; // Keep for backward compatibility
  final UserEntity? creator; // New creator object from API response
  final DateTime createdAt;
  final DateTime updatedAt;
  final double progress; // 0 to 100
  final SkillPlanMetadata metadata;

  const SkillPlanEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.steps,
    required this.isPublic,
    required this.creatorId,
    this.creator,
    required this.createdAt,
    required this.updatedAt,
    this.progress = 0.0,
    required this.metadata,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    steps,
    isPublic,
    creatorId,
    creator,
    createdAt,
    updatedAt,
    progress,
    metadata,
  ];

  /// Copy the entity with specific property changes
  SkillPlanEntity copyWith({
    String? id,
    String? name,
    String? description,
    List<SkillStepEntity>? steps,
    bool? isPublic,
    String? creatorId,
    UserEntity? creator,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? progress,
    SkillPlanMetadata? metadata,
  }) {
    return SkillPlanEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      steps: steps ?? this.steps,
      isPublic: isPublic ?? this.isPublic,
      creatorId: creatorId ?? this.creatorId,
      creator: creator ?? this.creator,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      progress: progress ?? this.progress,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Calculate the current progress based on completed steps
  double calculateProgress() {
    if (steps.isEmpty) return 0.0;

    final completedSteps = steps.where((step) => step.isCompleted).length;
    return (completedSteps / steps.length) * 100;
  }
}

/// Metadata for a skill plan
class SkillPlanMetadata extends Entity {
  final String? category;
  final String? difficulty; // 'beginner', 'intermediate', 'advanced'
  final String?
  estimatedDuration; // Changed from int estimatedDays to String estimatedDuration
  final List<String> tags;

  const SkillPlanMetadata({
    this.category,
    this.difficulty,
    this.estimatedDuration,
    this.tags = const [],
  });

  @override
  List<Object?> get props => [category, difficulty, estimatedDuration, tags];

  /// Copy the metadata with specific property changes
  SkillPlanMetadata copyWith({
    String? category,
    String? difficulty,
    String? estimatedDuration,
    List<String>? tags,
  }) {
    return SkillPlanMetadata(
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      tags: tags ?? this.tags,
    );
  }
}

/// Resource linked to a skill step
class SkillStepResource extends Entity {
  final String type; // 'video', 'article', 'book', etc.
  final String url;
  final String title;

  const SkillStepResource({
    required this.type,
    required this.url,
    required this.title,
  });

  @override
  List<Object?> get props => [type, url, title];

  /// Copy with changes
  SkillStepResource copyWith({String? type, String? url, String? title}) {
    return SkillStepResource(
      type: type ?? this.type,
      url: url ?? this.url,
      title: title ?? this.title,
    );
  }
}
