import '../../../../core/domain/entities/entity.dart';
import 'skill_plan_entity.dart'; // Import for SkillStepResource

/// Entity representing a step in a skill plan
class SkillStepEntity extends Entity {
  final String id;
  final String title;
  final String description;
  final int order; // Added order field
  final bool isCompleted;
  final List<SkillStepTaskEntity> tasks;
  final List<SkillStepResource> resources; // Changed to proper type
  final DateTime? completedAt;
  final String? skillPlanId; // Reference to parent skill plan from API response

  const SkillStepEntity({
    required this.id,
    required this.title,
    required this.description,
    required this.order,
    this.isCompleted = false,
    this.tasks = const [],
    this.resources = const [],
    this.completedAt,
    this.skillPlanId,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    order,
    isCompleted,
    tasks,
    resources,
    completedAt,
    skillPlanId,
  ];

  /// Copy the entity with specific property changes
  SkillStepEntity copyWith({
    String? id,
    String? title,
    String? description,
    int? order,
    bool? isCompleted,
    List<SkillStepTaskEntity>? tasks,
    List<SkillStepResource>? resources,
    DateTime? completedAt,
    String? skillPlanId,
  }) {
    return SkillStepEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      order: order ?? this.order,
      isCompleted: isCompleted ?? this.isCompleted,
      tasks: tasks ?? this.tasks,
      resources: resources ?? this.resources,
      completedAt: completedAt ?? this.completedAt,
      skillPlanId: skillPlanId ?? this.skillPlanId,
    );
  }
}

/// Entity representing a task within a step
class SkillStepTaskEntity extends Entity {
  final String id;
  final String description;
  final bool isCompleted;
  final DateTime? completedAt;

  const SkillStepTaskEntity({
    required this.id,
    required this.description,
    this.isCompleted = false,
    this.completedAt,
  });

  @override
  List<Object?> get props => [id, description, isCompleted, completedAt];

  /// Copy the entity with specific property changes
  SkillStepTaskEntity copyWith({
    String? id,
    String? description,
    bool? isCompleted,
    DateTime? completedAt,
  }) {
    return SkillStepTaskEntity(
      id: id ?? this.id,
      description: description ?? this.description,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}
