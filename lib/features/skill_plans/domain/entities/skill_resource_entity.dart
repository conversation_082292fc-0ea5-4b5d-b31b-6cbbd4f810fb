import '../../../../core/domain/entities/entity.dart';

/// Resource types
enum ResourceType { article, video, audio, book, exercise, website, other }

/// Entity representing a resource for a skill step
class SkillResourceEntity extends Entity {
  final String id;
  final String title;
  final String url;
  final ResourceType type;
  final String? description;

  const SkillResourceEntity({
    required this.id,
    required this.title,
    required this.url,
    required this.type,
    this.description,
  });

  @override
  List<Object?> get props => [id, title, url, type, description];

  /// Copy the entity with specific property changes
  SkillResourceEntity copyWith({
    String? id,
    String? title,
    String? url,
    ResourceType? type,
    String? description,
  }) {
    return SkillResourceEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      url: url ?? this.url,
      type: type ?? this.type,
      description: description ?? this.description,
    );
  }
}
