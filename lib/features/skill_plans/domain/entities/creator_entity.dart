import '../../../../core/domain/entities/entity.dart';

/// Entity representing a skill plan creator
class CreatorEntity extends Entity {
  final String id;
  final String name;
  final String email;
  final String? profilePicture;

  const CreatorEntity({
    required this.id,
    required this.name,
    required this.email,
    this.profilePicture,
  });

  @override
  List<Object?> get props => [id, name, email, profilePicture];

  /// Copy the entity with specific property changes
  CreatorEntity copyWith({
    String? id,
    String? name,
    String? email,
    String? profilePicture,
  }) {
    return CreatorEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profilePicture: profilePicture ?? this.profilePicture,
    );
  }
}
