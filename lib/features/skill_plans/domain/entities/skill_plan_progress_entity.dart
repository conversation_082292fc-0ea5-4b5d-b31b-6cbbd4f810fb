import 'package:equatable/equatable.dart';

/// Domain entity representing skill plan progress information
class SkillPlanProgressEntity extends Equatable {
  /// Overall progress percentage (0-100)
  final int progress;

  /// List of completed step order numbers
  final List<int> completedSteps;

  const SkillPlanProgressEntity({
    required this.progress,
    required this.completedSteps,
  });

  @override
  List<Object> get props => [progress, completedSteps];

  @override
  String toString() =>
      'SkillPlanProgressEntity(progress: $progress, completedSteps: $completedSteps)';
}
