import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/skill_plan_entity.dart';
import '../entities/skill_step_entity.dart';
import '../repositories/skill_plan_repository.dart';

/// Use case for creating a custom skill plan
class CreateCustomPlanUseCase
    implements UseCase<SkillPlanEntity, CreateCustomPlanParams> {
  final SkillPlanRepository repository;

  CreateCustomPlanUseCase(this.repository);

  @override
  Future<Either<Failure, SkillPlanEntity>> call(
    CreateCustomPlanParams params,
  ) async {
    return repository.createSkillPlan(
      name: params.name,
      description: params.description,
      isPublic: params.isPublic,
      metadata: params.metadata,
      steps: params.steps,
    );
  }
}

/// Parameters for CreateCustomPlanUseCase
class CreateCustomPlanParams extends Equatable {
  final String name;
  final String description;
  final bool isPublic;
  final SkillPlanMetadata metadata;
  final List<SkillStepEntity> steps;

  const CreateCustomPlanParams({
    required this.name,
    required this.description,
    required this.isPublic,
    required this.metadata,
    required this.steps,
  });

  @override
  List<Object?> get props => [name, description, isPublic, metadata, steps];
}
