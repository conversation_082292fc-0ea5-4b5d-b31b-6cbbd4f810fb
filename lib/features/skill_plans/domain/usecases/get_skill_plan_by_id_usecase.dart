import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/skill_plan_entity.dart';
import '../repositories/skill_plan_repository.dart';

/// Use case for getting a skill plan by ID
class GetSkillPlanByIdUseCase
    implements UseCase<SkillPlanEntity, GetSkillPlanByIdParams> {
  final SkillPlanRepository repository;

  GetSkillPlanByIdUseCase(this.repository);

  @override
  Future<Either<Failure, SkillPlanEntity>> call(
    GetSkillPlanByIdParams params,
  ) async {
    return repository.getSkillPlanById(params.id);
  }
}

/// Parameters for GetSkillPlanByIdUseCase
class GetSkillPlanByIdParams extends Equatable {
  final String id;

  const GetSkillPlanByIdParams({required this.id});

  @override
  List<Object?> get props => [id];
}
