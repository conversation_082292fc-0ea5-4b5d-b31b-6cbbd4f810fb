import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/skill_plan_entity.dart';
import '../entities/skill_step_entity.dart';
import '../repositories/skill_plan_repository.dart';

/// Use case for updating an existing skill plan
class UpdateSkillPlanUseCase
    implements UseCase<SkillPlanEntity, UpdateSkillPlanParams> {
  final SkillPlanRepository repository;

  UpdateSkillPlanUseCase(this.repository);

  @override
  Future<Either<Failure, SkillPlanEntity>> call(
    UpdateSkillPlanParams params,
  ) async {
    return await repository.updateSkillPlan(
      id: params.id,
      name: params.name,
      description: params.description,
      isPublic: params.isPublic,
      metadata: params.metadata,
      steps: params.steps,
    );
  }
}

/// Parameters for updating a skill plan
class UpdateSkillPlanParams {
  final String id;
  final String? name;
  final String? description;
  final bool? isPublic;
  final SkillPlanMetadata? metadata;
  final List<SkillStepEntity>? steps;

  const UpdateSkillPlanParams({
    required this.id,
    this.name,
    this.description,
    this.isPublic,
    this.metadata,
    this.steps,
  });
}
