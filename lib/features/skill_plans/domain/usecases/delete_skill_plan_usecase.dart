import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/skill_plan_repository.dart';

/// Use case for deleting a skill plan
class DeleteSkillPlanUseCase implements UseCase<void, DeleteSkillPlanParams> {
  final SkillPlanRepository repository;

  DeleteSkillPlanUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteSkillPlanParams params) async {
    return await repository.deleteSkillPlan(params.id);
  }
}

/// Parameters for deleting a skill plan
class DeleteSkillPlanParams {
  final String id;

  const DeleteSkillPlanParams({required this.id});
}
