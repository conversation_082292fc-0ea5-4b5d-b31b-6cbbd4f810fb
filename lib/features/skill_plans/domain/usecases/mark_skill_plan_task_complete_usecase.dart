import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/skill_step_entity.dart';
import '../repositories/skill_plan_repository.dart';

/// Use case for marking a skill plan task as complete or incomplete
/// Use case for marking a skill plan task as complete or incomplete
class MarkSkillPlanTaskCompleteUseCase
    implements UseCase<SkillStepEntity, MarkSkillPlanTaskCompleteParams> {
  final SkillPlanRepository repository;

  MarkSkillPlanTaskCompleteUseCase(this.repository);

  @override
  Future<Either<Failure, SkillStepEntity>> call(
    MarkSkillPlanTaskCompleteParams params,
  ) async {
    return repository.markSkillPlanStepTaskComplete(
      planId: params.planId,
      stepId: params.stepId,
      taskIndex: params.taskIndex,
      isComplete: params.isComplete,
    );
  }
}

/// Parameters for MarkSkillPlanTaskCompleteUseCase
class MarkSkillPlanTaskCompleteParams extends Equatable {
  final String planId;
  final String stepId;
  final int taskIndex;
  final bool isComplete;

  const MarkSkillPlanTaskCompleteParams({
    required this.planId,
    required this.stepId,
    required this.taskIndex,
    required this.isComplete,
  });

  @override
  List<Object?> get props => [planId, stepId, taskIndex, isComplete];
}
