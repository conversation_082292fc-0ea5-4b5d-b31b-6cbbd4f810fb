import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/skill_plan_progress_entity.dart';
import '../repositories/skill_plan_repository.dart';

/// Use case for updating a skill plan's progress
class UpdateSkillPlanProgressUseCase
    implements UseCase<SkillPlanProgressEntity, UpdateSkillPlanProgressParams> {
  final SkillPlanRepository repository;

  UpdateSkillPlanProgressUseCase(this.repository);

  @override
  Future<Either<Failure, SkillPlanProgressEntity>> call(
    UpdateSkillPlanProgressParams params,
  ) async {
    return repository.updateSkillPlanProgress(
      planId: params.planId,
      order: params.order,
      completed: params.completed,
    );
  }
}

/// Parameters for UpdateSkillPlanProgressUseCase
class UpdateSkillPlanProgressParams extends Equatable {
  final String planId;
  final int order;
  final bool completed;

  const UpdateSkillPlanProgressParams({
    required this.planId,
    required this.order,
    required this.completed,
  });

  @override
  List<Object?> get props => [planId, order, completed];
}
