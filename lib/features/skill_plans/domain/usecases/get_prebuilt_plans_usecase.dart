import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/skill_plan_entity.dart';
import '../repositories/skill_plan_repository.dart';

/// Use case for getting pre-built skill plans
class GetPrebuiltPlansUseCase
    implements UseCase<List<SkillPlanEntity>, GetPrebuiltPlansParams> {
  final SkillPlanRepository repository;

  GetPrebuiltPlansUseCase(this.repository);

  @override
  Future<Either<Failure, List<SkillPlanEntity>>> call(
    GetPrebuiltPlansParams params,
  ) async {
    return repository.getPrebuiltPlans();
  }
}

/// Parameters for GetPrebuiltPlansUseCase
/// Can be expanded to include filters if needed
class GetPrebuiltPlansParams extends Equatable {
  const GetPrebuiltPlansParams();

  @override
  List<Object?> get props => [];
}
