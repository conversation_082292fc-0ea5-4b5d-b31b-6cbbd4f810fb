import 'package:dartz/dartz.dart';

import '../../../../core/domain/repositories/repository.dart';
import '../../../../core/error/failures.dart';
import '../entities/skill_plan_entity.dart';
import '../entities/skill_plan_progress_entity.dart';
import '../entities/skill_step_entity.dart';

/// Repository interface for skill plan-related operations
abstract class SkillPlanRepository extends Repository {
  // Skill Plan operations

  /// Get a list of skill plans based on filters
  Future<Either<Failure, List<SkillPlanEntity>>> getSkillPlans({
    bool? isPublic,
    String? creatorId,
  });

  /// Get a skill plan by ID
  Future<Either<Failure, SkillPlanEntity>> getSkillPlanById(String id);

  /// Create a new skill plan
  Future<Either<Failure, SkillPlanEntity>> createSkillPlan({
    required String name,
    required String description,
    required bool isPublic,
    required SkillPlanMetadata metadata,
    required List<SkillStepEntity> steps,
  });

  /// Update an existing skill plan
  Future<Either<Failure, SkillPlanEntity>> updateSkillPlan({
    required String id,
    String? name,
    String? description,
    bool? isPublic,
    SkillPlanMetadata? metadata,
    List<SkillStepEntity>? steps,
  });

  /// Delete a skill plan
  Future<Either<Failure, void>> deleteSkillPlan(String id);

  // Skill Step operations

  /// Update a specific step in a skill plan
  Future<Either<Failure, SkillStepEntity>> updateSkillPlanStep({
    required String planId,
    required String stepId,
    String? title,
    String? description,
    bool? isCompleted,
  });

  /// Mark a specific task within a step as complete
  Future<Either<Failure, SkillStepEntity>> markSkillPlanStepTaskComplete({
    required String planId,
    required String stepId,
    required int taskIndex,
    required bool isComplete,
  });

  /// Update the overall progress of a skill plan
  Future<Either<Failure, SkillPlanProgressEntity>> updateSkillPlanProgress({
    required String planId,
    required int order,
    required bool completed,
  });

  /// Get pre-built (public) skill plans
  Future<Either<Failure, List<SkillPlanEntity>>> getPrebuiltPlans();
}
