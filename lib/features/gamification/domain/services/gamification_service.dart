import 'package:power_up/features/gamification/domain/entities/xp_entity.dart';
import 'package:power_up/features/gamification/domain/usecases/process_gamification_event_usecase.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:dartz/dartz.dart';

/// Service to handle gamification events across the app
class GamificationService {
  final ProcessGamificationEventUseCase processEventUseCase;

  GamificationService({required this.processEventUseCase});

  /// Process habit completion and update gamification stats
  Future<Either<Failure, GamificationEventResult>> onHabitCompleted({
    required String userId,
    required String habitId,
    required DateTime completionDate,
    bool isFirstTime = false,
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.habitCompleted,
      relatedId: habitId,
      eventDate: completionDate,
      metadata: {'isFirstTime': isFirstTime, ...?metadata},
    );

    return await processEventUseCase(params);
  }

  /// Process task completion and update gamification stats
  Future<Either<Failure, GamificationEventResult>> onTaskCompleted({
    required String userId,
    required String taskId,
    required DateTime completionDate,
    String priority = 'medium',
    bool isFirstTime = false,
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.taskCompleted,
      relatedId: taskId,
      eventDate: completionDate,
      metadata: {
        'priority': priority,
        'isFirstTime': isFirstTime,
        ...?metadata,
      },
    );

    return await processEventUseCase(params);
  }

  /// Process perfect day achievement
  Future<Either<Failure, GamificationEventResult>> onPerfectDay({
    required String userId,
    required DateTime date,
    int completedHabits = 0,
    int totalHabits = 0,
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.perfectDay,
      eventDate: date,
      metadata: {
        'completedHabits': completedHabits,
        'totalHabits': totalHabits,
        ...?metadata,
      },
    );

    return await processEventUseCase(params);
  }

  /// Process streak milestone achievement
  Future<Either<Failure, GamificationEventResult>> onStreakMilestone({
    required String userId,
    required String relatedId,
    required int streakLength,
    required DateTime date,
    String streakType = 'habit',
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.streakMilestone,
      relatedId: relatedId,
      eventDate: date,
      metadata: {
        'streakLength': streakLength,
        'streakType': streakType,
        ...?metadata,
      },
    );

    return await processEventUseCase(params);
  }

  /// Process level up achievement
  Future<Either<Failure, GamificationEventResult>> onLevelUp({
    required String userId,
    required int newLevel,
    required DateTime date,
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.levelUp,
      eventDate: date,
      metadata: {'newLevel': newLevel, ...?metadata},
    );

    return await processEventUseCase(params);
  }

  /// Process challenge completion
  Future<Either<Failure, GamificationEventResult>> onChallengeCompleted({
    required String userId,
    required String challengeId,
    required DateTime completionDate,
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.challengeCompleted,
      relatedId: challengeId,
      eventDate: completionDate,
      metadata: metadata,
    );

    return await processEventUseCase(params);
  }

  /// Process first time action
  Future<Either<Failure, GamificationEventResult>> onFirstTimeAction({
    required String userId,
    required String actionType,
    required DateTime date,
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.firstTimeAction,
      eventDate: date,
      metadata: {'actionType': actionType, ...?metadata},
    );

    return await processEventUseCase(params);
  }

  /// Process social interaction
  Future<Either<Failure, GamificationEventResult>> onSocialInteraction({
    required String userId,
    required String interactionType,
    required DateTime date,
    String? targetUserId,
    Map<String, dynamic>? metadata,
  }) async {
    final params = GamificationEventParams(
      userId: userId,
      eventType: GamificationEventType.socialInteraction,
      eventDate: date,
      metadata: {
        'interactionType': interactionType,
        'targetUserId': targetUserId,
        ...?metadata,
      },
    );

    return await processEventUseCase(params);
  }

  /// Check if user achieved perfect week
  bool isPerfectWeek({
    required int completedDays,
    required int totalDays,
    double threshold = 1.0,
  }) {
    if (totalDays == 0) return false;
    final completionRate = completedDays / totalDays;
    return completionRate >= threshold;
  }

  /// Check if user achieved perfect month
  bool isPerfectMonth({
    required int completedDays,
    required int totalDays,
    double threshold = 0.9, // Allow 90% completion for perfect month
  }) {
    if (totalDays == 0) return false;
    final completionRate = completedDays / totalDays;
    return completionRate >= threshold;
  }

  /// Calculate XP multiplier based on streak length
  double getStreakMultiplier(int streakLength) {
    if (streakLength < 3) return 1.0;
    if (streakLength < 7) return 1.2;
    if (streakLength < 14) return 1.5;
    if (streakLength < 30) return 1.8;
    return 2.0; // Maximum 100% bonus for 30+ day streaks
  }

  /// Get badge criteria definitions
  Map<String, Map<String, dynamic>> getBadgeCriteria() {
    return {
      'first_habit': {
        'type': 'first_time',
        'description': 'Complete your first habit',
        'criteria': {'actionType': 'habit_completion'},
      },
      'streak_3': {
        'type': 'streak',
        'description': 'Maintain a 3-day streak',
        'criteria': {'requiredStreak': 3},
      },
      'streak_7': {
        'type': 'streak',
        'description': 'Maintain a 7-day streak',
        'criteria': {'requiredStreak': 7},
      },
      'streak_30': {
        'type': 'streak',
        'description': 'Maintain a 30-day streak',
        'criteria': {'requiredStreak': 30},
      },
      'level_5': {
        'type': 'milestone',
        'description': 'Reach level 5',
        'criteria': {'requiredLevel': 5},
      },
      'level_10': {
        'type': 'milestone',
        'description': 'Reach level 10',
        'criteria': {'requiredLevel': 10},
      },
      'perfect_week': {
        'type': 'achievement',
        'description': 'Complete all habits for a week',
        'criteria': {'requiredAchievement': 'perfect_week'},
      },
      'social_butterfly': {
        'type': 'social',
        'description': 'Interact with 5 community members',
        'criteria': {'requiredInteractions': 5},
      },
      'task_master': {
        'type': 'consistency',
        'description': 'Complete 100 tasks',
        'criteria': {'requiredTasks': 100},
      },
      'habit_builder': {
        'type': 'consistency',
        'description': 'Build 10 different habits',
        'criteria': {'requiredHabits': 10},
      },
    };
  }

  /// Get XP earning rules
  Map<XPSource, int> getXPRules() {
    return {
      XPSource.habitCompletion: 10,
      XPSource.taskCompletion: 15,
      XPSource.challengeParticipation: 25,
      XPSource.streakMaintained: 20,
      XPSource.focusSession: 12,
      XPSource.podcastListened: 8,
      XPSource.loginStreak: 5,
      XPSource.communityInteraction: 8,
      XPSource.streakMilestone: 50,
      XPSource.badgeEarned: 30,
      XPSource.challengeCompleted: 100,
      XPSource.perfectDay: 50,
      XPSource.perfectWeek: 200,
      XPSource.levelUp: 0, // No additional XP for leveling up
      XPSource.socialInteraction: 8,
      XPSource.aiInsight: 5,
      XPSource.profileUpdate: 3,
      XPSource.appUsage: 2,
      XPSource.feedback: 10,
      XPSource.bonus: 10,
    };
  }

  /// Calculate level from total XP
  int calculateLevel(int totalXP) {
    if (totalXP < 150) return 1;
    if (totalXP < 450) return 2;
    if (totalXP < 900) return 3;
    if (totalXP < 1500) return 4;

    // For higher levels, use exponential scaling
    int level = 4;
    int xpForNextLevel = 1500;
    int currentXP = totalXP;

    while (currentXP >= xpForNextLevel) {
      level++;
      currentXP -= xpForNextLevel;
      xpForNextLevel = (xpForNextLevel * 1.5).round();
    }

    return level;
  }

  /// Calculate XP needed for next level
  int calculateXPToNextLevel(int totalXP, int currentLevel) {
    final nextLevelXP = _getXPForLevel(currentLevel + 1);
    return nextLevelXP - totalXP;
  }

  /// Get total XP required for a specific level
  int _getXPForLevel(int level) {
    switch (level) {
      case 1:
        return 0;
      case 2:
        return 150;
      case 3:
        return 450;
      case 4:
        return 900;
      case 5:
        return 1500;
      default:
        // For higher levels, use exponential scaling
        int xp = 1500;
        for (int i = 5; i < level; i++) {
          xp += (xp * 0.5).round();
        }
        return xp;
    }
  }
}
