import 'package:equatable/equatable.dart';

/// Enum representing different types of rewards in the gamification system
enum RewardType { badge, xp, achievement, customization, unlock, powerUp }

/// Enum representing when rewards are triggered
enum RewardTrigger {
  streakMilestone,
  taskCompletion,
  habitConsistency,
  levelUp,
  firstTime,
  perfectWeek,
  perfectMonth,
  challengeComplete,
}

/// Domain entity representing a reward in the gamification system
class RewardEntity extends Equatable {
  final String id;
  final String name;
  final String description;
  final RewardType type;
  final RewardTrigger trigger;
  final Map<String, dynamic>
  criteria; // Flexible criteria for earning the reward
  final Map<String, dynamic> rewardData; // Data specific to the reward type
  final bool isActive;
  final DateTime createdAt;
  final DateTime? expiresAt;

  const RewardEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.trigger,
    required this.criteria,
    required this.rewardData,
    required this.createdAt,
    this.isActive = true,
    this.expiresAt,
  });

  /// Create a copy of this RewardEntity with modified fields
  RewardEntity copyWith({
    String? id,
    String? name,
    String? description,
    RewardType? type,
    RewardTrigger? trigger,
    Map<String, dynamic>? criteria,
    Map<String, dynamic>? rewardData,
    bool? isActive,
    DateTime? createdAt,
    DateTime? expiresAt,
  }) {
    return RewardEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      trigger: trigger ?? this.trigger,
      criteria: criteria ?? this.criteria,
      rewardData: rewardData ?? this.rewardData,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  /// Check if the reward criteria is met based on provided data
  bool isCriteriaMet(Map<String, dynamic> userData) {
    switch (trigger) {
      case RewardTrigger.streakMilestone:
        final requiredStreak = criteria['streakLength'] as int? ?? 0;
        final userStreak = userData['currentStreak'] as int? ?? 0;
        return userStreak >= requiredStreak;

      case RewardTrigger.taskCompletion:
        final requiredTasks = criteria['taskCount'] as int? ?? 0;
        final completedTasks = userData['completedTasks'] as int? ?? 0;
        return completedTasks >= requiredTasks;

      case RewardTrigger.habitConsistency:
        final requiredRate = criteria['consistencyRate'] as double? ?? 0.0;
        final userRate = userData['consistencyRate'] as double? ?? 0.0;
        return userRate >= requiredRate;

      case RewardTrigger.levelUp:
        final targetLevel = criteria['level'] as int? ?? 0;
        final userLevel = userData['level'] as int? ?? 0;
        return userLevel >= targetLevel;

      case RewardTrigger.firstTime:
        final actionType = criteria['actionType'] as String? ?? '';
        final hasPerformed =
            userData['hasPerformed_$actionType'] as bool? ?? false;
        return hasPerformed;

      case RewardTrigger.perfectWeek:
        final perfectWeeks = userData['perfectWeeks'] as int? ?? 0;
        final requiredWeeks = criteria['perfectWeekCount'] as int? ?? 1;
        return perfectWeeks >= requiredWeeks;

      case RewardTrigger.perfectMonth:
        final perfectMonths = userData['perfectMonths'] as int? ?? 0;
        final requiredMonths = criteria['perfectMonthCount'] as int? ?? 1;
        return perfectMonths >= requiredMonths;

      case RewardTrigger.challengeComplete:
        final challengeId = criteria['challengeId'] as String? ?? '';
        final completedChallenges =
            userData['completedChallenges'] as List<String>? ?? [];
        return completedChallenges.contains(challengeId);
    }
  }

  /// Check if the reward is currently valid (not expired)
  bool get isValid {
    if (!isActive) return false;
    if (expiresAt == null) return true;
    return DateTime.now().isBefore(expiresAt!);
  }

  /// Get the XP value if this is an XP reward
  int get xpValue {
    if (type != RewardType.xp) return 0;
    return rewardData['xpAmount'] as int? ?? 0;
  }

  /// Get the badge ID if this is a badge reward
  String get badgeId {
    if (type != RewardType.badge) return '';
    return rewardData['badgeId'] as String? ?? '';
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    type,
    trigger,
    criteria,
    rewardData,
    isActive,
    createdAt,
    expiresAt,
  ];
}

/// Entity representing a user's earned reward
class UserRewardEntity extends Equatable {
  final String id;
  final String userId;
  final String rewardId;
  final DateTime earnedAt;
  final Map<String, dynamic> earnedData; // Context data when reward was earned
  final bool isViewed;

  const UserRewardEntity({
    required this.id,
    required this.userId,
    required this.rewardId,
    required this.earnedAt,
    required this.earnedData,
    this.isViewed = false,
  });

  /// Create a copy of this UserRewardEntity with modified fields
  UserRewardEntity copyWith({
    String? id,
    String? userId,
    String? rewardId,
    DateTime? earnedAt,
    Map<String, dynamic>? earnedData,
    bool? isViewed,
  }) {
    return UserRewardEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      rewardId: rewardId ?? this.rewardId,
      earnedAt: earnedAt ?? this.earnedAt,
      earnedData: earnedData ?? this.earnedData,
      isViewed: isViewed ?? this.isViewed,
    );
  }

  /// Mark the reward as viewed
  UserRewardEntity markAsViewed() {
    return copyWith(isViewed: true);
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    rewardId,
    earnedAt,
    earnedData,
    isViewed,
  ];
}
