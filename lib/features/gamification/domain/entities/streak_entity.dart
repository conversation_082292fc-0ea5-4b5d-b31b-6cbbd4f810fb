import 'package:equatable/equatable.dart';

/// Domain entity representing a streak in the gamification system
class StreakEntity extends Equatable {
  final String id;
  final String userId;
  final String relatedId; // habit ID or task ID
  final String relatedType; // 'habit' or 'task'
  final int currentStreak;
  final int longestStreak;
  final DateTime lastUpdated;
  final DateTime startDate;
  final bool isActive;

  const StreakEntity({
    required this.id,
    required this.userId,
    required this.relatedId,
    required this.relatedType,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastUpdated,
    required this.startDate,
    this.isActive = true,
  });

  /// Create a copy of this StreakEntity with modified fields
  StreakEntity copyWith({
    String? id,
    String? userId,
    String? relatedId,
    String? relatedType,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastUpdated,
    DateTime? startDate,
    bool? isActive,
  }) {
    return StreakEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      relatedId: relatedId ?? this.relatedId,
      relatedType: relatedType ?? this.relatedType,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      startDate: startDate ?? this.startDate,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Increment the current streak
  StreakEntity incrementStreak() {
    final newCurrentStreak = currentStreak + 1;
    final newLongestStreak =
        newCurrentStreak > longestStreak ? newCurrentStreak : longestStreak;

    return copyWith(
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      lastUpdated: DateTime.now(),
    );
  }

  /// Reset the current streak to zero
  StreakEntity resetStreak() {
    return copyWith(currentStreak: 0, lastUpdated: DateTime.now());
  }

  /// Check if streak is broken (no activity in the expected timeframe)
  bool get isStreakBroken {
    final now = DateTime.now();
    final timeDiff = now.difference(lastUpdated);

    // Consider streak broken if no activity for more than 24 hours
    return timeDiff.inHours > 24;
  }

  /// Get the streak duration in days
  int get streakDurationInDays {
    return DateTime.now().difference(startDate).inDays;
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    relatedId,
    relatedType,
    currentStreak,
    longestStreak,
    lastUpdated,
    startDate,
    isActive,
  ];
}
