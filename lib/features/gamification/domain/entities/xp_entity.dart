import 'package:equatable/equatable.dart';

/// Sources of XP in the app
enum XPSource {
  taskCompletion,
  taskCompleted, // Alias for test compatibility
  habitCompletion,
  habitCompleted, // Alias for test compatibility
  challengeParticipation,
  badgeAwarded,
  streakMaintained,
  focusSession,
  podcastListened,
  loginStreak,
  communityInteraction,
  // Additional sources for comprehensive gamification
  streakMilestone,
  badgeEarned,
  challengeCompleted,
  perfectDay,
  perfectWeek,
  levelUp,
  socialInteraction,
  aiInsight,
  profileUpdate,
  appUsage,
  feedback,
  bonus,
}

/// Domain entity representing experience points (XP) in the gamification system
class XPEntity extends Equatable {
  final String id;
  final String userId;
  final int totalXP;
  final int currentLevelXP;
  final int level;
  final int xpToNextLevel;
  final DateTime lastUpdated;
  final List<XPTransactionEntity> recentTransactions;

  const XPEntity({
    required this.id,
    required this.userId,
    required this.totalXP,
    required this.currentLevelXP,
    required this.level,
    required this.xpToNextLevel,
    required this.lastUpdated,
    this.recentTransactions = const [],
  });

  /// Create a copy of this XPEntity with modified fields
  XPEntity copyWith({
    String? id,
    String? userId,
    int? totalXP,
    int? currentLevelXP,
    int? level,
    int? xpToNextLevel,
    DateTime? lastUpdated,
    List<XPTransactionEntity>? recentTransactions,
  }) {
    return XPEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      totalXP: totalXP ?? this.totalXP,
      currentLevelXP: currentLevelXP ?? this.currentLevelXP,
      level: level ?? this.level,
      xpToNextLevel: xpToNextLevel ?? this.xpToNextLevel,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      recentTransactions: recentTransactions ?? this.recentTransactions,
    );
  }

  /// Add XP and recalculate level if necessary
  XPEntity addXP(int xpAmount, XPSource source, {String? description}) {
    final newTotalXP = totalXP + xpAmount;

    // Calculate new level and XP requirements
    final levelInfo = _calculateLevelInfo(newTotalXP);

    // Create transaction record
    final transaction = XPTransactionEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      amount: xpAmount,
      source: source,
      description: description ?? _getDefaultDescription(source),
      timestamp: DateTime.now(),
    );

    // Add transaction to recent list (keep only last 10)
    final updatedTransactions =
        [transaction, ...recentTransactions].take(10).toList();

    return copyWith(
      totalXP: newTotalXP,
      currentLevelXP: levelInfo['currentLevelXP'],
      level: levelInfo['level'],
      xpToNextLevel: levelInfo['xpToNextLevel'],
      lastUpdated: DateTime.now(),
      recentTransactions: updatedTransactions,
    );
  }

  /// Calculate level information based on total XP
  Map<String, int> _calculateLevelInfo(int totalXP) {
    // XP requirements increase exponentially: level 1 = 100 XP, level 2 = 250 XP, etc.
    int level = 1;
    int xpForCurrentLevel = 0;
    int xpForNextLevel = 100;

    while (totalXP >= xpForNextLevel) {
      xpForCurrentLevel = xpForNextLevel;
      level++;
      xpForNextLevel = (100 * level * 1.5).round(); // Exponential growth
    }

    return {
      'level': level,
      'currentLevelXP': totalXP - xpForCurrentLevel,
      'xpToNextLevel': xpForNextLevel - totalXP,
    };
  }

  /// Get default description for XP source
  String _getDefaultDescription(XPSource source) {
    switch (source) {
      case XPSource.taskCompletion:
      case XPSource.taskCompleted:
        return 'Task completed';
      case XPSource.habitCompletion:
      case XPSource.habitCompleted:
        return 'Habit completed';
      case XPSource.challengeParticipation:
        return 'Challenge participation';
      case XPSource.badgeAwarded:
        return 'Badge earned';
      case XPSource.streakMaintained:
        return 'Streak maintained';
      case XPSource.focusSession:
        return 'Focus session completed';
      case XPSource.podcastListened:
        return 'Podcast listened';
      case XPSource.loginStreak:
        return 'Daily login';
      case XPSource.communityInteraction:
        return 'Community interaction';
      case XPSource.streakMilestone:
        return 'Streak milestone reached';
      case XPSource.badgeEarned:
        return 'Badge earned';
      case XPSource.challengeCompleted:
        return 'Challenge completed';
      case XPSource.perfectDay:
        return 'Perfect day achieved';
      case XPSource.perfectWeek:
        return 'Perfect week achieved';
      case XPSource.levelUp:
        return 'Level up bonus';
      case XPSource.socialInteraction:
        return 'Social interaction';
      case XPSource.aiInsight:
        return 'AI insight viewed';
      case XPSource.profileUpdate:
        return 'Profile updated';
      case XPSource.appUsage:
        return 'App usage bonus';
      case XPSource.feedback:
        return 'Feedback provided';
      case XPSource.bonus:
        return 'Special bonus';
    }
  }

  /// Get progress to next level as percentage (0.0 to 1.0)
  double get progressToNextLevel {
    final totalXPForLevel = currentLevelXP + xpToNextLevel;
    if (totalXPForLevel == 0) return 0.0;
    return currentLevelXP / totalXPForLevel;
  }

  /// Check if user has gained XP recently (within last hour)
  bool get hasRecentXP {
    final now = DateTime.now();
    final timeDiff = now.difference(lastUpdated);
    return timeDiff.inHours < 1;
  }

  /// Alias for level property to match expected interface
  int get currentLevel => level;

  @override
  List<Object?> get props => [
    id,
    userId,
    totalXP,
    currentLevelXP,
    level,
    xpToNextLevel,
    lastUpdated,
    recentTransactions,
  ];
}

/// Entity representing an XP transaction/earning event
class XPTransactionEntity extends Equatable {
  final String id;
  final String userId;
  final int amount;
  final XPSource source;
  final String description;
  final DateTime timestamp;
  final String? sourceId; // ID of the related action (task, habit, etc.)

  const XPTransactionEntity({
    required this.id,
    required this.userId,
    required this.amount,
    required this.source,
    required this.description,
    required this.timestamp,
    this.sourceId,
  });

  /// Create a copy of this XPTransactionEntity with modified fields
  XPTransactionEntity copyWith({
    String? id,
    String? userId,
    int? amount,
    XPSource? source,
    String? description,
    DateTime? timestamp,
    String? sourceId,
  }) {
    return XPTransactionEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      amount: amount ?? this.amount,
      source: source ?? this.source,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      sourceId: sourceId ?? this.sourceId,
    );
  }

  /// Check if this transaction happened today
  bool get isToday {
    final now = DateTime.now();
    return timestamp.year == now.year &&
        timestamp.month == now.month &&
        timestamp.day == now.day;
  }

  /// Get display name for the XP source
  String get sourceDisplayName {
    switch (source) {
      case XPSource.taskCompletion:
      case XPSource.taskCompleted:
        return 'Task';
      case XPSource.habitCompletion:
      case XPSource.habitCompleted:
        return 'Habit';
      case XPSource.challengeParticipation:
        return 'Challenge';
      case XPSource.badgeAwarded:
        return 'Badge';
      case XPSource.streakMaintained:
        return 'Streak';
      case XPSource.focusSession:
        return 'Focus';
      case XPSource.podcastListened:
        return 'Podcast';
      case XPSource.loginStreak:
        return 'Login';
      case XPSource.communityInteraction:
        return 'Community';
      case XPSource.streakMilestone:
        return 'Milestone';
      case XPSource.badgeEarned:
        return 'Badge';
      case XPSource.challengeCompleted:
        return 'Challenge';
      case XPSource.perfectDay:
        return 'Perfect Day';
      case XPSource.perfectWeek:
        return 'Perfect Week';
      case XPSource.levelUp:
        return 'Level Up';
      case XPSource.socialInteraction:
        return 'Social';
      case XPSource.aiInsight:
        return 'AI Insight';
      case XPSource.profileUpdate:
        return 'Profile';
      case XPSource.appUsage:
        return 'App Usage';
      case XPSource.feedback:
        return 'Feedback';
      case XPSource.bonus:
        return 'Bonus';
    }
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    amount,
    source,
    description,
    timestamp,
    sourceId,
  ];
}
