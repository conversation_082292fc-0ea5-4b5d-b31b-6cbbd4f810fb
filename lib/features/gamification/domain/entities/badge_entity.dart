import 'package:equatable/equatable.dart';

/// Types of badges that can be awarded
enum BadgeType {
  streak,
  milestone,
  consistency,
  achievement,
  challenge,
  special,
}

/// Rarity levels for badges
enum BadgeRarity { common, uncommon, rare, epic, legendary }

/// Domain entity representing a badge in the gamification system
class BadgeEntity extends Equatable {
  final String id;
  final String name;
  final String description;
  final String iconUrl;
  final BadgeType type;
  final BadgeRarity rarity;
  final int xpReward;
  final Map<String, dynamic> criteria; // Conditions to earn this badge
  final bool isActive;
  final DateTime createdAt;
  final String category; // Badge category for filtering
  final DateTime? earnedAt; // When the user earned this badge (if earned)

  const BadgeEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.iconUrl,
    required this.type,
    required this.rarity,
    required this.xpReward,
    required this.criteria,
    this.isActive = true,
    required this.createdAt,
    required this.category,
    this.earnedAt,
  });

  /// Create a copy of this BadgeEntity with modified fields
  BadgeEntity copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    BadgeType? type,
    BadgeRarity? rarity,
    int? xpReward,
    Map<String, dynamic>? criteria,
    bool? isActive,
    DateTime? createdAt,
    String? category,
    DateTime? earnedAt,
  }) {
    return BadgeEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      type: type ?? this.type,
      rarity: rarity ?? this.rarity,
      xpReward: xpReward ?? this.xpReward,
      criteria: criteria ?? this.criteria,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      category: category ?? this.category,
      earnedAt: earnedAt ?? this.earnedAt,
    );
  }

  /// Get rarity color for UI
  String get rarityColor {
    switch (rarity) {
      case BadgeRarity.common:
        return '#8E8E93'; // Gray
      case BadgeRarity.uncommon:
        return '#34C759'; // Green
      case BadgeRarity.rare:
        return '#007AFF'; // Blue
      case BadgeRarity.epic:
        return '#AF52DE'; // Purple
      case BadgeRarity.legendary:
        return '#FF9500'; // Orange
    }
  }

  /// Get display name for rarity
  String get rarityDisplayName {
    switch (rarity) {
      case BadgeRarity.common:
        return 'Common';
      case BadgeRarity.uncommon:
        return 'Uncommon';
      case BadgeRarity.rare:
        return 'Rare';
      case BadgeRarity.epic:
        return 'Epic';
      case BadgeRarity.legendary:
        return 'Legendary';
    }
  }

  /// Get display name for type
  String get typeDisplayName {
    switch (type) {
      case BadgeType.streak:
        return 'Streak';
      case BadgeType.milestone:
        return 'Milestone';
      case BadgeType.consistency:
        return 'Consistency';
      case BadgeType.achievement:
        return 'Achievement';
      case BadgeType.challenge:
        return 'Challenge';
      case BadgeType.special:
        return 'Special';
    }
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    iconUrl,
    type,
    rarity,
    xpReward,
    criteria,
    isActive,
    createdAt,
    category,
    earnedAt,
  ];
}

/// Entity representing a badge that has been awarded to a user
class UserBadgeEntity extends Equatable {
  final String id;
  final String userId;
  final String badgeId;
  final BadgeEntity badge;
  final DateTime awardedAt;
  final String? triggerAction; // What action triggered this badge
  final Map<String, dynamic>? metadata; // Additional data about the award

  const UserBadgeEntity({
    required this.id,
    required this.userId,
    required this.badgeId,
    required this.badge,
    required this.awardedAt,
    this.triggerAction,
    this.metadata,
  });

  /// Create a copy of this UserBadgeEntity with modified fields
  UserBadgeEntity copyWith({
    String? id,
    String? userId,
    String? badgeId,
    BadgeEntity? badge,
    DateTime? awardedAt,
    String? triggerAction,
    Map<String, dynamic>? metadata,
  }) {
    return UserBadgeEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      badgeId: badgeId ?? this.badgeId,
      badge: badge ?? this.badge,
      awardedAt: awardedAt ?? this.awardedAt,
      triggerAction: triggerAction ?? this.triggerAction,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if this badge was awarded recently (within last 24 hours)
  bool get isRecentlyAwarded {
    final now = DateTime.now();
    final timeDiff = now.difference(awardedAt);
    return timeDiff.inHours <= 24;
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    badgeId,
    badge,
    awardedAt,
    triggerAction,
    metadata,
  ];
}
