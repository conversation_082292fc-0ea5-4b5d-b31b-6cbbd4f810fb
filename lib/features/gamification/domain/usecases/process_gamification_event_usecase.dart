import 'package:power_up/features/gamification/domain/entities/xp_entity.dart';
import 'package:power_up/features/gamification/domain/usecases/calculate_streak_usecase.dart';
import 'package:power_up/features/gamification/domain/usecases/add_xp_usecase.dart';
import 'package:power_up/features/gamification/domain/usecases/award_badge_usecase.dart';
import 'package:power_up/features/gamification/domain/repositories/gamification_repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:dartz/dartz.dart';

/// Enum representing different types of gamification events
enum GamificationEventType {
  habitCompleted,
  taskCompleted,
  perfectDay,
  perfectWeek,
  perfectMonth,
  streakMilestone,
  levelUp,
  challengeCompleted,
  firstTimeAction,
  socialInteraction,
  profileUpdate,
  appUsage,
  feedback,
}

/// Use case for processing gamification events and awarding appropriate rewards
class ProcessGamificationEventUseCase {
  final CalculateStreakUseCase calculateStreakUseCase;
  final AddXPUseCase addXPUseCase;
  final AwardBadgeUseCase awardBadgeUseCase;
  final GamificationRepository repository;

  ProcessGamificationEventUseCase({
    required this.calculateStreakUseCase,
    required this.addXPUseCase,
    required this.awardBadgeUseCase,
    required this.repository,
  });

  /// Execute the gamification event processing
  Future<Either<Failure, GamificationEventResult>> call(
    GamificationEventParams params,
  ) async {
    try {
      final result = GamificationEventResult(
        userId: params.userId,
        eventType: params.eventType,
        xpGained: 0,
        badgesEarned: [],
        streakUpdated: null,
        hasLeveledUp: false,
        oldLevel: 0,
        newLevel: 0,
      );

      // Get user's current level before processing
      final currentXP = await repository.getUserXP(params.userId);
      result.oldLevel = currentXP?.currentLevel ?? 1;

      // Process the event based on type
      switch (params.eventType) {
        case GamificationEventType.habitCompleted:
          await _processHabitCompleted(params, result);
          break;
        case GamificationEventType.taskCompleted:
          await _processTaskCompleted(params, result);
          break;
        case GamificationEventType.perfectDay:
          await _processPerfectDay(params, result);
          break;
        case GamificationEventType.perfectWeek:
          await _processPerfectWeek(params, result);
          break;
        case GamificationEventType.perfectMonth:
          await _processPerfectMonth(params, result);
          break;
        case GamificationEventType.streakMilestone:
          await _processStreakMilestone(params, result);
          break;
        case GamificationEventType.levelUp:
          await _processLevelUp(params, result);
          break;
        case GamificationEventType.challengeCompleted:
          await _processChallengeCompleted(params, result);
          break;
        case GamificationEventType.firstTimeAction:
          await _processFirstTimeAction(params, result);
          break;
        case GamificationEventType.socialInteraction:
          await _processSocialInteraction(params, result);
          break;
        case GamificationEventType.profileUpdate:
          await _processProfileUpdate(params, result);
          break;
        case GamificationEventType.appUsage:
          await _processAppUsage(params, result);
          break;
        case GamificationEventType.feedback:
          await _processFeedback(params, result);
          break;
      }

      // Check for potential badge awards after processing
      await _checkForBadgeAwards(params.userId, result);

      return Right(result);
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to process gamification event: ${e.toString()}',
        ),
      );
    }
  }

  /// Process habit completion event
  Future<void> _processHabitCompleted(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    final habitId = params.relatedId ?? '';

    // Update streak
    final streakParams = CalculateStreakParams(
      userId: params.userId,
      relatedId: habitId,
      relatedType: 'habit',
      isSuccess: true,
      activityDate: params.eventDate,
    );

    final streakResult = await calculateStreakUseCase(streakParams);
    streakResult.fold(
      (failure) => {},
      (streak) => result.streakUpdated = streak,
    );

    // Award XP
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 10,
      source: XPSource.habitCompletion,
      isStreakBonus: (result.streakUpdated?.currentStreak ?? 0) > 1,
      isFirstTime: params.metadata?['isFirstTime'] == true,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process task completion event
  Future<void> _processTaskCompleted(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    // final taskId = params.relatedId ?? '';
    final taskPriority = params.metadata?['priority'] as String? ?? 'medium';

    // Calculate XP based on task priority
    int baseXP = 15; // Base XP for task completion
    switch (taskPriority.toLowerCase()) {
      case 'high':
        baseXP = 25;
        break;
      case 'medium':
        baseXP = 15;
        break;
      case 'low':
        baseXP = 10;
        break;
    }

    // Award XP
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: baseXP,
      source: XPSource.taskCompletion,
      isFirstTime: params.metadata?['isFirstTime'] == true,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process perfect day event
  Future<void> _processPerfectDay(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    // Award bonus XP for perfect day
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 50,
      source: XPSource.perfectDay,
      isPerfectDay: true,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process perfect week event
  Future<void> _processPerfectWeek(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    // Award bonus XP for perfect week
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 200,
      source: XPSource.perfectWeek,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process perfect month event
  Future<void> _processPerfectMonth(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    // Award bonus XP for perfect month
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 1000,
      source: XPSource.perfectWeek, // Using perfectWeek as closest match
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process streak milestone event
  Future<void> _processStreakMilestone(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    final streakLength = params.metadata?['streakLength'] as int? ?? 0;

    // Award XP based on streak length
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: streakLength * 5, // 5 XP per day in streak
      source: XPSource.streakMilestone,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process level up event
  Future<void> _processLevelUp(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    // Level up doesn't award additional XP, but might unlock badges
    result.hasLeveledUp = true;
    result.newLevel =
        params.metadata?['newLevel'] as int? ?? result.oldLevel + 1;
  }

  /// Process challenge completed event
  Future<void> _processChallengeCompleted(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    // Award significant XP for challenge completion
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 100,
      source: XPSource.challengeCompleted,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process first time action event
  Future<void> _processFirstTimeAction(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    // final actionType = params.metadata?['actionType'] as String? ?? '';

    // Award bonus XP for first time actions
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 20,
      source: XPSource.bonus,
      isFirstTime: true,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process social interaction event
  Future<void> _processSocialInteraction(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 5,
      source: XPSource.socialInteraction,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process profile update event
  Future<void> _processProfileUpdate(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 10,
      source: XPSource.profileUpdate,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process app usage event
  Future<void> _processAppUsage(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 2,
      source: XPSource.appUsage,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Process feedback event
  Future<void> _processFeedback(
    GamificationEventParams params,
    GamificationEventResult result,
  ) async {
    final xpParams = AddXPParams(
      userId: params.userId,
      baseAmount: 15,
      source: XPSource.feedback,
      metadata: params.metadata,
    );

    final xpResult = await addXPUseCase(xpParams);
    xpResult.fold((failure) => {}, (xpRes) {
      result.xpGained = xpRes.xpGained;
      result.hasLeveledUp = xpRes.hasLeveledUp;
      result.newLevel = xpRes.newLevel;
    });
  }

  /// Check for badge awards based on the event and current user stats
  Future<void> _checkForBadgeAwards(
    String userId,
    GamificationEventResult result,
  ) async {
    // Get available badges
    final availableBadges = await repository.getAvailableBadges();

    // Check each badge to see if criteria is met
    for (final badge in availableBadges) {
      final awardParams = AwardBadgeParams(
        userId: userId,
        badgeId: badge.id,
        checkCriteria: true,
      );

      final badgeResult = await awardBadgeUseCase(awardParams);
      badgeResult.fold((failure) => {}, (userBadge) {
        if (userBadge != null) {
          result.badgesEarned.add(userBadge);
        }
      });
    }
  }
}

/// Parameters for the ProcessGamificationEventUseCase
class GamificationEventParams {
  final String userId;
  final GamificationEventType eventType;
  final String? relatedId;
  final DateTime eventDate;
  final Map<String, dynamic>? metadata;

  GamificationEventParams({
    required this.userId,
    required this.eventType,
    this.relatedId,
    required this.eventDate,
    this.metadata,
  });
}

/// Result of processing a gamification event
class GamificationEventResult {
  final String userId;
  final GamificationEventType eventType;
  int xpGained;
  final List<dynamic> badgesEarned; // UserBadgeEntity list
  dynamic streakUpdated; // StreakEntity
  bool hasLeveledUp;
  int oldLevel;
  int newLevel;

  GamificationEventResult({
    required this.userId,
    required this.eventType,
    required this.xpGained,
    required this.badgesEarned,
    required this.streakUpdated,
    required this.hasLeveledUp,
    required this.oldLevel,
    required this.newLevel,
  });

  /// Check if there are any achievements to show
  bool get hasAchievements =>
      xpGained > 0 || badgesEarned.isNotEmpty || hasLeveledUp;
}
