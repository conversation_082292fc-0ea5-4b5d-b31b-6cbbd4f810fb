import 'package:power_up/core/error/failures.dart';
import 'package:power_up/features/gamification/domain/entities/xp_entity.dart';
import 'package:power_up/features/gamification/domain/repositories/gamification_repository.dart';
import 'package:dartz/dartz.dart';

/// Use case for adding XP to users
class AddXPUseCase {
  final GamificationRepository repository;

  AddXPUseCase(this.repository);

  /// Execute the XP addition process
  Future<Either<Failure, XPResult>> call(AddXPParams params) async {
    try {
      // Get current user XP
      final currentXP = await repository.getUserXP(params.userId);
      final oldLevel = currentXP?.currentLevel ?? 1;

      // Calculate XP amount based on source and multipliers
      final calculatedAmount = _calculateXPAmount(params);

      // Add XP to user
      final updatedXP = await repository.addXP(
        params.userId,
        calculatedAmount,
        params.source,
        metadata: params.metadata,
      );

      // Check for level up
      final newLevel = updatedXP.currentLevel;
      final hasLeveledUp = newLevel > oldLevel;

      return Right(
        XPResult(
          xpEntity: updatedXP,
          xpGained: calculatedAmount,
          hasLeveledUp: hasLeveledUp,
          oldLevel: oldLevel,
          newLevel: newLevel,
        ),
      );
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to add XP: ${e.toString()}'));
    }
  }

  /// Calculate the actual XP amount based on source and modifiers
  int _calculateXPAmount(AddXPParams params) {
    int baseAmount = params.baseAmount;

    // Apply source-specific multipliers
    double multiplier = _getSourceMultiplier(params.source);

    // Apply bonus multipliers
    if (params.isStreakBonus) {
      multiplier *= 1.5; // 50% bonus for streak activities
    }

    if (params.isPerfectDay) {
      multiplier *= 2.0; // 100% bonus for perfect day
    }

    if (params.isFirstTime) {
      multiplier *= 3.0; // 200% bonus for first time
    }

    // Apply custom multiplier
    multiplier *= params.customMultiplier;

    return (baseAmount * multiplier).round();
  }

  /// Get multiplier based on XP source
  double _getSourceMultiplier(XPSource source) {
    switch (source) {
      case XPSource.habitCompletion:
        return 1.0;
      case XPSource.habitCompleted: // Alias for test compatibility
        return 1.0;
      case XPSource.taskCompletion:
        return 1.2;
      case XPSource.taskCompleted: // Alias for test compatibility
        return 1.2;
      case XPSource.challengeParticipation:
        return 1.5;
      case XPSource.badgeAwarded:
        return 1.5;
      case XPSource.streakMaintained:
        return 1.8;
      case XPSource.focusSession:
        return 1.3;
      case XPSource.podcastListened:
        return 0.8;
      case XPSource.loginStreak:
        return 0.5;
      case XPSource.communityInteraction:
        return 0.8;
      case XPSource.streakMilestone:
        return 2.0;
      case XPSource.badgeEarned:
        return 1.5;
      case XPSource.challengeCompleted:
        return 3.0;
      case XPSource.perfectDay:
        return 2.5;
      case XPSource.perfectWeek:
        return 5.0;
      case XPSource.levelUp:
        return 0.0; // Level up doesn't give additional XP
      case XPSource.socialInteraction:
        return 0.8;
      case XPSource.aiInsight:
        return 0.5;
      case XPSource.profileUpdate:
        return 0.3;
      case XPSource.appUsage:
        return 0.2;
      case XPSource.feedback:
        return 1.0;
      case XPSource.bonus:
        return 1.0;
    }
  }
}

/// Parameters for the AddXPUseCase
class AddXPParams {
  final String userId;
  final int baseAmount;
  final XPSource source;
  final bool isStreakBonus;
  final bool isPerfectDay;
  final bool isFirstTime;
  final double customMultiplier;
  final Map<String, dynamic>? metadata;

  AddXPParams({
    required this.userId,
    required this.baseAmount,
    required this.source,
    this.isStreakBonus = false,
    this.isPerfectDay = false,
    this.isFirstTime = false,
    this.customMultiplier = 1.0,
    this.metadata,
  });
}

/// Result of adding XP
class XPResult {
  final XPEntity xpEntity;
  final int xpGained;
  final bool hasLeveledUp;
  final int oldLevel;
  final int newLevel;

  XPResult({
    required this.xpEntity,
    required this.xpGained,
    required this.hasLeveledUp,
    required this.oldLevel,
    required this.newLevel,
  });
}
