import 'package:power_up/features/gamification/domain/repositories/gamification_repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:dartz/dartz.dart';

/// Use case for getting user's gamification statistics
class GetUserGamificationStatsUseCase {
  final GamificationRepository repository;

  GetUserGamificationStatsUseCase(this.repository);

  /// Execute the get user gamification stats operation
  Future<Either<Failure, GamificationStatsResult>> call(String userId) async {
    try {
      // Get user XP and level
      final userXP = await repository.getUserXP(userId);
      final currentLevel = userXP?.currentLevel ?? 1;
      final totalXP = userXP?.totalXP ?? 0;
      final xpToNextLevel = userXP?.xpToNextLevel ?? 0;

      // Get user streaks
      final userStreaks = await repository.getUserStreaks(userId);
      final activeStreaks =
          userStreaks.where((s) => s.isActive && s.currentStreak > 0).toList();
      final longestStreak = userStreaks.fold<int>(
        0,
        (max, streak) =>
            streak.longestStreak > max ? streak.longestStreak : max,
      );

      // Get user badges
      final userBadges = await repository.getUserBadges(userId);
      final totalBadges = userBadges.length;

      // Get user rewards
      final userRewards = await repository.getUserRewards(userId);
      final unviewedRewards = userRewards.where((r) => !r.isViewed).toList();

      // Get full gamification stats
      final stats = await repository.getGamificationStats(userId);

      return Right(
        GamificationStatsResult(
          userId: userId,
          currentLevel: currentLevel,
          totalXP: totalXP,
          xpToNextLevel: xpToNextLevel,
          activeStreaksCount: activeStreaks.length,
          longestStreak: longestStreak,
          totalBadges: totalBadges,
          unviewedRewardsCount: unviewedRewards.length,
          recentTransactions: userXP?.recentTransactions ?? [],
          streaks: userStreaks,
          badges: userBadges,
          rewards: userRewards,
          detailedStats: stats,
        ),
      );
    } catch (e) {
      return Left(
        ServerFailure(
          message: 'Failed to get gamification stats: ${e.toString()}',
        ),
      );
    }
  }
}

/// Result containing user's gamification statistics
class GamificationStatsResult {
  final String userId;
  final int currentLevel;
  final int totalXP;
  final int xpToNextLevel;
  final int activeStreaksCount;
  final int longestStreak;
  final int totalBadges;
  final int unviewedRewardsCount;
  final List<dynamic> recentTransactions; // XPTransactionEntity list
  final List<dynamic> streaks; // StreakEntity list
  final List<dynamic> badges; // UserBadgeEntity list
  final List<dynamic> rewards; // UserRewardEntity list
  final Map<String, dynamic> detailedStats;

  const GamificationStatsResult({
    required this.userId,
    required this.currentLevel,
    required this.totalXP,
    required this.xpToNextLevel,
    required this.activeStreaksCount,
    required this.longestStreak,
    required this.totalBadges,
    required this.unviewedRewardsCount,
    required this.recentTransactions,
    required this.streaks,
    required this.badges,
    required this.rewards,
    required this.detailedStats,
  });

  /// Get level progress as a percentage (0.0 to 1.0)
  double get levelProgress {
    if (xpToNextLevel == 0) return 1.0;

    // Special case for level 1 - progress is based on XP towards level 2
    if (currentLevel == 1) {
      final xpNeededForLevel2 = _getXPForLevel(2);
      if (xpNeededForLevel2 == 0) return 1.0;
      return (totalXP / xpNeededForLevel2).clamp(0.0, 1.0);
    }

    // Calculate XP earned in current level
    final xpInCurrentLevel = totalXP - _getXPForLevel(currentLevel - 1);
    final xpNeededForCurrentLevel =
        _getXPForLevel(currentLevel) - _getXPForLevel(currentLevel - 1);

    if (xpNeededForCurrentLevel == 0) return 1.0;

    return (xpInCurrentLevel / xpNeededForCurrentLevel).clamp(0.0, 1.0);
  }

  /// Calculate total XP needed for a specific level
  int _getXPForLevel(int level) {
    if (level <= 0) return 0;
    int totalXP = 0;
    for (int i = 1; i < level; i++) {
      totalXP += (100 * i * 1.5).round();
    }
    return totalXP;
  }

  /// Get rank based on level (Bronze, Silver, Gold, etc.)
  String get rank {
    if (currentLevel >= 50) return 'Diamond';
    if (currentLevel >= 40) return 'Platinum';
    if (currentLevel >= 30) return 'Gold';
    if (currentLevel >= 20) return 'Silver';
    if (currentLevel >= 10) return 'Bronze';
    return 'Novice';
  }

  /// Check if user has any unviewed content
  bool get hasUnviewedContent => unviewedRewardsCount > 0;
}
