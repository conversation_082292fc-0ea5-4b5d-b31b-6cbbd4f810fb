import 'package:power_up/features/gamification/domain/entities/badge_entity.dart';
import 'package:power_up/features/gamification/domain/repositories/gamification_repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:dartz/dartz.dart';

/// Use case for awarding badges to users
class AwardBadgeUseCase {
  final GamificationRepository repository;

  AwardBadgeUseCase(this.repository);

  /// Execute the badge awarding process
  Future<Either<Failure, UserBadgeEntity?>> call(
    AwardBadgeParams params,
  ) async {
    try {
      // Check if user already has this badge
      final hasEarnedBadge = await repository.hasUserEarnedBadge(
        params.userId,
        params.badgeId,
      );

      if (hasEarnedBadge) {
        // User already has this badge, return null
        return const Right(null);
      }

      // Check if badge criteria is met
      if (params.checkCriteria && !await _isBadgeCriteriaMet(params)) {
        return Left(ValidationFailure(message: 'Badge criteria not met'));
      }

      // Award the badge
      final userBadge = await repository.awardBadge(
        params.userId,
        params.badgeId,
      );
      return Right(userBadge);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to award badge: ${e.toString()}'),
      );
    }
  }

  /// Check if the badge criteria is met for the user
  Future<bool> _isBadgeCriteriaMet(AwardBadgeParams params) async {
    try {
      // Get available badges to find the specific badge
      final availableBadges = await repository.getAvailableBadges();
      final badge = availableBadges.firstWhere(
        (b) => b.id == params.badgeId,
        orElse: () => throw Exception('Badge not found'),
      );

      // Check criteria based on badge type
      switch (badge.type) {
        case BadgeType.streak:
          return await _checkStreakCriteria(params.userId, badge);
        case BadgeType.milestone:
          return await _checkMilestoneCriteria(params.userId, badge);
        case BadgeType.consistency:
          return await _checkConsistencyCriteria(params.userId, badge);
        case BadgeType.achievement:
          return await _checkAchievementCriteria(params.userId, badge);
        case BadgeType.challenge:
          return await _checkChallengeCriteria(params.userId, badge);
        case BadgeType.special:
          return await _checkSpecialCriteria(params.userId, badge);
      }
    } catch (e) {
      return false;
    }
  }

  /// Check streak-based badge criteria
  Future<bool> _checkStreakCriteria(String userId, BadgeEntity badge) async {
    final userStreaks = await repository.getUserStreaks(userId);
    final maxStreak = userStreaks.fold<int>(
      0,
      (max, streak) => streak.currentStreak > max ? streak.currentStreak : max,
    );

    final requiredStreak = badge.criteria['requiredStreak'] as int? ?? 0;
    return maxStreak >= requiredStreak;
  }

  /// Check milestone-based badge criteria
  Future<bool> _checkMilestoneCriteria(String userId, BadgeEntity badge) async {
    final stats = await repository.getGamificationStats(userId);
    final userLevel = stats['level'] as int? ?? 0;
    final requiredLevel = badge.criteria['requiredLevel'] as int? ?? 0;
    return userLevel >= requiredLevel;
  }

  /// Check consistency-based badge criteria
  Future<bool> _checkConsistencyCriteria(
    String userId,
    BadgeEntity badge,
  ) async {
    final stats = await repository.getGamificationStats(userId);
    final consistencyRate = stats['consistencyRate'] as double? ?? 0.0;
    final requiredRate =
        badge.criteria['requiredConsistencyRate'] as double? ?? 0.0;
    return consistencyRate >= requiredRate;
  }

  /// Check achievement-based badge criteria
  Future<bool> _checkAchievementCriteria(
    String userId,
    BadgeEntity badge,
  ) async {
    final stats = await repository.getGamificationStats(userId);
    final achievements = stats['achievements'] as List<String>? ?? [];
    final requiredAchievement =
        badge.criteria['requiredAchievement'] as String? ?? '';
    return achievements.contains(requiredAchievement);
  }

  /// Check challenge-based badge criteria
  Future<bool> _checkChallengeCriteria(String userId, BadgeEntity badge) async {
    final stats = await repository.getGamificationStats(userId);
    final completedChallenges =
        stats['completedChallenges'] as List<String>? ?? [];
    final requiredChallenge =
        badge.criteria['requiredChallenge'] as String? ?? '';
    return completedChallenges.contains(requiredChallenge);
  }

  /// Check special badge criteria
  Future<bool> _checkSpecialCriteria(String userId, BadgeEntity badge) async {
    final stats = await repository.getGamificationStats(userId);
    final specialCriteria =
        badge.criteria['specialCriteria'] as Map<String, dynamic>? ?? {};

    // Check all special criteria
    for (final entry in specialCriteria.entries) {
      final userValue = stats[entry.key];
      final requiredValue = entry.value;

      if (userValue != requiredValue) {
        return false;
      }
    }

    return true;
  }
}

/// Parameters for the AwardBadgeUseCase
class AwardBadgeParams {
  final String userId;
  final String badgeId;
  final bool checkCriteria;

  AwardBadgeParams({
    required this.userId,
    required this.badgeId,
    this.checkCriteria = true,
  });
}
