import 'package:power_up/features/gamification/domain/entities/streak_entity.dart';
import 'package:power_up/features/gamification/domain/repositories/gamification_repository.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:dartz/dartz.dart';

/// Use case for calculating and updating streaks
class CalculateStreakUseCase {
  final GamificationRepository repository;

  CalculateStreakUseCase(this.repository);

  /// Execute the streak calculation and update
  Future<Either<Failure, StreakEntity>> call(
    CalculateStreakParams params,
  ) async {
    try {
      // Get existing streak or create new one
      StreakEntity? existingStreak = await repository.getStreak(
        params.userId,
        params.relatedId,
        params.relatedType,
      );

      StreakEntity updatedStreak;

      if (existingStreak == null) {
        // Create new streak
        updatedStreak = StreakEntity(
          id: _generateId(),
          userId: params.userId,
          relatedId: params.relatedId,
          relatedType: params.relatedType,
          currentStreak: params.isSuccess ? 1 : 0,
          longestStreak: params.isSuccess ? 1 : 0,
          lastUpdated: DateTime.now(),
          startDate: DateTime.now(),
          isActive: true,
        );
      } else {
        // Update existing streak
        if (params.isSuccess) {
          // Check if streak should continue or reset
          if (_shouldContinueStreak(existingStreak, params.activityDate)) {
            updatedStreak = existingStreak.incrementStreak();
          } else {
            // Reset streak and start new one
            updatedStreak = existingStreak.resetStreak().copyWith(
              currentStreak: 1,
              startDate: params.activityDate,
            );
          }
        } else {
          // Activity failed, check if streak should be broken
          if (_shouldBreakStreak(existingStreak, params.activityDate)) {
            updatedStreak = existingStreak.resetStreak();
          } else {
            // Keep existing streak
            updatedStreak = existingStreak;
          }
        }
      }

      // Save updated streak
      final savedStreak = await repository.updateStreak(updatedStreak);
      return Right(savedStreak);
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to calculate streak: ${e.toString()}'),
      );
    }
  }

  /// Check if the streak should continue based on timing
  bool _shouldContinueStreak(StreakEntity streak, DateTime activityDate) {
    final timeDiff = activityDate.difference(streak.lastUpdated);

    // Allow up to 48 hours gap for flexibility
    return timeDiff.inHours <= 48;
  }

  /// Check if the streak should be broken
  bool _shouldBreakStreak(StreakEntity streak, DateTime activityDate) {
    final timeDiff = activityDate.difference(streak.lastUpdated);

    // Break streak if more than 48 hours since last activity
    return timeDiff.inHours > 48;
  }

  /// Generate a unique ID for the streak
  String _generateId() {
    return 'streak_${DateTime.now().millisecondsSinceEpoch}';
  }
}

/// Parameters for the CalculateStreakUseCase
class CalculateStreakParams {
  final String userId;
  final String relatedId;
  final String relatedType;
  final bool isSuccess;
  final DateTime activityDate;

  CalculateStreakParams({
    required this.userId,
    required this.relatedId,
    required this.relatedType,
    required this.isSuccess,
    required this.activityDate,
  });
}
