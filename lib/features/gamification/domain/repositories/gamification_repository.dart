import 'package:power_up/features/gamification/domain/entities/streak_entity.dart';
import 'package:power_up/features/gamification/domain/entities/badge_entity.dart';
import 'package:power_up/features/gamification/domain/entities/xp_entity.dart';
import 'package:power_up/features/gamification/domain/entities/reward_entity.dart';

/// Repository interface for gamification operations
abstract class GamificationRepository {
  // Streak operations
  Future<StreakEntity?> getStreak(
    String userId,
    String relatedId,
    String relatedType,
  );
  Future<List<StreakEntity>> getUserStreaks(String userId);
  Future<StreakEntity> updateStreak(StreakEntity streak);
  Future<void> deleteStreak(String streakId);

  // Badge operations
  Future<List<UserBadgeEntity>> getUserBadges(String userId);
  Future<UserBadgeEntity> awardBadge(String userId, String badgeId);
  Future<bool> hasUserEarnedBadge(String userId, String badgeId);
  Future<List<BadgeEntity>> getAvailableBadges();

  // XP operations
  Future<XPEntity?> getUserXP(String userId);
  Future<XPEntity> addXP(
    String userId,
    int amount,
    XPSource source, {
    Map<String, dynamic>? metadata,
  });
  Future<List<XPTransactionEntity>> getXPTransactions(
    String userId, {
    int? limit,
  });
  Future<int> getUserLevel(String userId);

  // Reward operations
  Future<List<RewardEntity>> getAvailableRewards();
  Future<List<UserRewardEntity>> getUserRewards(String userId);
  Future<UserRewardEntity> awardReward(
    String userId,
    String rewardId,
    Map<String, dynamic> earnedData,
  );
  Future<UserRewardEntity> markRewardAsViewed(String userRewardId);

  // Analytics operations
  Future<Map<String, dynamic>> getGamificationStats(String userId);
  Future<List<Map<String, dynamic>>> getLeaderboard({int limit = 10});
}
