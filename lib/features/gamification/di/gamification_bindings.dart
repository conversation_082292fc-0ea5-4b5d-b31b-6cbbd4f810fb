import 'package:get/get.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/features/core/data/api/api_client.dart';
import 'package:power_up/features/gamification/data/datasources/gamification_local_data_source.dart';
import 'package:power_up/features/gamification/data/datasources/gamification_local_data_source_impl.dart';
import 'package:power_up/features/gamification/data/datasources/gamification_remote_data_source.dart';
import 'package:power_up/features/gamification/data/datasources/gamification_remote_data_source_impl.dart';
import 'package:power_up/features/gamification/data/repositories/gamification_repository_impl.dart';
import 'package:power_up/features/gamification/domain/repositories/gamification_repository.dart';
import 'package:power_up/features/gamification/domain/usecases/add_xp_usecase.dart';
import 'package:power_up/features/gamification/domain/usecases/award_badge_usecase.dart';
import 'package:power_up/features/gamification/domain/usecases/calculate_streak_usecase.dart';
import 'package:power_up/features/gamification/domain/usecases/get_user_gamification_stats_usecase.dart';
import 'package:power_up/features/gamification/domain/usecases/process_gamification_event_usecase.dart';
import 'package:power_up/features/gamification/presentation/controllers/gamification_controller.dart';

/// Bindings for gamification feature
class GamificationBindings implements Bindings {
  @override
  void dependencies() {
    // Core dependencies
    final networkInfo = Get.find<NetworkInfo>();
    final storageService = Get.find<StorageService>();
    final apiClient = Get.find<ApiClient>();

    // Data sources
    Get.lazyPut<GamificationLocalDataSource>(
      () => GamificationLocalDataSourceImpl(storageService: storageService),
    );

    Get.lazyPut<GamificationRemoteDataSource>(
      () => GamificationRemoteDataSourceImpl(apiClient: apiClient),
    );

    // Repository
    Get.lazyPut<GamificationRepository>(
      () => GamificationRepositoryImpl(
        remoteDataSource: Get.find<GamificationRemoteDataSource>(),
        localDataSource: Get.find<GamificationLocalDataSource>(),
        networkInfo: networkInfo,
      ),
    );

    // Use cases
    Get.lazyPut(() => AddXPUseCase(Get.find<GamificationRepository>()));
    Get.lazyPut(() => AwardBadgeUseCase(Get.find<GamificationRepository>()));
    Get.lazyPut(
      () => CalculateStreakUseCase(Get.find<GamificationRepository>()),
    );
    Get.lazyPut(
      () => GetUserGamificationStatsUseCase(Get.find<GamificationRepository>()),
    );

    // Complex use case with multiple dependencies
    Get.lazyPut(
      () => ProcessGamificationEventUseCase(
        calculateStreakUseCase: Get.find<CalculateStreakUseCase>(),
        addXPUseCase: Get.find<AddXPUseCase>(),
        awardBadgeUseCase: Get.find<AwardBadgeUseCase>(),
        repository: Get.find<GamificationRepository>(),
      ),
    );

    // Controller for gamification UI
    Get.lazyPut<GamificationController>(
      () => GamificationController(
        getUserGamificationStatsUseCase:
            Get.find<GetUserGamificationStatsUseCase>(),
      ),
    );
  }
}
