import 'package:get/get.dart';
import 'package:power_up/core/presentation/controllers/base_controller.dart';
import 'package:power_up/core/services/auth_service.dart';
import 'package:power_up/features/gamification/domain/entities/badge_entity.dart';
import 'package:power_up/features/gamification/domain/entities/streak_entity.dart';
import 'package:power_up/features/gamification/domain/entities/xp_entity.dart';
import 'package:power_up/features/gamification/domain/usecases/get_user_gamification_stats_usecase.dart';

/// Controller for managing gamification UI state and data
class GamificationController extends BaseController {
  final GetUserGamificationStatsUseCase _getUserGamificationStatsUseCase;
  final AuthService _authService;

  // Observable state
  final Rx<XPEntity?> userXP = Rx<XPEntity?>(null);
  final RxList<BadgeEntity> userBadges = <BadgeEntity>[].obs;
  final RxList<StreakEntity> userStreaks = <StreakEntity>[].obs;

  // Computed values
  int get currentLevel => userXP.value?.currentLevel ?? 1;
  int get currentXP => userXP.value?.totalXP ?? 0;
  int get xpToNextLevel => userXP.value?.xpToNextLevel ?? 0;
  double get levelProgress {
    final total = currentXP + xpToNextLevel;
    if (total == 0) return 0.0;
    return currentXP / total;
  }

  // Achievement state
  final RxBool showAchievementDialog = false.obs;
  final RxString achievementTitle = ''.obs;
  final RxString achievementMessage = ''.obs;

  GamificationController({
    required GetUserGamificationStatsUseCase getUserGamificationStatsUseCase,
  }) : _getUserGamificationStatsUseCase = getUserGamificationStatsUseCase,
       _authService = Get.find<AuthService>();

  @override
  void onInit() {
    super.onInit();
    fetchGamificationStats();
  }

  /// Fetch user's gamification statistics
  Future<void> fetchGamificationStats() async {
    final userId = _authService.currentUserId;
    if (userId == null) return;

    setLoading(true);

    final result = await _getUserGamificationStatsUseCase(userId);
    result.fold(
      (failure) {
        handleError(failure);
        // Don't show error to user for gamification data
      },
      (stats) {
        // Create XPEntity from stats
        userXP.value = XPEntity(
          id: 'user_xp_$userId',
          userId: userId,
          totalXP: stats.totalXP,
          currentLevelXP: stats.totalXP, // For now, use total XP
          level: stats.currentLevel,
          xpToNextLevel: stats.xpToNextLevel,
          lastUpdated: DateTime.now(),
          recentTransactions:
              stats.recentTransactions.cast<XPTransactionEntity>(),
        );

        // Set badges and streaks from the result
        userBadges.assignAll(stats.badges.cast<BadgeEntity>());
        userStreaks.assignAll(stats.streaks.cast<StreakEntity>());
      },
    );

    setLoading(false);
  }

  /// Show achievement notification
  void showAchievement(String title, String message) {
    achievementTitle.value = title;
    achievementMessage.value = message;
    showAchievementDialog.value = true;
  }

  /// Hide achievement notification
  void hideAchievement() {
    showAchievementDialog.value = false;
  }

  /// Get the longest current streak
  int get longestCurrentStreak {
    if (userStreaks.isEmpty) return 0;
    return userStreaks
        .map((streak) => streak.currentStreak)
        .reduce((a, b) => a > b ? a : b);
  }

  /// Get total number of badges earned
  int get totalBadgesEarned => userBadges.length;

  /// Get badges by category
  List<BadgeEntity> getBadgesByCategory(String category) {
    return userBadges.where((badge) => badge.category == category).toList();
  }

  /// Get recently earned badges (last 7 days)
  List<BadgeEntity> get recentBadges {
    final weekAgo = DateTime.now().subtract(const Duration(days: 7));
    return userBadges
        .where(
          (badge) => badge.earnedAt != null && badge.earnedAt!.isAfter(weekAgo),
        )
        .toList();
  }

  /// Refresh gamification data
  Future<void> refresh() async {
    await fetchGamificationStats();
  }
}
