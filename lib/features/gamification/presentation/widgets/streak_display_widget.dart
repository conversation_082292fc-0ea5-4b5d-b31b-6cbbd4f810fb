import 'package:flutter/material.dart';
import 'package:power_up/features/gamification/domain/entities/streak_entity.dart';

/// Widget to display a streak with fire animation and progress
class StreakDisplayWidget extends StatelessWidget {
  final StreakEntity streak;

  const StreakDisplayWidget({super.key, required this.streak});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // Streak Icon with animation
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: _getStreakColor(streak.currentStreak),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.local_fire_department,
                color: Colors.white,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Streak Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getStreakTitle(streak),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${streak.currentStreak} days',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getStreakColor(streak.currentStreak),
                    ),
                  ),
                  if (streak.longestStreak > streak.currentStreak) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Best: ${streak.longestStreak} days',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ],
              ),
            ),

            // Streak Level Indicator
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStreakColor(
                      streak.currentStreak,
                    ).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStreakLevel(streak.currentStreak),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: _getStreakColor(streak.currentStreak),
                    ),
                  ),
                ),
                // if (streak.lastUpdated != null) ...[
                const SizedBox(height: 4),
                Text(
                  'Updated ${_getTimeAgo(streak.lastUpdated)}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
                ),
                // ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Get streak color based on current streak length
  Color _getStreakColor(int streakLength) {
    if (streakLength == 0) {
      return Colors.grey;
    } else if (streakLength < 7) {
      return Colors.orange[400]!;
    } else if (streakLength < 30) {
      return Colors.orange[600]!;
    } else if (streakLength < 100) {
      return Colors.red[500]!;
    } else {
      return Colors.red[700]!;
    }
  }

  /// Get streak level text based on length
  String _getStreakLevel(int streakLength) {
    if (streakLength == 0) {
      return 'Start';
    } else if (streakLength < 7) {
      return 'Beginner';
    } else if (streakLength < 30) {
      return 'Good';
    } else if (streakLength < 100) {
      return 'Great';
    } else {
      return 'Legendary';
    }
  }

  /// Get streak title based on type
  String _getStreakTitle(StreakEntity streak) {
    // Since we don't have the type in the entity, we'll use a generic title
    // This could be enhanced based on the actual streak type (habit, task, etc.)
    return 'Daily Streak';
  }

  /// Get time ago text
  String _getTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Animated streak counter widget for showing live streak updates
class AnimatedStreakCounter extends StatefulWidget {
  final int streak;
  final Duration animationDuration;

  const AnimatedStreakCounter({
    super.key,
    required this.streak,
    this.animationDuration = const Duration(milliseconds: 500),
  });

  @override
  State<AnimatedStreakCounter> createState() => _AnimatedStreakCounterState();
}

class _AnimatedStreakCounterState extends State<AnimatedStreakCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  int _displayedStreak = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.elasticOut));

    _displayedStreak = widget.streak;
  }

  @override
  void didUpdateWidget(AnimatedStreakCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.streak != widget.streak) {
      _displayedStreak = widget.streak;
      _controller.forward().then((_) => _controller.reverse());
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.local_fire_department,
                color: _getStreakColor(_displayedStreak),
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                '$_displayedStreak',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getStreakColor(_displayedStreak),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getStreakColor(int streakLength) {
    if (streakLength == 0) {
      return Colors.grey;
    } else if (streakLength < 7) {
      return Colors.orange[400]!;
    } else if (streakLength < 30) {
      return Colors.orange[600]!;
    } else {
      return Colors.red[500]!;
    }
  }
}
