import 'package:flutter/material.dart';

/// Widget to display user's level progress with XP bar
class LevelProgressWidget extends StatelessWidget {
  final int currentXP;
  final int xpToNextLevel;
  final double progress;

  const LevelProgressWidget({
    super.key,
    required this.currentXP,
    required this.xpToNextLevel,
    required this.progress,
  });

  @override
  Widget build(BuildContext context) {
    // final totalXPForLevel = currentXP + xpToNextLevel;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // XP Text Info
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '$currentXP XP',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
            Text(
              '$xpToNextLevel XP to next level',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Progress Bar
        ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              _getProgressColor(progress),
            ),
            minHeight: 12,
          ),
        ),

        const SizedBox(height: 4),

        // Progress Percentage
        Text(
          '${(progress * 100).toStringAsFixed(1)}% complete',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  /// Get progress bar color based on completion percentage
  Color _getProgressColor(double progress) {
    if (progress < 0.3) {
      return Colors.red[400]!;
    } else if (progress < 0.7) {
      return Colors.orange[400]!;
    } else {
      return Colors.green[400]!;
    }
  }
}
