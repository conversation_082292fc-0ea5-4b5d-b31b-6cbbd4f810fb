import 'package:flutter/material.dart';
import 'package:power_up/features/gamification/domain/entities/badge_entity.dart';

/// Widget to display a grid of user badges
class BadgesGridWidget extends StatelessWidget {
  final List<BadgeEntity> badges;

  const BadgesGridWidget({super.key, required this.badges});

  @override
  Widget build(BuildContext context) {
    if (badges.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            children: [
              Icon(Icons.emoji_events, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No badges earned yet',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                'Complete habits and tasks to earn your first badge!',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: badges.length,
      itemBuilder: (context, index) {
        final badge = badges[index];
        return BadgeCard(badge: badge);
      },
    );
  }
}

/// Individual badge card widget
class BadgeCard extends StatelessWidget {
  final BadgeEntity badge;

  const BadgeCard({super.key, required this.badge});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _showBadgeDetails(context),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Badge Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getBadgeColor(badge.category),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getBadgeIcon(badge.category),
                  color: Colors.white,
                  size: 24,
                ),
              ),

              const SizedBox(height: 8),

              // Badge Name
              Text(
                badge.name,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show badge details in a dialog
  void _showBadgeDetails(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: _getBadgeColor(badge.category),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getBadgeIcon(badge.category),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(badge.name, style: const TextStyle(fontSize: 18)),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  badge.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                if (badge.earnedAt != null) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Earned: ${_formatDate(badge.earnedAt!)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  /// Get badge color based on category
  Color _getBadgeColor(String category) {
    switch (category.toLowerCase()) {
      case 'streak':
        return Colors.orange;
      case 'completion':
        return Colors.green;
      case 'consistency':
        return Colors.blue;
      case 'milestone':
        return Colors.purple;
      case 'special':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  /// Get badge icon based on category
  IconData _getBadgeIcon(String category) {
    switch (category.toLowerCase()) {
      case 'streak':
        return Icons.local_fire_department;
      case 'completion':
        return Icons.check_circle;
      case 'consistency':
        return Icons.schedule;
      case 'milestone':
        return Icons.flag;
      case 'special':
        return Icons.star;
      default:
        return Icons.emoji_events;
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
