import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../datasources/gamification_remote_data_source.dart';
import '../datasources/gamification_local_data_source.dart';
import '../models/streak_model.dart';
import '../../domain/entities/streak_entity.dart';
import '../../domain/entities/badge_entity.dart';
import '../../domain/entities/xp_entity.dart';
import '../../domain/entities/reward_entity.dart';
import '../../domain/repositories/gamification_repository.dart';

/// Implementation of GamificationRepository
class GamificationRepositoryImpl implements GamificationRepository {
  final GamificationRemoteDataSource remoteDataSource;
  final GamificationLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  GamificationRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  /// Initialize the repository
  Future<void> init() async {
    await localDataSource.init();
  }

  /// Clear repository data
  Future<void> clearData() async {
    await localDataSource.clear();
  }

  // Streak operations
  @override
  Future<StreakEntity?> getStreak(
    String userId,
    String relatedId,
    String relatedType,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteStreak = await remoteDataSource.getStreak(
          userId,
          relatedId,
          relatedType,
        );

        if (remoteStreak != null) {
          // Cache the remote data
          await localDataSource.cacheStreak(remoteStreak);
          return remoteStreak;
        }

        return null;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedStreak = await localDataSource.getCachedStreak(
            userId,
            relatedId,
            relatedType,
          );
          return cachedStreak;
        } on CacheException {
          return Left(
                ServerFailure(message: e.message, statusCode: e.statusCode),
              )
              as StreakEntity?;
        }
      }
    } else {
      try {
        final cachedStreak = await localDataSource.getCachedStreak(
          userId,
          relatedId,
          relatedType,
        );
        return cachedStreak;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<List<StreakEntity>> getUserStreaks(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteStreaks = await remoteDataSource.getUserStreaks(userId);

        // Cache the remote data
        await localDataSource.cacheUserStreaks(userId, remoteStreaks);

        return remoteStreaks;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedStreaks = await localDataSource.getCachedUserStreaks(
            userId,
          );
          return cachedStreaks;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedStreaks = await localDataSource.getCachedUserStreaks(
          userId,
        );
        return cachedStreaks;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<StreakEntity> updateStreak(StreakEntity streak) async {
    if (await networkInfo.isConnected) {
      try {
        final streakModel = StreakModel.fromEntity(streak);
        final updatedStreak = await remoteDataSource.updateStreak(streakModel);

        // Cache the updated streak
        await localDataSource.cacheStreak(updatedStreak);

        return updatedStreak;
      } on ServerException catch (e) {
        throw ServerFailure(message: e.message, statusCode: e.statusCode);
      }
    } else {
      throw const NetworkFailure(message: 'Cannot update streak while offline');
    }
  }

  @override
  Future<void> deleteStreak(String streakId) async {
    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.deleteStreak(streakId);

        // Remove from cache
        await localDataSource.removeCachedStreak(streakId);
      } on ServerException catch (e) {
        throw ServerFailure(message: e.message, statusCode: e.statusCode);
      }
    } else {
      throw const NetworkFailure(message: 'Cannot delete streak while offline');
    }
  }

  // Badge operations
  @override
  Future<List<UserBadgeEntity>> getUserBadges(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteBadges = await remoteDataSource.getUserBadges(userId);

        // Cache the remote data
        await localDataSource.cacheUserBadges(userId, remoteBadges);

        return remoteBadges;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedBadges = await localDataSource.getCachedUserBadges(
            userId,
          );
          return cachedBadges;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedBadges = await localDataSource.getCachedUserBadges(userId);
        return cachedBadges;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<UserBadgeEntity> awardBadge(String userId, String badgeId) async {
    if (await networkInfo.isConnected) {
      try {
        final userBadge = await remoteDataSource.awardBadge(userId, badgeId);

        // Cache the new badge
        await localDataSource.cacheUserBadge(userBadge);

        return userBadge;
      } on ServerException catch (e) {
        throw ServerFailure(message: e.message, statusCode: e.statusCode);
      }
    } else {
      throw const NetworkFailure(message: 'Cannot award badge while offline');
    }
  }

  @override
  Future<bool> hasUserEarnedBadge(String userId, String badgeId) async {
    if (await networkInfo.isConnected) {
      try {
        return await remoteDataSource.hasUserEarnedBadge(userId, badgeId);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedBadges = await localDataSource.getCachedUserBadges(
            userId,
          );
          return cachedBadges.any((badge) => badge.badgeId == badgeId);
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedBadges = await localDataSource.getCachedUserBadges(userId);
        return cachedBadges.any((badge) => badge.badgeId == badgeId);
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<List<BadgeEntity>> getAvailableBadges() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteBadges = await remoteDataSource.getAvailableBadges();

        // Cache the remote data
        await localDataSource.cacheAvailableBadges(remoteBadges);

        return remoteBadges;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedBadges = await localDataSource.getCachedAvailableBadges();
          return cachedBadges;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedBadges = await localDataSource.getCachedAvailableBadges();
        return cachedBadges;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  // XP operations
  @override
  Future<XPEntity?> getUserXP(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteXP = await remoteDataSource.getUserXP(userId);

        if (remoteXP != null) {
          // Cache the remote data
          await localDataSource.cacheUserXP(remoteXP);
          return remoteXP;
        }

        return null;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedXP = await localDataSource.getCachedUserXP(userId);
          return cachedXP;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedXP = await localDataSource.getCachedUserXP(userId);
        return cachedXP;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<XPEntity> addXP(
    String userId,
    int amount,
    XPSource source, {
    Map<String, dynamic>? metadata,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedXP = await remoteDataSource.addXP(
          userId,
          amount,
          source,
          metadata: metadata,
        );

        // Cache the updated XP
        await localDataSource.cacheUserXP(updatedXP);

        return updatedXP;
      } on ServerException catch (e) {
        throw ServerFailure(message: e.message, statusCode: e.statusCode);
      }
    } else {
      throw const NetworkFailure(message: 'Cannot add XP while offline');
    }
  }

  @override
  Future<List<XPTransactionEntity>> getXPTransactions(
    String userId, {
    int? limit,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteTransactions = await remoteDataSource.getXPTransactions(
          userId,
          limit: limit,
        );

        // Cache the remote data
        await localDataSource.cacheXPTransactions(userId, remoteTransactions);

        return remoteTransactions;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedTransactions = await localDataSource
              .getCachedXPTransactions(userId);
          return cachedTransactions;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedTransactions = await localDataSource
            .getCachedXPTransactions(userId);
        return cachedTransactions;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<int> getUserLevel(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        return await remoteDataSource.getUserLevel(userId);
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedXP = await localDataSource.getCachedUserXP(userId);
          return cachedXP?.level ?? 1;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedXP = await localDataSource.getCachedUserXP(userId);
        return cachedXP?.level ?? 1;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  // Reward operations
  @override
  Future<List<RewardEntity>> getAvailableRewards() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteRewards = await remoteDataSource.getAvailableRewards();

        // Cache the remote data
        await localDataSource.cacheAvailableRewards(remoteRewards);

        return remoteRewards;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedRewards =
              await localDataSource.getCachedAvailableRewards();
          return cachedRewards;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedRewards = await localDataSource.getCachedAvailableRewards();
        return cachedRewards;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<List<UserRewardEntity>> getUserRewards(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteRewards = await remoteDataSource.getUserRewards(userId);

        // Cache the remote data
        await localDataSource.cacheUserRewards(userId, remoteRewards);

        return remoteRewards;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedRewards = await localDataSource.getCachedUserRewards(
            userId,
          );
          return cachedRewards;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedRewards = await localDataSource.getCachedUserRewards(
          userId,
        );
        return cachedRewards;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<UserRewardEntity> awardReward(
    String userId,
    String rewardId,
    Map<String, dynamic> earnedData,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final userReward = await remoteDataSource.awardReward(
          userId,
          rewardId,
          earnedData,
        );

        // Cache the new reward
        await localDataSource.cacheUserReward(userReward);

        return userReward;
      } on ServerException catch (e) {
        throw ServerFailure(message: e.message, statusCode: e.statusCode);
      }
    } else {
      throw const NetworkFailure(message: 'Cannot award reward while offline');
    }
  }

  @override
  Future<UserRewardEntity> markRewardAsViewed(String userRewardId) async {
    if (await networkInfo.isConnected) {
      try {
        final updatedReward = await remoteDataSource.markRewardAsViewed(
          userRewardId,
        );

        // Cache the updated reward
        await localDataSource.cacheUserReward(updatedReward);

        return updatedReward;
      } on ServerException catch (e) {
        throw ServerFailure(message: e.message, statusCode: e.statusCode);
      }
    } else {
      throw const NetworkFailure(
        message: 'Cannot mark reward as viewed while offline',
      );
    }
  }

  // Analytics operations
  @override
  Future<Map<String, dynamic>> getGamificationStats(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteStats = await remoteDataSource.getGamificationStats(userId);

        // Cache the remote data
        await localDataSource.cacheGamificationStats(userId, remoteStats);

        return remoteStats;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedStats = await localDataSource.getCachedGamificationStats(
            userId,
          );
          return cachedStats ?? {};
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedStats = await localDataSource.getCachedGamificationStats(
          userId,
        );
        return cachedStats ?? {};
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getLeaderboard({int limit = 10}) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteLeaderboard = await remoteDataSource.getLeaderboard(
          limit: limit,
        );

        // Cache the remote data
        await localDataSource.cacheLeaderboard(remoteLeaderboard);

        return remoteLeaderboard;
      } on ServerException catch (e) {
        // Fallback to cached data if server fails
        try {
          final cachedLeaderboard =
              await localDataSource.getCachedLeaderboard();
          return cachedLeaderboard;
        } on CacheException {
          throw ServerFailure(message: e.message, statusCode: e.statusCode);
        }
      }
    } else {
      try {
        final cachedLeaderboard = await localDataSource.getCachedLeaderboard();
        return cachedLeaderboard;
      } on CacheException catch (e) {
        throw CacheFailure(message: e.message);
      }
    }
  }
}
