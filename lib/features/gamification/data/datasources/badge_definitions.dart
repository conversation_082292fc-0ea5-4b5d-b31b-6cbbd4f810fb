import '../../domain/entities/badge_entity.dart';
import '../models/badge_model.dart';

class BadgeDefinitions {
  static Map<String, BadgeModel> get badgeMap => {
    'first_habit': BadgeModel(
      id: 'first_habit',
      name: 'First Steps',
      description: 'Created your first habit',
      iconUrl: 'assets/badges/first_habit.png',
      type: BadgeType.achievement,
      rarity: BadgeRarity.common,
      xpReward: 50,
      criteria: const {'habitsCreated': 1},
      category: 'habits',
      isActive: true,
      createdAt: DateTime.now(),
    ),
    'habit_master': BadgeModel(
      id: 'habit_master',
      name: 'Habit Master',
      description: 'Completed 100 habits',
      iconUrl: 'assets/badges/habit_master.png',
      type: BadgeType.milestone,
      rarity: BadgeRarity.epic,
      xpReward: 500,
      criteria: {'habitsCompleted': 100},
      category: 'habits',
      isActive: true,
      createdAt: DateTime.now(),
    ),
    'streak_warrior': <PERSON>geModel(
      id: 'streak_warrior',
      name: 'Streak Warrior',
      description: 'Maintained a 30-day streak',
      iconUrl: 'assets/badges/streak_warrior.png',
      type: BadgeType.streak,
      rarity: BadgeRarity.rare,
      xpReward: 200,
      criteria: {'longestStreak': 30},
      category: 'streaks',
      isActive: true,
      createdAt: DateTime.now(),
    ),
    'consistency_champion': BadgeModel(
      id: 'consistency_champion',
      name: 'Consistency Champion',
      description: 'Completed habits for 7 consecutive days',
      iconUrl: 'assets/badges/consistency_champion.png',
      type: BadgeType.consistency,
      rarity: BadgeRarity.uncommon,
      xpReward: 100,
      criteria: {'consecutiveDays': 7},
      category: 'streaks',
      isActive: true,
      createdAt: DateTime.now(),
    ),
    'goal_achiever': BadgeModel(
      id: 'goal_achiever',
      name: 'Goal Achiever',
      description: 'Reached your first goal',
      iconUrl: 'assets/badges/goal_achiever.png',
      type: BadgeType.achievement,
      rarity: BadgeRarity.common,
      xpReward: 75,
      criteria: {'goalsReached': 1},
      category: 'goals',
      isActive: true,
      createdAt: DateTime.now(),
    ),
  };
}
