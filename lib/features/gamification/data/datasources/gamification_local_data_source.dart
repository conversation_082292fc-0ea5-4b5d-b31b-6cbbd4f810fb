import '../../../../core/data/local_datasource.dart';
import '../models/streak_model.dart';
import '../models/badge_model.dart';
import '../models/xp_model.dart';
import '../models/reward_model.dart';

/// Local data source interface for gamification operations
abstract class GamificationLocalDataSource extends LocalDataSource {
  // Streak operations
  Future<void> cacheStreak(StreakModel streak);
  Future<void> cacheUserStreaks(String userId, List<StreakModel> streaks);
  Future<StreakModel?> getCachedStreak(
    String userId,
    String relatedId,
    String relatedType,
  );
  Future<List<StreakModel>> getCachedUserStreaks(String userId);
  Future<void> removeCachedStreak(String streakId);

  // Badge operations
  Future<void> cacheAvailableBadges(List<BadgeModel> badges);
  Future<List<BadgeModel>> getCachedAvailableBadges();
  Future<void> cacheUserBadges(String userId, List<UserBadgeModel> badges);
  Future<List<UserBadgeModel>> getCachedUserBadges(String userId);
  Future<void> cacheUserBadge(UserBadgeModel userBadge);

  // XP operations
  Future<void> cacheUserXP(XPModel xp);
  Future<XPModel?> getCachedUserXP(String userId);
  Future<void> cacheXPTransactions(
    String userId,
    List<XPTransactionModel> transactions,
  );
  Future<List<XPTransactionModel>> getCachedXPTransactions(String userId);
  Future<void> cacheXPTransaction(XPTransactionModel transaction);

  // Reward operations
  Future<void> cacheAvailableRewards(List<RewardModel> rewards);
  Future<List<RewardModel>> getCachedAvailableRewards();
  Future<void> cacheUserRewards(String userId, List<UserRewardModel> rewards);
  Future<List<UserRewardModel>> getCachedUserRewards(String userId);
  Future<void> cacheUserReward(UserRewardModel userReward);

  // Analytics operations
  Future<void> cacheGamificationStats(
    String userId,
    Map<String, dynamic> stats,
  );
  Future<Map<String, dynamic>?> getCachedGamificationStats(String userId);
  Future<void> cacheLeaderboard(List<Map<String, dynamic>> leaderboard);
  Future<List<Map<String, dynamic>>> getCachedLeaderboard();

  // General cache operations
  Future<void> clearUserCache(String userId);
  Future<void> clearAllGamificationCache();
}
