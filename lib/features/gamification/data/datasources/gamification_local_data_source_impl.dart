import '../../../../core/error/exceptions.dart';
import '../../../core/data/local/storage_service.dart';
import '../models/streak_model.dart';
import '../models/badge_model.dart';
import '../models/xp_model.dart';
import '../models/reward_model.dart';
import 'gamification_local_data_source.dart';

/// Local data source implementation for gamification using StorageService
class GamificationLocalDataSourceImpl implements GamificationLocalDataSource {
  final StorageService? _providedStorageService;
  late final StorageService _storageService;
  bool _isInitialized = false;

  // Storage keys
  static const String _streaksKey = 'gamification_streaks';
  static const String _userStreaksKey = 'gamification_user_streaks';
  static const String _availableBadgesKey = 'gamification_available_badges';
  static const String _userBadgesKey = 'gamification_user_badges';
  static const String _userXPKey = 'gamification_user_xp';
  static const String _xpTransactionsKey = 'gamification_xp_transactions';
  static const String _availableRewardsKey = 'gamification_available_rewards';
  static const String _userRewardsKey = 'gamification_user_rewards';
  static const String _gamificationStatsKey = 'gamification_stats';
  static const String _leaderboardKey = 'gamification_leaderboard';

  GamificationLocalDataSourceImpl({StorageService? storageService})
    : _providedStorageService = storageService;

  @override
  Future<void> init() async {
    if (!_isInitialized) {
      if (_providedStorageService != null) {
        _storageService = _providedStorageService;
      } else {
        _storageService = await StorageService.getInstance();
      }
      _isInitialized = true;
    }
  }

  @override
  Future<void> dispose() async {
    // No resources to dispose for StorageService
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await init();
    }
  }

  @override
  Future<T?> get<T>({
    required String key,
    required T Function(dynamic json) fromJson,
  }) async {
    await _ensureInitialized();
    final data = _storageService.getData(key);
    return data != null ? fromJson(data) : null;
  }

  @override
  Future<void> set<T>({required String key, required T data}) async {
    await _ensureInitialized();
    await _storageService.setData(key, data);
  }

  @override
  Future<void> remove(String key) async {
    await _ensureInitialized();
    await _storageService.removeData(key);
  }

  @override
  Future<void> clear() async {
    try {
      await _ensureInitialized();
      final keysToRemove = [
        _streaksKey,
        _userStreaksKey,
        _availableBadgesKey,
        _userBadgesKey,
        _userXPKey,
        _xpTransactionsKey,
        _availableRewardsKey,
        _userRewardsKey,
        _gamificationStatsKey,
        _leaderboardKey,
      ];

      for (final key in keysToRemove) {
        await _storageService.removeData(key);
      }
    } catch (e) {
      throw CacheException(message: 'Failed to clear cache: $e');
    }
  }

  String _getUserStreaksKey(String userId) => '${_userStreaksKey}_$userId';
  String _getUserBadgesKey(String userId) => '${_userBadgesKey}_$userId';
  String _getUserXPKey(String userId) => '${_userXPKey}_$userId';
  String _getUserXPTransactionsKey(String userId) =>
      '${_xpTransactionsKey}_$userId';
  String _getUserRewardsKey(String userId) => '${_userRewardsKey}_$userId';
  String _getUserStatsKey(String userId) => '${_gamificationStatsKey}_$userId';

  // Streak operations
  @override
  Future<void> cacheStreak(StreakModel streak) async {
    try {
      await _ensureInitialized();
      final streaks = await _getAllCachedStreaks();

      // Update or add streak
      final existingIndex = streaks.indexWhere((s) => s.id == streak.id);
      if (existingIndex != -1) {
        streaks[existingIndex] = streak;
      } else {
        streaks.add(streak);
      }

      await _storageService.setData(
        _streaksKey,
        streaks.map((s) => s.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to cache streak: $e');
    }
  }

  @override
  Future<void> cacheUserStreaks(
    String userId,
    List<StreakModel> streaks,
  ) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(
        _getUserStreaksKey(userId),
        streaks.map((s) => s.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to cache user streaks: $e');
    }
  }

  @override
  Future<StreakModel?> getCachedStreak(
    String userId,
    String relatedId,
    String relatedType,
  ) async {
    try {
      await _ensureInitialized();
      final streaks = await _getAllCachedStreaks();

      for (final streak in streaks) {
        if (streak.userId == userId &&
            streak.relatedId == relatedId &&
            streak.relatedType == relatedType) {
          return streak;
        }
      }

      return null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached streak: $e');
    }
  }

  @override
  Future<List<StreakModel>> getCachedUserStreaks(String userId) async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<List<dynamic>>(
        _getUserStreaksKey(userId),
      );

      if (data == null) return [];

      return data
          .map((json) => StreakModel.fromJson(Map<String, dynamic>.from(json)))
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached user streaks: $e');
    }
  }

  @override
  Future<void> removeCachedStreak(String streakId) async {
    try {
      await _ensureInitialized();
      final streaks = await _getAllCachedStreaks();

      streaks.removeWhere((s) => s.id == streakId);

      await _storageService.setData(
        _streaksKey,
        streaks.map((s) => s.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to remove cached streak: $e');
    }
  }

  Future<List<StreakModel>> _getAllCachedStreaks() async {
    final data = _storageService.getData<List<dynamic>>(_streaksKey);
    if (data == null) return [];

    return data
        .map((json) => StreakModel.fromJson(Map<String, dynamic>.from(json)))
        .toList();
  }

  // Badge operations
  @override
  Future<void> cacheAvailableBadges(List<BadgeModel> badges) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(
        _availableBadgesKey,
        badges.map((b) => b.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to cache available badges: $e');
    }
  }

  @override
  Future<List<BadgeModel>> getCachedAvailableBadges() async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<List<dynamic>>(_availableBadgesKey);

      if (data == null) return [];

      return data
          .map((json) => BadgeModel.fromJson(Map<String, dynamic>.from(json)))
          .toList();
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached available badges: $e',
      );
    }
  }

  @override
  Future<void> cacheUserBadges(
    String userId,
    List<UserBadgeModel> badges,
  ) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(
        _getUserBadgesKey(userId),
        badges.map((b) => b.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to cache user badges: $e');
    }
  }

  @override
  Future<List<UserBadgeModel>> getCachedUserBadges(String userId) async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<List<dynamic>>(
        _getUserBadgesKey(userId),
      );

      if (data == null) return [];

      return data
          .map(
            (json) => UserBadgeModel.fromJson(Map<String, dynamic>.from(json)),
          )
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached user badges: $e');
    }
  }

  @override
  Future<void> cacheUserBadge(UserBadgeModel userBadge) async {
    try {
      await _ensureInitialized();
      final badges = await getCachedUserBadges(userBadge.userId);

      // Update or add badge
      final existingIndex = badges.indexWhere((b) => b.id == userBadge.id);
      if (existingIndex != -1) {
        badges[existingIndex] = userBadge;
      } else {
        badges.add(userBadge);
      }

      await cacheUserBadges(userBadge.userId, badges);
    } catch (e) {
      throw CacheException(message: 'Failed to cache user badge: $e');
    }
  }

  // XP operations
  @override
  Future<void> cacheUserXP(XPModel xp) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(_getUserXPKey(xp.userId), xp.toJson());
    } catch (e) {
      throw CacheException(message: 'Failed to cache user XP: $e');
    }
  }

  @override
  Future<XPModel?> getCachedUserXP(String userId) async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<Map<String, dynamic>>(
        _getUserXPKey(userId),
      );

      if (data == null) return null;

      return XPModel.fromJson(data);
    } catch (e) {
      throw CacheException(message: 'Failed to get cached user XP: $e');
    }
  }

  @override
  Future<void> cacheXPTransactions(
    String userId,
    List<XPTransactionModel> transactions,
  ) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(
        _getUserXPTransactionsKey(userId),
        transactions.map((t) => t.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to cache XP transactions: $e');
    }
  }

  @override
  Future<List<XPTransactionModel>> getCachedXPTransactions(
    String userId,
  ) async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<List<dynamic>>(
        _getUserXPTransactionsKey(userId),
      );

      if (data == null) return [];

      return data
          .map(
            (json) =>
                XPTransactionModel.fromJson(Map<String, dynamic>.from(json)),
          )
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached XP transactions: $e');
    }
  }

  @override
  Future<void> cacheXPTransaction(XPTransactionModel transaction) async {
    try {
      await _ensureInitialized();
      final transactions = await getCachedXPTransactions(transaction.userId);

      // Add new transaction at the beginning (most recent first)
      transactions.insert(0, transaction);

      // Limit to 100 most recent transactions
      if (transactions.length > 100) {
        transactions.removeRange(100, transactions.length);
      }

      await cacheXPTransactions(transaction.userId, transactions);
    } catch (e) {
      throw CacheException(message: 'Failed to cache XP transaction: $e');
    }
  }

  // Reward operations
  @override
  Future<void> cacheAvailableRewards(List<RewardModel> rewards) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(
        _availableRewardsKey,
        rewards.map((r) => r.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to cache available rewards: $e');
    }
  }

  @override
  Future<List<RewardModel>> getCachedAvailableRewards() async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<List<dynamic>>(_availableRewardsKey);

      if (data == null) return [];

      return data
          .map((json) => RewardModel.fromJson(Map<String, dynamic>.from(json)))
          .toList();
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached available rewards: $e',
      );
    }
  }

  @override
  Future<void> cacheUserRewards(
    String userId,
    List<UserRewardModel> rewards,
  ) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(
        _getUserRewardsKey(userId),
        rewards.map((r) => r.toJson()).toList(),
      );
    } catch (e) {
      throw CacheException(message: 'Failed to cache user rewards: $e');
    }
  }

  @override
  Future<List<UserRewardModel>> getCachedUserRewards(String userId) async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<List<dynamic>>(
        _getUserRewardsKey(userId),
      );

      if (data == null) return [];

      return data
          .map(
            (json) => UserRewardModel.fromJson(Map<String, dynamic>.from(json)),
          )
          .toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached user rewards: $e');
    }
  }

  @override
  Future<void> cacheUserReward(UserRewardModel userReward) async {
    try {
      await _ensureInitialized();
      final rewards = await getCachedUserRewards(userReward.userId);

      // Update or add reward
      final existingIndex = rewards.indexWhere((r) => r.id == userReward.id);
      if (existingIndex != -1) {
        rewards[existingIndex] = userReward;
      } else {
        rewards.add(userReward);
      }

      await cacheUserRewards(userReward.userId, rewards);
    } catch (e) {
      throw CacheException(message: 'Failed to cache user reward: $e');
    }
  }

  // Analytics operations
  @override
  Future<void> cacheGamificationStats(
    String userId,
    Map<String, dynamic> stats,
  ) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(_getUserStatsKey(userId), stats);
    } catch (e) {
      throw CacheException(message: 'Failed to cache gamification stats: $e');
    }
  }

  @override
  Future<Map<String, dynamic>?> getCachedGamificationStats(
    String userId,
  ) async {
    try {
      await _ensureInitialized();
      return _storageService.getData<Map<String, dynamic>>(
        _getUserStatsKey(userId),
      );
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached gamification stats: $e',
      );
    }
  }

  @override
  Future<void> cacheLeaderboard(List<Map<String, dynamic>> leaderboard) async {
    try {
      await _ensureInitialized();
      await _storageService.setData(_leaderboardKey, leaderboard);
    } catch (e) {
      throw CacheException(message: 'Failed to cache leaderboard: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getCachedLeaderboard() async {
    try {
      await _ensureInitialized();
      final data = _storageService.getData<List<dynamic>>(_leaderboardKey);

      if (data == null) return [];

      return data.map((item) => Map<String, dynamic>.from(item)).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached leaderboard: $e');
    }
  }

  // General cache operations
  @override
  Future<void> clearUserCache(String userId) async {
    try {
      await _ensureInitialized();
      final userKeysToRemove = [
        _getUserStreaksKey(userId),
        _getUserBadgesKey(userId),
        _getUserXPKey(userId),
        _getUserXPTransactionsKey(userId),
        _getUserRewardsKey(userId),
        _getUserStatsKey(userId),
      ];

      for (final key in userKeysToRemove) {
        await _storageService.removeData(key);
      }
    } catch (e) {
      throw CacheException(message: 'Failed to clear user cache: $e');
    }
  }

  @override
  Future<void> clearAllGamificationCache() async {
    await clear();
  }
}
