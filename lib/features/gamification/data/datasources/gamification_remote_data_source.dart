import '../../../../core/data/remote_datasource.dart';
import '../models/streak_model.dart';
import '../models/badge_model.dart';
import '../models/xp_model.dart';
import '../models/reward_model.dart';
import '../../domain/entities/xp_entity.dart';

/// Remote data source interface for gamification operations
abstract class GamificationRemoteDataSource extends RemoteDataSource {
  // Streak operations
  Future<StreakModel?> getStreak(
    String userId,
    String relatedId,
    String relatedType,
  );
  Future<List<StreakModel>> getUserStreaks(String userId);
  Future<StreakModel> updateStreak(StreakModel streak);
  Future<void> deleteStreak(String streakId);

  // Badge operations
  Future<List<UserBadgeModel>> getUserBadges(String userId);
  Future<UserBadgeModel> awardBadge(String userId, String badgeId);
  Future<bool> hasUserEarnedBadge(String userId, String badgeId);
  Future<List<BadgeModel>> getAvailableBadges();

  // XP operations
  Future<XPModel?> getUserXP(String userId);
  Future<XPModel> addXP(
    String userId,
    int amount,
    XPSource source, {
    Map<String, dynamic>? metadata,
  });
  Future<List<XPTransactionModel>> getXPTransactions(
    String userId, {
    int? limit,
  });
  Future<int> getUserLevel(String userId);

  // Reward operations
  Future<List<RewardModel>> getAvailableRewards();
  Future<List<UserRewardModel>> getUserRewards(String userId);
  Future<UserRewardModel> awardReward(
    String userId,
    String rewardId,
    Map<String, dynamic> earnedData,
  );
  Future<UserRewardModel> markRewardAsViewed(String userRewardId);

  // Analytics operations
  Future<Map<String, dynamic>> getGamificationStats(String userId);
  Future<List<Map<String, dynamic>>> getLeaderboard({int limit = 10});
}
