import '../../../../core/error/exceptions.dart';
import '../../../../features/core/data/api/api_client.dart';
import '../models/streak_model.dart';
import '../models/badge_model.dart';
import '../models/xp_model.dart';
import '../models/reward_model.dart';
import '../../domain/entities/xp_entity.dart';
import '../../domain/entities/badge_entity.dart';
import '../../domain/entities/reward_entity.dart';
import 'gamification_remote_data_source.dart';
import 'badge_definitions.dart';

import 'dart:math' as math;

/// HTTP API implementation of gamification remote data source
class GamificationRemoteDataSourceImpl implements GamificationRemoteDataSource {
  final ApiClient _apiClient;

  GamificationRemoteDataSourceImpl({required ApiClient apiClient})
    : _apiClient = apiClient;

  @override
  Future<void> init() async {
    // No initialization needed for HTTP client
  }

  @override
  Future<void> dispose() async {
    // No cleanup needed for HTTP client
  }

  @override
  Future<T> request<T>({
    required String endpoint,
    required T Function(Map<String, dynamic> json) fromJson,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    String method = 'GET',
    Map<String, String>? headers,
  }) async {
    try {
      late dynamic response;

      switch (method.toLowerCase()) {
        case 'get':
          response = await _apiClient.get<Map<String, dynamic>>(
            endpoint: endpoint,
            fromData: (data) => data as Map<String, dynamic>,
            queryParameters: queryParameters,
          );
          break;
        case 'post':
          response = await _apiClient.post<Map<String, dynamic>>(
            endpoint: endpoint,
            fromData: (data) => data as Map<String, dynamic>,
            data: body,
          );
          break;
        case 'put':
          response = await _apiClient.put<Map<String, dynamic>>(
            endpoint: endpoint,
            fromData: (data) => data as Map<String, dynamic>,
            data: body,
          );
          break;
        case 'delete':
          response = await _apiClient.delete<Map<String, dynamic>>(
            endpoint: endpoint,
            fromData: (data) => data as Map<String, dynamic>,
          );
          break;
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }

      return fromJson(response);
    } catch (e) {
      throw ServerException(message: 'API request failed: $e');
    }
  }

  /// Helper method to calculate user level from total XP
  int _calculateLevel(int totalXP) {
    // Level calculation: Level = sqrt(totalXP / 100)
    // This means: Level 1 = 100 XP, Level 2 = 400 XP, Level 3 = 900 XP, etc.
    return (math.sqrt(totalXP / 100)).floor() + 1;
  }

  /// Helper method to calculate XP needed for next level
  int _calculateXPToNextLevel(int totalXP) {
    final currentLevel = _calculateLevel(totalXP);
    final nextLevelRequiredXP = (currentLevel * currentLevel) * 100;
    return nextLevelRequiredXP - totalXP;
  }

  /// Helper method to get XP source description
  String _getXPSourceDescription(XPSource source) {
    switch (source) {
      case XPSource.habitCompletion:
        return 'Completed a habit';
      case XPSource.streakMilestone:
        return 'Reached a streak milestone';
      case XPSource.badgeEarned:
        return 'Earned a new badge';
      case XPSource.challengeCompleted:
        return 'Completed a challenge';
      default:
        return 'Other activity';
    }
  }

  // Streak operations
  @override
  Future<StreakModel?> getStreak(
    String userId,
    String relatedId,
    String relatedType,
  ) async {
    try {
      // Get specific habit to extract streak data
      final response = await _apiClient.get<Map<String, dynamic>>(
        endpoint: '/habits/$relatedId',
        fromData: (data) => data as Map<String, dynamic>,
      );

      return StreakModel(
        id: relatedId,
        userId: userId,
        relatedId: relatedId,
        relatedType: relatedType,
        currentStreak: response['currentStreak'] as int? ?? 0,
        longestStreak: response['longestStreak'] as int? ?? 0,
        lastUpdated:
            response['lastCompletedAt'] != null
                ? DateTime.parse(response['lastCompletedAt'] as String)
                : DateTime.now(),
        startDate: DateTime.now().subtract(
          Duration(days: response['currentStreak'] as int? ?? 0),
        ),
        isActive: (response['currentStreak'] as int? ?? 0) > 0,
      );
    } catch (e) {
      throw ServerException(message: 'Failed to get streak: $e');
    }
  }

  @override
  Future<List<StreakModel>> getUserStreaks(String userId) async {
    try {
      // Get all user habits and convert to streaks
      final response = await _apiClient.get<List<dynamic>>(
        endpoint: '/habits',
        fromData: (data) => data as List<dynamic>,
      );

      return response
          .map((habit) {
            if (habit is! Map<String, dynamic>) return null;
            final habitMap = habit;
            final id = habitMap['id'];
            final currentStreak = habitMap['currentStreak'];
            final longestStreak = habitMap['longestStreak'];
            final lastCompletedAt = habitMap['lastCompletedAt'];
            String? idStr = id is String ? id : null;
            int currentStreakInt = currentStreak is int ? currentStreak : 0;
            int longestStreakInt = longestStreak is int ? longestStreak : 0;
            DateTime lastUpdatedDate;
            if (lastCompletedAt is String) {
              try {
                lastUpdatedDate = DateTime.parse(lastCompletedAt);
              } catch (_) {
                lastUpdatedDate = DateTime.now();
              }
            } else {
              lastUpdatedDate = DateTime.now();
            }
            if (idStr == null) return null;
            return StreakModel(
              id: idStr,
              userId: userId,
              relatedId: idStr,
              relatedType: 'habit',
              currentStreak: currentStreakInt,
              longestStreak: longestStreakInt,
              lastUpdated: lastUpdatedDate,
              startDate: DateTime.now().subtract(
                Duration(days: currentStreakInt),
              ),
              isActive: currentStreakInt > 0,
            );
          })
          .whereType<StreakModel>()
          .toList();
    } catch (e) {
      throw ServerException(message: 'Failed to get user streaks: $e');
    }
  }

  @override
  Future<StreakModel> updateStreak(StreakModel streak) async {
    try {
      // Update habit completion which affects streak
      final response = await _apiClient.put<Map<String, dynamic>>(
        endpoint: '/habits/${streak.relatedId}',
        fromData: (data) => data as Map<String, dynamic>,
        data: {
          'lastCompletedAt': streak.lastUpdated.toIso8601String(),
          'currentStreak': streak.currentStreak,
          'longestStreak': streak.longestStreak,
        },
      );

      return StreakModel(
        id: streak.id,
        userId: streak.userId,
        relatedId: streak.relatedId,
        relatedType: streak.relatedType,
        currentStreak:
            response['currentStreak'] as int? ?? streak.currentStreak,
        longestStreak:
            response['longestStreak'] as int? ?? streak.longestStreak,
        lastUpdated:
            response['lastCompletedAt'] != null
                ? DateTime.parse(response['lastCompletedAt'] as String)
                : streak.lastUpdated,
        startDate: streak.startDate,
        isActive:
            (response['currentStreak'] as int? ?? streak.currentStreak) > 0,
      );
    } catch (e) {
      throw ServerException(message: 'Failed to update streak: $e');
    }
  }

  @override
  Future<void> deleteStreak(String streakId) async {
    try {
      // Streaks are linked to habits, so we don't delete them directly
      // This would typically reset the habit's streak data
      await _apiClient.put<Map<String, dynamic>>(
        endpoint: '/habits/$streakId',
        fromData: (data) => data as Map<String, dynamic>,
        data: {'currentStreak': 0, 'lastCompletedAt': null},
      );
    } catch (e) {
      throw ServerException(message: 'Failed to delete streak: $e');
    }
  }

  // Badge operations
  @override
  Future<List<UserBadgeModel>> getUserBadges(String userId) async {
    try {
      // Get user profile which contains badge information
      final response = await _apiClient.get<Map<String, dynamic>>(
        endpoint: '/users/profile',
        fromData: (data) => data as Map<String, dynamic>,
      );

      final profile = response['user'] as Map<String, dynamic>? ?? response;
      final badgesList = profile['badges'] as List<dynamic>? ?? [];

      return badgesList.map((badgeId) {
        final badge = _getBadgeDefinition(badgeId.toString());
        return UserBadgeModel(
          id: '$userId-$badgeId',
          userId: userId,
          badgeId: badgeId.toString(),
          badge: badge,
          awardedAt: DateTime.now(), // API doesn't provide earned date
        );
      }).toList();
    } catch (e) {
      throw ServerException(message: 'Failed to get user badges: $e');
    }
  }

  @override
  Future<UserBadgeModel> awardBadge(String userId, String badgeId) async {
    try {
      // This would be handled by the backend automatically
      // For now, return a mock user badge
      final badge = _getBadgeDefinition(badgeId);
      return UserBadgeModel(
        id: '$userId-$badgeId',
        userId: userId,
        badgeId: badgeId,
        badge: badge,
        awardedAt: DateTime.now(),
      );
    } catch (e) {
      throw ServerException(message: 'Failed to award badge: $e');
    }
  }

  @override
  Future<bool> hasUserEarnedBadge(String userId, String badgeId) async {
    try {
      final userBadges = await getUserBadges(userId);
      return userBadges.any((badge) => badge.badgeId == badgeId);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<BadgeModel>> getAvailableBadges() async {
    // Return hardcoded badge definitions since API doesn't provide them
    return [
      _getBadgeDefinition('first_habit'),
      _getBadgeDefinition('habit_master'),
      _getBadgeDefinition('streak_warrior'),
      _getBadgeDefinition('consistency_champion'),
      _getBadgeDefinition('goal_achiever'),
    ];
  }

  /// Helper method to get badge definition by ID
  BadgeModel _getBadgeDefinition(String badgeId) {
    return BadgeDefinitions.badgeMap[badgeId] ??
        BadgeModel(
          id: badgeId,
          name: 'Unknown Badge',
          description: 'Badge description not available',
          iconUrl: 'assets/badges/default.png',
          type: BadgeType.achievement,
          rarity: BadgeRarity.common,
          xpReward: 10,
          criteria: const {},
          category: 'other',
          isActive: true,
          createdAt: DateTime.now(),
        );
  }

  // XP operations
  @override
  Future<XPModel?> getUserXP(String userId) async {
    try {
      // Get user XP from profile endpoint
      final response = await _apiClient.get<Map<String, dynamic>>(
        endpoint: '/users/profile',
        fromData: (data) => data as Map<String, dynamic>,
      );

      final profile = response['user'] as Map<String, dynamic>? ?? response;

      // Extract XP data from profile
      final totalXP = profile['xp'] as int? ?? 0;
      final xpData = {
        'id': '${userId}_xp',
        'userId': userId,
        'totalXP': totalXP,
        'currentLevel': _calculateLevel(totalXP),
        'xpToNextLevel': _calculateXPToNextLevel(totalXP),
        'lastUpdated': DateTime.now().toIso8601String(),
        'recentTransactions': <Map<String, dynamic>>[],
      };

      return XPModel.fromJson(xpData);
    } catch (e) {
      throw ServerException(message: 'Failed to get user XP: $e');
    }
  }

  @override
  Future<XPModel> addXP(
    String userId,
    int amount,
    XPSource source, {
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Get current XP first
      final currentXP = await getUserXP(userId);
      final newTotalXP = (currentXP?.totalXP ?? 0) + amount;

      // Create updated XP model
      final xpData = {
        'id': '${userId}_xp',
        'userId': userId,
        'totalXP': newTotalXP,
        'currentLevel': _calculateLevel(newTotalXP),
        'xpToNextLevel': _calculateXPToNextLevel(newTotalXP),
        'lastUpdated': DateTime.now().toIso8601String(),
        'recentTransactions': [
          {
            'id': '${DateTime.now().millisecondsSinceEpoch}',
            'userId': userId,
            'amount': amount,
            'source': source.name,
            'description': _getXPSourceDescription(source),
            'timestamp': DateTime.now().toIso8601String(),
          },
        ],
      };

      return XPModel.fromJson(xpData);
    } catch (e) {
      throw ServerException(message: 'Failed to add XP: $e');
    }
  }

  @override
  Future<List<XPTransactionModel>> getXPTransactions(
    String userId, {
    int? limit,
  }) async {
    try {
      // Get habits to derive XP transactions from habit completions
      final response = await _apiClient.get<List<dynamic>>(
        endpoint: '/habits',
        fromData: (data) => data as List<dynamic>,
      );

      final transactions = <XPTransactionModel>[];
      for (final habit in response) {
        final habitMap = habit as Map<String, dynamic>;
        final xpReward = habitMap['xpReward'] as int? ?? 10;

        // Create mock transaction for each habit completion
        if (xpReward > 0) {
          transactions.add(
            XPTransactionModel(
              id: '${habitMap['id']}_xp',
              userId: userId,
              amount: xpReward,
              source: XPSource.habitCompletion,
              description: 'Completed habit: ${habitMap['name']}',
              timestamp:
                  habitMap['lastCompletedAt'] != null
                      ? DateTime.parse(habitMap['lastCompletedAt'] as String)
                      : DateTime.now(),
            ),
          );
        }
      }

      // Sort by timestamp descending and limit if requested
      transactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      if (limit != null && limit > 0) {
        return transactions.take(limit).toList();
      }
      return transactions;
    } catch (e) {
      throw ServerException(message: 'Failed to get XP transactions: $e');
    }
  }

  @override
  Future<int> getUserLevel(String userId) async {
    final xp = await getUserXP(userId);
    return xp?.currentLevel ?? 1;
  }

  // Reward operations
  @override
  Future<List<RewardModel>> getAvailableRewards() async {
    // Mock implementation - in real app this would come from API
    return [
      RewardModel(
        id: 'custom_theme',
        name: 'Custom Theme',
        description: 'Unlock a custom app theme',
        type: RewardType.customization,
        trigger: RewardTrigger.levelUp,
        criteria: const {'xpRequired': 1000},
        rewardData: const {'theme': 'custom'},
        createdAt: DateTime.now(),
        isActive: true,
      ),
      RewardModel(
        id: 'premium_feature',
        name: 'Premium Features',
        description: 'Access premium app features for 30 days',
        type: RewardType.unlock,
        trigger: RewardTrigger.streakMilestone,
        criteria: const {'streakDays': 30},
        rewardData: const {'featureAccess': 'premium', 'duration': 30},
        createdAt: DateTime.now(),
        isActive: true,
      ),
    ];
  }

  @override
  Future<List<UserRewardModel>> getUserRewards(String userId) async {
    // Mock implementation - return empty list for now
    return [];
  }

  @override
  Future<UserRewardModel> awardReward(
    String userId,
    String rewardId,
    Map<String, dynamic> earnedData,
  ) async {
    // Mock implementation
    return UserRewardModel(
      id: '${userId}_$rewardId',
      userId: userId,
      rewardId: rewardId,
      earnedAt: DateTime.now(),
      earnedData: earnedData,
    );
  }

  @override
  Future<UserRewardModel> markRewardAsViewed(String userRewardId) async {
    // Mock implementation - would update viewed status in real app
    throw UnimplementedError('Mark reward as viewed not implemented');
  }

  // Analytics operations
  @override
  Future<Map<String, dynamic>> getGamificationStats(String userId) async {
    try {
      final xp = await getUserXP(userId);
      final badges = await getUserBadges(userId);
      final streaks = await getUserStreaks(userId);

      return {
        'totalXP': xp?.totalXP ?? 0,
        'currentLevel': xp?.currentLevel ?? 1,
        'badgesEarned': badges.length,
        'activeStreaks': streaks.where((s) => s.isActive).length,
        'longestStreak':
            streaks.isNotEmpty
                ? streaks.map((s) => s.longestStreak).reduce(math.max)
                : 0,
      };
    } catch (e) {
      throw ServerException(message: 'Failed to get gamification stats: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getLeaderboard({int limit = 10}) async {
    // Mock implementation - would get from API in real app
    return [];
  }
}
