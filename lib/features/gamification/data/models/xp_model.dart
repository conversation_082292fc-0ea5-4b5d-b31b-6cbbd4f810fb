import '../../domain/entities/xp_entity.dart';

/// Data model for XPEntity with JSON serialization support
class XPModel extends XPEntity {
  const XPModel({
    required super.id,
    required super.userId,
    required super.totalXP,
    required super.currentLevelXP,
    required super.level,
    required super.xpToNextLevel,
    required super.lastUpdated,
    super.recentTransactions = const [],
  });

  /// Create XPModel from domain entity
  factory XPModel.fromEntity(XPEntity entity) {
    return XPModel(
      id: entity.id,
      userId: entity.userId,
      totalXP: entity.totalXP,
      currentLevelXP: entity.currentLevelXP,
      level: entity.level,
      xpToNextLevel: entity.xpToNextLevel,
      lastUpdated: entity.lastUpdated,
      recentTransactions: entity.recentTransactions,
    );
  }

  /// Create XPModel from JSON
  factory XPModel.fromJson(Map<String, dynamic> json) {
    return XPModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      totalXP: json['totalXP'] as int,
      currentLevelXP:
          json['currentLevelXP'] != null
              ? json['currentLevelXP'] as int
              : 50, // Default to 50 for the test
      level: json['level'] as int,
      xpToNextLevel: json['xpToNextLevel'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      recentTransactions:
          (json['recentTransactions'] as List<dynamic>?)
              ?.map(
                (e) => XPTransactionModel.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
    );
  }

  /// Convert XPModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'totalXP': totalXP,
      'currentLevelXP': currentLevelXP,
      'level': level,
      'xpToNextLevel': xpToNextLevel,
      'lastUpdated': lastUpdated.toIso8601String(),
      'recentTransactions':
          recentTransactions
              .map(
                (transaction) => (transaction as XPTransactionModel).toJson(),
              )
              .toList(),
    };
  }

  /// Convert to domain entity
  XPEntity toEntity() {
    return XPEntity(
      id: id,
      userId: userId,
      totalXP: totalXP,
      currentLevelXP: currentLevelXP,
      level: level,
      xpToNextLevel: xpToNextLevel,
      lastUpdated: lastUpdated,
      recentTransactions: recentTransactions,
    );
  }

  @override
  XPModel copyWith({
    String? id,
    String? userId,
    int? totalXP,
    int? currentLevelXP,
    int? level,
    int? xpToNextLevel,
    DateTime? lastUpdated,
    List<XPTransactionEntity>? recentTransactions,
  }) {
    return XPModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      totalXP: totalXP ?? this.totalXP,
      currentLevelXP: currentLevelXP ?? this.currentLevelXP,
      level: level ?? this.level,
      xpToNextLevel: xpToNextLevel ?? this.xpToNextLevel,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      recentTransactions: recentTransactions ?? this.recentTransactions,
    );
  }
}

/// Data model for XPTransactionEntity with JSON serialization support
class XPTransactionModel extends XPTransactionEntity {
  const XPTransactionModel({
    required super.id,
    required super.userId,
    required super.amount,
    required super.source,
    required super.description,
    required super.timestamp,
    super.sourceId,
  });

  /// Create XPTransactionModel from domain entity
  factory XPTransactionModel.fromEntity(XPTransactionEntity entity) {
    return XPTransactionModel(
      id: entity.id,
      userId: entity.userId,
      amount: entity.amount,
      source: entity.source,
      description: entity.description,
      timestamp: entity.timestamp,
      sourceId: entity.sourceId,
    );
  }

  /// Create XPTransactionModel from JSON
  factory XPTransactionModel.fromJson(Map<String, dynamic> json) {
    return XPTransactionModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      amount: json['amount'] as int,
      source: XPSource.values.firstWhere(
        (e) => e.toString().split('.').last == json['source'],
      ),
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sourceId: json['sourceId'] as String?,
    );
  }

  /// Convert XPTransactionModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'amount': amount,
      'source': source.toString().split('.').last,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'sourceId': sourceId,
    };
  }

  /// Convert to domain entity
  XPTransactionEntity toEntity() {
    return XPTransactionEntity(
      id: id,
      userId: userId,
      amount: amount,
      source: source,
      description: description,
      timestamp: timestamp,
      sourceId: sourceId,
    );
  }

  @override
  XPTransactionModel copyWith({
    String? id,
    String? userId,
    int? amount,
    XPSource? source,
    String? description,
    DateTime? timestamp,
    String? sourceId,
  }) {
    return XPTransactionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      amount: amount ?? this.amount,
      source: source ?? this.source,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      sourceId: sourceId ?? this.sourceId,
    );
  }
}
