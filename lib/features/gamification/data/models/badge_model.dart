import '../../domain/entities/badge_entity.dart';

/// Data model for BadgeEntity with JSON serialization support
class BadgeModel extends BadgeEntity {
  const BadgeModel({
    required super.id,
    required super.name,
    required super.description,
    required super.iconUrl,
    required super.type,
    required super.rarity,
    required super.xpReward,
    required super.criteria,
    super.isActive = true,
    required super.createdAt,
    required super.category,
    super.earnedAt,
  });

  /// Create BadgeModel from domain entity
  factory BadgeModel.fromEntity(BadgeEntity entity) {
    return BadgeModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      iconUrl: entity.iconUrl,
      type: entity.type,
      rarity: entity.rarity,
      xpReward: entity.xpReward,
      criteria: entity.criteria,
      isActive: entity.isActive,
      createdAt: entity.createdAt,
      category: entity.category,
      earnedAt: entity.earnedAt,
    );
  }

  /// Create BadgeModel from JSON
  factory BadgeModel.fromJson(Map<String, dynamic> json) {
    return BadgeModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      iconUrl: json['iconUrl'] as String,
      type: BadgeType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => BadgeType.achievement,
      ),
      rarity: BadgeRarity.values.firstWhere(
        (e) => e.name == json['rarity'],
        orElse: () => BadgeRarity.common,
      ),
      xpReward: json['xpReward'] as int,
      criteria: Map<String, dynamic>.from(json['criteria'] as Map),
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      category: json['category'] as String,
      earnedAt:
          json['earnedAt'] != null
              ? DateTime.parse(json['earnedAt'] as String)
              : null,
    );
  }

  /// Convert BadgeModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'type': type.name,
      'rarity': rarity.name,
      'xpReward': xpReward,
      'criteria': criteria,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'category': category,
      'earnedAt': earnedAt?.toIso8601String(),
    };
  }

  /// Convert to domain entity
  BadgeEntity toEntity() {
    return BadgeEntity(
      id: id,
      name: name,
      description: description,
      iconUrl: iconUrl,
      type: type,
      rarity: rarity,
      xpReward: xpReward,
      criteria: criteria,
      isActive: isActive,
      createdAt: createdAt,
      category: category,
      earnedAt: earnedAt,
    );
  }
}

/// Data model for UserBadgeEntity with JSON serialization support
class UserBadgeModel extends UserBadgeEntity {
  const UserBadgeModel({
    required super.id,
    required super.userId,
    required super.badgeId,
    required super.badge,
    required super.awardedAt,
    super.triggerAction,
    super.metadata,
  });

  /// Create UserBadgeModel from domain entity
  factory UserBadgeModel.fromEntity(UserBadgeEntity entity) {
    return UserBadgeModel(
      id: entity.id,
      userId: entity.userId,
      badgeId: entity.badgeId,
      badge: entity.badge,
      awardedAt: entity.awardedAt,
      triggerAction: entity.triggerAction,
      metadata: entity.metadata,
    );
  }

  /// Create UserBadgeModel from JSON
  factory UserBadgeModel.fromJson(Map<String, dynamic> json) {
    return UserBadgeModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      badgeId: json['badgeId'] as String,
      badge: BadgeModel.fromJson(json['badge'] as Map<String, dynamic>),
      awardedAt: DateTime.parse(json['awardedAt'] as String),
      triggerAction: json['triggerAction'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert UserBadgeModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'badgeId': badgeId,
      'badge': (badge as BadgeModel).toJson(),
      'awardedAt': awardedAt.toIso8601String(),
    };
  }

  /// Convert to domain entity
  UserBadgeEntity toEntity() {
    return UserBadgeEntity(
      id: id,
      userId: userId,
      badgeId: badgeId,
      badge: badge,
      awardedAt: awardedAt,
      triggerAction: triggerAction,
      metadata: metadata,
    );
  }
}
