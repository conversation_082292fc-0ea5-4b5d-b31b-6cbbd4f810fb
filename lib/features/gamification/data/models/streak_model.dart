import '../../domain/entities/streak_entity.dart';

/// Data model for StreakEntity with JSON serialization support
class StreakModel extends StreakEntity {
  const StreakModel({
    required super.id,
    required super.userId,
    required super.relatedId,
    required super.relatedType,
    required super.currentStreak,
    required super.longestStreak,
    required super.lastUpdated,
    required super.startDate,
    super.isActive,
  });

  /// Create StreakModel from domain entity
  factory StreakModel.fromEntity(StreakEntity entity) {
    return StreakModel(
      id: entity.id,
      userId: entity.userId,
      relatedId: entity.relatedId,
      relatedType: entity.relatedType,
      currentStreak: entity.currentStreak,
      longestStreak: entity.longestStreak,
      lastUpdated: entity.lastUpdated,
      startDate: entity.startDate,
      isActive: entity.isActive,
    );
  }

  /// Create StreakModel from JSON
  factory StreakModel.fromJson(Map<String, dynamic> json) {
    return StreakModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      relatedId: json['relatedId'] as String,
      relatedType: json['relatedType'] as String,
      currentStreak: json['currentStreak'] as int,
      longestStreak: json['longestStreak'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      startDate: DateTime.parse(json['startDate'] as String),
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  /// Convert StreakModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'relatedId': relatedId,
      'relatedType': relatedType,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastUpdated': lastUpdated.toIso8601String(),
      'startDate': startDate.toIso8601String(),
      'isActive': isActive,
    };
  }

  /// Convert to domain entity
  StreakEntity toEntity() {
    return StreakEntity(
      id: id,
      userId: userId,
      relatedId: relatedId,
      relatedType: relatedType,
      currentStreak: currentStreak,
      longestStreak: longestStreak,
      lastUpdated: lastUpdated,
      startDate: startDate,
      isActive: isActive,
    );
  }

  @override
  StreakModel copyWith({
    String? id,
    String? userId,
    String? relatedId,
    String? relatedType,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastUpdated,
    DateTime? startDate,
    bool? isActive,
  }) {
    return StreakModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      relatedId: relatedId ?? this.relatedId,
      relatedType: relatedType ?? this.relatedType,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      startDate: startDate ?? this.startDate,
      isActive: isActive ?? this.isActive,
    );
  }
}
