import '../../domain/entities/reward_entity.dart';

/// Data model for RewardEntity with JSON serialization support
class RewardModel extends RewardEntity {
  const RewardModel({
    required super.id,
    required super.name,
    required super.description,
    required super.type,
    required super.trigger,
    required super.criteria,
    required super.rewardData,
    required super.createdAt,
    super.isActive = true,
    super.expiresAt,
  });

  /// Create RewardModel from domain entity
  factory RewardModel.fromEntity(RewardEntity entity) {
    return RewardModel(
      id: entity.id,
      name: entity.name,
      description: entity.description,
      type: entity.type,
      trigger: entity.trigger,
      criteria: entity.criteria,
      rewardData: entity.rewardData,
      createdAt: entity.createdAt,
      isActive: entity.isActive,
      expiresAt: entity.expiresAt,
    );
  }

  /// Create RewardModel from JSON
  factory RewardModel.fromJson(Map<String, dynamic> json) {
    return RewardModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: RewardType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RewardType.xp,
      ),
      trigger: RewardTrigger.values.firstWhere(
        (e) => e.name == json['trigger'],
        orElse: () => RewardTrigger.taskCompletion,
      ),
      criteria: Map<String, dynamic>.from(json['criteria'] as Map),
      rewardData: Map<String, dynamic>.from(json['rewardData'] as Map),
      createdAt: DateTime.parse(json['createdAt'] as String),
      isActive: json['isActive'] as bool? ?? true,
      expiresAt:
          json['expiresAt'] != null
              ? DateTime.parse(json['expiresAt'] as String)
              : null,
    );
  }

  /// Convert RewardModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'trigger': trigger.name,
      'criteria': criteria,
      'rewardData': rewardData,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      if (expiresAt != null) 'expiresAt': expiresAt!.toIso8601String(),
    };
  }

  /// Convert to domain entity
  RewardEntity toEntity() {
    return RewardEntity(
      id: id,
      name: name,
      description: description,
      type: type,
      trigger: trigger,
      criteria: criteria,
      rewardData: rewardData,
      createdAt: createdAt,
      isActive: isActive,
      expiresAt: expiresAt,
    );
  }
}

/// Data model for UserRewardEntity with JSON serialization support
class UserRewardModel extends UserRewardEntity {
  const UserRewardModel({
    required super.id,
    required super.userId,
    required super.rewardId,
    required super.earnedAt,
    required super.earnedData,
    super.isViewed = false,
  });

  /// Create UserRewardModel from domain entity
  factory UserRewardModel.fromEntity(UserRewardEntity entity) {
    return UserRewardModel(
      id: entity.id,
      userId: entity.userId,
      rewardId: entity.rewardId,
      earnedAt: entity.earnedAt,
      earnedData: entity.earnedData,
      isViewed: entity.isViewed,
    );
  }

  /// Create UserRewardModel from JSON
  factory UserRewardModel.fromJson(Map<String, dynamic> json) {
    return UserRewardModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      rewardId: json['rewardId'] as String,
      earnedAt: DateTime.parse(json['earnedAt'] as String),
      earnedData: Map<String, dynamic>.from(json['earnedData'] as Map),
      isViewed: json['isViewed'] as bool? ?? false,
    );
  }

  /// Convert UserRewardModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'rewardId': rewardId,
      'earnedAt': earnedAt.toIso8601String(),
      'earnedData': earnedData,
      'isViewed': isViewed,
    };
  }

  /// Convert to domain entity
  UserRewardEntity toEntity() {
    return UserRewardEntity(
      id: id,
      userId: userId,
      rewardId: rewardId,
      earnedAt: earnedAt,
      earnedData: earnedData,
      isViewed: isViewed,
    );
  }

  @override
  UserRewardModel copyWith({
    String? id,
    String? userId,
    String? rewardId,
    DateTime? earnedAt,
    Map<String, dynamic>? earnedData,
    bool? isViewed,
  }) {
    return UserRewardModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      rewardId: rewardId ?? this.rewardId,
      earnedAt: earnedAt ?? this.earnedAt,
      earnedData: earnedData ?? this.earnedData,
      isViewed: isViewed ?? this.isViewed,
    );
  }
}
