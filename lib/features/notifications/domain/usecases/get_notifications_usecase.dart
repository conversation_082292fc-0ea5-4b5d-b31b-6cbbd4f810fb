import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notification_entity.dart';
import '../entities/notification_response_entity.dart';
import '../repositories/notification_repository.dart';

/// Use case for retrieving user notifications with pagination
class GetNotificationsUseCase {
  final NotificationRepository repository;

  GetNotificationsUseCase(this.repository);

  Future<Either<Failure, NotificationResponseEntity>> call({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    NotificationStatus? status,
    String? search,
    String? sortBy,
    String? sortOrder,
  }) async {
    return await repository.getNotifications(
      page: page,
      limit: limit,
      type: type,
      status: status,
      search: search,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
  }
}
