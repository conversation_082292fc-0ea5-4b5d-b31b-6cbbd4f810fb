import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notification_preferences_entity.dart';
import '../repositories/notification_repository.dart';

/// Use case for retrieving user notification preferences
class GetNotificationPreferencesUseCase {
  final NotificationRepository repository;

  GetNotificationPreferencesUseCase(this.repository);

  Future<Either<Failure, NotificationPreferencesEntity>> call() async {
    return await repository.getNotificationPreferences();
  }
}
