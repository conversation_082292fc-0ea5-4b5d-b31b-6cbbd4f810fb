import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notification_entity.dart';
import '../repositories/notification_repository.dart';

/// Use case for sending push notifications via FCM
class SendPushNotificationUseCase {
  final NotificationRepository repository;

  SendPushNotificationUseCase(this.repository);

  Future<Either<Failure, bool>> call(NotificationEntity notification) async {
    return await repository.sendPushNotification(notification);
  }
}
