import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notification_preferences_entity.dart';
import '../repositories/notification_repository.dart';

/// Use case for updating user notification preferences
class UpdateNotificationPreferencesUseCase {
  final NotificationRepository repository;

  UpdateNotificationPreferencesUseCase(this.repository);

  Future<Either<Failure, NotificationPreferencesEntity>> call(
    NotificationPreferencesEntity preferences,
  ) async {
    return await repository.updateNotificationPreferences(preferences);
  }
}
