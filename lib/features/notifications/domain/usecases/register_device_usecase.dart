import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/notification_repository.dart';
import '../entities/device_entity.dart';

/// Use case for registering device for push notifications
class RegisterDeviceUseCase {
  final NotificationRepository repository;

  RegisterDeviceUseCase(this.repository);

  Future<Either<Failure, void>> call(RegisterDeviceParams params) async {
    return await repository.registerDevice(
      deviceToken: params.deviceToken,
      deviceType: params.deviceType,
      deviceName: params.deviceName,
    );
  }
}

class RegisterDeviceParams {
  final String deviceToken;
  final DeviceType deviceType;
  final String? deviceName;

  RegisterDeviceParams({
    required this.deviceToken,
    required this.deviceType,
    this.deviceName,
  });
}
