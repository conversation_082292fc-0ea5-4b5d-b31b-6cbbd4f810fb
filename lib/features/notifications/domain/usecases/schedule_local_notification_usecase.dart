import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notification_entity.dart';
import '../repositories/notification_repository.dart';

/// Use case for scheduling local notifications
class ScheduleLocalNotificationUseCase {
  final NotificationRepository repository;

  ScheduleLocalNotificationUseCase(this.repository);

  Future<Either<Failure, void>> call(NotificationEntity notification) async {
    return await repository.scheduleNotificationFromEntity(notification);
  }
}
