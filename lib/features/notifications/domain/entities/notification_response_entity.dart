import 'package:equatable/equatable.dart';
import 'notification_entity.dart';

/// Entity representing a paginated notification response
class NotificationResponseEntity extends Equatable {
  final List<NotificationEntity> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  const NotificationResponseEntity({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  @override
  List<Object?> get props => [
    data,
    total,
    page,
    limit,
    totalPages,
    hasNext,
    hasPrev,
  ];
}
