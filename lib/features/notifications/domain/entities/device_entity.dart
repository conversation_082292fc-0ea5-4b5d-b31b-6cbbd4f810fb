import 'package:equatable/equatable.dart';

/// Entity representing a registered device for push notifications
class DeviceEntity extends Equatable {
  final String id;
  final String deviceToken;
  final DeviceType deviceType;
  final String? deviceName;
  final String userId;
  final DateTime registeredAt;
  final DateTime lastUsed;
  final bool isActive;

  const DeviceEntity({
    required this.id,
    required this.deviceToken,
    required this.deviceType,
    this.deviceName,
    required this.userId,
    required this.registeredAt,
    required this.lastUsed,
    this.isActive = true,
  });

  DeviceEntity copyWith({
    String? id,
    String? deviceToken,
    DeviceType? deviceType,
    String? deviceName,
    String? userId,
    DateTime? registeredAt,
    DateTime? lastUsed,
    bool? isActive,
  }) {
    return DeviceEntity(
      id: id ?? this.id,
      deviceToken: deviceToken ?? this.deviceToken,
      deviceType: deviceType ?? this.deviceType,
      deviceName: deviceName ?? this.deviceName,
      userId: userId ?? this.userId,
      registeredAt: registeredAt ?? this.registeredAt,
      lastUsed: lastUsed ?? this.lastUsed,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object?> get props => [
    id,
    deviceToken,
    deviceType,
    deviceName,
    userId,
    registeredAt,
    lastUsed,
    isActive,
  ];
}

/// Device types supported for push notifications
enum DeviceType { android, ios, web }
