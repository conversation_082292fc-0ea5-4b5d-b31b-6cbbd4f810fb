import 'package:equatable/equatable.dart';

/// Entity representing notification preferences for a user
class NotificationPreferencesEntity extends Equatable {
  final String userId;
  final bool taskReminders;
  final bool habitReminders;
  final bool streakAlerts;
  final bool milestoneCelebrations;
  final bool challengeUpdates;
  final bool communityUpdates;
  final bool podcastNotifications;
  final bool systemUpdates;
  final bool inAppMessaging;
  final DateTime? quietHoursStart;
  final DateTime? quietHoursEnd;
  final DateTime updatedAt;

  const NotificationPreferencesEntity({
    required this.userId,
    this.taskReminders = true,
    this.habitReminders = true,
    this.streakAlerts = true,
    this.milestoneCelebrations = true,
    this.challengeUpdates = true,
    this.communityUpdates = false,
    this.podcastNotifications = true,
    this.systemUpdates = true,
    this.inAppMessaging = true,
    this.quietHoursStart,
    this.quietHoursEnd,
    required this.updatedAt,
  });

  NotificationPreferencesEntity copyWith({
    String? userId,
    bool? taskReminders,
    bool? habitReminders,
    bool? streakAlerts,
    bool? milestoneCelebrations,
    bool? challengeUpdates,
    bool? communityUpdates,
    bool? podcastNotifications,
    bool? systemUpdates,
    bool? inAppMessaging,
    DateTime? quietHoursStart,
    DateTime? quietHoursEnd,
    DateTime? updatedAt,
  }) {
    return NotificationPreferencesEntity(
      userId: userId ?? this.userId,
      taskReminders: taskReminders ?? this.taskReminders,
      habitReminders: habitReminders ?? this.habitReminders,
      streakAlerts: streakAlerts ?? this.streakAlerts,
      milestoneCelebrations:
          milestoneCelebrations ?? this.milestoneCelebrations,
      challengeUpdates: challengeUpdates ?? this.challengeUpdates,
      communityUpdates: communityUpdates ?? this.communityUpdates,
      podcastNotifications: podcastNotifications ?? this.podcastNotifications,
      systemUpdates: systemUpdates ?? this.systemUpdates,
      inAppMessaging: inAppMessaging ?? this.inAppMessaging,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    userId,
    taskReminders,
    habitReminders,
    streakAlerts,
    milestoneCelebrations,
    challengeUpdates,
    communityUpdates,
    podcastNotifications,
    systemUpdates,
    inAppMessaging,
    quietHoursStart,
    quietHoursEnd,
    updatedAt,
  ];
}
