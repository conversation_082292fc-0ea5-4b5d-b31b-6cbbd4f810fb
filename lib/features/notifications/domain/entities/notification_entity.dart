import 'package:equatable/equatable.dart';

/// Entity representing a notification in the app
class NotificationEntity extends Equatable {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationStatus status;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final DateTime? sentAt;
  final Map<String, dynamic>? data;
  final bool isRead;
  final String? userId;
  final String? actionUrl;

  const NotificationEntity({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.createdAt,
    this.status = NotificationStatus.pending,
    this.scheduledAt,
    this.sentAt,
    this.data,
    this.isRead = false,
    this.userId,
    this.actionUrl,
  });

  NotificationEntity copyWith({
    String? id,
    String? title,
    String? body,
    NotificationType? type,
    NotificationStatus? status,
    DateTime? createdAt,
    DateTime? scheduledAt,
    DateTime? sentAt,
    Map<String, dynamic>? data,
    bool? isRead,
    String? userId,
    String? actionUrl,
  }) {
    return NotificationEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      userId: userId ?? this.userId,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  @override
  List<Object?> get props => [
    id,
    title,
    body,
    type,
    status,
    createdAt,
    scheduledAt,
    sentAt,
    data,
    isRead,
    userId,
    actionUrl,
  ];
}

/// Types of notifications in the app
enum NotificationType {
  taskReminder,
  habitReminder,
  streakAlert,
  milestoneAchievement,
  challengeUpdate,
  communityPost,
  podcastReady,
  systemUpdate,
}

/// Status of notifications
enum NotificationStatus { pending, sent, failed, cancelled }
