import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notification_entity.dart';
import '../entities/notification_response_entity.dart';
import '../entities/notification_preferences_entity.dart';
import '../entities/device_entity.dart';

/// Abstract repository for notification operations
abstract class NotificationRepository {
  /// Register device for push notifications
  Future<Either<Failure, void>> registerDevice({
    required String deviceToken,
    required DeviceType deviceType,
    String? deviceName,
  });

  /// Get user's registered devices
  Future<Either<Failure, List<DeviceEntity>>> getUserDevices();

  /// Remove a device
  Future<Either<Failure, void>> removeDevice(String deviceId);

  /// Get notification preferences
  Future<Either<Failure, NotificationPreferencesEntity>>
  getNotificationPreferences();

  /// Update notification preferences
  Future<Either<Failure, NotificationPreferencesEntity>>
  updateNotificationPreferences(NotificationPreferencesEntity preferences);

  /// Send test notification
  Future<Either<Failure, void>> sendTestNotification();

  /// Get user notifications with pagination (new method name)
  Future<Either<Failure, NotificationResponseEntity>> getNotifications({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    NotificationStatus? status,
    String? search,
    String? sortBy,
    String? sortOrder,
  });

  /// Get user notifications (legacy method)
  Future<Either<Failure, List<NotificationEntity>>> getUserNotifications({
    int? limit,
    int? offset,
  });

  /// Send push notification via FCM
  Future<Either<Failure, bool>> sendPushNotification(
    NotificationEntity notification,
  );

  /// Mark notification as read
  Future<Either<Failure, void>> markNotificationAsRead(String notificationId);

  /// Mark all notifications as read
  Future<Either<Failure, void>> markAllNotificationsAsRead();

  /// Delete notification
  Future<Either<Failure, void>> deleteNotification(String notificationId);

  /// Schedule local notification
  Future<Either<Failure, void>> scheduleLocalNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    NotificationType? type,
    Map<String, dynamic>? data,
  });

  /// Schedule local notification from entity
  Future<Either<Failure, void>> scheduleNotificationFromEntity(
    NotificationEntity notification,
  );

  /// Cancel local notification
  Future<Either<Failure, void>> cancelLocalNotification(int id);

  /// Cancel all local notifications
  Future<Either<Failure, void>> cancelAllLocalNotifications();

  /// Get FCM token
  Future<Either<Failure, String>> getFCMToken();

  /// Initialize FCM
  Future<Either<Failure, void>> initializeFCM();
}
