import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/notification_controller.dart';
import '../../domain/entities/notification_preferences_entity.dart';
import '../widgets/preference_switch_tile.dart';
import '../widgets/quiet_hours_picker.dart';

/// Screen for managing notification preferences
class NotificationPreferencesScreen extends StatelessWidget {
  const NotificationPreferencesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NotificationController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Preferences'),
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        foregroundColor: Theme.of(context).textTheme.titleLarge?.color,
      ),
      body: Obx(() {
        final preferences = controller.preferences.value;

        if (preferences == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Push Notifications Section
              _buildSectionHeader('Push Notifications'),
              Card(
                child: Column(
                  children: [
                    PreferenceSwitchTile(
                      title: 'Task Reminders',
                      subtitle: 'Get reminded about upcoming and overdue tasks',
                      value: preferences.taskReminders,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(taskReminders: value),
                          ),
                      icon: Icons.task_outlined,
                    ),
                    const Divider(height: 1),
                    PreferenceSwitchTile(
                      title: 'Habit Reminders',
                      subtitle: 'Daily reminders for your habits',
                      value: preferences.habitReminders,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(habitReminders: value),
                          ),
                      icon: Icons.track_changes_outlined,
                    ),
                    const Divider(height: 1),
                    PreferenceSwitchTile(
                      title: 'Streak Alerts',
                      subtitle: 'Celebrate your streaks and get motivated',
                      value: preferences.streakAlerts,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(streakAlerts: value),
                          ),
                      icon: Icons.local_fire_department_outlined,
                    ),
                    const Divider(height: 1),
                    PreferenceSwitchTile(
                      title: 'Milestone Celebrations',
                      subtitle:
                          'Get notified when you reach important milestones',
                      value: preferences.milestoneCelebrations,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(milestoneCelebrations: value),
                          ),
                      icon: Icons.emoji_events_outlined,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Content Updates Section
              _buildSectionHeader('Content Updates'),
              Card(
                child: Column(
                  children: [
                    PreferenceSwitchTile(
                      title: 'Challenge Updates',
                      subtitle:
                          'Updates about challenges you\'re participating in',
                      value: preferences.challengeUpdates,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(challengeUpdates: value),
                          ),
                      icon: Icons.sports_score_outlined,
                    ),
                    const Divider(height: 1),
                    PreferenceSwitchTile(
                      title: 'Community Updates',
                      subtitle: 'Activity from your community and connections',
                      value: preferences.communityUpdates,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(communityUpdates: value),
                          ),
                      icon: Icons.group_outlined,
                    ),
                    const Divider(height: 1),
                    PreferenceSwitchTile(
                      title: 'Podcast Notifications',
                      subtitle: 'New podcast episodes and recommendations',
                      value: preferences.podcastNotifications,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(podcastNotifications: value),
                          ),
                      icon: Icons.podcasts_outlined,
                    ),
                    const Divider(height: 1),
                    PreferenceSwitchTile(
                      title: 'System Updates',
                      subtitle:
                          'App updates, maintenance, and important announcements',
                      value: preferences.systemUpdates,
                      onChanged:
                          (value) => _updatePreference(
                            controller,
                            preferences.copyWith(systemUpdates: value),
                          ),
                      icon: Icons.system_update_outlined,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // In-App Messaging Section
              _buildSectionHeader('In-App Features'),
              Card(
                child: PreferenceSwitchTile(
                  title: 'In-App Messaging',
                  subtitle: 'Show contextual messages and tips within the app',
                  value: preferences.inAppMessaging,
                  onChanged:
                      (value) => _updatePreference(
                        controller,
                        preferences.copyWith(inAppMessaging: value),
                      ),
                  icon: Icons.message_outlined,
                ),
              ),

              const SizedBox(height: 24),

              // Quiet Hours Section
              _buildSectionHeader('Quiet Hours'),
              Card(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Icon(
                            Icons.bedtime_outlined,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Do Not Disturb',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                                Text(
                                  'Set quiet hours when you won\'t receive notifications',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    QuietHoursPicker(
                      startTime: preferences.quietHoursStart,
                      endTime: preferences.quietHoursEnd,
                      onTimesChanged:
                          (start, end) => _updatePreference(
                            controller,
                            preferences.copyWith(
                              quietHoursStart: start,
                              quietHoursEnd: end,
                            ),
                          ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Reset to defaults button
              Center(
                child: TextButton.icon(
                  onPressed: () => _resetToDefaults(controller),
                  icon: const Icon(Icons.restore),
                  label: const Text('Reset to Defaults'),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, left: 4.0),
      child: Text(
        title,
        style: Get.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: Get.theme.primaryColor,
        ),
      ),
    );
  }

  void _updatePreference(
    NotificationController controller,
    NotificationPreferencesEntity updatedPreferences,
  ) {
    controller.updateNotificationPreferences(
      updatedPreferences.copyWith(updatedAt: DateTime.now()),
    );
  }

  void _resetToDefaults(NotificationController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('Reset Preferences'),
        content: const Text(
          'Are you sure you want to reset all notification preferences to their default values?',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          TextButton(
            onPressed: () {
              Get.back();
              final currentPrefs = controller.preferences.value!;
              final defaultPrefs = NotificationPreferencesEntity(
                userId: currentPrefs.userId,
                updatedAt: DateTime.now(),
              );
              controller.updateNotificationPreferences(defaultPrefs);
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
