import 'package:get/get.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../../domain/entities/notification_entity.dart';
import '../../domain/entities/notification_preferences_entity.dart';
import '../../domain/entities/device_entity.dart';
import '../../domain/usecases/get_notifications_usecase.dart';
import '../../domain/usecases/mark_notification_read_usecase.dart';
import '../../domain/usecases/get_notification_preferences_usecase.dart';
import '../../domain/usecases/update_notification_preferences_usecase.dart';
import '../../domain/usecases/register_device_usecase.dart';
import '../../domain/usecases/send_push_notification_usecase.dart';
import '../../domain/usecases/schedule_local_notification_usecase.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/utils/logger.dart';

/// Controller for managing notifications and preferences
class NotificationController extends Getx<PERSON><PERSON>roller {
  final GetNotificationsUseCase getNotificationsUseCase;
  final MarkNotificationReadUseCase markNotificationReadUseCase;
  final GetNotificationPreferencesUseCase getNotificationPreferencesUseCase;
  final UpdateNotificationPreferencesUseCase
  updateNotificationPreferencesUseCase;
  final RegisterDeviceUseCase registerDeviceUseCase;
  final SendPushNotificationUseCase sendPushNotificationUseCase;
  final ScheduleLocalNotificationUseCase scheduleLocalNotificationUseCase;
  final AuthService authService;

  NotificationController({
    required this.getNotificationsUseCase,
    required this.markNotificationReadUseCase,
    required this.getNotificationPreferencesUseCase,
    required this.updateNotificationPreferencesUseCase,
    required this.registerDeviceUseCase,
    required this.sendPushNotificationUseCase,
    required this.scheduleLocalNotificationUseCase,
    required this.authService,
  });

  // Observable properties
  final RxList<NotificationEntity> notifications = <NotificationEntity>[].obs;
  final Rx<NotificationPreferencesEntity?> preferences =
      Rx<NotificationPreferencesEntity?>(null);
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxString errorMessage = ''.obs;
  final RxInt unreadCount = 0.obs;
  final RxInt currentPage = 1.obs;
  final RxBool hasMoreNotifications = true.obs;

  // Filter properties
  final Rx<NotificationType?> selectedType = Rx<NotificationType?>(null);
  final RxBool showOnlyUnread = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeNotifications();
    _setupFCMListeners();
  }

  /// Initialize notifications and preferences
  Future<void> _initializeNotifications() async {
    await loadNotifications();
    await loadNotificationPreferences();
    await _registerCurrentDevice();
  }

  /// Setup Firebase Cloud Messaging listeners
  void _setupFCMListeners() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      Logger.info(
        'Received foreground message: ${message.notification?.title}',
      );
      _handleForegroundMessage(message);
    });

    // Handle background message taps
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      Logger.info('Message clicked: ${message.notification?.title}');
      _handleMessageTap(message);
    });

    // Handle app opened from terminated state
    FirebaseMessaging.instance.getInitialMessage().then((
      RemoteMessage? message,
    ) {
      if (message != null) {
        Logger.info(
          'App opened from terminated state: ${message.notification?.title}',
        );
        _handleMessageTap(message);
      }
    });
  }

  /// Load notifications with pagination
  Future<void> loadNotifications({bool refresh = false}) async {
    final userId = authService.currentUser.value?.uid;
    if (userId == null) return;

    if (refresh) {
      currentPage.value = 1;
      hasMoreNotifications.value = true;
      notifications.clear();
    }

    isLoading.value = refresh;
    isLoadingMore.value = !refresh;
    errorMessage.value = '';

    try {
      final result = await getNotificationsUseCase(
        page: currentPage.value,
        limit: 20,
        type: selectedType.value,
        status:
            showOnlyUnread.value
                ? null
                : null, // We'll need to adjust this logic
      );

      result.fold(
        (failure) {
          errorMessage.value = failure.message;
          Logger.error('Failed to load notifications: ${failure.message}');
        },
        (response) {
          if (refresh) {
            notifications.assignAll(response.data);
          } else {
            notifications.addAll(response.data);
          }

          hasMoreNotifications.value = response.hasNext;
          currentPage.value++;
          _updateUnreadCount();
        },
      );
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
      Logger.error('Exception loading notifications: $e');
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (!hasMoreNotifications.value || isLoadingMore.value) return;
    await loadNotifications();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      final result = await markNotificationReadUseCase(notificationId);

      result.fold(
        (failure) {
          Logger.error(
            'Failed to mark notification as read: ${failure.message}',
          );
          Get.snackbar('Error', 'Failed to mark notification as read');
        },
        (success) {
          // Update local notification state
          final index = notifications.indexWhere((n) => n.id == notificationId);
          if (index != -1) {
            notifications[index] = notifications[index].copyWith(isRead: true);
            _updateUnreadCount();
          }
        },
      );
    } catch (e) {
      Logger.error('Exception marking notification as read: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
    }
  }

  /// Load notification preferences
  Future<void> loadNotificationPreferences() async {
    final userId = authService.currentUser.value?.uid;
    if (userId == null) return;

    try {
      final result = await getNotificationPreferencesUseCase();

      result.fold(
        (failure) {
          Logger.error(
            'Failed to load notification preferences: ${failure.message}',
          );
        },
        (prefs) {
          preferences.value = prefs;
        },
      );
    } catch (e) {
      Logger.error('Exception loading notification preferences: $e');
    }
  }

  /// Update notification preferences
  Future<void> updateNotificationPreferences(
    NotificationPreferencesEntity newPreferences,
  ) async {
    try {
      final result = await updateNotificationPreferencesUseCase(newPreferences);

      result.fold(
        (failure) {
          Logger.error(
            'Failed to update notification preferences: ${failure.message}',
          );
          Get.snackbar('Error', 'Failed to update notification preferences');
        },
        (updatedPrefs) {
          preferences.value = updatedPrefs;
          Get.snackbar('Success', 'Notification preferences updated');
        },
      );
    } catch (e) {
      Logger.error('Exception updating notification preferences: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
    }
  }

  /// Register current device for push notifications
  Future<void> _registerCurrentDevice() async {
    final userId = authService.currentUser.value?.uid;
    if (userId == null) return;

    try {
      final fcmToken = await FirebaseMessaging.instance.getToken();
      if (fcmToken == null) return;

      // Use RegisterDeviceParams instead of DeviceEntity directly
      final params = RegisterDeviceParams(
        deviceToken: fcmToken,
        deviceType: GetPlatform.isIOS ? DeviceType.ios : DeviceType.android,
        deviceName: GetPlatform.isIOS ? 'iPhone' : 'Android Device',
      );

      final result = await registerDeviceUseCase(params);

      result.fold(
        (failure) {
          Logger.error('Failed to register device: ${failure.message}');
        },
        (_) {
          Logger.info('Device registered successfully');
        },
      );
    } catch (e) {
      Logger.error('Exception registering device: $e');
    }
  }

  /// Handle foreground message
  void _handleForegroundMessage(RemoteMessage message) {
    // You can show an in-app notification here
    if (message.notification != null) {
      Get.snackbar(
        message.notification!.title ?? 'Notification',
        message.notification!.body ?? '',
        duration: const Duration(seconds: 4),
      );
    }

    // Refresh notifications to include the new one
    loadNotifications(refresh: true);
  }

  /// Handle message tap (background or terminated state)
  void _handleMessageTap(RemoteMessage message) {
    // Navigate to specific screen based on message data
    final data = message.data;
    if (data.containsKey('type')) {
      switch (data['type']) {
        case 'task_reminder':
          Get.toNamed('/tasks');
          break;
        case 'habit_reminder':
          Get.toNamed('/habits');
          break;
        case 'streak_alert':
          Get.toNamed('/streaks');
          break;
        case 'milestone_achievement':
          Get.toNamed('/achievements');
          break;
        default:
          Get.toNamed('/notifications');
      }
    }
  }

  /// Update unread count
  void _updateUnreadCount() {
    unreadCount.value = notifications.where((n) => !n.isRead).length;
  }

  /// Filter notifications by type
  void filterByType(NotificationType? type) {
    selectedType.value = type;
    loadNotifications(refresh: true);
  }

  /// Toggle show only unread filter
  void toggleUnreadFilter() {
    showOnlyUnread.value = !showOnlyUnread.value;
    loadNotifications(refresh: true);
  }

  /// Clear all filters
  void clearFilters() {
    selectedType.value = null;
    showOnlyUnread.value = false;
    loadNotifications(refresh: true);
  }

  /// Schedule a local notification
  Future<void> scheduleNotification(NotificationEntity notification) async {
    try {
      final result = await scheduleLocalNotificationUseCase(notification);

      result.fold(
        (failure) {
          Logger.error('Failed to schedule notification: ${failure.message}');
        },
        (success) {
          Logger.info('Notification scheduled successfully');
        },
      );
    } catch (e) {
      Logger.error('Exception scheduling notification: $e');
    }
  }

  /// Send push notification (admin feature)
  Future<void> sendPushNotification(NotificationEntity notification) async {
    try {
      final result = await sendPushNotificationUseCase(notification);

      result.fold(
        (failure) {
          Logger.error('Failed to send push notification: ${failure.message}');
          Get.snackbar('Error', 'Failed to send notification');
        },
        (success) {
          Logger.info('Push notification sent successfully');
          Get.snackbar('Success', 'Notification sent');
        },
      );
    } catch (e) {
      Logger.error('Exception sending push notification: $e');
      Get.snackbar('Error', 'An unexpected error occurred');
    }
  }
}
