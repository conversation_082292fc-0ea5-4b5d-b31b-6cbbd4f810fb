import 'package:flutter/material.dart';
import '../../domain/entities/notification_entity.dart';

/// Widget for filtering notifications by type
class NotificationFilterChips extends StatelessWidget {
  final NotificationType? selectedType;
  final bool showOnlyUnread;
  final Function(NotificationType?) onTypeSelected;
  final Function(bool) onUnreadFilterChanged;
  final VoidCallback onClearFilters;

  const NotificationFilterChips({
    super.key,
    this.selectedType,
    required this.showOnlyUnread,
    required this.onTypeSelected,
    required this.onUnreadFilterChanged,
    required this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          // Unread filter
          FilterChip(
            label: const Text('Unread'),
            selected: showOnlyUnread,
            onSelected: onUnreadFilterChanged,
            avatar:
                showOnlyUnread
                    ? const Icon(Icons.mark_email_unread, size: 16)
                    : const Icon(Icons.mark_email_read_outlined, size: 16),
          ),

          const SizedBox(width: 8),

          // Type filters
          ...NotificationType.values.map(
            (type) => Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: FilterChip(
                label: Text(_getTypeLabel(type)),
                selected: selectedType == type,
                onSelected: (selected) {
                  onTypeSelected(selected ? type : null);
                },
                avatar: Icon(_getTypeIcon(type), size: 16),
              ),
            ),
          ),

          // Clear filters
          if (selectedType != null || showOnlyUnread)
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: ActionChip(
                label: const Text('Clear'),
                onPressed: onClearFilters,
                avatar: const Icon(Icons.clear, size: 16),
              ),
            ),
        ],
      ),
    );
  }

  String _getTypeLabel(NotificationType type) {
    switch (type) {
      case NotificationType.taskReminder:
        return 'Tasks';
      case NotificationType.habitReminder:
        return 'Habits';
      case NotificationType.streakAlert:
        return 'Streaks';
      case NotificationType.milestoneAchievement:
        return 'Milestones';
      case NotificationType.challengeUpdate:
        return 'Challenges';
      case NotificationType.communityPost:
        return 'Community';
      case NotificationType.podcastReady:
        return 'Podcasts';
      case NotificationType.systemUpdate:
        return 'System';
    }
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.taskReminder:
        return Icons.task_outlined;
      case NotificationType.habitReminder:
        return Icons.track_changes_outlined;
      case NotificationType.streakAlert:
        return Icons.local_fire_department_outlined;
      case NotificationType.milestoneAchievement:
        return Icons.emoji_events_outlined;
      case NotificationType.challengeUpdate:
        return Icons.sports_score_outlined;
      case NotificationType.communityPost:
        return Icons.group_outlined;
      case NotificationType.podcastReady:
        return Icons.podcasts_outlined;
      case NotificationType.systemUpdate:
        return Icons.system_update_outlined;
    }
  }
}
