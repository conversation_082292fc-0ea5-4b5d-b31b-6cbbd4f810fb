import 'package:flutter/material.dart';

/// Widget for picking quiet hours (do not disturb) time range
class QuietHoursPicker extends StatelessWidget {
  final DateTime? startTime;
  final DateTime? endTime;
  final Function(DateTime?, DateTime?) onTimesChanged;

  const QuietHoursPicker({
    super.key,
    this.startTime,
    this.endTime,
    required this.onTimesChanged,
  });

  @override
  Widget build(BuildContext context) {
    final hasQuietHours = startTime != null && endTime != null;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      hasQuietHours ? Icons.bedtime : Icons.bedtime_outlined,
                      color:
                          hasQuietHours
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).disabledColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      hasQuietHours ? 'Enabled' : 'Disabled',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color:
                            hasQuietHours
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).disabledColor,
                      ),
                    ),
                  ],
                ),
              ),
              Switch.adaptive(
                value: hasQuietHours,
                onChanged: (enabled) {
                  if (enabled) {
                    // Enable with default times (10 PM to 8 AM)
                    final now = DateTime.now();
                    final start = DateTime(
                      now.year,
                      now.month,
                      now.day,
                      22,
                      0,
                    ); // 10 PM
                    final end = DateTime(
                      now.year,
                      now.month,
                      now.day + 1,
                      8,
                      0,
                    ); // 8 AM next day
                    onTimesChanged(start, end);
                  } else {
                    // Disable quiet hours
                    onTimesChanged(null, null);
                  }
                },
                activeColor: Theme.of(context).primaryColor,
              ),
            ],
          ),

          if (hasQuietHours) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTimePicker(
                    context,
                    'Start Time',
                    startTime!,
                    (time) => onTimesChanged(time, endTime),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTimePicker(
                    context,
                    'End Time',
                    endTime!,
                    (time) => onTimesChanged(startTime, time),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _formatQuietHoursRange(startTime!, endTime!),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimePicker(
    BuildContext context,
    String label,
    DateTime time,
    Function(DateTime) onTimeChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 4),
        InkWell(
          onTap: () => _selectTime(context, time, onTimeChanged),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatTime(time),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Icon(
                  Icons.access_time,
                  size: 18,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectTime(
    BuildContext context,
    DateTime currentTime,
    Function(DateTime) onTimeChanged,
  ) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(currentTime),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: Theme.of(context).cardColor,
              dialHandColor: Theme.of(context).primaryColor,
              dialBackgroundColor: Theme.of(
                context,
              ).primaryColor.withValues(alpha: 0.1),
              hourMinuteTextColor: Theme.of(context).primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final newTime = DateTime(
        currentTime.year,
        currentTime.month,
        currentTime.day,
        picked.hour,
        picked.minute,
      );
      onTimeChanged(newTime);
    }
  }

  String _formatTime(DateTime time) {
    final hour =
        time.hour == 0 ? 12 : (time.hour > 12 ? time.hour - 12 : time.hour);
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  String _formatQuietHoursRange(DateTime start, DateTime end) {
    final startStr = _formatTime(start);
    final endStr = _formatTime(end);

    // If end time is next day, indicate it
    if (end.day > start.day ||
        (end.day == start.day && end.hour < start.hour)) {
      return 'From $startStr to $endStr (+1 day)';
    } else {
      return 'From $startStr to $endStr';
    }
  }
}
