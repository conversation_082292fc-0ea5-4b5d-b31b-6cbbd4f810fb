import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../domain/entities/notification_entity.dart';

/// Widget for displaying a single notification in a list
class NotificationListItem extends StatelessWidget {
  final NotificationEntity notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onDelete;

  const NotificationListItem({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color:
            notification.isRead
                ? Theme.of(context).cardColor
                : Theme.of(context).primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border:
            notification.isRead
                ? Border.all(
                  color: Theme.of(context).dividerColor.withValues(alpha: 0.5),
                )
                : Border.all(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                ),
      ),
      child: InkWell(
        onTap: () {
          if (!notification.isRead && onMarkAsRead != null) {
            onMarkAsRead!();
          }
          onTap?.call();
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon based on notification type
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getNotificationColor(
                    notification.type,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              fontWeight:
                                  notification.isRead
                                      ? FontWeight.normal
                                      : FontWeight.w600,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 4),

                    Text(
                      notification.body,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    Row(
                      children: [
                        Text(
                          timeago.format(notification.createdAt),
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color: Theme.of(
                              context,
                            ).textTheme.bodySmall?.color?.withValues(alpha: 0.6),
                          ),
                        ),
                        const Spacer(),

                        // Action buttons
                        if (!notification.isRead && onMarkAsRead != null)
                          InkWell(
                            onTap: onMarkAsRead,
                            borderRadius: BorderRadius.circular(16),
                            child: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: Icon(
                                Icons.mark_email_read_outlined,
                                size: 16,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),

                        if (onDelete != null) ...[
                          const SizedBox(width: 8),
                          InkWell(
                            onTap: onDelete,
                            borderRadius: BorderRadius.circular(16),
                            child: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: Icon(
                                Icons.delete_outline,
                                size: 16,
                                color: Theme.of(context).colorScheme.error,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.taskReminder:
        return Icons.task_outlined;
      case NotificationType.habitReminder:
        return Icons.track_changes_outlined;
      case NotificationType.streakAlert:
        return Icons.local_fire_department_outlined;
      case NotificationType.milestoneAchievement:
        return Icons.emoji_events_outlined;
      case NotificationType.challengeUpdate:
        return Icons.sports_score_outlined;
      case NotificationType.communityPost:
        return Icons.group_outlined;
      case NotificationType.podcastReady:
        return Icons.podcasts_outlined;
      case NotificationType.systemUpdate:
        return Icons.system_update_outlined;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.taskReminder:
        return Colors.blue;
      case NotificationType.habitReminder:
        return Colors.green;
      case NotificationType.streakAlert:
        return Colors.orange;
      case NotificationType.milestoneAchievement:
        return Colors.purple;
      case NotificationType.challengeUpdate:
        return Colors.red;
      case NotificationType.communityPost:
        return Colors.teal;
      case NotificationType.podcastReady:
        return Colors.indigo;
      case NotificationType.systemUpdate:
        return Colors.grey;
    }
  }
}
