import 'package:get/get.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:power_up/features/core/data/api/api_client.dart';
import 'package:power_up/core/services/secure_storage_service.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/core/services/auth_service.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import '../data/datasources/notification_remote_data_source.dart';
import '../data/datasources/notification_local_data_source.dart';
import '../data/repositories/notification_repository_impl.dart';
import '../domain/repositories/notification_repository.dart';
import '../domain/usecases/get_notifications_usecase.dart';
import '../domain/usecases/mark_notification_read_usecase.dart';
import '../domain/usecases/get_notification_preferences_usecase.dart';
import '../domain/usecases/update_notification_preferences_usecase.dart';
import '../domain/usecases/register_device_usecase.dart';
import '../domain/usecases/send_push_notification_usecase.dart';
import '../domain/usecases/schedule_local_notification_usecase.dart';
import '../presentation/controllers/notification_controller.dart';

/// Dependency injection binding for notification module
class NotificationBindings extends Bindings {
  @override
  void dependencies() {
    // Data Sources
    Get.lazyPut<NotificationRemoteDataSource>(
      () => NotificationRemoteDataSourceImpl(
        apiClient: Get.find<ApiClient>(),
        secureStorage: Get.find<SecureStorageService>(),
      ),
    );

    Get.lazyPut<NotificationLocalDataSource>(
      () => NotificationLocalDataSourceImpl(
        localNotifications: Get.find<FlutterLocalNotificationsPlugin>(),
        firebaseMessaging: Get.find<FirebaseMessaging>(),
        storageService: Get.find<StorageService>(),
      ),
    );

    // Repository
    Get.lazyPut<NotificationRepository>(
      () => NotificationRepositoryImpl(
        remoteDataSource: Get.find<NotificationRemoteDataSource>(),
        localDataSource: Get.find<NotificationLocalDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
        authService: Get.find<AuthService>(),
      ),
    );

    // Use Cases
    Get.lazyPut(
      () => GetNotificationsUseCase(Get.find<NotificationRepository>()),
    );
    Get.lazyPut(
      () => MarkNotificationReadUseCase(Get.find<NotificationRepository>()),
    );
    Get.lazyPut(
      () =>
          GetNotificationPreferencesUseCase(Get.find<NotificationRepository>()),
    );
    Get.lazyPut(
      () => UpdateNotificationPreferencesUseCase(
        Get.find<NotificationRepository>(),
      ),
    );
    Get.lazyPut(
      () => RegisterDeviceUseCase(Get.find<NotificationRepository>()),
    );
    Get.lazyPut(
      () => SendPushNotificationUseCase(Get.find<NotificationRepository>()),
    );
    Get.lazyPut(
      () =>
          ScheduleLocalNotificationUseCase(Get.find<NotificationRepository>()),
    );

    // Controller
    Get.lazyPut(
      () => NotificationController(
        getNotificationsUseCase: Get.find<GetNotificationsUseCase>(),
        markNotificationReadUseCase: Get.find<MarkNotificationReadUseCase>(),
        getNotificationPreferencesUseCase:
            Get.find<GetNotificationPreferencesUseCase>(),
        updateNotificationPreferencesUseCase:
            Get.find<UpdateNotificationPreferencesUseCase>(),
        registerDeviceUseCase: Get.find<RegisterDeviceUseCase>(),
        sendPushNotificationUseCase: Get.find<SendPushNotificationUseCase>(),
        scheduleLocalNotificationUseCase:
            Get.find<ScheduleLocalNotificationUseCase>(),
        authService: Get.find<AuthService>(),
      ),
    );
  }
}
