import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/services/auth_service.dart';
import '../../domain/entities/notification_entity.dart';
import '../../domain/entities/notification_response_entity.dart';
import '../../domain/entities/notification_preferences_entity.dart';
import '../../domain/entities/device_entity.dart';
import '../../domain/repositories/notification_repository.dart';
import '../datasources/notification_remote_data_source.dart';
import '../datasources/notification_local_data_source.dart';
import '../models/notification_model.dart';
import '../models/notification_preferences_model.dart';
import '../models/device_model.dart';

/// Implementation of notification repository
class NotificationRepositoryImpl implements NotificationRepository {
  final NotificationRemoteDataSource remoteDataSource;
  final NotificationLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AuthService authService;

  NotificationRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.authService,
  });

  /// Helper method to get current user ID from auth service
  String _getCurrentUserId() {
    final userId = authService.currentUserId;
    if (userId == null) {
      throw const UnauthorizedException('User not authenticated');
    }
    return userId;
  }

  @override
  Future<Either<Failure, void>> registerDevice({
    required String deviceToken,
    required DeviceType deviceType,
    String? deviceName,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final device = DeviceModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          userId: _getCurrentUserId(),
          deviceToken: deviceToken,
          deviceType: deviceType,
          deviceName: deviceName,
          registeredAt: DateTime.now(),
          lastUsed: DateTime.now(),
          isActive: true,
        );

        await remoteDataSource.registerDevice(device);
        return const Right(null);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to register device: $e'));
    }
  }

  @override
  Future<Either<Failure, List<DeviceEntity>>> getUserDevices() async {
    try {
      if (await networkInfo.isConnected) {
        final devices = await remoteDataSource.getUserDevices(
          _getCurrentUserId(),
        ); // User ID from auth
        return Right(devices.map((model) => model.toEntity()).toList());
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to fetch user devices: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> removeDevice(String deviceId) async {
    try {
      if (await networkInfo.isConnected) {
        await remoteDataSource.deleteDevice(deviceId);
        return const Right(null);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to remove device: $e'));
    }
  }

  @override
  Future<Either<Failure, NotificationPreferencesEntity>>
  getNotificationPreferences() async {
    if (await networkInfo.isConnected) {
      try {
        final userId = _getCurrentUserId(); // Get user ID from auth service
        final remotePreferences = await remoteDataSource
            .getNotificationPreferences(userId);

        // Cache the preferences
        await localDataSource.cacheNotificationPreferences(remotePreferences);

        return Right(remotePreferences.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } on UnauthorizedException catch (e) {
        return Left(AuthFailure(message: e.message));
      } catch (e) {
        return Left(
          ServerFailure(
            message: 'Failed to fetch notification preferences: $e',
          ),
        );
      }
    } else {
      try {
        final userId = _getCurrentUserId(); // Get user ID from auth service
        final cachedPreferences = await localDataSource
            .getCachedNotificationPreferences(userId);
        if (cachedPreferences != null) {
          return Right(cachedPreferences.toEntity());
        } else {
          return const Left(
            CacheFailure(message: 'No cached notification preferences found'),
          );
        }
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, NotificationPreferencesEntity>>
  updateNotificationPreferences(
    NotificationPreferencesEntity preferences,
  ) async {
    try {
      final preferencesModel = NotificationPreferencesModel.fromEntity(
        preferences,
      );

      if (await networkInfo.isConnected) {
        final updatedPreferences = await remoteDataSource
            .updateNotificationPreferences(preferencesModel);

        // Cache the updated preferences
        await localDataSource.cacheNotificationPreferences(updatedPreferences);

        return Right(updatedPreferences.toEntity());
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to update notification preferences: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> sendTestNotification() async {
    try {
      if (await networkInfo.isConnected) {
        final notification = NotificationModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: 'Test Notification',
          body: 'This is a test notification',
          type: NotificationType.systemUpdate,
          createdAt: DateTime.now(),
          isRead: false,
          userId: _getCurrentUserId(), // Get user ID from auth service
        );

        await remoteDataSource.sendPushNotification(notification);
        return const Right(null);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to send test notification: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, List<NotificationEntity>>> getUserNotifications({
    int? limit,
    int? offset,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final userId = _getCurrentUserId(); // Get user ID from auth service
        final remoteResponse = await remoteDataSource.getNotifications(
          page: (offset ?? 0) ~/ (limit ?? 20) + 1,
          limit: limit ?? 20,
        );

        // Cache the fetched notifications
        await localDataSource.cacheNotifications(remoteResponse.data, userId);

        return Right(
          remoteResponse.data.map((model) => model.toEntity()).toList(),
        );
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } on UnauthorizedException catch (e) {
        return Left(AuthFailure(message: e.message));
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to fetch notifications: $e'),
        );
      }
    } else {
      try {
        final userId = _getCurrentUserId(); // Get user ID from auth service
        final cachedNotifications = await localDataSource
            .getCachedNotifications(userId);
        return Right(
          cachedNotifications.map((model) => model.toEntity()).toList(),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, void>> markNotificationAsRead(
    String notificationId,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        await remoteDataSource.markNotificationAsRead(notificationId);
        return const Right(null);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to mark notification as read: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> markAllNotificationsAsRead() async {
    try {
      if (await networkInfo.isConnected) {
        // This would need to be implemented on the server side
        // For now, we'll just return success
        return const Right(null);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to mark all notifications as read: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> deleteNotification(
    String notificationId,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        await remoteDataSource.deleteNotification(notificationId);
        return const Right(null);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to delete notification: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> scheduleLocalNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    NotificationType? type,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = NotificationModel(
        id: id.toString(),
        title: title,
        body: body,
        type: type ?? NotificationType.systemUpdate,
        createdAt: DateTime.now(),
        scheduledAt: scheduledDate,
        data: data,
        isRead: false,
        userId: _getCurrentUserId(), // Get user ID from auth service
      );

      await localDataSource.scheduleLocalNotification(notification);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        CacheFailure(message: 'Failed to schedule local notification: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> cancelLocalNotification(int id) async {
    try {
      await localDataSource.cancelLocalNotification(id);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        CacheFailure(message: 'Failed to cancel local notification: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> cancelAllLocalNotifications() async {
    try {
      await localDataSource.cancelAllLocalNotifications();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        CacheFailure(message: 'Failed to cancel all local notifications: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, String>> getFCMToken() async {
    try {
      final token = await localDataSource.getFCMToken();
      if (token != null) {
        return Right(token);
      } else {
        return const Left(CacheFailure(message: 'FCM token not available'));
      }
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to get FCM token: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> initializeFCM() async {
    try {
      await localDataSource.requestNotificationPermissions();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(CacheFailure(message: 'Failed to initialize FCM: $e'));
    }
  }

  @override
  Future<Either<Failure, NotificationResponseEntity>> getNotifications({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    NotificationStatus? status,
    String? search,
    String? sortBy,
    String? sortOrder,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteResponse = await remoteDataSource.getNotifications(
          page: page,
          limit: limit,
          type: type,
          status: status,
          search: search,
          sortBy: sortBy,
          sortOrder: sortOrder,
        );

        // Cache the fetched notifications (just the data part)
        final userId = _getCurrentUserId();
        await localDataSource.cacheNotifications(remoteResponse.data, userId);

        return Right(remoteResponse.toEntity());
      } on ServerException catch (e) {
        return Left(ServerFailure(message: e.message));
      } on UnauthorizedException catch (e) {
        return Left(AuthFailure(message: e.message));
      } catch (e) {
        return Left(
          ServerFailure(message: 'Failed to fetch notifications: $e'),
        );
      }
    } else {
      try {
        final userId = _getCurrentUserId();
        final cachedNotifications = await localDataSource
            .getCachedNotifications(userId);

        // Create a mock response entity for cached data
        return Right(
          NotificationResponseEntity(
            data: cachedNotifications.map((model) => model.toEntity()).toList(),
            total: cachedNotifications.length,
            page: page,
            limit: limit,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          ),
        );
      } on CacheException catch (e) {
        return Left(CacheFailure(message: e.message));
      }
    }
  }

  @override
  Future<Either<Failure, bool>> sendPushNotification(
    NotificationEntity notification,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final notificationModel = NotificationModel(
          id: notification.id,
          title: notification.title,
          body: notification.body,
          type: notification.type,
          createdAt: notification.createdAt,
          scheduledAt: notification.scheduledAt,
          data: notification.data,
          isRead: notification.isRead,
          userId: notification.userId,
        );

        await remoteDataSource.sendPushNotification(notificationModel);
        return const Right(true);
      } else {
        return const Left(NetworkFailure(message: 'No internet connection'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to send push notification: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> scheduleNotificationFromEntity(
    NotificationEntity notification,
  ) async {
    try {
      final notificationModel = NotificationModel(
        id: notification.id,
        title: notification.title,
        body: notification.body,
        type: notification.type,
        createdAt: notification.createdAt,
        scheduledAt: notification.scheduledAt,
        data: notification.data,
        isRead: notification.isRead,
        userId: notification.userId,
      );

      await localDataSource.scheduleLocalNotification(notificationModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(
        CacheFailure(
          message: 'Failed to schedule notification from entity: $e',
        ),
      );
    }
  }
}
