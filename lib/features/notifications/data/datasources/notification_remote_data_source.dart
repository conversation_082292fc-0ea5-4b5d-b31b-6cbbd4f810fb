import '../../../../core/error/exceptions.dart';
import '../../../../core/services/secure_storage_service.dart';
import '../../../core/data/api/api_client.dart';
import '../models/notification_model.dart';
import '../models/notification_response_model.dart';
import '../models/notification_preferences_model.dart';
import '../models/device_model.dart';
import '../../domain/entities/notification_entity.dart';

/// Remote data source for notification operations
abstract class NotificationRemoteDataSource {
  Future<NotificationResponseModel> getNotifications({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    NotificationStatus? status,
    String? search,
    String? sortBy,
    String? sortOrder,
  });

  Future<NotificationModel> createNotification(NotificationModel notification);
  Future<bool> markNotificationAsRead(String notificationId);
  Future<bool> deleteNotification(String notificationId);

  Future<NotificationPreferencesModel> getNotificationPreferences(
    String userId,
  );
  Future<NotificationPreferencesModel> updateNotificationPreferences(
    NotificationPreferencesModel preferences,
  );

  Future<DeviceModel> registerDevice(DeviceModel device);
  Future<List<DeviceModel>> getUserDevices(String userId);
  Future<bool> updateDeviceStatus(String deviceId, bool isActive);
  Future<bool> deleteDevice(String deviceId);

  Future<bool> sendPushNotification(NotificationModel notification);
}

/// Implementation of notification remote data source
class NotificationRemoteDataSourceImpl implements NotificationRemoteDataSource {
  final ApiClient apiClient;
  final SecureStorageService secureStorage;

  NotificationRemoteDataSourceImpl({
    required this.apiClient,
    required this.secureStorage,
  });

  @override
  Future<NotificationResponseModel> getNotifications({
    int page = 1,
    int limit = 20,
    NotificationType? type,
    NotificationStatus? status,
    String? search,
    String? sortBy,
    String? sortOrder,
  }) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    final queryParams = <String, dynamic>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (type != null) queryParams['type'] = type.name;
    if (status != null) queryParams['status'] = status.name;
    if (search != null && search.isNotEmpty) queryParams['search'] = search;
    if (sortBy != null && sortBy.isNotEmpty) queryParams['sortBy'] = sortBy;
    if (sortOrder != null && sortOrder.isNotEmpty) {
      queryParams['sortOrder'] = sortOrder;
    }

    try {
      return await apiClient.get<NotificationResponseModel>(
        endpoint: '/notifications',
        queryParameters: queryParams,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData:
            (data) => NotificationResponseModel.fromJson(
              data as Map<String, dynamic>,
            ),
      );
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to fetch notifications: $e');
    }
  }

  @override
  Future<NotificationModel> createNotification(
    NotificationModel notification,
  ) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      return await apiClient.post<NotificationModel>(
        endpoint: '/notifications',
        data: notification.toJson(),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => NotificationModel.fromJson(data['data']),
      );
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to create notification: $e');
    }
  }

  @override
  Future<bool> markNotificationAsRead(String notificationId) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      await apiClient.patch<Map<String, dynamic>>(
        endpoint: '/notifications/$notificationId/read',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => data as Map<String, dynamic>,
      );
      return true;
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to mark notification as read: $e');
    }
  }

  @override
  Future<bool> deleteNotification(String notificationId) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      await apiClient.delete<Map<String, dynamic>>(
        endpoint: '/notifications/$notificationId',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => data as Map<String, dynamic>,
      );
      return true;
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to delete notification: $e');
    }
  }

  @override
  Future<NotificationPreferencesModel> getNotificationPreferences(
    String userId,
  ) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      return await apiClient.get<NotificationPreferencesModel>(
        endpoint: '/users/$userId/notification-preferences',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => NotificationPreferencesModel.fromJson(data['data']),
      );
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to fetch notification preferences: $e',
      );
    }
  }

  @override
  Future<NotificationPreferencesModel> updateNotificationPreferences(
    NotificationPreferencesModel preferences,
  ) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      return await apiClient.put<NotificationPreferencesModel>(
        endpoint: '/users/${preferences.userId}/notification-preferences',
        data: preferences.toJson(),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => NotificationPreferencesModel.fromJson(data['data']),
      );
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Failed to update notification preferences: $e',
      );
    }
  }

  @override
  Future<DeviceModel> registerDevice(DeviceModel device) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      return await apiClient.post<DeviceModel>(
        endpoint: '/devices',
        data: device.toJson(),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => DeviceModel.fromJson(data['data']),
      );
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to register device: $e');
    }
  }

  @override
  Future<List<DeviceModel>> getUserDevices(String userId) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      return await apiClient.get<List<DeviceModel>>(
        endpoint: '/users/$userId/devices',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) {
          final List<dynamic> devicesJson = data['data'];
          return devicesJson.map((json) => DeviceModel.fromJson(json)).toList();
        },
      );
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to fetch user devices: $e');
    }
  }

  @override
  Future<bool> updateDeviceStatus(String deviceId, bool isActive) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      await apiClient.patch<Map<String, dynamic>>(
        endpoint: '/devices/$deviceId',
        data: {'is_active': isActive},
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => data as Map<String, dynamic>,
      );
      return true;
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to update device status: $e');
    }
  }

  @override
  Future<bool> deleteDevice(String deviceId) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      await apiClient.delete<Map<String, dynamic>>(
        endpoint: '/devices/$deviceId',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => data as Map<String, dynamic>,
      );
      return true;
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to delete device: $e');
    }
  }

  @override
  Future<bool> sendPushNotification(NotificationModel notification) async {
    final token = await secureStorage.getAccessToken();
    if (token == null) {
      throw const UnauthorizedException('No access token found');
    }

    try {
      await apiClient.post<Map<String, dynamic>>(
        endpoint: '/notifications/push',
        data: notification.toJson(),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        fromData: (data) => data as Map<String, dynamic>,
      );
      return true;
    } catch (e) {
      if (e is UnauthorizedException || e is ServerException) {
        rethrow;
      }
      throw ServerException(message: 'Failed to send push notification: $e');
    }
  }
}
