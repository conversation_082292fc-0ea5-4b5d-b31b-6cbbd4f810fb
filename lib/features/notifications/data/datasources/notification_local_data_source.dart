import 'dart:convert';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:timezone/timezone.dart' as tz;
import '../../../../core/error/exceptions.dart';
import '../models/notification_model.dart';
import '../models/notification_preferences_model.dart';

/// Local data source for notification operations
abstract class NotificationLocalDataSource {
  Future<bool> scheduleLocalNotification(NotificationModel notification);
  Future<bool> cancelLocalNotification(int notificationId);
  Future<bool> cancelAllLocalNotifications();

  Future<String?> getFCMToken();
  Future<bool> requestNotificationPermissions();

  Future<void> cacheNotificationPreferences(
    NotificationPreferencesModel preferences,
  );
  Future<NotificationPreferencesModel?> getCachedNotificationPreferences(
    String userId,
  );

  Future<void> cacheNotifications(
    List<NotificationModel> notifications,
    String userId,
  );
  Future<List<NotificationModel>> getCachedNotifications(String userId);
}

/// Implementation of notification local data source
class NotificationLocalDataSourceImpl implements NotificationLocalDataSource {
  final FlutterLocalNotificationsPlugin localNotifications;
  final FirebaseMessaging firebaseMessaging;
  final StorageService storageService;

  static const String _notificationPrefsKey = 'notification_preferences_';
  static const String _cachedNotificationsKey = 'cached_notifications_';

  NotificationLocalDataSourceImpl({
    required this.localNotifications,
    required this.firebaseMessaging,
    required this.storageService,
  });

  @override
  Future<bool> scheduleLocalNotification(NotificationModel notification) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'power_up_notifications',
        'Power Up Notifications',
        channelDescription: 'Notifications for Power Up app',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        enableLights: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final notificationId = notification.id.hashCode;

      if (notification.scheduledAt != null) {
        // Schedule notification for future delivery
        final scheduledDate = tz.TZDateTime.from(
          notification.scheduledAt!,
          tz.local,
        );
        await localNotifications.zonedSchedule(
          notificationId,
          notification.title,
          notification.body,
          scheduledDate,
          notificationDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
          payload: json.encode(notification.toJson()),
        );
      } else {
        // Show immediate notification
        await localNotifications.show(
          notificationId,
          notification.title,
          notification.body,
          notificationDetails,
          payload: json.encode(notification.toJson()),
        );
      }

      return true;
    } catch (e) {
      throw CacheException(
        message: 'Failed to schedule local notification: $e',
      );
    }
  }

  @override
  Future<bool> cancelLocalNotification(int notificationId) async {
    try {
      await localNotifications.cancel(notificationId);
      return true;
    } catch (e) {
      throw CacheException(message: 'Failed to cancel local notification: $e');
    }
  }

  @override
  Future<bool> cancelAllLocalNotifications() async {
    try {
      await localNotifications.cancelAll();
      return true;
    } catch (e) {
      throw CacheException(
        message: 'Failed to cancel all local notifications: $e',
      );
    }
  }

  @override
  Future<String?> getFCMToken() async {
    try {
      return await firebaseMessaging.getToken();
    } catch (e) {
      throw CacheException(message: 'Failed to get FCM token: $e');
    }
  }

  @override
  Future<bool> requestNotificationPermissions() async {
    try {
      // Request local notification permissions
      final localPermissionResult =
          await localNotifications
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >()
              ?.requestNotificationsPermission();

      // Request FCM permissions
      final fcmSettings = await firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      return (localPermissionResult ?? true) &&
          fcmSettings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      throw CacheException(
        message: 'Failed to request notification permissions: $e',
      );
    }
  }

  @override
  Future<void> cacheNotificationPreferences(
    NotificationPreferencesModel preferences,
  ) async {
    try {
      final key = '$_notificationPrefsKey${preferences.userId}';
      final jsonString = json.encode(preferences.toJson());
      await storageService.setData(key, jsonString);
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache notification preferences: $e',
      );
    }
  }

  @override
  Future<NotificationPreferencesModel?> getCachedNotificationPreferences(
    String userId,
  ) async {
    try {
      final key = '$_notificationPrefsKey$userId';
      final jsonString = storageService.getData(key);

      if (jsonString == null) return null;

      final jsonData = json.decode(jsonString);
      return NotificationPreferencesModel.fromJson(jsonData);
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached notification preferences: $e',
      );
    }
  }

  @override
  Future<void> cacheNotifications(
    List<NotificationModel> notifications,
    String userId,
  ) async {
    try {
      final key = '$_cachedNotificationsKey$userId';
      final jsonList = notifications.map((n) => n.toJson()).toList();
      final jsonString = json.encode(jsonList);
      await storageService.setData(key, jsonString);
    } catch (e) {
      throw CacheException(message: 'Failed to cache notifications: $e');
    }
  }

  @override
  Future<List<NotificationModel>> getCachedNotifications(String userId) async {
    try {
      final key = '$_cachedNotificationsKey$userId';
      final jsonString = storageService.getData(key);

      if (jsonString == null) return [];

      final jsonList = json.decode(jsonString) as List<dynamic>;
      return jsonList.map((json) => NotificationModel.fromJson(json)).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get cached notifications: $e');
    }
  }
}
