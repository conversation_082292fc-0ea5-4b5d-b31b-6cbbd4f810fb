import 'notification_model.dart';
import '../../domain/entities/notification_response_entity.dart';

/// Response model for paginated notifications API
class NotificationResponseModel {
  final List<NotificationModel> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  const NotificationResponseModel({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  /// Create NotificationResponseModel from JSON
  factory NotificationResponseModel.fromJson(Map<String, dynamic> json) {
    final List<dynamic> dataJson = json['data'] as List<dynamic>;
    final notifications =
        dataJson
            .map(
              (item) =>
                  NotificationModel.fromJson(item as Map<String, dynamic>),
            )
            .toList();

    return NotificationResponseModel(
      data: notifications,
      total: json['total'] as int,
      page: json['page'] as int,
      limit: json['limit'] as int,
      totalPages: json['totalPages'] as int,
      hasNext: json['hasNext'] as bool,
      hasPrev: json['hasPrev'] as bool,
    );
  }

  /// Convert NotificationResponseModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'data': data.map((notification) => notification.toJson()).toList(),
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
      'hasNext': hasNext,
      'hasPrev': hasPrev,
    };
  }

  /// Convert to domain entity
  NotificationResponseEntity toEntity() {
    return NotificationResponseEntity(
      data: data.map((notification) => notification.toEntity()).toList(),
      total: total,
      page: page,
      limit: limit,
      totalPages: totalPages,
      hasNext: hasNext,
      hasPrev: hasPrev,
    );
  }
}
