import '../../domain/entities/notification_entity.dart';

/// Data model for notification with serialization capabilities
class NotificationModel extends NotificationEntity {
  const NotificationModel({
    required super.id,
    required super.title,
    required super.body,
    required super.type,
    required super.createdAt,
    super.status,
    super.scheduledAt,
    super.sentAt,
    super.data,
    super.isRead,
    super.userId,
    super.actionUrl,
  });

  /// Create NotificationModel from JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      type: NotificationType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => NotificationType.systemUpdate,
      ),
      status: NotificationStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => NotificationStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      scheduledAt:
          json['scheduledAt'] != null
              ? DateTime.parse(json['scheduledAt'] as String)
              : null,
      sentAt:
          json['sentAt'] != null
              ? DateTime.parse(json['sentAt'] as String)
              : null,
      data: json['data'] as Map<String, dynamic>?,
      isRead: json['isRead'] as bool? ?? false,
      userId: json['userId'] as String?,
      actionUrl: json['actionUrl'] as String?,
    );
  }

  /// Convert NotificationModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'scheduledAt': scheduledAt?.toIso8601String(),
      'sentAt': sentAt?.toIso8601String(),
      'data': data,
      'isRead': isRead,
      'userId': userId,
      'actionUrl': actionUrl,
    };
  }

  /// Create NotificationModel from domain entity
  factory NotificationModel.fromEntity(NotificationEntity entity) {
    return NotificationModel(
      id: entity.id,
      title: entity.title,
      body: entity.body,
      type: entity.type,
      status: entity.status,
      createdAt: entity.createdAt,
      scheduledAt: entity.scheduledAt,
      sentAt: entity.sentAt,
      data: entity.data,
      isRead: entity.isRead,
      userId: entity.userId,
      actionUrl: entity.actionUrl,
    );
  }

  /// Convert to domain entity
  NotificationEntity toEntity() {
    return NotificationEntity(
      id: id,
      title: title,
      body: body,
      type: type,
      status: status,
      createdAt: createdAt,
      scheduledAt: scheduledAt,
      sentAt: sentAt,
      data: data,
      isRead: isRead,
      userId: userId,
      actionUrl: actionUrl,
    );
  }
}
