import '../../domain/entities/notification_preferences_entity.dart';

/// Data model for notification preferences with serialization capabilities
class NotificationPreferencesModel extends NotificationPreferencesEntity {
  const NotificationPreferencesModel({
    required super.userId,
    super.taskReminders,
    super.habitReminders,
    super.streakAlerts,
    super.milestoneCelebrations,
    super.challengeUpdates,
    super.communityUpdates,
    super.podcastNotifications,
    super.systemUpdates,
    super.inAppMessaging,
    super.quietHoursStart,
    super.quietHoursEnd,
    required super.updatedAt,
  });

  /// Create NotificationPreferencesModel from JSON
  factory NotificationPreferencesModel.fromJson(Map<String, dynamic> json) {
    return NotificationPreferencesModel(
      userId: json['user_id'] as String,
      taskReminders: json['task_reminders'] as bool? ?? true,
      habitReminders: json['habit_reminders'] as bool? ?? true,
      streakAlerts: json['streak_alerts'] as bool? ?? true,
      milestoneCelebrations: json['milestone_celebrations'] as bool? ?? true,
      challengeUpdates: json['challenge_updates'] as bool? ?? true,
      communityUpdates: json['community_updates'] as bool? ?? false,
      podcastNotifications: json['podcast_notifications'] as bool? ?? true,
      systemUpdates: json['system_updates'] as bool? ?? true,
      inAppMessaging: json['in_app_messaging'] as bool? ?? true,
      quietHoursStart:
          json['quiet_hours_start'] != null
              ? DateTime.parse(json['quiet_hours_start'] as String)
              : null,
      quietHoursEnd:
          json['quiet_hours_end'] != null
              ? DateTime.parse(json['quiet_hours_end'] as String)
              : null,
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert NotificationPreferencesModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'task_reminders': taskReminders,
      'habit_reminders': habitReminders,
      'streak_alerts': streakAlerts,
      'milestone_celebrations': milestoneCelebrations,
      'challenge_updates': challengeUpdates,
      'community_updates': communityUpdates,
      'podcast_notifications': podcastNotifications,
      'system_updates': systemUpdates,
      'in_app_messaging': inAppMessaging,
      'quiet_hours_start': quietHoursStart?.toIso8601String(),
      'quiet_hours_end': quietHoursEnd?.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create NotificationPreferencesModel from domain entity
  factory NotificationPreferencesModel.fromEntity(
    NotificationPreferencesEntity entity,
  ) {
    return NotificationPreferencesModel(
      userId: entity.userId,
      taskReminders: entity.taskReminders,
      habitReminders: entity.habitReminders,
      streakAlerts: entity.streakAlerts,
      milestoneCelebrations: entity.milestoneCelebrations,
      challengeUpdates: entity.challengeUpdates,
      communityUpdates: entity.communityUpdates,
      podcastNotifications: entity.podcastNotifications,
      systemUpdates: entity.systemUpdates,
      inAppMessaging: entity.inAppMessaging,
      quietHoursStart: entity.quietHoursStart,
      quietHoursEnd: entity.quietHoursEnd,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to domain entity
  NotificationPreferencesEntity toEntity() {
    return NotificationPreferencesEntity(
      userId: userId,
      taskReminders: taskReminders,
      habitReminders: habitReminders,
      streakAlerts: streakAlerts,
      milestoneCelebrations: milestoneCelebrations,
      challengeUpdates: challengeUpdates,
      communityUpdates: communityUpdates,
      podcastNotifications: podcastNotifications,
      systemUpdates: systemUpdates,
      inAppMessaging: inAppMessaging,
      quietHoursStart: quietHoursStart,
      quietHoursEnd: quietHoursEnd,
      updatedAt: updatedAt,
    );
  }
}
