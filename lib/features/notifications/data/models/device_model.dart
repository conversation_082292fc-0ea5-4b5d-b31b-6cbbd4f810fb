import '../../domain/entities/device_entity.dart';

/// Data model for device with serialization capabilities
class DeviceModel extends DeviceEntity {
  const DeviceModel({
    required super.id,
    required super.userId,
    required super.deviceToken,
    required super.deviceType,
    super.deviceName,
    required super.registeredAt,
    required super.lastUsed,
    super.isActive,
  });

  /// Create DeviceModel from JSON
  factory DeviceModel.fromJson(Map<String, dynamic> json) {
    return DeviceModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      deviceToken: json['device_token'] as String,
      deviceType: DeviceType.values.firstWhere(
        (e) => e.name == json['device_type'],
        orElse: () => DeviceType.android,
      ),
      deviceName: json['device_name'] as String?,
      registeredAt: DateTime.parse(json['registered_at'] as String),
      lastUsed: DateTime.parse(json['last_used'] as String),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// Convert DeviceModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'device_token': deviceToken,
      'device_type': deviceType.name,
      'device_name': deviceName,
      'registered_at': registeredAt.toIso8601String(),
      'last_used': lastUsed.toIso8601String(),
      'is_active': isActive,
    };
  }

  /// Create DeviceModel from domain entity
  factory DeviceModel.fromEntity(DeviceEntity entity) {
    return DeviceModel(
      id: entity.id,
      userId: entity.userId,
      deviceToken: entity.deviceToken,
      deviceType: entity.deviceType,
      deviceName: entity.deviceName,
      registeredAt: entity.registeredAt,
      lastUsed: entity.lastUsed,
      isActive: entity.isActive,
    );
  }

  /// Convert to domain entity
  DeviceEntity toEntity() {
    return DeviceEntity(
      id: id,
      userId: userId,
      deviceToken: deviceToken,
      deviceType: deviceType,
      deviceName: deviceName,
      registeredAt: registeredAt,
      lastUsed: lastUsed,
      isActive: isActive,
    );
  }
}
