import 'package:flutter/foundation.dart';

import '../../../../core/constants/api_constants.dart';
import '../../../../core/error/exceptions.dart';
import '../../../core/data/api/api_client.dart';
import '../models/app_constants_model.dart';
import 'app_constants_remote_data_source.dart';

/// Implementation of app constants remote data source
class AppConstantsRemoteDataSourceImpl implements AppConstantsRemoteDataSource {
  final ApiClient apiClient;

  AppConstantsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<AppConstantsModel> getAppConstants() async {
    try {
      if (kDebugMode) {
        print('Fetching app constants from: ${ApiConstants.adminAppConstantsEndpoint}');
      }

      return await apiClient.get<AppConstantsModel>(
        endpoint: ApiConstants.adminAppConstantsEndpoint,
        headers: {'Content-Type': 'application/json'},
        fromData: (data) {
          if (kDebugMode) {
            print('Received app constants data: $data');
          }
          return AppConstantsModel.fromJson(data);
        },
      );
    } on ServerException {
      if (kDebugMode) {
        print('Server exception while fetching app constants');
      }
      rethrow;
    } on NetworkException {
      if (kDebugMode) {
        print('Network exception while fetching app constants');
      }
      rethrow;
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error while fetching app constants: $e');
      }
      throw ServerException(
        message: 'Failed to fetch app constants: ${e.toString()}',
      );
    }
  }
}
