import 'package:get/get.dart';

import '../../../core/network/network_info.dart';
import '../../../core/services/app_constants_service.dart';
import '../../core/data/api/api_client.dart';
import '../data/datasources/app_constants_remote_data_source.dart';
import '../data/datasources/app_constants_remote_data_source_impl.dart';
import '../data/repositories/app_constants_repository_impl.dart';
import '../domain/repositories/app_constants_repository.dart';
import '../domain/usecases/clear_app_constants_cache_usecase.dart';
import '../domain/usecases/get_app_constants_usecase.dart';
import '../domain/usecases/get_cached_app_constants_usecase.dart';
import '../presentation/controllers/app_constants_controller.dart';

/// Dependency injection bindings for app constants module
class AppConstantsBindings extends Bindings {
  @override
  void dependencies() {
    // Data sources
    Get.lazyPut<AppConstantsRemoteDataSource>(
      () => AppConstantsRemoteDataSourceImpl(
        apiClient: Get.find<ApiClient>(),
      ),
    );

    // Repositories
    Get.lazyPut<AppConstantsRepository>(
      () => AppConstantsRepositoryImpl(
        remoteDataSource: Get.find<AppConstantsRemoteDataSource>(),
        networkInfo: Get.find<NetworkInfo>(),
      ),
    );

    // Use cases
    Get.lazyPut(
      () => GetAppConstantsUseCase(Get.find<AppConstantsRepository>()),
    );
    Get.lazyPut(
      () => GetCachedAppConstantsUseCase(Get.find<AppConstantsRepository>()),
    );
    Get.lazyPut(
      () => ClearAppConstantsCacheUseCase(Get.find<AppConstantsRepository>()),
    );

    // Service (singleton)
    Get.put<AppConstantsService>(
      AppConstantsService(
        getAppConstantsUseCase: Get.find<GetAppConstantsUseCase>(),
        getCachedAppConstantsUseCase: Get.find<GetCachedAppConstantsUseCase>(),
        clearAppConstantsCacheUseCase: Get.find<ClearAppConstantsCacheUseCase>(),
      ),
      permanent: true,
    );

    // Controller
    Get.lazyPut(
      () => AppConstantsController(
        appConstantsService: Get.find<AppConstantsService>(),
      ),
    );
  }
}
