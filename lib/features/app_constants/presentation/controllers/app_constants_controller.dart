import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../../../../core/services/app_constants_service.dart';
import '../../domain/entities/app_constants_entity.dart';

/// Controller for managing app constants in the UI
class AppConstantsController extends GetxController {
  final AppConstantsService _appConstantsService;

  AppConstantsController({required AppConstantsService appConstantsService})
    : _appConstantsService = appConstantsService;

  // Getters that delegate to the service
  AppConstantsEntity? get appConstants => _appConstantsService.appConstants;
  bool get isLoading => _appConstantsService.isLoading;
  bool get isInitialized => _appConstantsService.isInitialized;
  String get errorMessage => _appConstantsService.errorMessage;

  // Observable getters for reactive UI
  Rx<AppConstantsEntity?> get appConstantsRx =>
      _appConstantsService.appConstantsRx;
  RxBool get isLoadingRx => _appConstantsService.isLoadingRx;
  RxBool get isInitializedRx => _appConstantsService.isInitializedRx;
  RxString get errorMessageRx => _appConstantsService.errorMessageRx;

  @override
  void onInit() {
    super.onInit();
    if (kDebugMode) {
      print('AppConstantsController initialized');
    }
  }

  /// Refresh app constants from the backend
  Future<void> refreshConstants() async {
    await _appConstantsService.refreshConstants();
  }

  /// Clear cache and refresh constants
  Future<void> clearCacheAndRefresh() async {
    await _appConstantsService.clearCacheAndRefresh();
  }

  /// Show success message
  void showSuccessMessage(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  /// Show error message
  void showErrorMessage(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  // Convenience methods that delegate to the service
  String getString(String key, [String? defaultValue]) {
    return _appConstantsService.getString(key, defaultValue);
  }

  bool getBool(String key, [bool defaultValue = false]) {
    return _appConstantsService.getBool(key, defaultValue);
  }

  int getInt(String key, [int defaultValue = 0]) {
    return _appConstantsService.getInt(key, defaultValue);
  }

  double getDouble(String key, [double defaultValue = 0.0]) {
    return _appConstantsService.getDouble(key, defaultValue);
  }

  List<T>? getList<T>(String key, [List<T>? defaultValue]) {
    return _appConstantsService.getList<T>(key, defaultValue);
  }

  Map<String, dynamic>? getMap(
    String key, [
    Map<String, dynamic>? defaultValue,
  ]) {
    return _appConstantsService.getMap(key, defaultValue);
  }

  T? getConstant<T>(String key, [T? defaultValue]) {
    return _appConstantsService.getConstant<T>(key, defaultValue);
  }

  // Feature flags convenience methods
  bool get enableSocialLogin => _appConstantsService.enableSocialLogin;
  bool get enablePushNotifications =>
      _appConstantsService.enablePushNotifications;
  bool get enablePremiumFeatures => _appConstantsService.enablePremiumFeatures;
  bool get enableDarkMode => _appConstantsService.enableDarkMode;
  bool get enableOfflineMode => _appConstantsService.enableOfflineMode;

  // API configuration convenience methods
  String get apiBaseUrl => _appConstantsService.apiBaseUrl;
  int get apiConnectTimeout => _appConstantsService.apiConnectTimeout;
  int get apiReceiveTimeout => _appConstantsService.apiReceiveTimeout;

  // UI configuration convenience methods
  String get appName => _appConstantsService.appName;
  String get appVersion => _appConstantsService.appVersion;
  double get splashLogoSize => _appConstantsService.splashLogoSize;
  double get appBarLogoHeight => _appConstantsService.appBarLogoHeight;
  double get pageSpacing => _appConstantsService.pageSpacing;
  double get sectionSpacing => _appConstantsService.sectionSpacing;

  // Gamification convenience methods
  int get xpPerHabit => _appConstantsService.xpPerHabit;
  int get xpPerTask => _appConstantsService.xpPerTask;
  int get xpPerChallenge => _appConstantsService.xpPerChallenge;
  int get levelUpThreshold => _appConstantsService.levelUpThreshold;

  // Limits convenience methods
  int get maxHabitsPerUser => _appConstantsService.maxHabitsPerUser;
  int get maxTasksPerUser => _appConstantsService.maxTasksPerUser;
  int get maxChallengesPerUser => _appConstantsService.maxChallengesPerUser;

  // Store URLs convenience methods
  String get appStoreUrl => _appConstantsService.appStoreUrl;
  String get playStoreUrl => _appConstantsService.playStoreUrl;

  // Force update convenience methods
  bool get forceUpdateEnabled => _appConstantsService.forceUpdateEnabled;
  String get forceUpdateMinimumVersion =>
      _appConstantsService.forceUpdateMinimumVersion;
  String get forceUpdateCurrentVersion =>
      _appConstantsService.forceUpdateCurrentVersion;
  String get forceUpdateTitle => _appConstantsService.forceUpdateTitle;
  String get forceUpdateMessage => _appConstantsService.forceUpdateMessage;
  String get forceUpdateButtonText =>
      _appConstantsService.forceUpdateButtonText;
}
