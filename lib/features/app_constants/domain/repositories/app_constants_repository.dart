import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/app_constants_entity.dart';

/// Repository interface for app constants
abstract class AppConstantsRepository {
  /// Fetch app constants from remote source
  Future<Either<Failure, AppConstantsEntity>> getAppConstants();
  
  /// Get cached app constants if available
  AppConstantsEntity? getCachedAppConstants();
  
  /// Clear cached app constants
  Future<void> clearCache();
}
