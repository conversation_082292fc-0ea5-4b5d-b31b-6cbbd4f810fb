import 'package:equatable/equatable.dart';

/// Entity representing app constants from the backend
class AppConstantsEntity extends Equatable {
  final String id;
  final Map<String, dynamic> constants;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AppConstantsEntity({
    required this.id,
    required this.constants,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Get a constant value by key with optional default value
  T? getConstant<T>(String key, [T? defaultValue]) {
    try {
      final value = _getNestedValue(constants, key);
      if (value is T) {
        return value;
      }
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  /// Get a nested value from the constants map using dot notation
  /// Example: getConstant('features.socialLogin') for nested objects
  dynamic _getNestedValue(Map<String, dynamic> map, String key) {
    final keys = key.split('.');
    dynamic current = map;
    
    for (final k in keys) {
      if (current is Map<String, dynamic> && current.containsKey(k)) {
        current = current[k];
      } else {
        return null;
      }
    }
    
    return current;
  }

  /// Get string constant
  String? getString(String key, [String? defaultValue]) {
    return getConstant<String>(key, defaultValue);
  }

  /// Get boolean constant
  bool getBool(String key, [bool defaultValue = false]) {
    return getConstant<bool>(key, defaultValue) ?? defaultValue;
  }

  /// Get integer constant
  int getInt(String key, [int defaultValue = 0]) {
    return getConstant<int>(key, defaultValue) ?? defaultValue;
  }

  /// Get double constant
  double getDouble(String key, [double defaultValue = 0.0]) {
    return getConstant<double>(key, defaultValue) ?? defaultValue;
  }

  /// Get list constant
  List<T>? getList<T>(String key, [List<T>? defaultValue]) {
    final value = getConstant<List<dynamic>>(key);
    if (value != null) {
      try {
        return value.cast<T>();
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// Get map constant
  Map<String, dynamic>? getMap(String key, [Map<String, dynamic>? defaultValue]) {
    return getConstant<Map<String, dynamic>>(key, defaultValue);
  }

  @override
  List<Object?> get props => [id, constants, createdAt, updatedAt];

  @override
  String toString() {
    return 'AppConstantsEntity(id: $id, constants: $constants, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
