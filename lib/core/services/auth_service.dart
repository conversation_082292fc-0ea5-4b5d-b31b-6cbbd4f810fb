import 'package:get/get.dart';
import 'package:power_up/core/util/app_constants.dart';
import 'package:power_up/features/core/data/local/storage_service.dart';
import 'package:power_up/core/services/secure_storage_service.dart';

/// Simple auth user class for compatibility
class AuthUser {
  final String uid;
  final String? email;

  AuthUser({required this.uid, this.email});
}

/// Service for managing authentication state
class AuthService extends GetxService {
  final Rx<String?> _currentUserId = Rx<String?>(null);
  final Rx<AuthUser?> _currentUser = Rx<AuthUser?>(null);

  late final SecureStorageService _secureStorageService;

  /// Get the current user ID
  String? get currentUserId => _currentUserId.value;

  /// Get the current user (for compatibility with controller)
  Rx<AuthUser?> get currentUser => _currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUserId.value != null;

  /// Observable stream of authentication state
  Stream<String?> get authStateChanges => _currentUserId.stream;

  /// Set the current user ID (placeholder method)
  void getCurrentUser() async {
    // Try secure storage first, then fall back to regular storage
    String? userId = await _secureStorageService.getUserId();

    if (userId == null || userId.isEmpty) {
      userId = Get.find<StorageService>().getData<String>(
        AppConstants.storageUserId,
      );
    }

    _currentUserId.value = userId;
    if (userId != null) {
      // Get user email from secure storage first, then regular storage
      String? userEmail = await _secureStorageService.getEmail();
      if (userEmail == null || userEmail.isEmpty) {
        userEmail = Get.find<StorageService>().getData<String>(
          AppConstants.storageUserEmail,
        );
      }

      _currentUser.value = AuthUser(
        uid: userId,
        email: userEmail ?? '<EMAIL>',
      );
    } else {
      _currentUser.value = null;
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    _currentUserId.value = null;
    _currentUser.value = null;
    // Clear secure storage as well
    await _secureStorageService.clearAll();
  }

  /// Initialize the auth service
  @override
  void onInit() {
    super.onInit();
    _secureStorageService = Get.find<SecureStorageService>();
    getCurrentUser();
  }
}
