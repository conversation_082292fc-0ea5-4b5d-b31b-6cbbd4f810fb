import 'dart:async';
import 'package:get/get.dart';
import 'package:power_up/core/error/failures.dart';
import 'package:power_up/core/network/network_info.dart';
import 'package:power_up/features/tasks/data/datasources/task_local_data_source.dart';
import 'package:power_up/features/tasks/data/datasources/task_remote_data_source.dart';
import 'package:power_up/features/habits/data/datasources/habit_local_data_source.dart';
import 'package:power_up/features/habits/data/datasources/habit_remote_data_source.dart';

/// Service responsible for data synchronization between local and remote sources
class DataSyncService {
  final TaskLocalDataSource _taskLocalDataSource;
  final TaskRemoteDataSource _taskRemoteDataSource;
  final HabitLocalDataSource _habitLocalDataSource;
  final HabitRemoteDataSource _habitRemoteDataSource;
  final NetworkInfo _networkInfo;

  // Observables for sync status
  final RxBool isSyncing = false.obs;
  final RxString lastSyncTime = ''.obs;
  final RxBool hasError = false.obs;
  final RxString syncMessage = ''.obs;

  // Constructor
  DataSyncService({
    required TaskLocalDataSource taskLocalDataSource,
    required TaskRemoteDataSource taskRemoteDataSource,
    required HabitLocalDataSource habitLocalDataSource,
    required HabitRemoteDataSource habitRemoteDataSource,
    required NetworkInfo networkInfo,
  }) : _taskLocalDataSource = taskLocalDataSource,
       _taskRemoteDataSource = taskRemoteDataSource,
       _habitLocalDataSource = habitLocalDataSource,
       _habitRemoteDataSource = habitRemoteDataSource,
       _networkInfo = networkInfo;

  /// Start periodic sync in the background
  void startPeriodicSync({Duration interval = const Duration(minutes: 15)}) {
    // Initial sync
    syncData();

    // Set up periodic sync
    Timer.periodic(interval, (_) => syncData());
  }

  /// Manually trigger data synchronization
  Future<bool> syncData() async {
    // Return if already syncing or no connection
    if (isSyncing.value) return false;
    if (!await _networkInfo.isConnected) {
      hasError.value = true;
      syncMessage.value = 'No internet connection available';
      return false;
    }

    isSyncing.value = true;
    hasError.value = false;
    syncMessage.value = 'Syncing data...';

    try {
      // Sync tasks
      await _syncTasks();

      // Sync habits
      await _syncHabits();

      // Update last sync time
      final now = DateTime.now();
      lastSyncTime.value = _formatDateTime(now);
      syncMessage.value = 'Data synchronized successfully';

      isSyncing.value = false;
      return true;
    } catch (e) {
      hasError.value = true;
      syncMessage.value = 'Failed to sync: ${e.toString()}';
      isSyncing.value = false;
      return false;
    }
  }

  /// Sync tasks between local and remote storage
  Future<void> _syncTasks() async {
    try {
      // 1. Upload locally created tasks (with temporary IDs) to remote
      final localOnlyTasks = await _taskLocalDataSource.getLocalOnlyTasks();
      for (final task in localOnlyTasks) {
        // Create task remotely
        final remoteTask = await _taskRemoteDataSource.createTask(
          title: task.title,
          description: task.description,
          dueDate: task.dueDate,
          priority: task.priority,
        );

        // Delete local temp task and save the proper one
        await _taskLocalDataSource.deleteTask(task.id);
        await _taskLocalDataSource.saveTask(remoteTask);
      }

      // 2. Upload locally modified tasks to remote
      final modifiedTasks = await _taskLocalDataSource.getModifiedTasks();
      for (final task in modifiedTasks) {
        await _taskRemoteDataSource.updateTask(
          id: task.id,
          title: task.title,
          description: task.description,
          dueDate: task.dueDate,
          priority: task.priority,
        );

        // Update completion status separately
        if (task.isCompleted) {
          await _taskRemoteDataSource.markTaskComplete(task.id);
        } else {
          await _taskRemoteDataSource.markTaskIncomplete(task.id);
        }

        // Mark as synced
        await _taskLocalDataSource.markTaskSynced(task.id);
      }

      // 3. Download all latest tasks from remote to ensure consistency
      final remoteTasks = await _taskRemoteDataSource.getTasks();
      await _taskLocalDataSource.saveTasks(remoteTasks);
    } catch (e) {
      throw ServerFailure(message: 'Task sync failed: ${e.toString()}');
    }
  }

  /// Sync habits between local and remote storage
  Future<void> _syncHabits() async {
    try {
      // 1. Upload locally created habits to remote
      final localOnlyHabits = await _habitLocalDataSource.getLocalOnlyHabits();
      for (final habit in localOnlyHabits) {
        // Create habit remotely
        final remoteHabit = await _habitRemoteDataSource.createHabit(
          name: habit.name,
          description: habit.description,
          frequency: habit.frequency,
          reminderSettings: habit.reminderSettings,
          customDays: habit.customDays,
        );

        // Delete local temp habit and save the proper one
        await _habitLocalDataSource.deleteHabit(habit.id);
        await _habitLocalDataSource.saveHabit(remoteHabit);
      }

      // 2. Upload locally modified habits to remote
      final modifiedHabits = await _habitLocalDataSource.getModifiedHabits();
      for (final habit in modifiedHabits) {
        await _habitRemoteDataSource.updateHabit(
          id: habit.id,
          name: habit.name,
          description: habit.description,
          frequency: habit.frequency,
          reminderSettings: habit.reminderSettings,
          customDays: habit.customDays,
        );

        // Mark as synced
        await _habitLocalDataSource.markHabitSynced(habit.id);
      }

      // 3. Sync habit completions
      await _syncHabitCompletions();

      // 4. Download all latest habits from remote to ensure consistency
      final remoteHabits = await _habitRemoteDataSource.getHabits();
      await _habitLocalDataSource.saveHabits(remoteHabits);
    } catch (e) {
      throw ServerFailure(message: 'Habit sync failed: ${e.toString()}');
    }
  }

  /// Sync habit completions specifically (completion history)
  Future<void> _syncHabitCompletions() async {
    try {
      // Get local completions that need syncing
      final unsynced = await _habitLocalDataSource.getUnsyncedCompletions();

      // Send to server
      for (final completion in unsynced) {
        await _habitRemoteDataSource.markHabitComplete(completion.habitId);

        // Mark completion as synced
        await _habitLocalDataSource.markCompletionSynced(
          completion.habitId,
          completion.date,
        );
      }
    } catch (e) {
      throw ServerFailure(
        message: 'Habit completion sync failed: ${e.toString()}',
      );
    }
  }

  /// Format DateTime to a readable string
  String _formatDateTime(DateTime dateTime) {
    return '${_twoDigits(dateTime.day)}/${_twoDigits(dateTime.month)}/${dateTime.year} ${_twoDigits(dateTime.hour)}:${_twoDigits(dateTime.minute)}';
  }

  /// Format a number as two digits
  String _twoDigits(int n) {
    return n.toString().padLeft(2, '0');
  }
}
