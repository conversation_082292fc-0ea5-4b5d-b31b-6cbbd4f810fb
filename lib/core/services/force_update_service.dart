import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import 'app_constants_service.dart';

/// Service for handling force update functionality
class ForceUpdateService extends GetxService {
  final AppConstantsService _appConstantsService;

  ForceUpdateService({
    required AppConstantsService appConstantsService,
  }) : _appConstantsService = appConstantsService;

  /// Check if force update is required on app launch
  Future<void> checkForceUpdateOnLaunch() async {
    try {
      // Wait for app constants to be initialized
      if (!_appConstantsService.isInitialized) {
        if (kDebugMode) {
          print('Waiting for app constants to initialize...');
        }
        // Wait up to 10 seconds for initialization
        int attempts = 0;
        while (!_appConstantsService.isInitialized && attempts < 100) {
          await Future.delayed(const Duration(milliseconds: 100));
          attempts++;
        }
      }

      if (!_appConstantsService.forceUpdateEnabled) {
        if (kDebugMode) {
          print('Force update is disabled');
        }
        return;
      }

      final currentAppVersion = await _getCurrentAppVersion();
      final minimumRequiredVersion = _appConstantsService.forceUpdateMinimumVersion;

      if (kDebugMode) {
        print('Current app version: $currentAppVersion');
        print('Minimum required version: $minimumRequiredVersion');
      }

      if (_isUpdateRequired(currentAppVersion, minimumRequiredVersion)) {
        if (kDebugMode) {
          print('Force update required - showing dialog');
        }
        _showForceUpdateDialog();
      } else {
        if (kDebugMode) {
          print('App version is up to date');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking force update: $e');
      }
      // Don't block the app if force update check fails
    }
  }

  /// Get current app version from package info
  Future<String> _getCurrentAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting package info: $e');
      }
      // Return default version if package info fails
      return '1.0.0';
    }
  }

  /// Check if update is required by comparing versions
  bool _isUpdateRequired(String currentVersion, String minimumVersion) {
    try {
      final current = _parseVersion(currentVersion);
      final minimum = _parseVersion(minimumVersion);

      // Compare major.minor.patch
      for (int i = 0; i < 3; i++) {
        if (current[i] < minimum[i]) {
          return true;
        } else if (current[i] > minimum[i]) {
          return false;
        }
      }
      
      return false; // Versions are equal
    } catch (e) {
      if (kDebugMode) {
        print('Error comparing versions: $e');
      }
      // If version parsing fails, don't force update
      return false;
    }
  }

  /// Parse version string into [major, minor, patch] integers
  List<int> _parseVersion(String version) {
    final parts = version.split('.');
    final result = <int>[];
    
    for (int i = 0; i < 3; i++) {
      if (i < parts.length) {
        result.add(int.tryParse(parts[i]) ?? 0);
      } else {
        result.add(0);
      }
    }
    
    return result;
  }

  /// Show non-dismissible force update dialog
  void _showForceUpdateDialog() {
    Get.dialog(
      WillPopScope(
        onWillPop: () async => false, // Prevent back button dismissal
        child: AlertDialog(
          title: Text(_appConstantsService.forceUpdateTitle),
          content: Text(_appConstantsService.forceUpdateMessage),
          actions: [
            ElevatedButton(
              onPressed: _openAppStore,
              child: Text(_appConstantsService.forceUpdateButtonText),
            ),
          ],
        ),
      ),
      barrierDismissible: false, // Prevent tap outside dismissal
    );
  }

  /// Open appropriate app store for update
  Future<void> _openAppStore() async {
    try {
      String storeUrl;
      if (Platform.isIOS) {
        storeUrl = _appConstantsService.appStoreUrl;
      } else if (Platform.isAndroid) {
        storeUrl = _appConstantsService.playStoreUrl;
      } else {
        storeUrl = _appConstantsService.playStoreUrl; // Default to Play Store
      }

      final Uri url = Uri.parse(storeUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        if (kDebugMode) {
          print('Could not launch store URL: $storeUrl');
        }
        // Show error message to user
        Get.snackbar(
          'Error',
          'Could not open app store. Please update manually.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening app store: $e');
      }
      Get.snackbar(
        'Error',
        'Could not open app store. Please update manually.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Manual check for force update (can be called from settings)
  Future<bool> checkForceUpdate() async {
    try {
      if (!_appConstantsService.forceUpdateEnabled) {
        return false;
      }

      final currentAppVersion = await _getCurrentAppVersion();
      final minimumRequiredVersion = _appConstantsService.forceUpdateMinimumVersion;

      return _isUpdateRequired(currentAppVersion, minimumRequiredVersion);
    } catch (e) {
      if (kDebugMode) {
        print('Error in manual force update check: $e');
      }
      return false;
    }
  }

  /// Get current app version (public method)
  Future<String> getCurrentAppVersion() async {
    return await _getCurrentAppVersion();
  }
}
