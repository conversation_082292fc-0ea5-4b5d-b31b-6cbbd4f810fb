import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SyncDataService {
  // Singleton instance
  static final SyncDataService _instance = SyncDataService._internal();

  factory SyncDataService() {
    return _instance;
  }

  SyncDataService._internal();

  // Sync status observables
  final RxBool _isSyncing = false.obs;
  final RxString _lastSyncTime = ''.obs;
  final RxBool _hasConnectionError = false.obs;

  // Getters
  bool get isSyncing => _isSyncing.value;
  String get lastSyncTime => _lastSyncTime.value;
  bool get hasConnectionError => _hasConnectionError.value;

  /// Sync local data with remote server
  Future<bool> syncData() async {
    // Return if already syncing
    if (_isSyncing.value) {
      return false;
    }

    _isSyncing.value = true;
    _hasConnectionError.value = false;

    try {
      // Here you would implement the actual sync logic connecting to your backend
      // For example:
      // 1. Get all local tasks that have been modified since last sync
      // 2. Push them to the server
      // 3. Get all remote tasks that have been modified since last sync
      // 4. Update local storage with remote changes

      // Simulate a network delay
      await Future.delayed(const Duration(seconds: 2));

      // Update last sync time
      final now = DateTime.now();
      _lastSyncTime.value = _formatDateTime(now);

      // Save last sync time to persistent storage
      // await _storage.write(key: 'last_sync_time', value: now.toIso8601String());

      _isSyncing.value = false;
      return true;
    } catch (e) {
      debugPrint('Sync error: $e');
      _hasConnectionError.value = true;
      _isSyncing.value = false;
      return false;
    }
  }

  /// Start periodic sync in the background
  void startPeriodicSync({Duration interval = const Duration(minutes: 15)}) {
    // Sync immediately
    syncData();

    // Then sync periodically
    Future.doWhile(() async {
      await Future.delayed(interval);
      await syncData();
      return true; // Continue looping
    });
  }

  /// Format DateTime to a readable string
  String _formatDateTime(DateTime dateTime) {
    return '${_twoDigits(dateTime.day)}/${_twoDigits(dateTime.month)}/${dateTime.year} ${_twoDigits(dateTime.hour)}:${_twoDigits(dateTime.minute)}';
  }

  /// Format a number as two digits
  String _twoDigits(int n) {
    return n.toString().padLeft(2, '0');
  }
}
