import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../features/core/data/local/storage_service.dart';

/// Service for handling theme-related operations locally
class ThemeService extends GetxService {
  final StorageService _storageService;

  ThemeService(this._storageService);

  // Storage keys
  static const String _themeKey = 'theme_mode';
  static const String _isDarkModeKey = 'is_dark_mode';

  // Observable theme mode
  final _themeMode = ThemeMode.system.obs;
  final _isDarkMode = false.obs;

  // Getters
  ThemeMode get themeMode => _themeMode.value;
  bool get isDarkMode => _isDarkMode.value;
  String get themeModeString {
    switch (_themeMode.value) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  @override
  void onInit() {
    super.onInit();
    _loadThemeFromStorage();
  }

  /// Load theme preference from local storage
  void _loadThemeFromStorage() {
    final savedTheme = _storageService.getData<String>(_themeKey) ?? 'system';
    final savedIsDark = _storageService.getData<bool>(_isDarkModeKey) ?? false;

    _isDarkMode.value = savedIsDark;
    _updateThemeMode(savedTheme);
  }

  /// Update theme mode
  Future<void> updateThemeMode(String mode) async {
    await _storageService.setData(_themeKey, mode);
    _updateThemeMode(mode);
  }

  /// Update dark mode state (for switch UI)
  Future<void> updateDarkMode(bool isDark) async {
    _isDarkMode.value = isDark;
    await _storageService.setData(_isDarkModeKey, isDark);

    // Update theme mode based on dark mode preference
    final newMode = isDark ? 'dark' : 'light';
    await updateThemeMode(newMode);
  }

  /// Internal method to update theme mode
  void _updateThemeMode(String mode) {
    switch (mode.toLowerCase()) {
      case 'light':
        _themeMode.value = ThemeMode.light;
        _isDarkMode.value = false;
        break;
      case 'dark':
        _themeMode.value = ThemeMode.dark;
        _isDarkMode.value = true;
        break;
      case 'system':
        _themeMode.value = ThemeMode.system;
        // For system mode, check current brightness
        _isDarkMode.value = Get.isPlatformDarkMode;
        break;
      default:
        _themeMode.value = ThemeMode.system;
        _isDarkMode.value = Get.isPlatformDarkMode;
        break;
    }

    // Update GetX theme
    Get.changeThemeMode(_themeMode.value);
  }

  /// Toggle between light and dark mode
  Future<void> toggleTheme() async {
    final newIsDark = !_isDarkMode.value;
    await updateDarkMode(newIsDark);
  }

  /// Reset to system theme
  Future<void> resetToSystemTheme() async {
    await updateThemeMode('system');
  }
}
