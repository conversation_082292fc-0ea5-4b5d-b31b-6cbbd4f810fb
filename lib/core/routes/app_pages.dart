import 'package:get/get.dart';
import 'package:power_up/features/community/community.dart';

import '../../features/authentication/di/auth_bindings.dart';
import '../../features/authentication/presentation/screens/login_screen.dart';
import '../../features/authentication/presentation/screens/register_screen.dart';
import '../../features/authentication/presentation/screens/forgot_password_screen.dart';
import '../../features/core/presentation/screens/splash_screen.dart';
import '../../features/core/presentation/screens/home_screen.dart';
import '../../features/core/presentation/screens/onboarding_screen.dart';
import '../../features/core/presentation/screens/profile_screen.dart';
import '../../features/core/presentation/screens/notifications_screen.dart';
import '../../features/profile/presentation/screens/profile_edit_screen.dart';
import '../../features/profile/di/profile_bindings.dart';
import '../../features/habits/presentation/screens/habits_screen.dart';
import '../../features/habits/di/habits_bindings.dart';
import '../../features/tasks/presentation/screens/tasks_screen.dart';
import '../../features/tasks/di/tasks_bindings.dart';
import '../../features/calendar/presentation/screens/calendar_screen.dart';
import '../../features/podcasts/presentation/screens/podcasts_screen.dart';
import '../../features/podcasts/di/podcasts_bindings.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';
import '../../features/settings/di/settings_bindings.dart';
import '../../features/settings/presentation/screens/feedback_screen.dart';
import '../../features/settings/presentation/screens/help_screen.dart';
import '../../features/help/presentation/screens/help_article_detail_screen.dart';
import '../../features/settings/presentation/screens/legal_document_screen.dart';
import '../../features/help/presentation/bindings/help_bindings.dart';
import '../../features/statistics/presentation/screens/statistics_screen.dart';
import '../../features/statistics/di/statistics_bindings.dart';
import '../../features/gamification/presentation/screens/achievements_screen.dart';
import '../../features/gamification/di/gamification_bindings.dart';
import '../../features/skill_plans/presentation/screens/skill_plans_screen.dart';
import '../../features/skill_plans/presentation/screens/skill_plan_detail_screen.dart';
import '../../features/skill_plans/presentation/screens/create_custom_plan_screen.dart';
import '../../features/skill_plans/di/skill_plans_bindings.dart';
import '../../features/notifications/di/notification_bindings.dart';
import '../../features/user_management/di/user_management_bindings.dart';
import '../../features/focus_timer/presentation/screens/focus_timer_screen.dart';
import '../../features/focus_timer/di/focus_timer_bindings.dart';

import 'app_routes.dart';

/// App pages configuration for GetX routes
class AppPages {
  /// Private constructor to prevent instantiation
  AppPages._();

  static final routes = [
    GetPage(
      name: AppRoutes.splash,
      page: () => const SplashScreen(),
      binding: AuthBindings(),
      transition: Transition.fade,
    ),
    GetPage(
      name: AppRoutes.onboarding,
      page: () => const OnboardingScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginScreen(),
      binding: AuthBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.register,
      page: () => const RegisterScreen(),
      binding: AuthBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.forgotPassword,
      page: () => const ForgotPasswordScreen(),
      binding: AuthBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.home,
      page: () => const HomeScreen(),
      bindings: [AuthBindings()],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.habits,
      page: () => const HabitsScreen(),
      bindings: [AuthBindings(), HabitsBindings()],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.tasks,
      page: () => const TasksScreen(),
      bindings: [AuthBindings(), TasksBindings()],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.calendar,
      page: () => const CalendarScreen(),
      bindings: [AuthBindings()],
      transition: Transition.fadeIn,
    ),
    ...CommunityRoutes.pages,
    GetPage(
      name: AppRoutes.podcasts,
      page: () => const PodcastsScreen(),
      bindings: [AuthBindings(), PodcastsBindings()],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsScreen(),
      bindings: [
        AuthBindings(),
        SettingsBindings(),
        NotificationBindings(),
        UserManagementBindings(),
      ],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.profile,
      page: () => const ProfileScreen(),
      bindings: [AuthBindings(), ProfileBindings(), UserManagementBindings()],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.profileEdit,
      page: () => const ProfileEditScreen(),
      bindings: [AuthBindings(), ProfileBindings(), UserManagementBindings()],
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.notifications,
      page: () => const NotificationsScreen(),
      bindings: [AuthBindings(), NotificationBindings()],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.statistics,
      page: () => const StatisticsScreen(),
      bindings: [AuthBindings(), StatisticsBindings()],
      transition: Transition.fadeIn,
    ),

    GetPage(
      name: AppRoutes.focusTimer,
      page: () => const FocusTimerScreen(),
      bindings: [AuthBindings(), FocusTimerBindings()],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.achievements,
      page: () => const AchievementsScreen(),
      binding: GamificationBindings(),
      transition: Transition.fadeIn,
    ),
    // Skill Plans screens
    GetPage(
      name: AppRoutes.skillPlans,
      page: () => const SkillPlansScreen(),
      binding: SkillPlansBindings(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: AppRoutes.skillPlanDetail,
      page: () => const SkillPlanDetailScreen(),
      binding: SkillPlansBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.createCustomPlan,
      page: () => const CreateCustomPlanScreen(),
      binding: SkillPlansBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.feedback,
      page: () => const FeedbackScreen(),
      binding: SettingsBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.help,
      page: () => const HelpScreen(),
      binding: HelpBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.helpArticleDetail,
      page: () => const HelpArticleDetailScreen(),
      binding: HelpBindings(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: AppRoutes.legal,
      page: () => const LegalDocumentScreen(),
      binding: SettingsBindings(),
      transition: Transition.rightToLeft,
    ),
  ];
}
