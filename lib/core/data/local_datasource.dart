import 'datasource.dart';

/// Base LocalDataSource abstract class for local storage
abstract class LocalDataSource extends DataSource {
  /// Get data from local storage
  Future<T?> get<T>({
    required String key,
    required T Function(dynamic json) fromJson,
  });

  /// Save data to local storage
  Future<void> set<T>({required String key, required T data});

  /// Remove data from local storage
  Future<void> remove(String key);

  /// Clear all data from local storage
  Future<void> clear();
}
