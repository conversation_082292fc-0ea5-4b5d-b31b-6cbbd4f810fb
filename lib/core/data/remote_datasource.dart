import 'datasource.dart';

/// Base RemoteDataSource abstract class for API calls
abstract class RemoteDataSource extends DataSource {
  /// Generic method for handling API requests
  Future<T> request<T>({
    required String endpoint,
    required T Function(Map<String, dynamic> json) fromJson,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? body,
    String method = 'GET',
    Map<String, String>? headers,
  });
}
