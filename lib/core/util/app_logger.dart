import 'package:flutter/foundation.dart';

/// Utility class for logging
class AppLogger {
  /// Private constructor to prevent instantiation
  AppLogger._();

  /// Info level log
  static void i(String tag, String message) {
    if (kDebugMode) {
      print('📘 INFO [$tag] $message');
    }
  }

  /// Debug level log
  static void d(String tag, String message) {
    if (kDebugMode) {
      print('📗 DEBUG [$tag] $message');
    }
  }

  /// Warning level log
  static void w(String tag, String message) {
    if (kDebugMode) {
      print('📙 WARNING [$tag] $message');
    }
  }

  /// Error level log
  static void e(String tag, String message, [dynamic error]) {
    if (kDebugMode) {
      print('📕 ERROR [$tag] $message');
      if (error != null) {
        print('ERROR DETAILS: $error');
      }
    }
  }
}
