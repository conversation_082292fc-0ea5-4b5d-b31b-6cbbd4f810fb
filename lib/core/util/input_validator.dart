import 'package:get/get.dart';

import '../error/failures.dart';
import '../util/app_logger.dart';

/// Utility class for input validation
class InputValidator {
  /// Private constructor to prevent instantiation
  InputValidator._();

  /// The tag for logging
  static const String _tag = 'InputValidator';

  /// Validate email address format
  static bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// Validate password strength (min 8 chars, at least 1 letter and 1 number)
  static bool isValidPassword(String password) {
    if (password.length < 8) return false;

    final hasLetter = RegExp(r'[a-zA-Z]').hasMatch(password);
    final hasNumber = RegExp(r'[0-9]').hasMatch(password);

    return hasLetter && hasNumber;
  }

  /// Check if a string is null or empty
  static bool isNullOrEmpty(String? value) {
    return value == null || value.trim().isEmpty;
  }

  /// Validate a form field and return a failure if invalid
  static ValidationFailure? validateField({
    required String fieldName,
    required String? value,
    bool required = true,
    int? minLength,
    int? maxLength,
    String? Function(String?)? validator,
  }) {
    // Check if required
    if (required && isNullOrEmpty(value)) {
      final message = '$fieldName is required';
      AppLogger.i(_tag, message);
      return ValidationFailure(message: message);
    }

    // Skip other validations if null or empty and not required
    if (isNullOrEmpty(value)) {
      return null;
    }

    final nonNullValue = value!;

    // Check min length
    if (minLength != null && nonNullValue.length < minLength) {
      final message = '$fieldName must be at least $minLength characters';
      AppLogger.i(_tag, message);
      return ValidationFailure(message: message);
    }

    // Check max length
    if (maxLength != null && nonNullValue.length > maxLength) {
      final message = '$fieldName cannot exceed $maxLength characters';
      AppLogger.i(_tag, message);
      return ValidationFailure(message: message);
    }

    // Run custom validator if provided
    if (validator != null) {
      final errorMessage = validator(nonNullValue);
      if (errorMessage != null) {
        AppLogger.i(_tag, errorMessage);
        return ValidationFailure(message: errorMessage);
      }
    }

    return null;
  }

  /// Validate email and return localized error message if invalid
  static String? validateEmailField(String? email) {
    if (isNullOrEmpty(email)) {
      return 'Email is required'.tr;
    }

    if (!isValidEmail(email!)) {
      return 'Please enter a valid email address'.tr;
    }

    return null;
  }

  /// Validate password and return localized error message if invalid
  static String? validatePasswordField(String? password) {
    if (isNullOrEmpty(password)) {
      return 'Password is required'.tr;
    }

    if (!isValidPassword(password!)) {
      return 'Password must be at least 8 characters with letters and numbers'
          .tr;
    }

    return null;
  }
}
