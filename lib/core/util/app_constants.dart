import 'package:get/get.dart';
import '../services/app_constants_service.dart';

/// Constants used throughout the app
/// Now uses dynamic constants from the backend via AppConstantsService
class AppConstants {
  /// Private constructor to prevent instantiation
  AppConstants._();

  /// Get the app constants service
  static AppConstantsService get _service => Get.find<AppConstantsService>();

  // API constants (now dynamic)
  static String get baseUrl => _service.apiBaseUrl;
  static int get apiConnectTimeout => _service.apiConnectTimeout;
  static int get apiReceiveTimeout => _service.apiReceiveTimeout;

  // Storage keys
  static const String storageUserToken = 'user_token';
  static const String storageUserId = 'user_id';
  static const String storageUserEmail = 'user_email';
  static const String storageUserProfile = 'user_profile';
  static const String storageThemeMode = 'theme_mode';

  // Feature flags (now dynamic)
  static bool get enableSocialLogin => _service.enableSocialLogin;
  static bool get enablePushNotifications => _service.enablePushNotifications;
  static bool get enablePremiumFeatures => _service.enablePremiumFeatures;
  static bool get enableDarkMode => _service.enableDarkMode;
  static bool get enableOfflineMode => _service.enableOfflineMode;

  // App settings
  static const int defaultPageSize = 20;
  static const String appVersion = '1.0.0';
}
