import 'dart:math';
import 'package:power_up/features/habits/data/models/habit_model.dart';
import 'package:power_up/features/habits/domain/entities/habit_entity.dart';
import 'package:power_up/features/tasks/data/models/task_model.dart';
import 'package:power_up/features/tasks/domain/entities/task_entity.dart';

/// A utility class to generate test data for development purposes
class TestDataGenerator {
  static final Random _random = Random();

  /// Generate a random task
  static TaskModel generateRandomTask({int daysOffset = 0, bool? isCompleted}) {
    final now = DateTime.now();
    final id = 'task_${now.millisecondsSinceEpoch}_${_random.nextInt(10000)}';

    final taskTitles = [
      'Complete project proposal',
      'Fix authentication bug',
      'Prepare presentation slides',
      'Call client about requirements',
      'Review pull request #128',
      'Update documentation',
      'Create wireframes for new feature',
      'Implement login screen',
      'Research cloud providers',
      'Set up CI/CD pipeline',
      'Write unit tests for API',
      'Refactor legacy code',
    ];

    final descriptions = [
      'Need to include cost estimates and timeline',
      'Users reporting issues with social login',
      'Focus on key metrics and product roadmap',
      'Discuss timeline and budget constraints',
      'Check for performance issues',
      'Update API reference and examples',
      'Consider mobile-first approach',
      'Follow the design from Figma',
      'Compare AWS, GCP, and Azure options',
      'Use GitHub Actions for automation',
      'Ensure edge cases are covered',
      'Improve code quality and performance',
    ];

    // Due date between -7 to +14 days from now with the specified offset
    final dueDate = now.add(
      Duration(
        days: daysOffset + _random.nextInt(21) - 7,
        hours: _random.nextInt(24),
        minutes: _random.nextInt(60),
      ),
    );

    // Randomly determine completion status if not specified
    final taskCompleted = isCompleted ?? _random.nextBool();

    // If completed, set a completion date
    final completedAt =
        taskCompleted
            ? dueDate.subtract(
              Duration(
                hours: _random.nextInt(48),
                minutes: _random.nextInt(60),
              ),
            )
            : null;

    return TaskModel(
      id: id,
      title: taskTitles[_random.nextInt(taskTitles.length)],
      description:
          _random.nextBool()
              ? descriptions[_random.nextInt(descriptions.length)]
              : null,
      dueDate: dueDate,
      isCompleted: taskCompleted,
      priority:
          TaskPriority.values[_random.nextInt(TaskPriority.values.length)],
      completedAt: completedAt,
      createdAt: now.subtract(Duration(days: _random.nextInt(10))),
      updatedAt: now.subtract(Duration(hours: _random.nextInt(48))),
    );
  }

  /// Generate a list of random tasks
  static List<TaskModel> generateRandomTasks({
    int count = 10,
    int doneTasks = -1, // -1 means random
  }) {
    List<TaskModel> tasks = [];

    // If doneTasks is not specified, generate a random number
    final completedCount =
        doneTasks >= 0 ? doneTasks : _random.nextInt(count + 1);

    // Generate completed tasks
    for (int i = 0; i < completedCount; i++) {
      tasks.add(generateRandomTask(isCompleted: true));
    }

    // Generate incomplete tasks
    for (int i = 0; i < count - completedCount; i++) {
      tasks.add(generateRandomTask(isCompleted: false));
    }

    return tasks;
  }

  /// Generate a random habit
  static HabitModel generateRandomHabit() {
    final now = DateTime.now();
    final id = 'habit_${now.millisecondsSinceEpoch}_${_random.nextInt(10000)}';

    final habitNames = [
      'Morning Meditation',
      'Read for 30 minutes',
      'Drink 8 glasses of water',
      'Exercise for 30 minutes',
      'Write in journal',
      'Take a walk outside',
      'Practice coding skills',
      'Learn a new language',
      'Stretch before bed',
      'Plan tomorrow\'s tasks',
      'No social media after 9 PM',
      'Take vitamins',
    ];

    final descriptions = [
      'Helps with focus and mindfulness',
      'Improves knowledge and relaxation',
      'Stay hydrated for better health',
      'Maintain physical fitness',
      'Process thoughts and reduce stress',
      'Get fresh air and sunlight',
      'Improve technical abilities',
      '15 minutes of Duolingo',
      'Improves sleep quality',
      'Increases productivity',
      'Better sleep and mental health',
      'Support immune system',
    ];

    // Create a random reminder time string
    final hour = 6 + _random.nextInt(16); // 6 to 22
    final minute = _random.nextInt(12) * 5; // 0, 5, 10, ..., 55
    final reminderSettings = {
      "enabled": _random.nextBool(),
      "time":
          "${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}",
    };

    // Generate a random streak between 0 and 30 days
    final currentStreak = _random.nextInt(31);
    final longestStreak = currentStreak + _random.nextInt(10);

    // Generate a random frequency
    final frequency =
        HabitFrequency.values[_random.nextInt(HabitFrequency.values.length)];

    // For custom frequency, generate some random days
    List<int> customDays = [];
    if (frequency == HabitFrequency.custom) {
      // Add between 1 and 7 days
      final numDays = 1 + _random.nextInt(7);
      while (customDays.length < numDays) {
        final day = _random.nextInt(7);
        if (!customDays.contains(day)) {
          customDays.add(day);
        }
      }
      customDays.sort();
    }

    // Create completion history
    Map<String, bool> completionHistory = {};
    final createdAt = now.subtract(Duration(days: 30 + _random.nextInt(60)));
    final updatedAt = now.subtract(Duration(days: _random.nextInt(30)));

    if (currentStreak > 0) {
      for (int i = 0; i < currentStreak; i++) {
        final date = now.subtract(Duration(days: i));
        final dateString = "${date.year}-${date.month}-${date.day}";
        completionHistory[dateString] = true;
      }
    }

    return HabitModel(
      id: id,
      name: habitNames[_random.nextInt(habitNames.length)],
      description:
          _random.nextBool()
              ? descriptions[_random.nextInt(descriptions.length)]
              : null,
      frequency: frequency,
      customDays: customDays,
      currentStreak: currentStreak,
      longestStreak: longestStreak,
      completionHistory: completionHistory,
      createdAt: createdAt,
      updatedAt: updatedAt,
      reminderSettings: reminderSettings,
    );
  }

  /// Generate a list of random habits
  static List<HabitModel> generateRandomHabits({int count = 5}) {
    List<HabitModel> habits = [];

    for (int i = 0; i < count; i++) {
      habits.add(generateRandomHabit());
    }

    return habits;
  }
}
