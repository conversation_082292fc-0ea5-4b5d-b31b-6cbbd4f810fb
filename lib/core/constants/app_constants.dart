import 'package:get/get.dart';
import '../services/app_constants_service.dart';

/// App constants for UI and assets
/// Now uses dynamic constants from the backend via AppConstantsService
class AppConstants {
  /// Private constructor to prevent instantiation
  AppConstants._();

  /// Get the app constants service
  static AppConstantsService get _service => Get.find<AppConstantsService>();

  // Application Information (now dynamic)
  static String get appName => _service.appName;
  static String get appVersion => _service.appVersion;

  // Asset Paths
  static const String logoPath = 'assets/images/logo.png';
  static const String logo1Path = 'assets/images/logo_1.png';
  static const String logo2Path = 'assets/images/logo_2.png';

  // Logo Dimensions (now dynamic)
  static double get splashLogoSize => _service.splashLogoSize;
  static double get appBarLogoHeight => _service.appBarLogoHeight;
  static double get drawerLogoSize => 64.0; // Not yet in backend

  // Spacing Constants (now dynamic)
  static double get pageSpacing => _service.pageSpacing;
  static double get sectionSpacing => _service.sectionSpacing;
  static double get largeSpacing => 32.0; // Not yet in backend
  static double get smallSpacing => 8.0; // Not yet in backend

  // Animation Durations
  static const int splashDuration = 3000; // milliseconds
  static const int shortAnimationDuration = 200;
  static const int normalAnimationDuration = 300;
  static const int longAnimationDuration = 500;

  // UI Constants
  static const double borderRadius = 12.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
}
