import 'dart:developer' as developer;

/// Utility class for logging with different levels
class Logger {
  static const String _tag = 'PowerUp';

  /// Log info level messages
  static void info(String message) {
    developer.log(
      message,
      name: _tag,
      level: 800, // INFO level
    );
  }

  /// Log error level messages
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _tag,
      level: 1000, // SEVERE level
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log warning level messages
  static void warning(String message) {
    developer.log(
      message,
      name: _tag,
      level: 900, // WARNING level
    );
  }

  /// Log debug level messages
  static void debug(String message) {
    developer.log(
      message,
      name: _tag,
      level: 500, // FINE level
    );
  }
}
