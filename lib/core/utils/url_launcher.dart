import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'logger.dart';

/// Utility class for handling URL operations
class UrlLauncherUtil {
  /// Private constructor to prevent instantiation
  UrlLauncherUtil._();

  /// Launch the given URL in the default browser
  static Future<bool> launchURL(String urlString) async {
    try {
      final url = Uri.parse(urlString);
      if (await canLaunchUrl(url)) {
        return await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        Logger.error('Could not launch $urlString');
        Get.snackbar(
          'Error',
          'Could not open the link. Please try again later.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      Logger.error('Error launching URL: $e');
      Get.snackbar(
        'Error',
        'Invalid URL. Please report this issue.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  /// Open email client with pre-filled email
  static Future<bool> launchEmail(
    String email, {
    String subject = '',
    String body = '',
  }) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: _encodeQueryParameters({'subject': subject, 'body': body}),
    );

    return launchUrl(emailUri);
  }

  /// Helper method to encode query parameters
  static String _encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
        )
        .join('&');
  }
}
