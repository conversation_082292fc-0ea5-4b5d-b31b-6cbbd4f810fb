import 'package:equatable/equatable.dart';

/// Base failure class for domain layer errors
abstract class Failure extends Equatable {
  final String message;
  final int? statusCode;

  const Failure({required this.message, this.statusCode});

  @override
  List<Object?> get props => [message, statusCode];
}

/// Server failures (API errors, backend errors)
class ServerFailure extends Failure {
  const ServerFailure({required String message, int? statusCode})
    : super(message: message, statusCode: statusCode);
}

/// Local data source failures (database errors, shared preferences)
class CacheFailure extends Failure {
  const CacheFailure({required String message}) : super(message: message);
}

/// Network related failures (connection issues)
class NetworkFailure extends Failure {
  const NetworkFailure({required String message}) : super(message: message);
}

/// Auth failures (invalid credentials, token expired)
class AuthFailure extends Failure {
  const AuthFailure({required String message, int? statusCode})
    : super(message: message, statusCode: statusCode);
}

/// Validation failures (input validation)
class ValidationFailure extends Failure {
  const ValidationFailure({required String message}) : super(message: message);
}

/// Unexpected errors
class UnexpectedFailure extends Failure {
  const UnexpectedFailure({required String message}) : super(message: message);
}
