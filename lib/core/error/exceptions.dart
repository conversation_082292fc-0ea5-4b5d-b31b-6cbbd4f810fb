/// Represents a custom exception for handling errors in the data layer
class AppException implements Exception {
  final String message;
  final String? prefix;
  final int? statusCode;

  const AppException({required this.message, this.prefix, this.statusCode});

  @override
  String toString() {
    return "${prefix ?? 'Error'}: $message";
  }
}

/// Exception for network connectivity issues
class NetworkException extends AppException {
  NetworkException({required String message})
    : super(message: message, prefix: 'Network Error');
}

/// Exception for server errors
class ServerException extends AppException {
  ServerException({required String message, int? statusCode})
    : super(message: message, prefix: 'Server Error', statusCode: statusCode);
}

/// Exception for cache errors
class CacheException extends AppException {
  CacheException({required String message})
    : super(message: message, prefix: 'Cache Error');
}

/// Exception for unauthorized access errors
class UnauthorizedException extends AppException {
  const UnauthorizedException(String message)
    : super(message: message, prefix: 'Unauthorized Error', statusCode: 401);
}

/// Exception for authentication errors
class AuthException extends AppException {
  AuthException({required String message, int? statusCode})
    : super(message: message, prefix: 'Auth Error', statusCode: statusCode);
}

/// Exception for validation errors
class ValidationException extends AppException {
  ValidationException({required String message, int? statusCode})
    : super(
        message: message,
        prefix: 'Validation Error',
        statusCode: statusCode,
      );
}
