import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../error/failures.dart';

/// Base abstract class for all use cases in the domain layer.
///
/// [Type] is the return type of the use case.
/// [Params] is the input parameters to the use case.
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// Used for use cases that don't require any parameters
class NoParams extends Equatable {
  @override
  List<Object?> get props => [];
}
