import 'package:flutter/material.dart';
import 'package:power_up/features/settings/presentation/screens/privacy_policy_screen.dart';

/// Shows a dialog explaining data usage when enabling AI features.
Future<void> showDataUsageExplanationDialog(BuildContext context) async {
  final theme = Theme.of(context);
  return showDialog(
    context: context,
    builder:
        (context) => AlertDialog(
          title: const Text('AI Feature Data Usage'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'To enable this AI-powered feature, some of your app usage data (such as habits, tasks, and mood logs) may be processed to personalize your experience. Data is never sold and is only used to improve your results.',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Text(
                'You can opt out of personalized AI features at any time in Privacy Settings.',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PrivacyPolicyScreen(),
                  ),
                );
              },
              child: const Text('View Privacy Policy'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
  );
}
