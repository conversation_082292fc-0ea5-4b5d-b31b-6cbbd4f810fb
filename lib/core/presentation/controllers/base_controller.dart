import 'package:get/get.dart';

import '../../error/failures.dart';

/// Base controller class that all feature controllers will extend
abstract class BaseController extends GetxController {
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxBool _hasError = false.obs;

  /// Current loading state
  bool get isLoading => _isLoading.value;

  /// Current error message
  String get errorMessage => _errorMessage.value;

  /// Whether there's an error to display
  bool get hasError => _hasError.value;

  /// Set loading state
  void setLoading(bool loading) {
    _isLoading.value = loading;
  }

  /// Handle error from a failure
  void handleError(Failure failure) {
    _errorMessage.value = failure.message;
    _hasError.value = true;
  }

  /// Clear any error
  void clearError() {
    _errorMessage.value = '';
    _hasError.value = false;
  }

  /// Show a snackbar with a message
  void showSnackbar(String title, String message, {bool isError = false}) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor:
          isError ? Get.theme.colorScheme.error : Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
    );
  }
}
