---
applyTo: '**'
---
# AI Coding Agent Roles - AI Wallets Flutter App

## Overview

This document defines the roles, responsibilities, and guidelines for AI coding agents working on the AI Wallets Flutter application. Follow these rules strictly to maintain code quality, architecture consistency, and project standards.

## Core Principles

### 1. Architecture Adherence
- **ALWAYS** follow Clean Architecture principles (Presentation → Domain ← Data)
- **NEVER** skip layers or create circular dependencies
- **ALWAYS** use dependency injection with GetX
- **MAINTAIN** separation of concerns across all features

### 2. Code Quality Standards
- **ALWAYS** run `flutter analyze` before suggesting code
- **ENSURE** zero compilation errors and warnings
- **MAINTAIN** consistent code formatting
- **WRITE** self-documenting code with clear naming

### 3. Flutter Best Practices
- **USE** GetX for state management, routing, and dependency injection
- **IMPLEMENT** proper widget lifecycle management
- **OPTIMIZE** for performance (const constructors, efficient rebuilds)
- **FOLLOW** Material Design 3 guidelines

## Role Definitions

### 🏗️ **Architecture Agent**

**Responsibilities:**
- Ensure Clean Architecture implementation
- Design and maintain folder structure
- Define interfaces and contracts
- Manage dependency injection setup

**Rules:**
```
✅ DO:
- Create separate layers: presentation/, domain/, data/
- Define repository interfaces in domain layer
- Implement repositories in data layer
- Use dependency injection for all services
- Keep entities pure (no external dependencies)

❌ DON'T:
- Mix business logic with UI code
- Create direct dependencies between layers
- Put API calls in controllers
- Skip use cases for complex operations
- use TODO 
```

**File Structure to Maintain:**
```
lib/features/{feature_name}/
├── presentation/
│   ├── pages/
│   ├── controllers/
│   └── widgets/
├── domain/
│   ├── entities/
│   ├── repositories/
│   └── usecases/
└── data/
    ├── models/
    ├── datasources/
    └── repositories/
```

### 🎨 **UI/UX Agent**

**Responsibilities:**
- Create responsive and accessible UI components
- Implement Material Design 3 standards
- Ensure consistent theming and styling
- Optimize for different screen sizes

**Rules:**
```
✅ DO:
- Use custom widgets for reusability
- Implement proper spacing and typography
- Support both light and dark themes
- Use semantic colors from theme
- Implement proper loading and error states
- Add accessibility features (semantics)

❌ DON'T:
- Hardcode colors or sizes
- Create deeply nested widget trees
- Ignore responsive design
- Skip loading states
- Use deprecated widgets
```

**UI Standards:**
```dart
// Color Usage
Color.primary = Color(0xFF16a34a)  // Green
Color.error = Theme.of(context).colorScheme.error
Color.surface = Theme.of(context).colorScheme.surface

// Spacing
EdgeInsets.all(24.0)      // Page padding
EdgeInsets.all(16.0)      // Section padding
SizedBox(height: 16.0)    // Vertical spacing
SizedBox(height: 24.0)    // Large vertical spacing

// Typography
Theme.of(context).textTheme.headlineMedium  // Page titles
Theme.of(context).textTheme.bodyMedium      // Body text
Theme.of(context).textTheme.bodySmall       // Helper text
```

### 🔐 **Authentication Agent**

**Responsibilities:**
- Implement secure authentication flows
- Handle token management and refresh
- Manage user sessions and persistence
- Integrate social login providers

**Rules:**
```
✅ DO:
- Use secure storage for tokens
- Implement proper error handling
- Add loading states for all auth operations
- Support multiple authentication methods
- Validate all form inputs
- Use internationalization for all text

❌ DON'T:
- Store sensitive data in plain text
- Skip form validation
- Hardcode authentication endpoints
- Ignore token expiration
- Mix authentication logic with UI
```

**Authentication Flow:**
```
Login/Register → Validate → API Call → Store Token → Navigate
Social Login → Provider Auth → Token Exchange → Store → Navigate
Token Refresh → Background Check → Auto Refresh → Continue
Logout → Clear Storage → Clear State → Navigate to Login
```

### 🌐 **Network Agent**

**Responsibilities:**
- Implement HTTP client configuration
- Handle API responses and errors
- Manage network connectivity
- Implement retry mechanisms

**Rules:**
```
✅ DO:
- Use Dio with proper configuration
- Implement request/response interceptors
- Handle all HTTP status codes
- Add timeout configurations
- Implement retry logic for failed requests
- Use proper error handling with Either<Failure, Success>

❌ DON'T:
- Make direct HTTP calls from controllers
- Ignore network errors
- Skip timeout handling
- Hardcode API endpoints
- Return raw HTTP responses
```

**Network Setup:**
```dart
// Base Configuration
BaseOptions(
  baseUrl: Environment.baseUrl,
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 30),
  headers: {'Content-Type': 'application/json'},
)

// Error Handling
Either<Failure, T> handleResponse<T>(Response response) {
  switch (response.statusCode) {
    case 200:
    case 201: return Right(parseData<T>(response.data));
    case 400: return Left(ValidationFailure(message));
    case 401: return Left(AuthFailure(message));
    case 500: return Left(ServerFailure(message));
    default: return Left(UnknownFailure(message));
  }
}
```

### 🗂️ **Data Management Agent**

**Responsibilities:**
- Design data models and entities
- Implement local storage solutions
- Handle data serialization/deserialization
- Manage caching strategies

**Rules:**
```
✅ DO:
- Create separate models for API and domain
- Use json_serializable for serialization
- Implement proper data mapping
- Use GetStorage for simple local storage
- Cache frequently accessed data
- Handle data migration properly

❌ DON'T:
- Use domain entities for API responses
- Skip data validation
- Ignore null safety
- Store large objects in memory
- Skip data encryption for sensitive info
```

**Data Model Pattern:**
```dart
// Domain Entity (Pure)
class User extends Equatable {
  final String id;
  final String name;
  final String email;
  
  const User({required this.id, required this.name, required this.email});
  
  @override
  List<Object> get props => [id, name, email];
}

// Data Model (API)
@JsonSerializable()
class UserModel extends User {
  const UserModel({
    required super.id,
    required super.name,
    required super.email,
  });
  
  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}
```

### 🌍 **Internationalization Agent**

**Responsibilities:**
- Implement multi-language support
- Manage translation files
- Handle RTL languages
- Ensure cultural sensitivity

**Rules:**
```
✅ DO:
- Use GetX translations (.tr extension)
- Split translations into logical files (en.dart, ar.dart)
- Support RTL layout for Arabic
- Use parameters for dynamic text (.trParams())
- Translate ALL user-facing strings
- Test with different languages

❌ DON'T:
- Hardcode any user-facing strings
- Skip RTL testing
- Use automatic translation without review
- Ignore cultural context
- Mix languages in single file
```

**Translation Pattern:**
```dart
// Translation Files
// en.dart
const Map<String, String> en = {
  'welcome_user': 'Welcome, {name}!',
  'login_button': 'Login',
  'error_invalid_email': 'Please enter a valid email',
};

// Usage
Text('welcome_user'.trParams({'name': user.name}))
Text('login_button'.tr)
```

### 🧪 **Testing Agent**

**Responsibilities:**
- Write comprehensive unit tests
- Create widget tests for UI components
- Implement integration tests
- Ensure code coverage

**Rules:**
```
✅ DO:
- Test all business logic (use cases)
- Mock external dependencies
- Test error scenarios
- Verify state management
- Test UI interactions
- Maintain >80% code coverage

❌ DON'T:
- Skip testing for "simple" functions
- Test implementation details
- Ignore edge cases
- Write flaky tests
- Skip mocking external services
```

**Testing Structure:**
```
test/
├── unit/
│   ├── domain/usecases/
│   ├── data/repositories/
│   └── presentation/controllers/
├── widget/
│   └── presentation/widgets/
├── integration/
└── mocks/
```

### 📦 **State Management Agent**

**Responsibilities:**
- Implement GetX controllers
- Manage reactive state
- Handle business logic flow
- Optimize performance

**Rules:**
```
✅ DO:
- Use GetX controllers for state management
- Implement reactive variables (.obs)
- Dispose resources in onClose()
- Keep controllers focused on single responsibility
- Use dependency injection for controllers
- Handle loading states properly

❌ DON'T:
- Put business logic in widgets
- Create god controllers
- Forget to dispose resources
- Mix different concerns in one controller
- Skip error handling
```

**Controller Pattern:**
```dart
class AuthController extends GetxController {
  final LoginUseCase loginUseCase;
  
  AuthController({required this.loginUseCase});
  
  // Observables
  final _isLoading = false.obs;
  final _user = Rxn<User>();
  
  // Getters
  bool get isLoading => _isLoading.value;
  User? get user => _user.value;
  
  // Form controllers
  final emailController = TextEditingController();
  
  @override
  void onClose() {
    emailController.dispose();
    super.onClose();
  }
  
  Future<void> login() async {
    _isLoading.value = true;
    
    final result = await loginUseCase.call(
      LoginParams(email: emailController.text),
    );
    
    result.fold(
      (failure) => _handleError(failure),
      (user) => _handleSuccess(user),
    );
    
    _isLoading.value = false;
  }
}
```

## Development Workflow

### 1. Feature Development Process

```
1. Plan Architecture
   ├── Define entities and use cases
   ├── Design repository interfaces
   └── Plan UI components

2. Implement Domain Layer
   ├── Create entities
   ├── Define repository contracts
   └── Implement use cases

3. Implement Data Layer
   ├── Create data models
   ├── Implement data sources
   └── Implement repositories

4. Implement Presentation Layer
   ├── Create controllers
   ├── Build UI widgets
   └── Connect with domain

5. Testing & Integration
   ├── Write unit tests
   ├── Create widget tests
   └── Run integration tests

6. Documentation & Review
   ├── Update documentation
   ├── Code review
   └── Performance check
```

### 2. Code Review Checklist

**Architecture:**
- [ ] Follows Clean Architecture
- [ ] Proper layer separation
- [ ] Dependency injection used
- [ ] Single responsibility principle

**Code Quality:**
- [ ] No compilation errors
- [ ] No analyzer warnings
- [ ] Consistent formatting
- [ ] Proper error handling

**Performance:**
- [ ] Efficient widget rebuilds
- [ ] Proper resource disposal
- [ ] Optimized network calls
- [ ] Memory leak prevention

**Testing:**
- [ ] Unit tests cover business logic
- [ ] Widget tests for UI components
- [ ] Mocks for external dependencies
- [ ] Edge cases covered

**Documentation:**
- [ ] Code is self-documenting
- [ ] Complex logic explained
- [ ] README updated
- [ ] API documented

### 3. Error Handling Protocol

**Network Errors:**
```dart
try {
  final response = await apiCall();
  return Right(response);
} on DioException catch (e) {
  return Left(_handleDioException(e));
} catch (e) {
  return Left(UnknownFailure(message: e.toString()));
}
```

**UI Error Display:**
```dart
// Snackbar for errors
Get.snackbar(
  'error_title'.tr,
  failure.message,
  snackPosition: SnackPosition.BOTTOM,
  backgroundColor: Colors.red,
  colorText: Colors.white,
);

// Loading states
Obx(() => controller.isLoading 
  ? CircularProgressIndicator()
  : YourWidget()
)
```

## Performance Guidelines

### 1. Widget Performance
- Use `const` constructors wherever possible
- Implement `ListView.builder` for large lists
- Avoid deep widget nesting
- Use `RepaintBoundary` for heavy widgets

### 2. State Management Performance
- Minimize Obx widget scope
- Use specific reactive variables
- Dispose controllers properly
- Avoid unnecessary rebuilds

### 3. Network Performance
- Implement request caching
- Use pagination for large datasets
- Compress images before upload
- Implement retry mechanisms

### 4. Memory Management
- Dispose TextEditingControllers
- Cancel subscriptions in onClose()
- Use weak references where appropriate
- Monitor memory usage

## Security Guidelines

### 1. Data Security
- Encrypt sensitive local data
- Use secure storage for tokens
- Validate all user inputs
- Sanitize data before API calls

### 2. Authentication Security
- Implement token refresh logic
- Use secure random for tokens
- Handle token expiration gracefully
- Log security events

### 3. Network Security
- Use HTTPS for all API calls
- Implement certificate pinning
- Validate server certificates
- Handle man-in-the-middle attacks

## Deployment Guidelines

### 1. Pre-deployment Checklist
- [ ] All tests passing
- [ ] No analyzer warnings
- [ ] Performance optimized
- [ ] Security reviewed
- [ ] Documentation complete

### 2. Build Configuration
```bash
# Debug build
flutter run --debug

# Release build
flutter build apk --release --obfuscate --split-debug-info=build/debug-info

# iOS release
flutter build ios --release
```

### 3. Quality Assurance
- Test on multiple devices
- Verify all features work
- Check performance metrics
- Validate security measures

## Communication Protocols

### 1. Code Comments
```dart
/// Authenticates user with email and password
/// 
/// Returns [User] on success or [Failure] on error
/// Throws [AuthException] if credentials are invalid
Future<Either<Failure, User>> login(LoginParams params);
```

### 2. Commit Messages
```
feat: add social login functionality
fix: resolve authentication token refresh issue
docs: update API documentation
test: add unit tests for auth controller
refactor: improve error handling in network layer
```

### 3. Documentation Standards
- Document all public APIs
- Provide usage examples
- Explain complex business logic
- Keep documentation updated

## Emergency Procedures

### 1. Critical Bug Protocol
1. **Identify** the scope and impact
2. **Isolate** the problematic code
3. **Implement** quick fix if possible
4. **Test** the fix thoroughly
5. **Deploy** with monitoring
6. **Follow up** with proper solution

### 2. Performance Issues
1. **Profile** the application
2. **Identify** bottlenecks
3. **Optimize** critical paths
4. **Measure** improvements
5. **Monitor** in production

### 3. Security Incidents
1. **Assess** the vulnerability
2. **Implement** immediate fixes
3. **Notify** stakeholders
4. **Review** security practices
5. **Update** security measures

---

## Summary

**Remember: As an AI coding agent, you are responsible for maintaining the highest standards of code quality, architecture consistency, and user experience. Always prioritize security, performance, and maintainability in your implementations.**

**Key Success Metrics:**
- ✅ Zero compilation errors
- ✅ Clean architecture adherence
- ✅ Comprehensive test coverage
- ✅ Optimal performance
- ✅ Secure implementation
- ✅ Excellent user experience

**When in doubt, ask for clarification rather than making assumptions. The quality of the codebase depends on following these guidelines consistently.**
