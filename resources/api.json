{"openapi": "3.0.0", "paths": {"/api/health": {"get": {"operationId": "AppController_getHealth", "parameters": [], "responses": {"200": {"description": "Returns the health status of the API", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "timestamp": {"type": "string", "example": "2025-01-09T12:00:00.000Z"}, "uptime": {"type": "number", "example": 123456}}}}}}}, "summary": "Check API health status", "tags": ["app"]}}, "/api/status": {"get": {"operationId": "AppController_getStatus", "parameters": [], "responses": {"200": {"description": "Returns detailed API status information", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "operational"}, "version": {"type": "string", "example": "1.0.0"}, "features": {"type": "array", "items": {"type": "string"}}}}}}}}, "summary": "Get API status information", "tags": ["app"]}}, "/api/users": {"post": {"operationId": "UsersController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "The user has been successfully created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "summary": "Create a new user", "tags": ["users"]}}, "/api/users/profile": {"get": {"operationId": "UsersController_getProfile", "parameters": [], "responses": {"200": {"description": "Returns the current user profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get current user profile", "tags": ["users"]}, "put": {"operationId": "UsersController_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "Returns the updated user profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update current user profile", "tags": ["users"]}}, "/api/users/{id}": {"get": {"operationId": "UsersController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns user by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get user by ID", "tags": ["users"]}}, "/api/users/account": {"delete": {"operationId": "UsersController_deleteAccount", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAccountDto"}}}}, "responses": {"200": {"description": "Account successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Account successfully deleted"}}}}}}}, "security": [{"bearer": []}], "summary": "Soft delete user account", "tags": ["users"]}}, "/api/users/data/export": {"get": {"operationId": "UsersController_exportUserData", "parameters": [], "responses": {"200": {"description": "Returns all user data for export", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDataExportDto"}}}}}, "security": [{"bearer": []}], "summary": "Export user data for GDPR compliance", "tags": ["users"]}}, "/api/users/data/export/pdf": {"get": {"operationId": "UsersController_exportUserDataAsPDF", "parameters": [], "responses": {"200": {"description": "Returns user data as PDF file", "headers": {"Content-Type": {"description": "PDF file content type", "schema": {"type": "string", "example": "application/pdf"}}, "Content-Disposition": {"description": "File download attachment header", "schema": {"type": "string", "example": "attachment; filename=\"user-data-export.pdf\""}}}, "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}, "security": [{"bearer": []}], "summary": "Export user data as PDF document", "tags": ["users"]}}, "/api/auth/register": {"post": {"operationId": "AuthController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"201": {"description": "User has been successfully registered", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}, "summary": "Register a new user", "tags": ["auth"]}}, "/api/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "User has been successfully logged in", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}, "summary": "Login with email and password", "tags": ["auth"]}}, "/api/auth/social-login": {"post": {"operationId": "AuthController_socialLogin", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SocialLoginDto"}}}}, "responses": {"200": {"description": "User has been successfully logged in", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}, "summary": "Login with social providers (Google, Apple) via Firebase", "tags": ["auth"]}}, "/api/auth/forgot-password": {"post": {"operationId": "AuthController_forgotPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "Password reset email sent if account exists", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "If an account with that email exists, a password reset link has been sent."}}}}}}}, "summary": "Request a password reset email", "tags": ["auth"]}}, "/api/auth/reset-password": {"post": {"operationId": "AuthController_resetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "Password has been reset successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password has been reset successfully."}}}}}}}, "summary": "Reset password using 8-character code from email", "tags": ["auth"]}}, "/api/auth/resend-reset-code": {"post": {"operationId": "AuthController_resendResetCode", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "Email address to resend reset code to", "example": "<EMAIL>"}}, "required": ["email"]}}}}, "responses": {"200": {"description": "New reset code sent if account exists and has active reset request", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "A new reset code has been sent to your email address."}}}}}}}, "summary": "Resend password reset code using email address", "tags": ["auth"]}}, "/api/auth/forgot-password-firebase": {"post": {"operationId": "AuthController_forgotPasswordFirebase", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "Firebase password reset email sent if account exists", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "If an account with that email exists, a password reset link has been sent."}}}}}}}, "summary": "Request a password reset using Firebase (alternative method)", "tags": ["auth"]}}, "/reset-password": {"get": {"operationId": "AuthWebController_showResetPasswordForm", "parameters": [{"name": "code", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Auth<PERSON><PERSON>"]}, "post": {"operationId": "AuthWebController_handleResetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Auth<PERSON><PERSON>"]}}, "/api/habits": {"post": {"operationId": "HabitsController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateHabitDto"}}}}, "responses": {"201": {"description": "The habit has been successfully created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new habit", "tags": ["habits"]}, "get": {"operationId": "HabitsController_findAll", "parameters": [], "responses": {"200": {"description": "Returns all habits for the user", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all habits", "tags": ["habits"]}}, "/api/habits/{id}": {"get": {"operationId": "HabitsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific habit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific habit", "tags": ["habits"]}, "put": {"operationId": "HabitsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHabitDto"}}}}, "responses": {"200": {"description": "Returns the updated habit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a habit", "tags": ["habits"]}}, "/api/habits/{id}/complete": {"post": {"operationId": "HabitsController_complete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the completed habit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark habit as completed", "tags": ["habits"]}}, "/api/messaging/direct": {"post": {"operationId": "MessagingController_sendDirectMessage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendDirectMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a direct message to a user", "tags": ["messaging"]}}, "/api/messaging/group": {"post": {"operationId": "MessagingController_sendGroupMessage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendGroupMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a message to a group", "tags": ["messaging"]}}, "/api/messaging/challenge": {"post": {"operationId": "MessagingController_sendChallengeMessage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendChallengeMessageDto"}}}}, "responses": {"201": {"description": "Message sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a message in a challenge", "tags": ["messaging"]}}, "/api/messaging/conversations": {"get": {"operationId": "MessagingController_getUserConversations", "parameters": [], "responses": {"200": {"description": "Returns user conversations"}}, "security": [{"bearer": []}], "summary": "Get user conversations", "tags": ["messaging"]}}, "/api/messaging/direct/{recipientId}": {"get": {"operationId": "MessagingController_getDirectMessages", "parameters": [{"name": "recipientId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns direct messages"}}, "security": [{"bearer": []}], "summary": "Get direct messages with a user", "tags": ["messaging"]}}, "/api/messaging/group/{groupId}": {"get": {"operationId": "MessagingController_getGroupMessages", "parameters": [{"name": "groupId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns group messages"}}, "security": [{"bearer": []}], "summary": "Get messages in a group", "tags": ["messaging"]}}, "/api/messaging/challenge/{challengeId}": {"get": {"operationId": "MessagingController_getChallengeMessages", "parameters": [{"name": "challengeId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns challenge messages"}}, "security": [{"bearer": []}], "summary": "Get messages in a challenge", "tags": ["messaging"]}}, "/api/messaging/read/{messageId}": {"put": {"operationId": "MessagingController_markMessageAsRead", "parameters": [{"name": "messageId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Message marked as read"}}, "security": [{"bearer": []}], "summary": "Mark a message as read", "tags": ["messaging"]}}, "/api/notifications": {"get": {"operationId": "NotificationsController_getAllNotifications", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by notification type", "schema": {"type": "string", "enum": ["habit_reminder", "habit_streak", "habit_milestone", "task_reminder", "task_milestone", "plan_progress", "plan_step_complete", "plan_complete", "challenge_start", "challenge_progress", "challenge_leaderboard", "challenge_complete", "challenge_achievement", "podcast_available", "podcast_recommendation"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by notification status", "schema": {"type": "string", "enum": ["pending", "sent", "delivered", "failed", "read"]}}, {"name": "search", "required": false, "in": "query", "description": "Search in title and body", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "sentAt", "readAt", "type", "status"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order (ASC/DESC)", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns paginated list of user notifications", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedNotificationsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get all notifications for the current user with pagination", "tags": ["notifications"]}}, "/api/notifications/register-device": {"post": {"operationId": "NotificationsController_registerDevice", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDeviceDto"}}}}, "responses": {"201": {"description": "Device token registered successfully"}}, "security": [{"bearer": []}], "summary": "Register a device token for push notifications", "tags": ["notifications"]}}, "/api/notifications/preferences": {"get": {"operationId": "NotificationsController_getNotificationPreferences", "parameters": [], "responses": {"200": {"description": "Returns user notification preferences"}}, "security": [{"bearer": []}], "summary": "Get user notification preferences", "tags": ["notifications"]}, "put": {"operationId": "NotificationsController_updateNotificationPreferences", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationPreferencesDto"}}}}, "responses": {"200": {"description": "Notification preferences updated successfully"}}, "security": [{"bearer": []}], "summary": "Update user notification preferences", "tags": ["notifications"]}}, "/api/notifications/devices/{deviceId}": {"delete": {"operationId": "NotificationsController_removeDevice", "parameters": [{"name": "deviceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Device removed successfully"}}, "security": [{"bearer": []}], "summary": "Remove a device", "tags": ["notifications"]}}, "/api/notifications/test-notification": {"post": {"operationId": "NotificationsController_sendTestNotification", "parameters": [], "responses": {"200": {"description": "Test notification sent successfully"}}, "security": [{"bearer": []}], "summary": "Send a test notification to the user's devices", "tags": ["notifications"]}}, "/api/notifications/devices": {"get": {"operationId": "NotificationsController_getUserDevices", "parameters": [], "responses": {"200": {"description": "Returns user's registered devices"}}, "security": [{"bearer": []}], "summary": "Get all registered devices for the user", "tags": ["notifications"]}}, "/api/notifications/{id}/read": {"put": {"operationId": "NotificationsController_markAsRead", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Notification marked as read successfully"}}, "security": [{"bearer": []}], "summary": "Mark a notification as read", "tags": ["notifications"]}}, "/api/tasks": {"post": {"operationId": "TasksController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTaskDto"}}}}, "responses": {"201": {"description": "The task has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new task", "tags": ["tasks"]}, "get": {"operationId": "TasksController_findAll", "parameters": [{"name": "filter", "required": false, "in": "query", "schema": {"enum": ["all", "upcoming", "overdue"], "type": "string"}}], "responses": {"200": {"description": "Returns tasks based on filter", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get tasks based on filter", "tags": ["tasks"]}}, "/api/tasks/{id}": {"get": {"operationId": "TasksController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific task by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific task", "tags": ["tasks"]}, "put": {"operationId": "TasksController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTaskDto"}}}}, "responses": {"200": {"description": "Returns the updated task", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a task", "tags": ["tasks"]}, "delete": {"operationId": "TasksController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The task has been successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Task deleted successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "Delete a task", "tags": ["tasks"]}}, "/api/tasks/{id}/complete": {"post": {"operationId": "TasksController_markComplete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the completed task", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark a task as complete", "tags": ["tasks"]}}, "/api/challenges": {"post": {"operationId": "ChallengesController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChallengeDto"}}}}, "responses": {"201": {"description": "The challenge has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new challenge", "tags": ["challenges"]}, "get": {"operationId": "ChallengesController_findAll", "parameters": [{"name": "filter", "required": false, "in": "query", "schema": {"enum": ["all", "active"], "type": "string"}}], "responses": {"200": {"description": "Returns challenges based on filter", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get challenges based on filter", "tags": ["challenges"]}}, "/api/challenges/{id}": {"get": {"operationId": "ChallengesController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific challenge by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific challenge", "tags": ["challenges"]}, "put": {"operationId": "ChallengesController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChallengeDto"}}}}, "responses": {"200": {"description": "Returns the updated challenge", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a challenge", "tags": ["challenges"]}, "delete": {"operationId": "ChallengesController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The challenge has been successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Challenge deleted successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "Delete a challenge", "tags": ["challenges"]}}, "/api/challenges/{id}/join": {"post": {"operationId": "ChallengesController_join", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the challenge user joined", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Join a challenge", "tags": ["challenges"]}}, "/api/challenges/{id}/leave": {"post": {"operationId": "ChallengesController_leave", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the challenge user left", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Leave a challenge", "tags": ["challenges"]}}, "/api/challenges/{id}/progress": {"post": {"operationId": "ChallengesController_updateProgress", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgressDto"}}}}, "responses": {"200": {"description": "Returns the updated progress", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgressDto"}}}}}, "security": [{"bearer": []}], "summary": "Update challenge progress", "tags": ["challenges"]}}, "/api/skill-plans": {"post": {"operationId": "SkillPlansController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSkillPlanDto"}}}}, "responses": {"201": {"description": "The skill plan has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create a new skill plan", "tags": ["skill-plans"]}, "get": {"operationId": "SkillPlansController_findAll", "parameters": [{"name": "isPublic", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "creatorId", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns skill plans based on filters", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get skill plans based on filters", "tags": ["skill-plans"]}}, "/api/skill-plans/ai-generate": {"post": {"operationId": "SkillPlansController_createWithAi", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAiSkillPlanDto"}}}}, "responses": {"201": {"description": "AI-generated skill plan has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate a skill plan using AI", "tags": ["skill-plans"]}}, "/api/skill-plans/{id}": {"get": {"operationId": "SkillPlansController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific skill plan by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific skill plan", "tags": ["skill-plans"]}, "put": {"operationId": "SkillPlansController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSkillPlanDto"}}}}, "responses": {"200": {"description": "Returns the updated skill plan", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a skill plan", "tags": ["skill-plans"]}, "delete": {"operationId": "SkillPlansController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The skill plan has been successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Skill plan deleted successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "Delete a skill plan", "tags": ["skill-plans"]}}, "/api/skill-plans/{planId}/steps/{stepId}": {"put": {"operationId": "SkillPlansController_updateStep", "parameters": [{"name": "planId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "stepId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStepDto"}}}}, "responses": {"200": {"description": "Returns the updated step", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update a step in a skill plan", "tags": ["skill-plans"]}}, "/api/skill-plans/{planId}/steps/{stepId}/tasks/{taskIndex}/complete": {"put": {"operationId": "SkillPlansController_markStepTaskComplete", "parameters": [{"name": "planId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "stepId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "taskIndex", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Task has been marked as complete", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark a task as complete in a skill plan step", "tags": ["skill-plans"]}}, "/api/skill-plans/{id}/progress": {"post": {"operationId": "SkillPlansController_updateProgress", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStepCompletionDto"}}}}, "responses": {"200": {"description": "Returns the updated progress information", "content": {"application/json": {"schema": {"properties": {"progress": {"type": "number"}, "completedSteps": {"type": "array", "items": {"type": "number"}}}}}}}}, "security": [{"bearer": []}], "summary": "Update skill plan progress", "tags": ["skill-plans"]}}, "/api/podcasts/generate": {"post": {"description": "Generate a new personalized podcast using your current progress data. Requires active subscription or free trial. Users can only generate one podcast per week.", "operationId": "PodcastsController_generate", "parameters": [], "responses": {"201": {"description": "The podcast has been successfully generated based on your current progress", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate a new personalized podcast", "tags": ["podcasts"]}}, "/api/podcasts/generate/custom": {"post": {"description": "Generate a personalized podcast with optional custom mood, topic, or language preferences. Your habit, task, and progress data will still be automatically included.", "operationId": "PodcastsController_generateCustom", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPodcastPreferencesDto"}}}}, "responses": {"201": {"description": "The podcast has been successfully generated with custom preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Generate a personalized podcast with custom preferences", "tags": ["podcasts"]}}, "/api/podcasts/weekly": {"get": {"description": "Get the weekly personalized podcast. Requires active subscription or free trial.", "operationId": "PodcastsController_getWeeklyPodcast", "parameters": [], "responses": {"200": {"description": "Returns the weekly personalized podcast", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get weekly personalized podcast", "tags": ["podcasts"]}}, "/api/podcasts/history": {"get": {"operationId": "PodcastsController_getHistory", "parameters": [], "responses": {"200": {"description": "Returns the list of listened podcasts", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get podcast listening history", "tags": ["podcasts"]}}, "/api/podcasts/{id}/listened": {"post": {"operationId": "PodcastsController_markAsListened", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the updated podcast marked as listened", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark a podcast as listened", "tags": ["podcasts"]}}, "/api/podcasts": {"get": {"operationId": "PodcastsController_findAll", "parameters": [], "responses": {"200": {"description": "Returns all podcasts for the current user", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all podcasts for current user", "tags": ["podcasts"]}}, "/api/podcasts/{id}": {"get": {"operationId": "PodcastsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a specific podcast by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PodcastResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get a specific podcast", "tags": ["podcasts"]}, "delete": {"operationId": "PodcastsController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The podcast has been successfully deleted"}}, "security": [{"bearer": []}], "summary": "Delete a podcast", "tags": ["podcasts"]}}, "/api/monetization/plans": {"get": {"operationId": "MonetizationController_getPlans", "parameters": [{"name": "platform", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of active subscription plans"}}, "summary": "Get all active subscription plans", "tags": ["Monetization"]}}, "/api/monetization/plans/{id}": {"get": {"operationId": "MonetizationController_getPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan details"}}, "summary": "Get subscription plan by ID", "tags": ["Monetization"]}}, "/api/monetization/subscribe": {"post": {"operationId": "MonetizationController_subscribe", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionDto"}}}}, "responses": {"201": {"description": "Subscription created successfully"}}, "security": [{"bearer": []}], "summary": "Create a new subscription", "tags": ["Monetization"]}}, "/api/monetization/my-subscriptions": {"get": {"operationId": "MonetizationController_getMySubscriptions", "parameters": [], "responses": {"200": {"description": "List of user subscriptions"}}, "security": [{"bearer": []}], "summary": "Get user subscriptions", "tags": ["Monetization"]}}, "/api/monetization/subscription-status": {"get": {"operationId": "MonetizationController_getSubscriptionStatus", "parameters": [], "responses": {"200": {"description": "Current subscription status"}}, "security": [{"bearer": []}], "summary": "Get current subscription status", "tags": ["Monetization"]}}, "/api/monetization/subscriptions/{id}": {"patch": {"operationId": "MonetizationController_updateSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionDto"}}}}, "responses": {"200": {"description": "Subscription updated successfully"}}, "security": [{"bearer": []}], "summary": "Update subscription", "tags": ["Monetization"]}}, "/api/monetization/subscriptions/{id}/cancel": {"post": {"operationId": "MonetizationController_cancelSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription cancelled successfully"}}, "security": [{"bearer": []}], "summary": "Cancel subscription", "tags": ["Monetization"]}}, "/api/monetization/verify/apple-receipt": {"post": {"operationId": "MonetizationController_verifyAppleReceipt", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyReceiptDto"}}}}, "responses": {"200": {"description": "Receipt verified successfully"}}, "security": [{"bearer": []}], "summary": "Verify Apple App Store receipt", "tags": ["Monetization"]}}, "/api/monetization/verify/google-purchase": {"post": {"operationId": "MonetizationController_verifyGooglePurchase", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPurchaseTokenDto"}}}}, "responses": {"200": {"description": "Purchase verified successfully"}}, "security": [{"bearer": []}], "summary": "Verify Google Play purchase", "tags": ["Monetization"]}}, "/api/monetization/validate-coupon": {"post": {"operationId": "MonetizationController_validateCoupon", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateCouponDto"}}}}, "responses": {"200": {"description": "Coupon validation result"}}, "security": [{"bearer": []}], "summary": "Validate coupon code", "tags": ["Monetization"]}}, "/api/admin/monetization/plans": {"get": {"operationId": "MonetizationAdminController_getAllPlans", "parameters": [], "responses": {"200": {"description": "List of all subscription plans"}}, "security": [{"bearer": []}], "summary": "Get all subscription plans (admin)", "tags": ["Admin - Monetization"]}, "post": {"operationId": "MonetizationAdminController_createPlan", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionPlanDto"}}}}, "responses": {"201": {"description": "Subscription plan created successfully"}}, "security": [{"bearer": []}], "summary": "Create new subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/plans/{id}": {"patch": {"operationId": "MonetizationAdminController_updatePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionPlanDto"}}}}, "responses": {"200": {"description": "Subscription plan updated successfully"}}, "security": [{"bearer": []}], "summary": "Update subscription plan", "tags": ["Admin - Monetization"]}, "delete": {"operationId": "MonetizationAdminController_deletePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/plans/{id}/activate": {"post": {"operationId": "MonetizationAdminController_activatePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan activated successfully"}}, "security": [{"bearer": []}], "summary": "Activate subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/plans/{id}/deactivate": {"post": {"operationId": "MonetizationAdminController_deactivatePlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription plan deactivated successfully"}}, "security": [{"bearer": []}], "summary": "Deactivate subscription plan", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/subscriptions": {"get": {"operationId": "MonetizationAdminController_getAllSubscriptions", "parameters": [], "responses": {"200": {"description": "List of all subscriptions"}}, "security": [{"bearer": []}], "summary": "Get all subscriptions (admin)", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/subscriptions/{id}": {"get": {"operationId": "MonetizationAdminController_getSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription details"}}, "security": [{"bearer": []}], "summary": "Get subscription by ID (admin)", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/subscriptions/{id}/cancel": {"post": {"operationId": "MonetizationAdminController_cancelSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscription cancelled successfully"}}, "security": [{"bearer": []}], "summary": "Cancel subscription (admin)", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons": {"get": {"operationId": "MonetizationAdminController_getAllCoupons", "parameters": [], "responses": {"200": {"description": "List of all coupon codes"}}, "security": [{"bearer": []}], "summary": "Get all coupon codes (admin)", "tags": ["Admin - Monetization"]}, "post": {"operationId": "MonetizationAdminController_createCoupon", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCouponCodeDto"}}}}, "responses": {"201": {"description": "Coupon code created successfully"}}, "security": [{"bearer": []}], "summary": "Create new coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}": {"get": {"operationId": "MonetizationAdminController_getCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code details"}}, "security": [{"bearer": []}], "summary": "Get coupon code by ID (admin)", "tags": ["Admin - Monetization"]}, "patch": {"operationId": "MonetizationAdminController_updateCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCouponCodeDto"}}}}, "responses": {"200": {"description": "Coupon code updated successfully"}}, "security": [{"bearer": []}], "summary": "Update coupon code", "tags": ["Admin - Monetization"]}, "delete": {"operationId": "MonetizationAdminController_deleteCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}/activate": {"post": {"operationId": "MonetizationAdminController_activateCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code activated successfully"}}, "security": [{"bearer": []}], "summary": "Activate coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}/deactivate": {"post": {"operationId": "MonetizationAdminController_deactivateCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code deactivated successfully"}}, "security": [{"bearer": []}], "summary": "Deactivate coupon code", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/coupons/{id}/stats": {"get": {"operationId": "MonetizationAdminController_getCouponStats", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Coupon code usage statistics"}}, "security": [{"bearer": []}], "summary": "Get coupon code usage statistics", "tags": ["Admin - Monetization"]}}, "/api/admin/monetization/analytics/overview": {"get": {"operationId": "MonetizationAdminController_getAnalyticsOverview", "parameters": [], "responses": {"200": {"description": "Monetization analytics overview"}}, "security": [{"bearer": []}], "summary": "Get monetization overview analytics", "tags": ["Admin - Monetization"]}}, "/api/webhooks/app-store": {"post": {"operationId": "WebhookController_handleAppStoreWebhook", "parameters": [], "responses": {"200": {"description": "Webhook processed successfully"}, "400": {"description": "Invalid webhook payload"}}, "summary": "Handle App Store webhook notifications", "tags": ["webhooks"]}}, "/api/webhooks/google-play": {"post": {"operationId": "WebhookController_handleGooglePlayWebhook", "parameters": [], "responses": {"200": {"description": "Webhook processed successfully"}, "400": {"description": "Invalid webhook payload"}}, "summary": "Handle Google Play webhook notifications", "tags": ["webhooks"]}}, "/api/analytics/user": {"get": {"description": "Returns detailed user analytics including overview, activity summary, engagement metrics, and progress trends for the specified period.", "operationId": "AnalyticsController_getUserAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Analysis period", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}], "responses": {"200": {"description": "Returns comprehensive user analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAnalyticsResponseDto"}, "example": {"period": "month", "periodStart": "2024-04-15T00:00:00Z", "periodEnd": "2024-05-15T23:59:59Z", "userOverview": {"userId": "user-123-uuid", "email": "<EMAIL>", "fullName": "<PERSON>", "totalXp": 2450, "badges": ["habit_master", "task_crusher"], "createdAt": "2024-01-15T10:30:00Z", "daysSinceCreation": 125}, "activitySummary": {"totalHabits": 12, "totalTasks": 89, "totalSkillPlans": 5, "totalChallenges": 3, "totalPodcasts": 15, "totalCalendarEvents": 45}, "engagementMetrics": {"averageDailyUsageMinutes": 45.5, "mostActiveDay": "Monday", "mostActiveHour": 9, "consistencyScore": 78.5, "activeDaysInPeriod": 25, "totalDaysInPeriod": 30}, "progressTrends": [{"date": "2024-05-15", "progressScore": 85.5, "habitsCompleted": 4, "tasksCompleted": 7, "xpGained": 150}], "overallWellnessScore": 82.3, "keyAchievements": ["Completed 5 skill plan steps", "Maintained 7-day habit streak"], "improvementAreas": ["Increase task completion rate", "Improve weekend consistency"], "generatedAt": "2024-05-15T14:30:00Z"}}}}}, "security": [{"bearer": []}], "summary": "Get comprehensive user analytics", "tags": ["analytics"]}}, "/api/analytics/comprehensive": {"get": {"description": "Returns detailed analytics covering all user data with metrics, insights, behavior patterns, and recommendations.", "operationId": "AnalyticsController_getComprehensiveAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Analysis period", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}], "responses": {"200": {"description": "Returns comprehensive analytics across all user data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComprehensiveAnalyticsResponseDto"}, "example": {"period": "month", "periodStart": "2024-04-15T00:00:00Z", "periodEnd": "2024-05-15T23:59:59Z", "overallWellnessScore": 84.7, "metrics": [{"category": "productivity", "name": "Task Completion Rate", "currentValue": 78.5, "targetValue": 85, "unit": "percentage", "trend": "improving", "changeFromPrevious": 5.2}], "areaPerformance": [{"area": "Habit Consistency", "score": 87.5, "level": "Excellent", "strengths": ["Consistent morning routine", "Strong weekday habits"], "weaknesses": ["Weekend consistency"], "metrics": []}], "insights": [{"priority": "high", "category": "productivity", "title": "Peak Productivity Hours Identified", "description": "Your productivity consistently peaks between 9-11 AM", "evidence": "Based on 30 days of task completion data", "recommendations": ["Schedule important tasks between 9-11 AM"], "confidence": "high"}], "behaviorPatterns": [{"type": "temporal", "name": "Morning Productivity Peak", "description": "User shows consistently higher productivity in morning hours", "frequency": "daily", "strength": 92.3, "dataPoints": 45}], "topStrengths": ["Habit Consistency", "Learning Progress"], "improvementPriorities": ["Weekend Productivity", "Evening Routine"], "nextPeriodRecommendations": ["Establish evening routine", "Join a new challenge"], "progressFromPrevious": 12.5, "motivationalMessage": "Outstanding progress this month!", "analysisConfidence": "high", "dataPointsAnalyzed": 1247, "generatedAt": "2024-05-15T14:30:00Z"}}}}}, "security": [{"bearer": []}], "summary": "Get comprehensive analytics across all areas", "tags": ["analytics"]}}, "/api/analytics/habits": {"get": {"description": "Returns comprehensive habit analytics including completion rates, streaks, trends, and insights.", "operationId": "AnalyticsController_getHabitAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Analysis period", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}], "responses": {"200": {"description": "Returns detailed habit analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HabitAnalyticsResponseDto"}, "example": {"period": "month", "periodStart": "2024-04-15T00:00:00Z", "periodEnd": "2024-05-15T23:59:59Z", "overallCompletionRate": 78.5, "totalHabits": 8, "activeHabits": 7, "totalCompletions": 156, "totalPossibleCompletions": 198, "averageStreakLength": 12.3, "longestCurrentStreak": 28, "totalXpEarned": 1560, "habits": [{"id": "habit-123-uuid", "name": "Morning Exercise", "currentStreak": 15, "longestStreak": 28, "completionRate": 85.7, "totalCompletions": 24, "scheduledDays": 28, "xpReward": 10, "totalXpEarned": 240}], "dailyTrends": [{"date": "2024-05-15", "completed": 4, "scheduled": 5, "completionRate": 80}], "bestDay": "Monday", "worstDay": "Friday", "mostConsistentHabit": "Morning Exercise", "habitNeedingAttention": "Evening Reading", "insights": [{"type": "streak_milestone", "title": "Strong Weekend Performance", "description": "Your habit completion rate is 15% higher on weekends", "relatedHabits": ["habit-123-uuid"], "recommendation": "Consider adding more challenging habits on weekends"}], "changeFromPrevious": 8.5, "consistencyScore": 82.7, "generatedAt": "2024-05-15T14:30:00Z"}}}}}, "security": [{"bearer": []}], "summary": "Get detailed habit analytics", "tags": ["analytics"]}}, "/api/analytics/tasks": {"get": {"description": "Returns comprehensive task analytics including completion rates, priority analysis, trends, and efficiency metrics.", "operationId": "AnalyticsController_getTaskAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Analysis period", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}], "responses": {"200": {"description": "Returns detailed task analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskAnalyticsResponseDto"}, "example": {"period": "month", "periodStart": "2024-04-15T00:00:00Z", "periodEnd": "2024-05-15T23:59:59Z", "totalTasks": 67, "completedTasks": 52, "overdueTasks": 8, "completionRate": 77.6, "onTimeCompletionRate": 84.6, "averageDaysToComplete": 2.8, "tasksByPriority": {"high": 15, "medium": 35, "low": 17}, "completionRateByPriority": {"high": 93.3, "medium": 77.1, "low": 64.7}, "tasks": [{"id": "task-123-u<PERSON>", "title": "Complete project proposal", "priority": "high", "completed": true, "dueDate": "2024-05-20T15:00:00Z", "completedAt": "2024-05-18T14:30:00Z", "daysToComplete": 3, "completedOnTime": true, "daysOverdue": 0}], "dailyTrends": [{"date": "2024-05-15", "created": 3, "completed": 5, "overdue": 1, "completionRate": 83.3}], "mostProductiveDay": "Tuesday", "highestCreationDay": "Monday", "averageOverdueDays": 3.2, "insights": [{"type": "completion_pattern", "title": "High Priority Task Focus", "description": "You complete 95% of high-priority tasks on time", "recommendation": "Consider reviewing priority levels for better management", "evidence": "Based on 45 completed tasks in the last month"}], "changeFromPrevious": 12.4, "efficiencyScore": 78.9, "recommendedFocus": ["Improve low-priority task completion", "Reduce overdue tasks"], "generatedAt": "2024-05-15T14:30:00Z"}}}}}, "security": [{"bearer": []}], "summary": "Get detailed task analytics", "tags": ["analytics"]}}, "/api/analytics/skill-plans": {"get": {"description": "Returns comprehensive skill plan analytics including progress, learning velocity, category distribution, and insights.", "operationId": "AnalyticsController_getSkillPlanAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Analysis period", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}], "responses": {"200": {"description": "Returns skill plan analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillPlanAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get skill plan analytics", "tags": ["analytics"]}}, "/api/analytics/productivity": {"get": {"description": "Returns detailed productivity analytics including hourly patterns, daily trends, peak hours, and optimization strategies.", "operationId": "AnalyticsController_getProductivityAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Analysis period", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}], "responses": {"200": {"description": "Returns productivity analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductivityAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get productivity analytics", "tags": ["analytics"]}}, "/api/analytics/engagement": {"get": {"description": "Returns comprehensive engagement analytics including podcast listening, calendar usage, challenge participation, and feature adoption.", "operationId": "AnalyticsController_getEngagementAnalytics", "parameters": [{"name": "period", "required": false, "in": "query", "description": "Analysis period", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}], "responses": {"200": {"description": "Returns engagement analytics for the specified period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementAnalyticsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get engagement analytics", "tags": ["analytics"]}}, "/api/calendar": {"post": {"operationId": "CalendarController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCalendarEventDto"}}}}, "responses": {"201": {"description": "Calendar event created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "400": {"description": "Bad request"}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Create a new calendar event", "tags": ["calendar"]}, "get": {"operationId": "CalendarController_findAll", "parameters": [{"name": "startDate", "required": false, "in": "query", "description": "Start date to filter events (ISO 8601 format)", "schema": {"example": "2025-06-01T00:00:00Z", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "End date to filter events (ISO 8601 format)", "schema": {"example": "2025-06-30T23:59:59Z", "type": "string"}}, {"name": "type", "required": false, "in": "query", "description": "Filter by event type", "schema": {"type": "string", "enum": ["habit", "task", "custom"]}}], "responses": {"200": {"description": "Calendar events retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Get all calendar events for the authenticated user", "tags": ["calendar"]}}, "/api/calendar/{id}": {"get": {"operationId": "CalendarController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Calendar event retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Get a specific calendar event by ID", "tags": ["calendar"]}, "patch": {"operationId": "CalendarController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCalendarEventDto"}}}}, "responses": {"200": {"description": "Calendar event updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Update a calendar event", "tags": ["calendar"]}, "delete": {"operationId": "CalendarController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "Calendar event deleted successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Delete a calendar event", "tags": ["calendar"]}}, "/api/calendar/{id}/toggle-complete": {"patch": {"operationId": "CalendarController_toggleComplete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Calendar event completion status toggled successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarEventResponseDto"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "Calendar event not found"}}, "security": [{"bearer": []}], "summary": "Toggle completion status of a calendar event", "tags": ["calendar"]}}, "/api/ai-suggestions": {"get": {"description": "Returns personalized improvement suggestions based on user data analysis using Google Gemini AI. Suggestions are cached for performance and can be filtered by category, priority, and other parameters.", "operationId": "AiSuggestionsController_getImprovementSuggestions", "parameters": [{"name": "category", "required": false, "in": "query", "description": "Filter suggestions by category", "schema": {"example": "habits", "type": "string", "enum": ["habits", "tasks", "productivity", "wellness", "learning", "goals", "mindfulness", "health", "relationships", "career"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter suggestions by priority level", "schema": {"example": "high", "type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "type", "required": false, "in": "query", "description": "Filter suggestions by type", "schema": {"example": "habit_improvement", "type": "string", "enum": ["habit_improvement", "task_optimization", "productivity_enhancement", "wellness_boost", "learning_acceleration", "goal_achievement", "time_management", "stress_reduction", "energy_optimization", "focus_improvement"]}}, {"name": "limit", "required": false, "in": "query", "description": "Maximum number of suggestions to return", "schema": {"minimum": 1, "maximum": 20, "default": 10, "example": 5, "type": "number"}}, {"name": "period", "required": false, "in": "query", "description": "Analytics period to consider for suggestions", "schema": {"default": "month", "example": "month", "type": "string", "enum": ["day", "week", "month", "quarter", "year"]}}, {"name": "forceRefresh", "required": false, "in": "query", "description": "Force refresh suggestions (bypass cache)", "schema": {"default": false, "example": false, "type": "boolean"}}, {"name": "actionableOnly", "required": false, "in": "query", "description": "Include only actionable suggestions", "schema": {"default": true, "example": true, "type": "boolean"}}, {"name": "minConfidence", "required": false, "in": "query", "description": "Minimum AI confidence threshold (0.0 - 1.0)", "schema": {"minimum": 0, "maximum": 1, "default": 0.6, "example": 0.7, "type": "number"}}], "responses": {"200": {"description": "Returns personalized improvement suggestions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImprovementSuggestionsResponseDto"}, "example": {"userId": "user-123-uuid", "suggestions": [{"id": "suggestion-456-u<PERSON>", "type": "habit_improvement", "category": "habits", "priority": "high", "difficulty": "medium", "title": "Optimize Your Morning Routine", "description": "Based on your activity patterns, establishing a consistent morning routine could improve your daily productivity by 25%.", "actionSteps": ["Set a consistent wake-up time (7:00 AM)", "Include 10 minutes of meditation or stretching", "Review your top 3 priorities for the day", "Prepare a healthy breakfast"], "expectedImpact": "Improved focus, better time management, and increased daily energy levels", "estimatedTimeToImplement": "2-3 weeks", "tags": ["morning", "routine", "productivity", "habits"], "aiConfidence": 0.87, "reasoning": "Your task completion rate is 40% higher in the morning hours (8-11 AM), and you currently lack a structured morning routine.", "metadata": {"dataSourcesUsed": ["habits", "tasks", "productivity_patterns"], "relatedInsights": ["peak_productivity_hours", "consistency_gaps"]}}], "totalSuggestions": 5, "analysis": {"analysisConfidence": 0.85, "dataQuality": "high", "identifiedPatterns": ["Strong weekday productivity", "Inconsistent weekend habits"], "improvementOpportunities": ["Weekend consistency", "Evening routine"], "userStrengths": ["Morning habits", "Task completion"]}, "generatedAt": "2024-05-15T14:30:00Z", "analysisConfidence": 0.85, "dataQuality": "high", "nextRefreshRecommended": "2024-05-16T14:30:00Z", "metadata": {"analysisType": "ai_powered", "dataSourcesUsed": ["habits", "tasks", "productivity", "analytics"], "aiModel": "gemini-2.0-flash", "processingTimeMs": 2340}}}}}}, "security": [{"bearer": []}], "summary": "Get AI-powered improvement suggestions", "tags": ["ai-suggestions"]}}, "/api/ai-suggestions/category/{category}": {"get": {"description": "Returns improvement suggestions filtered by a specific category (habits, tasks, productivity, etc.)", "operationId": "AiSuggestionsController_getSuggestionsByCategory", "parameters": [{"name": "category", "required": true, "in": "path", "description": "Category to filter suggestions by", "schema": {"enum": ["habits", "tasks", "productivity", "wellness", "learning", "goals", "mindfulness", "health", "relationships", "career"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Maximum number of suggestions to return", "schema": {"example": 5, "type": "number"}}], "responses": {"200": {"description": "Returns suggestions for the specified category", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImprovementSuggestionsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get suggestions by category", "tags": ["ai-suggestions"]}}, "/api/ai-suggestions/high-priority": {"get": {"description": "Returns the most important improvement suggestions for quick dashboard display", "operationId": "AiSuggestionsController_getHighPrioritySuggestions", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Maximum number of suggestions to return", "schema": {"example": 3, "type": "number"}}], "responses": {"200": {"description": "Returns high-priority suggestions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImprovementSuggestionsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get high-priority suggestions", "tags": ["ai-suggestions"]}}, "/api/ai-suggestions/{suggestionId}/implement": {"post": {"description": "Mark a suggestion as implemented by the user, optionally with implementation notes and success metrics", "operationId": "AiSuggestionsController_markSuggestionImplemented", "parameters": [{"name": "suggestionId", "required": true, "in": "path", "description": "ID of the suggestion to mark as implemented", "schema": {"example": "suggestion-456-u<PERSON>", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkSuggestionImplementedDto"}}}}, "responses": {"201": {"description": "Suggestion marked as implemented successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestionActionResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark suggestion as implemented", "tags": ["ai-suggestions"]}}, "/api/ai-suggestions/{suggestionId}/feedback": {"post": {"description": "Provide user feedback on a suggestion including rating, helpfulness, and comments", "operationId": "AiSuggestionsController_provideSuggestionFeedback", "parameters": [{"name": "suggestionId", "required": true, "in": "path", "description": "ID of the suggestion to provide feedback for", "schema": {"example": "suggestion-456-u<PERSON>", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProvideSuggestionFeedbackDto"}}}}, "responses": {"201": {"description": "Feed<PERSON> provided successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestionActionResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Provide feedback on a suggestion", "tags": ["ai-suggestions"]}}, "/api/ai-suggestions/{suggestionId}": {"delete": {"description": "Dismiss a suggestion that is not relevant or useful to the user", "operationId": "AiSuggestionsController_dismissSuggestion", "parameters": [{"name": "suggestionId", "required": true, "in": "path", "description": "ID of the suggestion to dismiss", "schema": {"example": "suggestion-456-u<PERSON>", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DismissSuggestionDto"}}}}, "responses": {"200": {"description": "Suggestion dismissed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestionActionResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Dismiss a suggestion", "tags": ["ai-suggestions"]}}, "/api/ai-suggestions/history": {"get": {"description": "Returns the history of suggestion generation, implementation, and feedback for the user", "operationId": "AiSuggestionsController_getSuggestionHistory", "parameters": [{"name": "days", "required": false, "in": "query", "description": "Number of days of history to return", "schema": {"example": 30, "type": "number"}}], "responses": {"200": {"description": "Returns suggestion history", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "history-123-uuid"}, "eventType": {"type": "string", "example": "suggestion_implemented"}, "eventDescription": {"type": "string", "example": "Implemented suggestion: Optimize Your Morning Routine"}, "eventData": {"type": "object"}, "contextData": {"type": "object"}, "suggestion": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "category": {"type": "string"}, "priority": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}}}}}}}}, "security": [{"bearer": []}], "summary": "Get suggestion history", "tags": ["ai-suggestions"]}}, "/podcasts/{filename}": {"get": {"operationId": "StaticFilesController_servePodcastFile", "parameters": [{"name": "filename", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["StaticFiles"]}}, "/api/webhooks/firebase/auth": {"post": {"operationId": "FirebaseWebhooksController_handleAuthWebhook", "parameters": [], "requestBody": {"required": true, "description": "Firebase Auth webhook payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirebaseWebhookDto"}, "examples": {"userCreated": {"value": {"event": {"type": "user.created", "data": {"uid": "firebase-uid", "email": "<EMAIL>", "metadata": {"createdAt": "2023-05-20T12:00:00.000Z"}}}}}, "userDeleted": {"value": {"event": {"type": "user.deleted", "data": {"uid": "firebase-uid"}}}}}}}}, "responses": {"201": {"description": ""}}, "summary": "Handle Firebase Authentication webhooks", "tags": ["webhooks"]}}, "/api/help/contact": {"post": {"operationId": "HelpController_submitContactInquiry", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactUsDto"}}}}, "responses": {"201": {"description": "Contact inquiry submitted successfully"}}, "summary": "Submit a contact inquiry", "tags": ["help"]}}, "/api/help/contacts": {"get": {"operationId": "HelpController_getContactInquiries", "parameters": [{"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get contact inquiries (Admin only)", "tags": ["help"]}}, "/api/help/contact/{id}": {"get": {"operationId": "HelpController_getContactInquiry", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Contact inquiry ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get a specific contact inquiry (Admin only)", "tags": ["help"]}}, "/api/help/contact/{id}/status": {"patch": {"operationId": "HelpController_updateContactInquiryStatus", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Contact inquiry ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Update contact inquiry status (Admin only)", "tags": ["help"]}}, "/api/help/feedback": {"post": {"operationId": "HelpController_submitFeedback", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeedbackDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> submitted successfully"}}, "summary": "Submit feedback", "tags": ["help"]}, "get": {"operationId": "HelpController_getFeedback", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get feedback submissions (Admin only)", "tags": ["help"]}}, "/api/help/feedback/{id}": {"get": {"operationId": "HelpController_getFeedbackById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get specific feedback (Admin only)", "tags": ["help"]}}, "/api/help/feedback/{id}/status": {"patch": {"operationId": "HelpController_updateFeedbackStatus", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Update feedback status (Admin only)", "tags": ["help"]}}, "/api/help/articles/search": {"get": {"operationId": "HelpController_searchHelpArticles", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"example": "how to reset password", "type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of results", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Results offset", "schema": {"default": 0, "example": 0, "type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "Search help articles", "tags": ["help"]}}, "/api/help/articles/popular": {"get": {"operationId": "HelpController_getPopularArticles", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of articles to return", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "summary": "Get popular help articles", "tags": ["help"]}}, "/api/help/articles/faq": {"get": {"operationId": "HelpController_getFAQs", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get frequently asked questions", "tags": ["help"]}}, "/api/help/articles/category/{category}": {"get": {"operationId": "HelpController_getHelpArticlesByCategory", "parameters": [{"name": "category", "required": true, "in": "path", "description": "Article category", "schema": {"enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"], "type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get help articles by category", "tags": ["help"]}}, "/api/help/articles/{id}": {"get": {"operationId": "HelpController_getHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Article ID", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get a specific help article", "tags": ["help"]}}, "/api/help/articles/{id}/helpful": {"post": {"operationId": "HelpController_markArticleHelpful", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Article ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"helpful": {"type": "boolean"}}}}}}, "responses": {"201": {"description": ""}}, "summary": "Mark article as helpful or not helpful", "tags": ["help"]}}, "/api/help/privacy-policy": {"get": {"operationId": "HelpController_getPrivacyPolicy", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get privacy policy content", "tags": ["help"]}}, "/api/help/terms-and-conditions": {"get": {"operationId": "HelpController_getTermsAndConditions", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get terms and conditions content", "tags": ["help"]}}, "/api/help/about-us": {"get": {"operationId": "HelpController_getAboutUs", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Get about us information", "tags": ["help"]}}, "/admin": {"get": {"operationId": "AdminWebController_adminRoot", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/login": {"get": {"operationId": "AdminWebController_loginPage", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_login", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/logout": {"post": {"operationId": "AdminWebController_logout", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/dashboard": {"get": {"operationId": "AdminWebController_dashboard", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/landing-page": {"get": {"operationId": "AdminWebController_landingPageManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/landing-page/{section}": {"post": {"operationId": "AdminWebController_updateLandingPageContent", "parameters": [{"name": "section", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/app-constants": {"get": {"operationId": "AdminWebController_appConstantsManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_updateAppConstants", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help": {"get": {"operationId": "AdminWebController_helpManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createHelpArticle", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help/new": {"get": {"operationId": "AdminWebController_newHelpArticle", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help/{id}/edit": {"get": {"operationId": "AdminWebController_editHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/help/{id}": {"post": {"operationId": "AdminWebController_updateHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/community": {"get": {"operationId": "AdminWebController_communityManagement", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/community/challenges/{id}/edit": {"get": {"operationId": "AdminWebController_editChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/community/challenges/{id}": {"post": {"operationId": "AdminWebController_updateChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/blog": {"get": {"operationId": "AdminWebController_blogManagement", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createBlogPost", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/blog/{id}": {"post": {"operationId": "AdminWebController_updateBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/api/blog": {"get": {"operationId": "AdminWebController_apiBlogGetAll", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_apiBlogCreate", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/api/blog/{id}": {"get": {"operationId": "AdminWebController_apiBlogGetOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "put": {"operationId": "AdminWebController_apiBlogUpdate", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "delete": {"operationId": "AdminWebController_apiBlogDelete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization": {"get": {"operationId": "AdminWebController_monetizationDashboard", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans": {"get": {"operationId": "AdminWebController_monetizationPlans", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createMonetizationPlan", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/new": {"get": {"operationId": "AdminWebController_newMonetizationPlan", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/{id}/edit": {"get": {"operationId": "AdminWebController_editMonetizationPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/{id}": {"post": {"operationId": "AdminWebController_updateMonetizationPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/plans/{id}/delete": {"post": {"operationId": "AdminWebController_deleteMonetizationPlan", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/subscriptions": {"get": {"operationId": "AdminWebController_monetizationSubscriptions", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/subscriptions/{id}": {"get": {"operationId": "AdminWebController_showMonetizationSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/subscriptions/{id}/cancel": {"post": {"operationId": "AdminWebController_cancelMonetizationSubscription", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons": {"get": {"operationId": "AdminWebController_monetizationCoupons", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}, "post": {"operationId": "AdminWebController_createMonetizationCoupon", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/new": {"get": {"operationId": "AdminWebController_newMonetizationCoupon", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/edit": {"get": {"operationId": "AdminWebController_editMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}": {"post": {"operationId": "AdminWebController_updateMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/delete": {"post": {"operationId": "AdminWebController_deleteMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/activate": {"post": {"operationId": "AdminWebController_activateMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/coupons/{id}/deactivate": {"post": {"operationId": "AdminWebController_deactivateMonetizationCoupon", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/analytics": {"get": {"operationId": "AdminWebController_monetizationAnalytics", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/analytics/export/csv": {"get": {"operationId": "AdminWebController_exportAnalyticsCSV", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/admin/monetization/analytics/export/report": {"get": {"operationId": "AdminWebController_generateAnalyticsReport", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["AdminWeb"]}}, "/api/admin/dashboard/stats": {"get": {"operationId": "AdminController_getDashboardStats", "parameters": [], "responses": {"200": {"description": "Returns dashboard statistics"}}, "security": [{"bearer": []}], "summary": "Get dashboard statistics", "tags": ["admin"]}}, "/api/admin/landing-page": {"get": {"operationId": "AdminController_getLandingPageContent", "parameters": [{"name": "section", "required": false, "in": "query", "description": "Specific section to retrieve", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns landing page content"}}, "security": [{"bearer": []}], "summary": "Get all landing page content sections", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateAllLandingPageContent", "parameters": [], "responses": {"200": {"description": "Landing page content updated successfully"}}, "security": [{"bearer": []}], "summary": "Update all landing page content at once", "tags": ["admin"]}}, "/api/admin/landing-page/{section}": {"put": {"operationId": "AdminController_updateLandingPageContent", "parameters": [{"name": "section", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Landing page content updated successfully"}}, "security": [{"bearer": []}], "summary": "Update landing page content for a specific section", "tags": ["admin"]}}, "/api/admin/app-constants": {"get": {"operationId": "AdminController_getAppConstants", "parameters": [], "responses": {"200": {"description": "Returns app constants"}}, "security": [{"bearer": []}], "summary": "Get app constants", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateAppConstants", "parameters": [], "responses": {"200": {"description": "App constants updated successfully"}}, "security": [{"bearer": []}], "summary": "Update app constants", "tags": ["admin"]}}, "/api/admin/help": {"get": {"operationId": "AdminController_getHelpArticles", "parameters": [], "responses": {"200": {"description": "Returns all help articles", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all help articles", "tags": ["admin"]}, "post": {"operationId": "AdminController_createHelpArticle", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateHelpArticleDto"}}}}, "responses": {"201": {"description": "Help article created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Create new help article", "tags": ["admin"]}}, "/api/admin/help/{id}": {"get": {"operationId": "AdminController_getHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns help article", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get help article by ID", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHelpArticleDto"}}}}, "responses": {"200": {"description": "Help article updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelpArticleResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update help article", "tags": ["admin"]}, "delete": {"operationId": "AdminController_deleteHelpArticle", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Help article deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete help article", "tags": ["admin"]}}, "/api/admin/community": {"get": {"operationId": "AdminController_getCommunityData", "parameters": [], "responses": {"200": {"description": "Returns community data and statistics"}}, "security": [{"bearer": []}], "summary": "Get community data overview", "tags": ["admin"]}}, "/api/admin/community/challenges": {"get": {"operationId": "AdminController_getChallenges", "parameters": [], "responses": {"200": {"description": "Returns all challenges"}}, "security": [{"bearer": []}], "summary": "Get all challenges", "tags": ["admin"]}}, "/api/admin/community/challenges/{id}": {"get": {"operationId": "AdminController_getChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns challenge details"}}, "security": [{"bearer": []}], "summary": "Get challenge by ID", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Challenge updated successfully"}}, "security": [{"bearer": []}], "summary": "Update challenge", "tags": ["admin"]}, "delete": {"operationId": "AdminController_deleteChallenge", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Challenge deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete challenge", "tags": ["admin"]}}, "/api/admin/blog/stats": {"get": {"operationId": "AdminController_getBlogStats", "parameters": [], "responses": {"200": {"description": "Returns blog statistics"}}, "security": [{"bearer": []}], "summary": "Get blog statistics", "tags": ["admin"]}}, "/api/admin/blog": {"get": {"operationId": "AdminController_getAllBlogPosts", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns all blog posts"}}, "security": [{"bearer": []}], "summary": "Get all blog posts", "tags": ["admin"]}, "post": {"operationId": "AdminController_createBlogPost", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}}}, "responses": {"201": {"description": "Blog post created successfully"}}, "security": [{"bearer": []}], "summary": "Create new blog post", "tags": ["admin"]}}, "/api/admin/blog/{id}": {"get": {"operationId": "AdminController_getBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns blog post"}}, "security": [{"bearer": []}], "summary": "Get blog post by ID", "tags": ["admin"]}, "put": {"operationId": "AdminController_updateBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogPostDto"}}}}, "responses": {"200": {"description": "Blog post updated successfully"}}, "security": [{"bearer": []}], "summary": "Update blog post", "tags": ["admin"]}, "delete": {"operationId": "AdminController_deleteBlogPost", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Blog post deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete blog post", "tags": ["admin"]}}, "/admin/api/upload/image": {"post": {"operationId": "UploadController_uploadImage", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Upload"]}}, "/admin/api/upload/image-by-url": {"post": {"operationId": "UploadController_uploadImageByUrl", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Upload"]}}, "/api/blog": {"get": {"operationId": "BlogController_getAllPosts", "parameters": [{"name": "query", "required": false, "in": "query", "description": "Search query", "schema": {"type": "string"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["draft", "published", "archived"]}}, {"name": "tags", "required": false, "in": "query", "description": "Filter by tags (comma-separated)", "schema": {"type": "string"}}, {"name": "featured", "required": false, "in": "query", "description": "Show only featured posts", "schema": {"type": "boolean"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"minimum": 1, "maximum": 100, "default": 10, "type": "number"}}, {"name": "offset", "required": false, "in": "query", "description": "Number of posts to skip", "schema": {"minimum": 0, "default": 0, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field", "schema": {"default": "publishedAt", "type": "string", "enum": ["createdAt", "publishedAt", "views", "likes", "title"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns paginated blog posts", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}}}}}, "summary": "Get all published blog posts", "tags": ["blog-api"]}}, "/api/blog/featured": {"get": {"operationId": "BlogController_getFeaturedPosts", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"example": 5, "type": "number"}}], "responses": {"200": {"description": "Returns featured blog posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}, "total": {"type": "number"}}}}}}}, "summary": "Get featured blog posts", "tags": ["blog-api"]}}, "/api/blog/category/{category}": {"get": {"operationId": "BlogController_getPostsByCategory", "parameters": [{"name": "category", "required": true, "in": "path", "description": "Blog category", "schema": {"enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of posts to return", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "Returns blog posts by category", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}, "total": {"type": "number"}}}}}}}, "summary": "Get blog posts by category", "tags": ["blog-api"]}}, "/api/blog/{slug}": {"get": {"operationId": "BlogController_getPostBySlug", "parameters": [{"name": "slug", "required": true, "in": "path", "description": "Blog post slug", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the blog post", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlogPostResponseDto"}}}}, "404": {"description": "Blog post not found"}}, "summary": "Get a blog post by slug", "tags": ["blog-api"]}}, "/api/blog/{id}/related": {"get": {"operationId": "BlogController_getRelatedPosts", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Blog post ID", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of related posts to return", "schema": {"example": 3, "type": "number"}}], "responses": {"200": {"description": "Returns related blog posts", "content": {"application/json": {"schema": {"type": "object", "properties": {"posts": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostResponseDto"}}, "total": {"type": "number"}}}}}}}, "summary": "Get related blog posts", "tags": ["blog-api"]}}, "/api/feedback": {"post": {"operationId": "FeedbackController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMobileFeedbackDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Submit mobile app feedback", "tags": ["Mobile Feedback"]}, "get": {"operationId": "FeedbackController_findAll", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search in title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns paginated feedback list", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get all feedback (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/anonymous": {"post": {"operationId": "FeedbackController_createAnonymous", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMobileFeedbackDto"}}}}, "responses": {"201": {"description": "Anonymous feedback submitted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "summary": "Submit anonymous feedback (no authentication required)", "tags": ["Mobile Feedback"]}}, "/api/feedback/my-feedback": {"get": {"operationId": "FeedbackController_findMyFeedback", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search term for title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": "Returns user's feedback list", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get current user's feedback", "tags": ["Mobile Feedback"]}}, "/api/feedback/stats": {"get": {"operationId": "FeedbackController_getStats", "parameters": [], "responses": {"200": {"description": "Returns feedback statistics"}}, "security": [{"bearer": []}], "summary": "Get feedback statistics (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/priority-stats": {"get": {"operationId": "FeedbackController_getPriorityStats", "parameters": [], "responses": {"200": {"description": "Returns priority statistics"}}, "security": [{"bearer": []}], "summary": "Get priority distribution statistics (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/top-feature-requests": {"get": {"operationId": "FeedbackController_getTopFeatureRequests", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Number of feature requests to return", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns top feature requests", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get top feature requests (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/{id}": {"get": {"operationId": "FeedbackController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns feedback details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get feedback by ID", "tags": ["Mobile Feedback"]}, "patch": {"operationId": "FeedbackController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMobileFeedbackDto"}}}}, "responses": {"200": {"description": "Feedback updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Update feedback (Admin only)", "tags": ["Mobile Feedback"]}, "delete": {"operationId": "FeedbackController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "responses": {"204": {"description": "Feedback deleted successfully"}}, "security": [{"bearer": []}], "summary": "Delete feedback (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/{id}/resolve": {"patch": {"operationId": "FeedbackController_markAsResolved", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"resolutionNotes": {"type": "string", "description": "Optional resolution notes"}}}}}}, "responses": {"200": {"description": "Feedback marked as resolved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Mark feedback as resolved (Admin only)", "tags": ["Mobile Feedback"]}}, "/api/feedback/{id}/assign": {"patch": {"operationId": "FeedbackController_assignToAdmin", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Feedback ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"adminId": {"type": "string", "description": "Admin user ID"}}, "required": ["adminId"]}}}}, "responses": {"200": {"description": "Fe<PERSON><PERSON> assigned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileFeedbackResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Assign feedback to admin (Admin only)", "tags": ["Mobile Feedback"]}}, "/admin/feedback": {"get": {"operationId": "FeedbackAdminWebController_index", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search term for title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/stats": {"get": {"operationId": "FeedbackAdminWebController_stats", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}": {"get": {"operationId": "FeedbackAdminWebController_show", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}, "post": {"operationId": "FeedbackAdminWebController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/edit": {"get": {"operationId": "FeedbackAdminWebController_edit", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/assign": {"post": {"operationId": "FeedbackAdminWebController_assign", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/resolve": {"post": {"operationId": "FeedbackAdminWebController_resolve", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/{id}/delete": {"post": {"operationId": "FeedbackAdminWebController_delete", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}, "/admin/feedback/export/csv": {"get": {"operationId": "FeedbackAdminWebController_exportCsv", "parameters": [{"name": "type", "required": false, "in": "query", "description": "Filter by feedback type", "schema": {"type": "string", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}}, {"name": "status", "required": false, "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}}, {"name": "priority", "required": false, "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "platform", "required": false, "in": "query", "description": "Filter by platform", "schema": {"type": "string", "enum": ["ios", "android", "web"]}}, {"name": "assignedTo", "required": false, "in": "query", "description": "Filter by assigned admin", "schema": {"type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search term for title and description", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"minimum": 1, "default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"minimum": 1, "maximum": 100, "default": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort field", "schema": {"default": "createdAt", "type": "string", "enum": ["createdAt", "updatedAt", "priority", "status", "rating"]}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort direction", "schema": {"default": "DESC", "type": "string", "enum": ["ASC", "DESC"]}}], "responses": {"200": {"description": ""}}, "tags": ["FeedbackAdminWeb"]}}}, "info": {"title": "Power Up API", "description": "The Power Up API description", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"CreateUserDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "StrongPassword123", "minLength": 6}, "firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}}, "required": ["email", "password", "firstName", "lastName"]}, "UserResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique user ID", "example": "123e4567-e89b-12d3-a456-************"}, "email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}, "xp": {"type": "number", "description": "User experience points", "example": 150}, "badges": {"description": "User badges earned", "example": ["early-bird", "consistent-performer"], "type": "array", "items": {"type": "string"}}, "provider": {"type": "string", "description": "Authentication provider", "enum": ["local", "google", "apple"], "example": "local"}, "picture": {"type": "string", "description": "User profile picture URL", "example": "https://example.com/avatar.jpg", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "User creation date", "example": "2025-05-19T10:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "User last update date", "example": "2025-05-19T10:00:00Z"}}, "required": ["id", "email", "firstName", "lastName", "xp", "badges", "provider", "picture", "createdAt", "updatedAt"]}, "UpdateUserDto": {"type": "object", "properties": {"firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}}}, "DeleteAccountDto": {"type": "object", "properties": {"password": {"type": "string", "description": "Password confirmation for account deletion", "minLength": 6}, "reason": {"type": "string", "description": "Optional reason for deleting the account"}}, "required": ["password"]}, "UserDataExportDto": {"type": "object", "properties": {"profile": {"type": "object", "description": "User profile information"}, "habits": {"description": "User habits data", "type": "array", "items": {"type": "string"}}, "tasks": {"description": "User tasks data", "type": "array", "items": {"type": "string"}}, "exportedAt": {"format": "date-time", "type": "string", "description": "Data export timestamp"}}, "required": ["profile", "habits", "tasks", "exportedAt"]}, "RegisterDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "StrongPassword123", "minLength": 6}, "firstName": {"type": "string", "description": "User first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User last name", "example": "<PERSON><PERSON>"}}, "required": ["email", "password", "firstName", "lastName"]}, "AuthResponseDto": {"type": "object", "properties": {"access_token": {"type": "string", "description": "JWT access token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "user": {"type": "object", "description": "User info", "example": {"id": "123e4567-e89b-12d3-a456-************", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}}}, "required": ["access_token", "user"]}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User password", "example": "StrongPassword123"}}, "required": ["email", "password"]}, "SocialLoginDto": {"type": "object", "properties": {"idToken": {"type": "string", "description": "Firebase ID token from social authentication", "example": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjFlOTczZWUwZTE2ZjdlZWY5NDY5MDdhMDYyYjZhODQyZGQzYzdiMTAiLCJ0eXAiOiJKV1QifQ..."}}, "required": ["idToken"]}, "ForgotPasswordDto": {"type": "object", "properties": {"email": {"type": "string", "description": "User email address to send password reset link", "example": "<EMAIL>"}}, "required": ["email"]}, "ResetPasswordDto": {"type": "object", "properties": {"code": {"type": "string", "description": "8-character reset code received in email", "example": "ABC12345"}, "newPassword": {"type": "string", "description": "New password", "example": "NewStrongPassword123", "minLength": 6}}, "required": ["code", "newPassword"]}, "ReminderSettingsDto": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Whether reminders are enabled for this habit", "example": true}, "time": {"type": "string", "description": "Time of day for the reminder in HH:MM format", "example": "08:00"}, "days": {"description": "Days of the week for the reminder", "example": ["Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}}, "required": ["enabled", "time", "days"]}, "CreateHabitDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the habit", "example": "Morning Meditation"}, "description": {"type": "string", "description": "Description of the habit", "example": "A 10-minute meditation practice every morning to start the day focused"}, "schedule": {"description": "Days or frequency of the habit", "example": ["daily", "weekdays", "Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}, "reminderSettings": {"description": "Reminder settings for the habit", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}, "required": ["name", "schedule"]}, "ReminderSettingsResponseDto": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Whether reminder is enabled", "example": true}, "time": {"type": "string", "description": "Time for the reminder", "example": "08:00"}, "days": {"description": "Days for the reminder", "example": ["Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}}, "required": ["enabled", "time", "days"]}, "HabitResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique habit ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Habit name", "example": "Morning Meditation"}, "description": {"type": "string", "description": "Habit description", "example": "A 10-minute mindfulness practice", "nullable": true}, "schedule": {"description": "Schedule (days of week)", "example": ["Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}, "currentStreak": {"type": "number", "description": "Current streak count", "example": 5}, "longestStreak": {"type": "number", "description": "Longest streak achieved", "example": 12}, "completion": {"type": "object", "description": "Completion record by date", "example": {"2025-06-01": true, "2025-06-02": false}}, "xpReward": {"type": "number", "description": "Experience points reward for completing habit", "example": 10}, "lastCompletedAt": {"format": "date-time", "type": "string", "description": "Date when habit was last completed", "example": "2025-06-01", "nullable": true}, "createdAt": {"format": "date-time", "type": "string", "description": "Habit creation date", "example": "2025-05-19T10:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Habit last update date", "example": "2025-05-19T10:00:00Z"}, "reminderSettings": {"description": "Reminder settings", "nullable": true, "allOf": [{"$ref": "#/components/schemas/ReminderSettingsResponseDto"}]}}, "required": ["id", "name", "description", "schedule", "currentStreak", "longestStreak", "completion", "xpReward", "lastCompletedAt", "createdAt", "updatedAt", "reminderSettings"]}, "UpdateHabitDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the habit", "example": "Morning Meditation"}, "description": {"type": "string", "description": "Description of the habit", "example": "A 10-minute meditation practice every morning to start the day focused"}, "schedule": {"description": "Days or frequency of the habit", "example": ["daily", "weekdays", "Monday", "Wednesday", "Friday"], "type": "array", "items": {"type": "string"}}, "reminderSettings": {"description": "Reminder settings for the habit", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}}, "SendDirectMessageDto": {"type": "object", "properties": {"recipientId": {"type": "string", "description": "ID of the recipient user", "example": "123e4567-e89b-12d3-a456-************"}, "content": {"type": "string", "description": "Message content", "example": "Hello, how are you doing today?"}, "attachments": {"description": "Optional attachment URLs", "example": ["https://example.com/image.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["recipientId", "content"]}, "SendGroupMessageDto": {"type": "object", "properties": {"groupId": {"type": "string", "description": "ID of the group", "example": "123e4567-e89b-12d3-a456-************"}, "content": {"type": "string", "description": "Message content", "example": "Hello everyone! How is the challenge going?"}, "attachments": {"description": "Optional attachment URLs", "example": ["https://example.com/image.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["groupId", "content"]}, "SendChallengeMessageDto": {"type": "object", "properties": {"challengeId": {"type": "string", "description": "ID of the challenge", "example": "123e4567-e89b-12d3-a456-************"}, "content": {"type": "string", "description": "Message content", "example": "Just completed my daily goal for the challenge!"}, "attachments": {"description": "Optional attachment URLs", "example": ["https://example.com/image.jpg"], "type": "array", "items": {"type": "string"}}}, "required": ["challengeId", "content"]}, "NotificationResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Notification ID"}, "userId": {"type": "string", "description": "User ID"}, "type": {"type": "string", "description": "Notification type", "enum": ["habit_reminder", "habit_streak", "habit_milestone", "task_reminder", "task_milestone", "plan_progress", "plan_step_complete", "plan_complete", "challenge_start", "challenge_progress", "challenge_leaderboard", "challenge_complete", "challenge_achievement", "podcast_available", "podcast_recommendation"]}, "title": {"type": "string", "description": "Notification title"}, "body": {"type": "string", "description": "Notification body"}, "status": {"type": "string", "description": "Notification status", "enum": ["pending", "sent", "delivered", "failed", "read"]}, "data": {"type": "object", "description": "Additional notification data", "additionalProperties": true}, "deviceToken": {"type": "string", "description": "Device token used for sending"}, "firebaseMessageId": {"type": "string", "description": "Firebase message ID"}, "errorMessage": {"type": "string", "description": "Error message if failed"}, "sentAt": {"format": "date-time", "type": "string", "description": "When notification was sent"}, "deliveredAt": {"format": "date-time", "type": "string", "description": "When notification was delivered"}, "readAt": {"format": "date-time", "type": "string", "description": "When notification was read"}, "createdAt": {"format": "date-time", "type": "string", "description": "When notification was created"}, "updatedAt": {"format": "date-time", "type": "string", "description": "When notification was last updated"}}, "required": ["id", "userId", "type", "title", "body", "status", "data", "deviceToken", "firebaseMessageId", "errorMessage", "sentAt", "deliveredAt", "readAt", "createdAt", "updatedAt"]}, "PaginatedNotificationsResponseDto": {"type": "object", "properties": {"data": {"description": "Array of notifications", "type": "array", "items": {"$ref": "#/components/schemas/NotificationResponseDto"}}, "total": {"type": "number", "description": "Total number of notifications"}, "page": {"type": "number", "description": "Current page number"}, "limit": {"type": "number", "description": "Number of items per page"}, "totalPages": {"type": "number", "description": "Total number of pages"}, "hasNext": {"type": "boolean", "description": "Whether there is a next page"}, "hasPrev": {"type": "boolean", "description": "Whether there is a previous page"}}, "required": ["data", "total", "page", "limit", "totalPages", "hasNext", "has<PERSON>rev"]}, "RegisterDeviceDto": {"type": "object", "properties": {"deviceToken": {"type": "string", "description": "Device token for push notifications", "example": "exampleDeviceToken123456789"}, "deviceType": {"type": "string", "description": "Device type", "example": "ios", "enum": ["ios", "android", "web"]}, "deviceName": {"type": "string", "description": "Device name", "example": "<PERSON>'s iPhone"}}, "required": ["deviceToken", "deviceType"]}, "UpdateNotificationPreferencesDto": {"type": "object", "properties": {"taskReminders": {"type": "boolean", "description": "Enable/disable task reminder notifications", "example": true}, "habitReminders": {"type": "boolean", "description": "Enable/disable habit reminder notifications", "example": true}, "streakAlerts": {"type": "boolean", "description": "Enable/disable streak alert notifications", "example": true}, "milestoneCelebrations": {"type": "boolean", "description": "Enable/disable milestone celebration notifications", "example": true}, "challengeUpdates": {"type": "boolean", "description": "Enable/disable challenge update notifications", "example": true}, "newMessages": {"type": "boolean", "description": "Enable/disable new message notifications", "example": true}, "podcastReady": {"type": "boolean", "description": "Enable/disable podcast ready notifications", "example": true}}}, "CreateTaskDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Task title", "example": "Complete project proposal"}, "description": {"type": "string", "description": "Task description", "example": "Write up the proposal with timeline and budget"}, "dueDate": {"format": "date-time", "type": "string", "description": "Task due date", "example": "2025-05-25T10:00:00Z"}, "priority": {"type": "string", "description": "Task priority", "enum": ["low", "medium", "high"], "example": "medium"}, "reminderSettings": {"description": "Task reminder settings", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}, "required": ["title", "dueDate", "priority"]}, "TaskResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique task ID", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Task title", "example": "Complete project proposal"}, "description": {"type": "string", "description": "Task description", "example": "Write up the proposal with timeline and budget", "nullable": true}, "dueDate": {"format": "date-time", "type": "string", "description": "Task due date", "example": "2025-05-25T10:00:00Z"}, "priority": {"type": "string", "description": "Task priority", "enum": ["low", "medium", "high"], "example": "medium"}, "completed": {"type": "boolean", "description": "Whether task is completed", "example": false}, "completedAt": {"format": "date-time", "type": "string", "description": "Date when task was completed", "example": "2025-05-25T14:30:00Z", "nullable": true}, "notifiedOverdue": {"type": "boolean", "description": "Whether user has been notified about overdue task", "example": false}, "createdAt": {"format": "date-time", "type": "string", "description": "Task creation date", "example": "2025-05-19T10:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Task last update date", "example": "2025-05-19T10:00:00Z"}, "reminderSettings": {"type": "object", "description": "Task reminder settings", "nullable": true, "example": {"enabled": true, "time": "09:00"}}}, "required": ["id", "title", "description", "dueDate", "priority", "completed", "completedAt", "notifiedOverdue", "createdAt", "updatedAt", "reminderSettings"]}, "UpdateTaskDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Task title", "example": "Complete project proposal"}, "description": {"type": "string", "description": "Task description", "example": "Write up the proposal with timeline and budget"}, "dueDate": {"format": "date-time", "type": "string", "description": "Task due date", "example": "2025-05-25T10:00:00Z"}, "priority": {"type": "string", "description": "Task priority", "enum": ["low", "medium", "high"], "example": "medium"}, "reminderSettings": {"description": "Task reminder settings", "allOf": [{"$ref": "#/components/schemas/ReminderSettingsDto"}]}}}, "ChallengeRewardsDto": {"type": "object", "properties": {"xp": {"type": "number", "description": "Experience points reward", "example": 500}, "badges": {"description": "Badges to be rewarded", "example": ["Challenge Champion", "Early Bird"], "type": "array", "items": {"type": "string"}}}, "required": ["xp", "badges"]}, "ChallengeRulesDto": {"type": "object", "properties": {"goals": {"description": "Challenge goals", "example": ["Complete 10 workouts", "Meditate for 20 days"], "type": "array", "items": {"type": "string"}}, "requirements": {"description": "Challenge requirements", "example": ["Log activity daily", "Share progress weekly"], "type": "array", "items": {"type": "string"}}, "rewards": {"description": "Challenge rewards", "allOf": [{"$ref": "#/components/schemas/ChallengeRewardsDto"}]}}, "required": ["goals", "requirements", "rewards"]}, "CreateChallengeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Challenge name", "example": "Summer Fitness Challenge"}, "description": {"type": "string", "description": "Challenge description", "example": "A 30-day challenge to improve your fitness and well-being"}, "startDate": {"format": "date-time", "type": "string", "description": "Challenge start date", "example": "2025-06-01T00:00:00Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Challenge end date", "example": "2025-06-30T23:59:59Z"}, "rules": {"description": "Challenge rules", "allOf": [{"$ref": "#/components/schemas/ChallengeRulesDto"}]}}, "required": ["name", "description", "startDate", "endDate", "rules"]}, "ChallengeRules": {"type": "object", "properties": {"goals": {"description": "Challenge goals", "example": ["Complete 10 workouts", "Meditate for 20 days"], "type": "array", "items": {"type": "string"}}, "requirements": {"description": "Challenge requirements", "example": ["Log activity daily", "Share progress weekly"], "type": "array", "items": {"type": "string"}}, "rewards": {"type": "object", "description": "Challenge rewards", "example": {"xp": 500, "badges": ["Challenge Champion", "Early Bird"]}}}, "required": ["goals", "requirements", "rewards"]}, "ChallengeResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique challenge ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Challenge name", "example": "Summer Fitness Challenge"}, "description": {"type": "string", "description": "Challenge description", "example": "A 30-day challenge to improve your fitness and well-being"}, "startDate": {"format": "date-time", "type": "string", "description": "Challenge start date", "example": "2025-06-01T00:00:00Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Challenge end date", "example": "2025-06-30T23:59:59Z"}, "rules": {"description": "Challenge rules", "allOf": [{"$ref": "#/components/schemas/ChallengeRules"}]}, "creatorId": {"type": "string", "description": "Challenge creator user ID", "example": "123e4567-e89b-12d3-a456-************"}, "participantCount": {"type": "number", "description": "Number of participants", "example": 125}, "joined": {"type": "boolean", "description": "Whether the current user joined the challenge", "example": false}, "userProgress": {"type": "number", "description": "User progress in the challenge (0-100)", "example": 50, "nullable": true}}, "required": ["id", "name", "description", "startDate", "endDate", "rules", "creatorId", "participantCount", "joined", "userProgress"]}, "UpdateChallengeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Challenge name", "example": "Summer Fitness Challenge"}, "description": {"type": "string", "description": "Challenge description", "example": "A 30-day challenge to improve your fitness and well-being"}, "startDate": {"format": "date-time", "type": "string", "description": "Challenge start date", "example": "2025-06-01T00:00:00Z"}, "endDate": {"format": "date-time", "type": "string", "description": "Challenge end date", "example": "2025-06-30T23:59:59Z"}, "rules": {"description": "Challenge rules", "allOf": [{"$ref": "#/components/schemas/ChallengeRulesDto"}]}}}, "ProgressDto": {"type": "object", "properties": {"progress": {"type": "number", "description": "Progress percentage (0-100)", "example": 50}}, "required": ["progress"]}, "SkillPlanMetadataDto": {"type": "object", "properties": {"category": {"type": "string", "description": "Skill plan category", "example": "Programming"}, "difficulty": {"type": "string", "description": "Skill plan difficulty level", "example": "intermediate", "enum": ["beginner", "intermediate", "advanced"]}, "estimatedDuration": {"type": "string", "description": "Estimated duration to complete the skill plan", "example": "2 weeks"}, "tags": {"description": "Skill plan tags", "example": ["programming", "typescript", "web development"], "type": "array", "items": {"type": "string"}}}, "required": ["category", "difficulty", "estimatedDuration", "tags"]}, "ResourceDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Resource type (e.g., video, article, book)", "example": "video"}, "url": {"type": "string", "description": "Resource URL", "example": "https://example.com/video123"}, "title": {"type": "string", "description": "Resource title", "example": "Introduction to TypeScript"}}, "required": ["type", "url", "title"]}, "TaskDto": {"type": "object", "properties": {"description": {"type": "string", "description": "Task description", "example": "Complete the code exercise"}, "isCompleted": {"type": "boolean", "description": "Whether the task is completed", "example": false}}, "required": ["description", "isCompleted"]}, "StepDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Step title", "example": "Getting Started with TypeScript"}, "description": {"type": "string", "description": "Step description", "example": "Learn the basics of TypeScript including types, interfaces and classes"}, "order": {"type": "number", "description": "Step order (position in sequence)", "example": 1}, "resources": {"description": "Step resources", "type": "array", "items": {"$ref": "#/components/schemas/ResourceDto"}}, "tasks": {"description": "Step tasks", "type": "array", "items": {"$ref": "#/components/schemas/TaskDto"}}}, "required": ["title", "description", "order"]}, "CreateSkillPlanDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Skill plan name", "example": "Learn TypeScript from Scratch"}, "description": {"type": "string", "description": "Skill plan description", "example": "A comprehensive guide to learning TypeScript from basics to advanced concepts"}, "isPublic": {"type": "boolean", "description": "Whether the skill plan is publicly accessible", "example": true}, "metadata": {"description": "Skill plan metadata", "allOf": [{"$ref": "#/components/schemas/SkillPlanMetadataDto"}]}, "steps": {"description": "Skill plan steps", "type": "array", "items": {"$ref": "#/components/schemas/StepDto"}}}, "required": ["name", "description", "isPublic", "metadata", "steps"]}, "Resource": {"type": "object", "properties": {"type": {"type": "string", "description": "Resource type", "example": "video"}, "url": {"type": "string", "description": "Resource URL", "example": "https://example.com/video123"}, "title": {"type": "string", "description": "Resource title", "example": "Introduction to TypeScript"}}, "required": ["type", "url", "title"]}, "Task": {"type": "object", "properties": {"description": {"type": "string", "description": "Task description", "example": "Complete the code exercise"}, "isCompleted": {"type": "boolean", "description": "Whether task is completed", "example": false}}, "required": ["description", "isCompleted"]}, "Step": {"type": "object", "properties": {"id": {"type": "number", "description": "Step ID", "example": 1}, "title": {"type": "string", "description": "Step title", "example": "Getting Started with TypeScript"}, "description": {"type": "string", "description": "Step description", "example": "Learn the basics of TypeScript including types, interfaces and classes"}, "order": {"type": "number", "description": "Step order", "example": 1}, "isCompleted": {"type": "boolean", "description": "Whether step is completed", "example": false}, "resources": {"description": "Step resources", "type": "array", "items": {"$ref": "#/components/schemas/Resource"}}, "tasks": {"description": "Step tasks", "type": "array", "items": {"$ref": "#/components/schemas/Task"}}}, "required": ["id", "title", "description", "order", "isCompleted", "resources", "tasks"]}, "SkillPlanResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique skill plan ID", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Skill plan name", "example": "Learn TypeScript from Scratch"}, "description": {"type": "string", "description": "Skill plan description", "example": "A comprehensive guide to learning TypeScript from basics to advanced concepts"}, "isPublic": {"type": "boolean", "description": "Whether skill plan is public", "example": true}, "creatorId": {"type": "string", "description": "Creator user ID", "example": "123e4567-e89b-12d3-a456-************"}, "createdAt": {"format": "date-time", "type": "string", "description": "Skill plan creation date", "example": "2025-05-19T10:00:00Z"}, "metadata": {"type": "object", "description": "Skill plan metadata", "example": {"category": "Programming", "difficulty": "intermediate", "estimatedDuration": "2 weeks", "tags": ["programming", "typescript", "web development"]}}, "steps": {"description": "Skill plan steps", "type": "array", "items": {"$ref": "#/components/schemas/Step"}}, "progressPercent": {"type": "number", "description": "Overall progress percentage", "example": 50}}, "required": ["id", "name", "description", "isPublic", "creatorId", "createdAt", "metadata", "steps", "progressPercent"]}, "CreateAiSkillPlanDto": {"type": "object", "properties": {"topic": {"type": "string", "description": "Topic or skill you want to learn", "example": "Learn React.js development"}, "level": {"type": "string", "description": "Your current experience level", "example": "beginner", "enum": ["beginner", "intermediate", "advanced"]}, "timeCommitment": {"type": "string", "description": "Estimated time you can dedicate per day (in minutes)", "example": 60}, "goals": {"description": "Specific learning goals or objectives", "example": "Build a portfolio website, Learn component-based development", "type": "array", "items": {"type": "string"}}, "preferredResources": {"description": "Preferred learning resources (video, article, practice, etc.)", "example": ["video", "practice"], "type": "array", "items": {"type": "string"}}, "isPublic": {"type": "boolean", "description": "Whether the skill plan should be public", "example": false}}, "required": ["topic", "level"]}, "UpdateSkillPlanDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Skill plan name", "example": "Learn TypeScript from Scratch"}, "description": {"type": "string", "description": "Skill plan description", "example": "A comprehensive guide to learning TypeScript from basics to advanced concepts"}, "isPublic": {"type": "boolean", "description": "Whether the skill plan is publicly accessible", "example": true}, "metadata": {"description": "Skill plan metadata", "allOf": [{"$ref": "#/components/schemas/SkillPlanMetadataDto"}]}, "steps": {"description": "Skill plan steps", "type": "array", "items": {"$ref": "#/components/schemas/StepDto"}}}}, "UpdateResourceDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Resource type (e.g., video, article, book)", "example": "video"}, "url": {"type": "string", "description": "Resource URL", "example": "https://example.com/updated-video"}, "title": {"type": "string", "description": "Resource title", "example": "Updated Introduction to TypeScript"}}, "required": ["type", "url", "title"]}, "UpdateStepDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Step title", "example": "Updated Getting Started with TypeScript"}, "description": {"type": "string", "description": "Step description", "example": "Updated basics of TypeScript"}, "order": {"type": "number", "description": "Step order (position in sequence)", "example": 2}, "resources": {"description": "Step resources", "type": "array", "items": {"$ref": "#/components/schemas/UpdateResourceDto"}}, "tasks": {"description": "Step tasks", "type": "array", "items": {"$ref": "#/components/schemas/UpdateTaskDto"}}}, "required": ["title", "description", "order"]}, "UpdateStepCompletionDto": {"type": "object", "properties": {"order": {"type": "number", "description": "Step order number", "example": 1}, "completed": {"type": "boolean", "description": "Whether the step is completed", "example": true}}, "required": ["order", "completed"]}, "PodcastResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique podcast ID", "example": "123e4567-e89b-12d3-a456-************"}, "title": {"type": "string", "description": "Podcast title", "example": "Your Daily Mindfulness Coach"}, "description": {"type": "string", "description": "Podcast description", "example": "A personalized meditation session focusing on stress reduction"}, "audioUrl": {"type": "string", "description": "Podcast audio URL", "example": "https://storage.example.com/podcasts/123e4567-e89b-12d3-a456-************.mp3"}, "durationSeconds": {"type": "number", "description": "Podcast duration in seconds", "example": 600}, "createdAt": {"format": "date-time", "type": "string", "description": "Podcast generation date", "example": "2025-05-19T10:00:00Z"}, "listened": {"type": "boolean", "description": "Whether the podcast has been listened to", "example": false}, "userId": {"type": "string", "description": "User ID who owns the podcast", "example": "123e4567-e89b-12d3-a456-************"}}, "required": ["id", "title", "description", "audioUrl", "durationSeconds", "createdAt", "listened", "userId"]}, "CustomPodcastPreferencesDto": {"type": "object", "properties": {"topic": {"type": "string", "description": "Optional podcast topic preference", "example": "Morning Motivation"}, "mood": {"type": "string", "description": "Optional mood preference to influence podcast tone", "example": "energetic"}, "language": {"type": "string", "description": "Optional language preference for podcast content", "example": "English"}}}, "CreateSubscriptionDto": {"type": "object", "properties": {"planId": {"type": "string"}, "couponId": {"type": "string"}, "originalTransactionId": {"type": "string"}, "latestReceipt": {"type": "string"}, "purchaseToken": {"type": "string"}, "startsAt": {"type": "string"}, "expiresAt": {"type": "string"}, "isFreeTrialTier": {"type": "boolean"}, "freeTrialEndsAt": {"type": "string"}, "price": {"type": "number"}, "currency": {"type": "string"}, "metadata": {"type": "object"}}, "required": ["planId", "startsAt", "expiresAt"]}, "UpdateSubscriptionDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["active", "expired", "cancelled", "pending", "grace_period", "free_trial"]}, "latestReceipt": {"type": "string"}, "purchaseToken": {"type": "string"}, "expiresAt": {"type": "string"}, "cancelledAt": {"type": "string"}, "autoRenew": {"type": "boolean"}, "metadata": {"type": "object"}}}, "VerifyReceiptDto": {"type": "object", "properties": {"receipt": {"type": "string"}, "productId": {"type": "string"}}, "required": ["receipt"]}, "VerifyPurchaseTokenDto": {"type": "object", "properties": {"purchaseToken": {"type": "string"}, "productId": {"type": "string"}, "packageName": {"type": "string"}}, "required": ["purchaseToken", "productId", "packageName"]}, "ValidateCouponDto": {"type": "object", "properties": {"code": {"type": "string"}, "planId": {"type": "string"}}, "required": ["code"]}, "CreateSubscriptionPlanDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["weekly", "monthly", "yearly"]}, "platform": {"type": "string", "enum": ["ios", "android", "web"]}, "price": {"type": "number"}, "currency": {"type": "string", "default": "USD"}, "productId": {"type": "string"}, "hasFreeTrialTier": {"type": "boolean", "default": false}, "freeTrialDays": {"type": "number", "default": 0}, "active": {"type": "boolean", "default": true}, "features": {"type": "array", "items": {"type": "string"}}, "sortOrder": {"type": "number", "default": 0}}, "required": ["name", "type", "platform", "price", "currency", "productId", "hasFreeTrialTier", "freeTrialDays", "active", "sortOrder"]}, "UpdateSubscriptionPlanDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "price": {"type": "number"}, "currency": {"type": "string"}, "productId": {"type": "string"}, "hasFreeTrialTier": {"type": "boolean"}, "freeTrialDays": {"type": "number"}, "active": {"type": "boolean"}, "features": {"type": "array", "items": {"type": "string"}}, "sortOrder": {"type": "number"}}}, "CreateCouponCodeDto": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["percentage", "fixed_amount", "free_trial_extension"]}, "value": {"type": "number"}, "maxUses": {"type": "number"}, "validFrom": {"type": "string"}, "validUntil": {"type": "string"}, "firstTimeOnly": {"type": "boolean", "default": false}, "applicablePlans": {"type": "array", "items": {"type": "string"}}, "active": {"type": "boolean", "default": true}, "minimumPurchaseAmount": {"type": "number"}, "freeTrialDays": {"type": "number"}}, "required": ["code", "name", "type", "validFrom", "validUntil", "firstTimeOnly", "active"]}, "UpdateCouponCodeDto": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "value": {"type": "number"}, "maxUses": {"type": "number"}, "validFrom": {"type": "string"}, "validUntil": {"type": "string"}, "firstTimeOnly": {"type": "boolean"}, "applicablePlans": {"type": "array", "items": {"type": "string"}}, "active": {"type": "boolean"}, "minimumPurchaseAmount": {"type": "number"}, "freeTrialDays": {"type": "number"}}}, "UserOverviewDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID", "example": "user-123-uuid"}, "email": {"type": "string", "description": "User email", "example": "<EMAIL>"}, "fullName": {"type": "string", "description": "User full name", "example": "<PERSON>"}, "totalXp": {"type": "number", "description": "Total XP earned by user", "example": 2450}, "badges": {"description": "Badges earned by user", "example": ["habit_master", "task_crusher", "learning_enthusiast"], "type": "array", "items": {"type": "string"}}, "createdAt": {"format": "date-time", "type": "string", "description": "Account creation date", "example": "2024-01-15T10:30:00Z"}, "daysSinceCreation": {"type": "number", "description": "Days since account creation", "example": 125}}, "required": ["userId", "email", "fullName", "totalXp", "badges", "createdAt", "daysSinceCreation"]}, "ActivitySummaryDto": {"type": "object", "properties": {"totalHabits": {"type": "number", "description": "Total number of habits created", "example": 12}, "totalTasks": {"type": "number", "description": "Total number of tasks created", "example": 89}, "totalSkillPlans": {"type": "number", "description": "Total number of skill plans", "example": 5}, "totalChallenges": {"type": "number", "description": "Total number of challenges participated", "example": 3}, "totalPodcasts": {"type": "number", "description": "Total number of podcasts generated", "example": 15}, "totalCalendarEvents": {"type": "number", "description": "Total number of calendar events", "example": 45}}, "required": ["totalHabits", "totalTasks", "totalSkillPlans", "totalChallenges", "totalPodcasts", "totalCalendarEvents"]}, "EngagementMetricsDto": {"type": "object", "properties": {"averageDailyUsageMinutes": {"type": "number", "description": "Average daily app usage (estimated from activity)", "example": 45.5}, "mostActiveDay": {"type": "string", "description": "Most active day of the week", "example": "Monday"}, "mostActiveHour": {"type": "number", "description": "Most active hour of the day (24-hour format)", "example": 9}, "consistencyScore": {"type": "number", "description": "Consistency score (0-100)", "example": 78.5}, "activeDaysInPeriod": {"type": "number", "description": "Days active in the period", "example": 25}, "totalDaysInPeriod": {"type": "number", "description": "Total days in the period", "example": 30}}, "required": ["averageDailyUsageMinutes", "mostActiveDay", "mostActiveHour", "consistencyScore", "activeDaysInPeriod", "totalDaysInPeriod"]}, "ProgressTrendDto": {"type": "object", "properties": {"date": {"type": "string", "description": "Date of the data point", "example": "2024-05-15"}, "progressScore": {"type": "number", "description": "Overall progress score for the day (0-100)", "example": 85.5}, "habitsCompleted": {"type": "number", "description": "Habits completed on this day", "example": 4}, "tasksCompleted": {"type": "number", "description": "Tasks completed on this day", "example": 7}, "xpGained": {"type": "number", "description": "XP gained on this day", "example": 150}}, "required": ["date", "progressScore", "habitsCompleted", "tasksCompleted", "xpGained"]}, "UserAnalyticsResponseDto": {"type": "object", "properties": {"period": {"type": "string", "description": "Analysis period", "example": "month", "enum": ["day", "week", "month", "quarter", "year"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date", "example": "2024-04-15T00:00:00Z"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date", "example": "2024-05-15T23:59:59Z"}, "userOverview": {"description": "User overview information", "allOf": [{"$ref": "#/components/schemas/UserOverviewDto"}]}, "activitySummary": {"description": "Activity summary across all modules", "allOf": [{"$ref": "#/components/schemas/ActivitySummaryDto"}]}, "engagementMetrics": {"description": "Engagement metrics and patterns", "allOf": [{"$ref": "#/components/schemas/EngagementMetricsDto"}]}, "progressTrends": {"description": "Daily progress trends over the period", "type": "array", "items": {"$ref": "#/components/schemas/ProgressTrendDto"}}, "overallWellnessScore": {"type": "number", "description": "Overall wellness score (0-100)", "example": 82.3}, "keyAchievements": {"description": "Key achievements in the period", "example": ["Completed 5 skill plan steps", "Maintained 7-day habit streak", "Generated 3 podcasts"], "type": "array", "items": {"type": "string"}}, "improvementAreas": {"description": "Areas for improvement", "example": ["Increase task completion rate", "Improve weekend consistency"], "type": "array", "items": {"type": "string"}}, "generatedAt": {"format": "date-time", "type": "string", "description": "Report generation timestamp", "example": "2024-05-15T14:30:00Z"}}, "required": ["period", "periodStart", "periodEnd", "userOverview", "activitySummary", "engagementMetrics", "progressTrends", "overallWellnessScore", "keyAchievements", "improvementAreas", "generatedAt"]}, "MetricDto": {"type": "object", "properties": {"category": {"type": "string", "description": "Metric category", "example": "productivity", "enum": ["productivity", "habits", "tasks", "learning", "engagement", "wellness"]}, "name": {"type": "string", "description": "Metric name", "example": "Task Completion Rate"}, "currentValue": {"type": "number", "description": "Current value", "example": 78.5}, "targetValue": {"type": "number", "description": "Target value (if applicable)", "example": 85}, "unit": {"type": "string", "description": "Unit of measurement", "example": "percentage"}, "trend": {"type": "string", "description": "Trend direction", "example": "improving", "enum": ["improving", "stable", "declining"]}, "changeFromPrevious": {"type": "number", "description": "Change from previous period", "example": 5.2}}, "required": ["category", "name", "currentValue", "targetValue", "unit", "trend", "changeFromPrevious"]}, "AreaPerformanceDto": {"type": "object", "properties": {"area": {"type": "string", "description": "Performance area name", "example": "Habit Consistency"}, "score": {"type": "number", "description": "Performance score (0-100)", "example": 87.5}, "level": {"type": "string", "description": "Performance level", "example": "Excellent", "enum": ["Poor", "Fair", "Good", "Very Good", "Excellent"]}, "strengths": {"description": "Identified strengths", "example": ["Consistent morning routine", "Strong weekday habits"], "type": "array", "items": {"type": "string"}}, "weaknesses": {"description": "Areas needing improvement", "example": ["Weekend consistency", "Evening routine"], "type": "array", "items": {"type": "string"}}, "metrics": {"description": "Supporting metrics", "type": "array", "items": {"$ref": "#/components/schemas/MetricDto"}}}, "required": ["area", "score", "level", "strengths", "weaknesses", "metrics"]}, "InsightDto": {"type": "object", "properties": {"priority": {"type": "string", "description": "Insight priority level", "example": "high", "enum": ["low", "medium", "high", "critical"]}, "category": {"type": "string", "description": "Insight category", "example": "productivity"}, "title": {"type": "string", "description": "Insight title", "example": "Peak Productivity Hours Identified"}, "description": {"type": "string", "description": "Detailed insight description", "example": "Your productivity consistently peaks between 9-11 AM"}, "evidence": {"type": "string", "description": "Supporting evidence", "example": "Based on 30 days of task completion data"}, "recommendations": {"description": "Actionable recommendations", "example": ["Schedule important tasks between 9-11 AM", "Block calendar during peak hours"], "type": "array", "items": {"type": "string"}}, "confidence": {"type": "string", "description": "Confidence level", "example": "high", "enum": ["low", "medium", "high"]}}, "required": ["priority", "category", "title", "description", "evidence", "recommendations", "confidence"]}, "BehaviorPatternDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Pattern type", "example": "temporal", "enum": ["temporal", "contextual", "behavioral", "performance"]}, "name": {"type": "string", "description": "Pattern name", "example": "Morning Productivity Peak"}, "description": {"type": "string", "description": "Pattern description", "example": "User shows consistently higher productivity in morning hours"}, "frequency": {"type": "string", "description": "Pattern frequency", "example": "daily", "enum": ["daily", "weekly", "monthly", "occasional"]}, "strength": {"type": "number", "description": "Pattern strength (0-100)", "example": 92.3}, "dataPoints": {"type": "number", "description": "Number of data points supporting this pattern", "example": 45}}, "required": ["type", "name", "description", "frequency", "strength", "dataPoints"]}, "ComprehensiveAnalyticsResponseDto": {"type": "object", "properties": {"period": {"type": "string", "description": "Analysis period", "example": "month", "enum": ["day", "week", "month", "quarter", "year"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date", "example": "2024-04-15T00:00:00Z"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date", "example": "2024-05-15T23:59:59Z"}, "overallWellnessScore": {"type": "number", "description": "Overall wellness score (0-100)", "example": 84.7}, "metrics": {"description": "Key performance metrics", "type": "array", "items": {"$ref": "#/components/schemas/MetricDto"}}, "areaPerformance": {"description": "Performance by area", "type": "array", "items": {"$ref": "#/components/schemas/AreaPerformanceDto"}}, "insights": {"description": "Generated insights and recommendations", "type": "array", "items": {"$ref": "#/components/schemas/InsightDto"}}, "behaviorPatterns": {"description": "Identified behavior patterns", "type": "array", "items": {"$ref": "#/components/schemas/BehaviorPatternDto"}}, "topStrengths": {"description": "Top identified strengths", "example": ["Habit Consistency", "Learning Progress", "Task Management"], "type": "array", "items": {"type": "string"}}, "improvementPriorities": {"description": "Priority improvement areas", "example": ["Weekend Productivity", "Evening Routine", "Challenge Participation"], "type": "array", "items": {"type": "string"}}, "nextPeriodRecommendations": {"description": "Recommendations for next period", "example": ["Establish evening routine", "Join a new challenge", "Increase weekend activity"], "type": "array", "items": {"type": "string"}}, "progressFromPrevious": {"type": "number", "description": "Progress change from previous period (%)", "example": 12.5}, "motivationalMessage": {"type": "string", "description": "Motivational message based on performance", "example": "Outstanding progress this month! Your consistency is paying off."}, "analysisConfidence": {"type": "string", "description": "Analysis confidence level", "example": "high", "enum": ["low", "medium", "high"]}, "dataPointsAnalyzed": {"type": "number", "description": "Total data points analyzed", "example": 1247}, "generatedAt": {"format": "date-time", "type": "string", "description": "Report generation timestamp", "example": "2024-05-15T14:30:00Z"}}, "required": ["period", "periodStart", "periodEnd", "overallWellnessScore", "metrics", "areaPerformance", "insights", "behaviorPatterns", "topStrengths", "improvementPriorities", "nextPeriodRecommendations", "progressFromPrevious", "motivationalMessage", "analysisConfidence", "dataPointsAnalyzed", "generatedAt"]}, "HabitDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Habit ID", "example": "habit-123-uuid"}, "name": {"type": "string", "description": "Habit name", "example": "Morning Exercise"}, "currentStreak": {"type": "number", "description": "Current streak length", "example": 15}, "longestStreak": {"type": "number", "description": "Longest streak achieved", "example": 28}, "completionRate": {"type": "number", "description": "Completion rate for the period (%)", "example": 85.7}, "totalCompletions": {"type": "number", "description": "Total completions in period", "example": 24}, "scheduledDays": {"type": "number", "description": "Total scheduled days in period", "example": 28}, "xpReward": {"type": "number", "description": "XP reward per completion", "example": 10}, "totalXpEarned": {"type": "number", "description": "Total XP earned from this habit", "example": 240}}, "required": ["id", "name", "currentStreak", "longestStreak", "completionRate", "totalCompletions", "scheduledDays", "xpReward", "totalXpEarned"]}, "HabitTrendDto": {"type": "object", "properties": {"date": {"type": "string", "description": "Date", "example": "2024-05-15"}, "completed": {"type": "number", "description": "Habits completed on this day", "example": 4}, "scheduled": {"type": "number", "description": "Habits scheduled for this day", "example": 5}, "completionRate": {"type": "number", "description": "Completion rate for this day (%)", "example": 80}}, "required": ["date", "completed", "scheduled", "completionRate"]}, "HabitInsightDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Insight type", "example": "streak_milestone", "enum": ["streak_milestone", "consistency_pattern", "improvement_opportunity", "achievement"]}, "title": {"type": "string", "description": "Insight title", "example": "Strong Weekend Performance"}, "description": {"type": "string", "description": "Detailed description", "example": "Your habit completion rate is 15% higher on weekends"}, "relatedHabits": {"description": "Related habit IDs", "example": ["habit-123-uuid", "habit-456-uuid"], "type": "array", "items": {"type": "string"}}, "recommendation": {"type": "string", "description": "Actionable recommendation", "example": "Consider adding more challenging habits on weekends"}}, "required": ["type", "title", "description", "relatedHabits", "recommendation"]}, "HabitAnalyticsResponseDto": {"type": "object", "properties": {"period": {"type": "string", "description": "Analysis period", "example": "month", "enum": ["day", "week", "month", "quarter", "year"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date", "example": "2024-04-15T00:00:00Z"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date", "example": "2024-05-15T23:59:59Z"}, "overallCompletionRate": {"type": "number", "description": "Overall habit completion rate (%)", "example": 78.5}, "totalHabits": {"type": "number", "description": "Total number of habits", "example": 8}, "activeHabits": {"type": "number", "description": "Number of active habits (with current streaks)", "example": 7}, "totalCompletions": {"type": "number", "description": "Total habit completions in period", "example": 156}, "totalPossibleCompletions": {"type": "number", "description": "Total possible completions in period", "example": 198}, "averageStreakLength": {"type": "number", "description": "Average streak length across all habits", "example": 12.3}, "longestCurrentStreak": {"type": "number", "description": "Longest current streak among all habits", "example": 28}, "totalXpEarned": {"type": "number", "description": "Total XP earned from habits", "example": 1560}, "habits": {"description": "Detailed habit information", "type": "array", "items": {"$ref": "#/components/schemas/HabitDto"}}, "dailyTrends": {"description": "Daily completion trends", "type": "array", "items": {"$ref": "#/components/schemas/HabitTrendDto"}}, "bestDay": {"type": "string", "description": "Best performing day of the week", "example": "Monday"}, "worstDay": {"type": "string", "description": "Worst performing day of the week", "example": "Friday"}, "mostConsistentHabit": {"type": "string", "description": "Most consistent habit", "example": "Morning Exercise"}, "habitNeedingAttention": {"type": "string", "description": "Habit that needs attention", "example": "Evening Reading"}, "insights": {"description": "Generated insights about habit patterns", "type": "array", "items": {"$ref": "#/components/schemas/HabitInsightDto"}}, "changeFromPrevious": {"type": "number", "description": "Change from previous period (%)", "example": 8.5}, "consistencyScore": {"type": "number", "description": "Overall consistency score (0-100)", "example": 82.7}, "generatedAt": {"format": "date-time", "type": "string", "description": "Report generation timestamp", "example": "2024-05-15T14:30:00Z"}}, "required": ["period", "periodStart", "periodEnd", "overallCompletionRate", "totalHabits", "activeHabits", "totalCompletions", "totalPossibleCompletions", "averageStreakLength", "longestCurrentStreak", "totalXpEarned", "habits", "dailyTrends", "bestDay", "worstDay", "mostConsistentHabit", "habitNeedingAttention", "insights", "changeFromPrevious", "consistencyScore", "generatedAt"]}, "TaskTrendDto": {"type": "object", "properties": {"date": {"type": "string", "description": "Date", "example": "2024-05-15"}, "created": {"type": "number", "description": "Tasks created on this day", "example": 3}, "completed": {"type": "number", "description": "Tasks completed on this day", "example": 5}, "overdue": {"type": "number", "description": "Tasks overdue on this day", "example": 1}, "completionRate": {"type": "number", "description": "Completion rate for this day (%)", "example": 83.3}}, "required": ["date", "created", "completed", "overdue", "completionRate"]}, "TaskInsightDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Insight type", "example": "completion_pattern", "enum": ["completion_pattern", "priority_analysis", "timing_insight", "efficiency_tip"]}, "title": {"type": "string", "description": "Insight title", "example": "High Priority Task Focus"}, "description": {"type": "string", "description": "Detailed description", "example": "You complete 95% of high-priority tasks on time"}, "recommendation": {"type": "string", "description": "Actionable recommendation", "example": "Consider reviewing priority levels for better management"}, "evidence": {"type": "string", "description": "Supporting evidence", "example": "Based on 45 completed tasks in the last month"}}, "required": ["type", "title", "description", "recommendation", "evidence"]}, "TaskAnalyticsResponseDto": {"type": "object", "properties": {"period": {"type": "string", "description": "Analysis period", "example": "month", "enum": ["day", "week", "month", "quarter", "year"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date", "example": "2024-04-15T00:00:00Z"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date", "example": "2024-05-15T23:59:59Z"}, "totalTasks": {"type": "number", "description": "Total number of tasks", "example": 67}, "completedTasks": {"type": "number", "description": "Number of completed tasks", "example": 52}, "overdueTasks": {"type": "number", "description": "Number of overdue tasks", "example": 8}, "completionRate": {"type": "number", "description": "Overall completion rate (%)", "example": 77.6}, "onTimeCompletionRate": {"type": "number", "description": "On-time completion rate (%)", "example": 84.6}, "averageDaysToComplete": {"type": "number", "description": "Average days to complete tasks", "example": 2.8}, "tasksByPriority": {"type": "object", "description": "Tasks grouped by priority", "example": {"high": 15, "medium": 35, "low": 17}}, "completionRateByPriority": {"type": "object", "description": "Completion rate by priority level", "example": {"high": 93.3, "medium": 77.1, "low": 64.7}}, "tasks": {"description": "Detailed task information", "type": "array", "items": {"$ref": "#/components/schemas/TaskDto"}}, "dailyTrends": {"description": "Daily task trends", "type": "array", "items": {"$ref": "#/components/schemas/TaskTrendDto"}}, "mostProductiveDay": {"type": "string", "description": "Most productive day of the week", "example": "Tuesday"}, "highestCreationDay": {"type": "string", "description": "Day with highest task creation", "example": "Monday"}, "averageOverdueDays": {"type": "number", "description": "Average days tasks are overdue", "example": 3.2}, "insights": {"description": "Generated insights about task patterns", "type": "array", "items": {"$ref": "#/components/schemas/TaskInsightDto"}}, "changeFromPrevious": {"type": "number", "description": "Change from previous period (%)", "example": 12.4}, "efficiencyScore": {"type": "number", "description": "Task efficiency score (0-100)", "example": 78.9}, "recommendedFocus": {"description": "Recommended focus areas", "example": ["Improve low-priority task completion", "Reduce overdue tasks"], "type": "array", "items": {"type": "string"}}, "generatedAt": {"format": "date-time", "type": "string", "description": "Report generation timestamp", "example": "2024-05-15T14:30:00Z"}}, "required": ["period", "periodStart", "periodEnd", "totalTasks", "completedTasks", "overdueTasks", "completionRate", "onTimeCompletionRate", "averageDaysToComplete", "tasksByPriority", "completionRateByPriority", "tasks", "dailyTrends", "mostProductiveDay", "highestCreationDay", "averageOverdueDays", "insights", "changeFromPrevious", "efficiencyScore", "recommendedFocus", "generatedAt"]}, "SkillPlanDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Skill plan ID", "example": "skill-plan-123-uuid"}, "title": {"type": "string", "description": "Skill plan title", "example": "Learn React Development"}, "category": {"type": "string", "description": "Skill category", "example": "Programming"}, "progress": {"type": "number", "description": "Current progress percentage", "example": 65.5}, "totalSteps": {"type": "number", "description": "Total number of steps", "example": 20}, "completedSteps": {"type": "number", "description": "Completed steps", "example": 13}, "estimatedCompletion": {"format": "date-time", "type": "string", "description": "Estimated completion date", "example": "2024-06-15T00:00:00Z"}, "daysSinceLastActivity": {"type": "number", "description": "Days since last activity", "example": 2}, "learningVelocity": {"type": "number", "description": "Learning velocity (steps per week)", "example": 3.5}}, "required": ["id", "title", "category", "progress", "totalSteps", "completedSteps", "estimatedCompletion", "daysSinceLastActivity", "learningVelocity"]}, "SkillPlanTrendDto": {"type": "object", "properties": {"date": {"type": "string", "description": "Date", "example": "2024-05-15"}, "stepsCompleted": {"type": "number", "description": "Steps completed on this day", "example": 2}, "timeSpentMinutes": {"type": "number", "description": "Time spent learning (minutes)", "example": 45}, "activeSkillPlans": {"type": "number", "description": "Number of active skill plans", "example": 3}}, "required": ["date", "stepsCompleted", "timeSpentMinutes", "activeSkillPlans"]}, "SkillPlanInsightDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Insight type", "example": "learning_pattern", "enum": ["learning_pattern", "progress_milestone", "velocity_change", "category_preference"]}, "title": {"type": "string", "description": "Insight title", "example": "Consistent Learning Schedule"}, "description": {"type": "string", "description": "Detailed description", "example": "You consistently complete skill plan steps on weekday evenings"}, "relatedSkillPlans": {"description": "Related skill plan IDs", "example": ["skill-plan-123-uuid", "skill-plan-456-uuid"], "type": "array", "items": {"type": "string"}}, "recommendation": {"type": "string", "description": "Actionable recommendation", "example": "Consider scheduling dedicated learning blocks during your peak learning times"}}, "required": ["type", "title", "description", "relatedSkillPlans", "recommendation"]}, "SkillPlanAnalyticsResponseDto": {"type": "object", "properties": {"period": {"type": "string", "description": "Analysis period", "example": "month", "enum": ["day", "week", "month", "quarter", "year"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date", "example": "2024-04-15T00:00:00Z"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date", "example": "2024-05-15T23:59:59Z"}, "totalSkillPlans": {"type": "number", "description": "Total number of skill plans", "example": 5}, "activeSkillPlans": {"type": "number", "description": "Number of active skill plans", "example": 4}, "completedSkillPlans": {"type": "number", "description": "Number of completed skill plans", "example": 1}, "averageProgress": {"type": "number", "description": "Average progress across all skill plans (%)", "example": 68.5}, "totalStepsCompleted": {"type": "number", "description": "Total steps completed in period", "example": 23}, "totalSteps": {"type": "number", "description": "Total steps across all skill plans", "example": 85}, "learningVelocity": {"type": "number", "description": "Learning velocity (steps per week)", "example": 5.2}, "skillPlans": {"description": "Detailed skill plan information", "type": "array", "items": {"$ref": "#/components/schemas/SkillPlanDto"}}, "categoryDistribution": {"type": "object", "description": "Distribution of skill plans by category", "example": {"Programming": 3, "Design": 1, "Business": 1}}, "dailyTrends": {"description": "Daily learning trends", "type": "array", "items": {"$ref": "#/components/schemas/SkillPlanTrendDto"}}, "mostActiveCategory": {"type": "string", "description": "Most active learning category", "example": "Programming"}, "insights": {"description": "Generated insights about learning patterns", "type": "array", "items": {"$ref": "#/components/schemas/SkillPlanInsightDto"}}, "changeFromPrevious": {"type": "number", "description": "Change from previous period (%)", "example": 15.3}, "recommendedFocus": {"description": "Recommended focus areas", "example": ["Complete React fundamentals", "Start advanced JavaScript course"], "type": "array", "items": {"type": "string"}}, "generatedAt": {"format": "date-time", "type": "string", "description": "Report generation timestamp", "example": "2024-05-15T14:30:00Z"}}, "required": ["period", "periodStart", "periodEnd", "totalSkillPlans", "activeSkillPlans", "completedSkillPlans", "averageProgress", "totalStepsCompleted", "totalSteps", "learningVelocity", "skillPlans", "categoryDistribution", "dailyTrends", "mostActiveCategory", "insights", "changeFromPrevious", "recommendedFocus", "generatedAt"]}, "ProductivityMetricDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Metric name", "example": "Tasks Completed Per Hour"}, "value": {"type": "number", "description": "Current value", "example": 2.3}, "unit": {"type": "string", "description": "Unit of measurement", "example": "tasks/hour"}, "trend": {"type": "string", "description": "Trend compared to previous period", "example": "improving", "enum": ["improving", "stable", "declining"]}, "change": {"type": "number", "description": "Change from previous period", "example": 0.4}}, "required": ["name", "value", "unit", "trend", "change"]}, "TimeBlockDto": {"type": "object", "properties": {"startHour": {"type": "number", "description": "Start hour (24-hour format)", "example": 9}, "endHour": {"type": "number", "description": "End hour (24-hour format)", "example": 11}, "productivityScore": {"type": "number", "description": "Productivity score for this time block (0-100)", "example": 92.5}, "activitiesCompleted": {"type": "number", "description": "Activities completed in this time block", "example": 8}, "focusLevel": {"type": "number", "description": "Average focus level (0-100)", "example": 87.3}}, "required": ["startHour", "endHour", "productivityScore", "activitiesCompleted", "focusLevel"]}, "DayProductivityDto": {"type": "object", "properties": {"date": {"type": "string", "description": "Date", "example": "2024-05-15"}, "productivityScore": {"type": "number", "description": "Overall productivity score for the day (0-100)", "example": 78.5}, "tasksCompleted": {"type": "number", "description": "Tasks completed", "example": 6}, "habitsCompleted": {"type": "number", "description": "Habits completed", "example": 4}, "focusTimeMinutes": {"type": "number", "description": "Total focus time (minutes)", "example": 180}, "distractionEvents": {"type": "number", "description": "Number of distraction events", "example": 3}}, "required": ["date", "productivityScore", "tasksCompleted", "habitsCompleted", "focusTimeMinutes", "distractionEvents"]}, "ProductivityInsightDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Insight type", "example": "peak_hours", "enum": ["peak_hours", "efficiency_pattern", "distraction_analysis", "optimization_opportunity"]}, "title": {"type": "string", "description": "Insight title", "example": "Morning Peak Performance"}, "description": {"type": "string", "description": "Detailed description", "example": "Your productivity is 40% higher between 9-11 AM compared to afternoon hours"}, "recommendations": {"description": "Actionable recommendations", "example": ["Schedule important tasks in the morning", "Block calendar during peak hours"], "type": "array", "items": {"type": "string"}}, "dataPoints": {"type": "number", "description": "Supporting data points", "example": 45}}, "required": ["type", "title", "description", "recommendations", "dataPoints"]}, "ProductivityAnalyticsResponseDto": {"type": "object", "properties": {"period": {"type": "string", "description": "Analysis period", "example": "month", "enum": ["day", "week", "month", "quarter", "year"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date", "example": "2024-04-15T00:00:00Z"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date", "example": "2024-05-15T23:59:59Z"}, "overallProductivityScore": {"type": "number", "description": "Overall productivity score (0-100)", "example": 82.7}, "peakProductivityHours": {"description": "Peak productivity hours", "example": [9, 10, 11], "type": "array", "items": {"type": "string"}}, "averageTasksPerDay": {"type": "number", "description": "Average tasks completed per day", "example": 4.2}, "averageHabitsPerDay": {"type": "number", "description": "Average habits completed per day", "example": 3.8}, "focusTimeMinutes": {"type": "number", "description": "Total focus time in minutes", "example": 3600}, "distractionEvents": {"type": "number", "description": "Total distraction events", "example": 45}, "metrics": {"description": "Key productivity metrics", "type": "array", "items": {"$ref": "#/components/schemas/ProductivityMetricDto"}}, "hourlyPatterns": {"description": "Productivity patterns by hour", "type": "array", "items": {"$ref": "#/components/schemas/TimeBlockDto"}}, "dailyProductivity": {"description": "Daily productivity trends", "type": "array", "items": {"$ref": "#/components/schemas/DayProductivityDto"}}, "timeBlocks": {"description": "Optimal time blocks for different activities", "type": "array", "items": {"$ref": "#/components/schemas/TimeBlockDto"}}, "insights": {"description": "Generated productivity insights", "type": "array", "items": {"$ref": "#/components/schemas/ProductivityInsightDto"}}, "changeFromPrevious": {"type": "number", "description": "Change from previous period (%)", "example": 8.5}, "recommendedOptimizations": {"description": "Recommended productivity optimizations", "example": ["Schedule deep work in morning hours", "Reduce afternoon meetings", "Take breaks every 90 minutes"], "type": "array", "items": {"type": "string"}}, "generatedAt": {"format": "date-time", "type": "string", "description": "Report generation timestamp", "example": "2024-05-15T14:30:00Z"}}, "required": ["period", "periodStart", "periodEnd", "overallProductivityScore", "peakProductivityHours", "averageTasksPerDay", "averageHabitsPerDay", "focusTimeMinutes", "distractionEvents", "metrics", "hourlyPatterns", "dailyProductivity", "timeBlocks", "insights", "changeFromPrevious", "recommendedOptimizations", "generatedAt"]}, "PodcastEngagementDto": {"type": "object", "properties": {"totalPodcasts": {"type": "number", "description": "Total podcasts generated", "example": 15}, "averageListenTime": {"type": "number", "description": "Average listen time per podcast (minutes)", "example": 5.2}, "completionRate": {"type": "number", "description": "Podcast completion rate (%)", "example": 78.5}, "favoriteTopics": {"description": "Favorite podcast topics", "example": ["Productivity", "Health", "Technology"], "type": "array", "items": {"type": "string"}}, "listeningPatterns": {"type": "object", "description": "Listening patterns by time of day", "example": {"morning": 40, "afternoon": 35, "evening": 25}}}, "required": ["totalPodcasts", "averageListenTime", "completionRate", "favoriteTopics", "listeningPatterns"]}, "CalendarEngagementDto": {"type": "object", "properties": {"totalEvents": {"type": "number", "description": "Total calendar events", "example": 45}, "averageEventsPerDay": {"type": "number", "description": "Average events per day", "example": 1.5}, "eventTypes": {"type": "object", "description": "Event types distribution", "example": {"meeting": 20, "task": 15, "reminder": 10}}, "planningConsistency": {"type": "number", "description": "Planning consistency score (0-100)", "example": 75.3}}, "required": ["totalEvents", "averageEventsPerDay", "eventTypes", "planningConsistency"]}, "ChallengeEngagementDto": {"type": "object", "properties": {"totalChallenges": {"type": "number", "description": "Total challenges participated", "example": 3}, "activeChallenges": {"type": "number", "description": "Currently active challenges", "example": 2}, "completionRate": {"type": "number", "description": "Challenge completion rate (%)", "example": 66.7}, "averageParticipationDays": {"type": "number", "description": "Average participation days per challenge", "example": 15.5}, "favoriteCategories": {"description": "Favorite challenge categories", "example": ["Fitness", "Productivity", "Learning"], "type": "array", "items": {"type": "string"}}}, "required": ["totalChallenges", "activeChallenges", "completionRate", "averageParticipationDays", "favoriteCategories"]}, "EngagementTrendDto": {"type": "object", "properties": {"date": {"type": "string", "description": "Date", "example": "2024-05-15"}, "totalInteractions": {"type": "number", "description": "Total interactions on this day", "example": 12}, "uniqueFeaturesUsed": {"type": "number", "description": "Unique features used", "example": 5}, "sessionDurationMinutes": {"type": "number", "description": "Session duration (minutes)", "example": 25.5}, "engagementScore": {"type": "number", "description": "Engagement score for the day (0-100)", "example": 78.5}}, "required": ["date", "totalInteractions", "uniqueFeaturesUsed", "sessionDurationMinutes", "engagementScore"]}, "EngagementInsightDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Insight type", "example": "feature_adoption", "enum": ["feature_adoption", "usage_pattern", "engagement_trend", "retention_factor"]}, "title": {"type": "string", "description": "Insight title", "example": "High Podcast Engagement"}, "description": {"type": "string", "description": "Detailed description", "example": "You consistently engage with podcast features, showing 85% completion rate"}, "recommendations": {"description": "Actionable recommendations", "example": ["Explore new podcast topics", "Share favorite episodes with friends"], "type": "array", "items": {"type": "string"}}, "impact": {"type": "string", "description": "Impact on overall engagement", "example": "high", "enum": ["low", "medium", "high"]}}, "required": ["type", "title", "description", "recommendations", "impact"]}, "EngagementAnalyticsResponseDto": {"type": "object", "properties": {"period": {"type": "string", "description": "Analysis period", "example": "month", "enum": ["day", "week", "month", "quarter", "year"]}, "periodStart": {"format": "date-time", "type": "string", "description": "Period start date", "example": "2024-04-15T00:00:00Z"}, "periodEnd": {"format": "date-time", "type": "string", "description": "Period end date", "example": "2024-05-15T23:59:59Z"}, "overallEngagementScore": {"type": "number", "description": "Overall engagement score (0-100)", "example": 84.2}, "totalInteractions": {"type": "number", "description": "Total interactions across all features", "example": 342}, "averageDailyInteractions": {"type": "number", "description": "Average daily interactions", "example": 11.4}, "featureAdoptionRate": {"type": "number", "description": "Feature adoption rate (%)", "example": 78.5}, "retentionScore": {"type": "number", "description": "User retention score (0-100)", "example": 92.3}, "podcastEngagement": {"description": "Podcast engagement metrics", "allOf": [{"$ref": "#/components/schemas/PodcastEngagementDto"}]}, "calendarEngagement": {"description": "Calendar engagement metrics", "allOf": [{"$ref": "#/components/schemas/CalendarEngagementDto"}]}, "challengeEngagement": {"description": "Challenge engagement metrics", "allOf": [{"$ref": "#/components/schemas/ChallengeEngagementDto"}]}, "dailyTrends": {"description": "Daily engagement trends", "type": "array", "items": {"$ref": "#/components/schemas/EngagementTrendDto"}}, "insights": {"description": "Generated engagement insights", "type": "array", "items": {"$ref": "#/components/schemas/EngagementInsightDto"}}, "changeFromPrevious": {"type": "number", "description": "Change from previous period (%)", "example": 12.7}, "recommendedActions": {"description": "Recommended actions to improve engagement", "example": ["Try new challenge categories", "Explore calendar planning features", "Generate more personalized podcasts"], "type": "array", "items": {"type": "string"}}, "generatedAt": {"format": "date-time", "type": "string", "description": "Report generation timestamp", "example": "2024-05-15T14:30:00Z"}}, "required": ["period", "periodStart", "periodEnd", "overallEngagementScore", "totalInteractions", "averageDailyInteractions", "featureAdoptionRate", "retentionScore", "podcastEngagement", "calendarEngagement", "challengeEngagement", "dailyTrends", "insights", "changeFromPrevious", "recommendedActions", "generatedAt"]}, "CreateCalendarEventDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the calendar event", "example": "Team Meeting"}, "description": {"type": "string", "description": "Description of the calendar event", "example": "Weekly team standup meeting"}, "startTime": {"type": "string", "description": "Start time of the event", "example": "2025-06-28T10:00:00Z"}, "endTime": {"type": "string", "description": "End time of the event", "example": "2025-06-28T11:00:00Z"}, "type": {"type": "string", "description": "Type of the calendar event", "enum": ["habit", "task", "custom"], "default": "custom"}, "relatedId": {"type": "string", "description": "ID of the related task or habit", "example": "uuid-string"}, "color": {"type": "string", "description": "Hex color code for the event", "example": "#FF5722"}, "isCompleted": {"type": "boolean", "description": "Whether the event is completed", "default": false}}, "required": ["title", "startTime", "endTime", "type"]}, "CalendarEventResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the calendar event"}, "title": {"type": "string", "description": "Title of the calendar event"}, "description": {"type": "string", "description": "Description of the calendar event"}, "startTime": {"format": "date-time", "type": "string", "description": "Start time of the event"}, "endTime": {"format": "date-time", "type": "string", "description": "End time of the event"}, "type": {"type": "string", "description": "Type of the calendar event", "enum": ["habit", "task", "custom"]}, "relatedId": {"type": "string", "description": "ID of the related task or habit"}, "color": {"type": "string", "description": "Hex color code for the event"}, "isCompleted": {"type": "boolean", "description": "Whether the event is completed"}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation timestamp"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update timestamp"}}, "required": ["id", "title", "startTime", "endTime", "type", "isCompleted", "createdAt", "updatedAt"]}, "UpdateCalendarEventDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the calendar event", "example": "Team Meeting"}, "description": {"type": "string", "description": "Description of the calendar event", "example": "Weekly team standup meeting"}, "startTime": {"type": "string", "description": "Start time of the event", "example": "2025-06-28T10:00:00Z"}, "endTime": {"type": "string", "description": "End time of the event", "example": "2025-06-28T11:00:00Z"}, "type": {"type": "string", "description": "Type of the calendar event", "enum": ["habit", "task", "custom"], "default": "custom"}, "relatedId": {"type": "string", "description": "ID of the related task or habit", "example": "uuid-string"}, "color": {"type": "string", "description": "Hex color code for the event", "example": "#FF5722"}, "isCompleted": {"type": "boolean", "description": "Whether the event is completed", "default": false}}}, "SuggestionDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique suggestion identifier", "example": "suggestion-123-u<PERSON>"}, "type": {"type": "string", "description": "Type of suggestion", "enum": ["habit_improvement", "task_optimization", "productivity_enhancement", "wellness_boost", "learning_acceleration", "goal_achievement", "time_management", "stress_reduction", "energy_optimization", "focus_improvement"], "example": "habit_improvement"}, "category": {"type": "string", "description": "Category of the suggestion", "enum": ["habits", "tasks", "productivity", "wellness", "learning", "goals", "mindfulness", "health", "relationships", "career"], "example": "habits"}, "priority": {"type": "string", "description": "Priority level of the suggestion", "enum": ["low", "medium", "high", "critical"], "example": "high"}, "difficulty": {"type": "string", "description": "Difficulty level to implement", "enum": ["easy", "medium", "hard", "expert"], "example": "medium"}, "title": {"type": "string", "description": "Short, actionable title", "example": "Optimize Your Morning Routine"}, "description": {"type": "string", "description": "Detailed description of the suggestion", "example": "Based on your activity patterns, establishing a consistent morning routine could improve your daily productivity by 25%."}, "actionSteps": {"description": "Step-by-step action items", "example": ["Set a consistent wake-up time (7:00 AM)", "Include 10 minutes of meditation or stretching", "Review your top 3 priorities for the day", "Prepare a healthy breakfast"], "type": "array", "items": {"type": "string"}}, "expectedImpact": {"type": "string", "description": "Expected positive impact", "example": "Improved focus, better time management, and increased daily energy levels"}, "estimatedTimeToImplement": {"type": "string", "description": "Estimated time to fully implement", "example": "2-3 weeks"}, "tags": {"description": "Relevant tags for categorization", "example": ["morning", "routine", "productivity", "habits"], "type": "array", "items": {"type": "string"}}, "aiConfidence": {"type": "number", "description": "AI confidence in this suggestion (0.0 - 1.0)", "example": 0.87}, "reasoning": {"type": "string", "description": "AI reasoning behind the suggestion", "example": "Your task completion rate is 40% higher in the morning hours (8-11 AM), and you currently lack a structured morning routine."}, "metadata": {"type": "object", "description": "Additional metadata about the suggestion", "example": {"dataSourcesUsed": ["habits", "tasks", "productivity_patterns"], "relatedInsights": ["peak_productivity_hours", "consistency_gaps"], "implementationTips": ["Start with just one element", "Track progress daily"]}}}, "required": ["id", "type", "category", "priority", "difficulty", "title", "description", "actionSteps", "expectedImpact", "estimatedTimeToImplement", "tags", "aiConfidence", "reasoning", "metadata"]}, "SuggestionAnalysisDto": {"type": "object", "properties": {"analysisConfidence": {"type": "number", "description": "Overall analysis confidence (0.0 - 1.0)", "example": 0.82}, "dataQuality": {"type": "string", "description": "Quality of available data for analysis", "example": "high", "enum": ["low", "medium", "high", "excellent"]}, "identifiedPatterns": {"description": "Key patterns identified in user data", "example": ["Strong weekday productivity, weaker weekends", "Peak performance in morning hours", "Inconsistent habit completion on Fridays"], "type": "array", "items": {"type": "string"}}, "improvementOpportunities": {"description": "Areas with the most improvement potential", "example": ["Weekend consistency", "Evening routine", "Task prioritization"], "type": "array", "items": {"type": "string"}}, "userStrengths": {"description": "User strengths to build upon", "example": ["Strong morning habits", "High task completion rate", "Consistent learning"], "type": "array", "items": {"type": "string"}}}, "required": ["analysisConfidence", "dataQuality", "identifiedPatterns", "improvementOpportunities", "userStrengths"]}, "ImprovementSuggestionsResponseDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID for whom suggestions were generated", "example": "user-123-uuid"}, "suggestions": {"description": "List of personalized improvement suggestions", "type": "array", "items": {"$ref": "#/components/schemas/SuggestionDto"}}, "totalSuggestions": {"type": "number", "description": "Total number of suggestions generated", "example": 8}, "analysis": {"description": "Analysis metadata and insights", "allOf": [{"$ref": "#/components/schemas/SuggestionAnalysisDto"}]}, "generatedAt": {"format": "date-time", "type": "string", "description": "When these suggestions were generated", "example": "2024-05-15T14:30:00Z"}, "analysisConfidence": {"type": "number", "description": "Overall analysis confidence (0.0 - 1.0)", "example": 0.85}, "dataQuality": {"type": "string", "description": "Quality of data used for analysis", "example": "high", "enum": ["low", "medium", "high", "excellent"]}, "nextRefreshRecommended": {"format": "date-time", "type": "string", "description": "Recommended time for next refresh", "example": "2024-05-16T14:30:00Z"}, "metadata": {"type": "object", "description": "<PERSON><PERSON><PERSON> about the analysis process", "example": {"analysisType": "ai_powered", "dataSourcesUsed": ["habits", "tasks", "productivity", "mood"], "aiModel": "gemini-2.0-flash", "processingTimeMs": 2340}}}, "required": ["userId", "suggestions", "totalSuggestions", "analysis", "generatedAt", "analysisConfidence", "dataQuality", "nextRefreshRecommended", "metadata"]}, "MarkSuggestionImplementedDto": {"type": "object", "properties": {"implementationNotes": {"type": "string", "description": "Additional notes about implementation", "example": "Started with just the wake-up time, will add meditation next week"}, "successMetrics": {"type": "object", "description": "Success metrics or outcomes", "example": {"energyLevel": 8, "productivityIncrease": "20%", "consistencyDays": 14}}}}, "SuggestionActionResponseDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the action was successful", "example": true}, "message": {"type": "string", "description": "Response message", "example": "Suggestion marked as implemented successfully"}, "suggestion": {"type": "object", "description": "Updated suggestion data"}}, "required": ["success", "message"]}, "ProvideSuggestionFeedbackDto": {"type": "object", "properties": {"rating": {"type": "number", "description": "Rating of the suggestion (1-5 stars)", "minimum": 1, "maximum": 5, "example": 4}, "helpful": {"type": "boolean", "description": "Whether the suggestion was helpful", "example": true}, "implemented": {"type": "boolean", "description": "Whether the user implemented the suggestion", "example": true}, "comments": {"type": "string", "description": "User comments about the suggestion", "example": "This suggestion really helped me improve my morning routine!"}, "difficulty": {"type": "string", "description": "Perceived difficulty of implementation", "enum": ["easy", "medium", "hard", "expert"], "example": "medium"}}, "required": ["rating", "helpful", "implemented"]}, "DismissSuggestionDto": {"type": "object", "properties": {"reason": {"type": "string", "description": "Reason for dismissing the suggestion", "example": "Not relevant to my current goals"}, "additionalFeedback": {"type": "string", "description": "Additional feedback about why it was dismissed", "example": "I already have a well-established morning routine"}}, "required": ["reason"]}, "FirebaseMetadataDto": {"type": "object", "properties": {"createdAt": {"type": "string", "description": "Created at timestamp", "example": "2023-05-20T12:00:00.000Z"}}}, "FirebaseEventDataDto": {"type": "object", "properties": {"uid": {"type": "string", "description": "Firebase user ID", "example": "firebase-uid-123456"}, "email": {"type": "string", "description": "User email", "example": "<EMAIL>"}, "metadata": {"description": "User metadata", "allOf": [{"$ref": "#/components/schemas/FirebaseMetadataDto"}]}}, "required": ["uid"]}, "FirebaseEventDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Event type", "example": "user.created", "enum": ["user.created", "user.deleted"]}, "data": {"description": "Event data", "allOf": [{"$ref": "#/components/schemas/FirebaseEventDataDto"}]}}, "required": ["type", "data"]}, "FirebaseWebhookDto": {"type": "object", "properties": {"event": {"description": "Firebase event", "allOf": [{"$ref": "#/components/schemas/FirebaseEventDto"}]}}, "required": ["event"]}, "ContactUsDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Full name of the person contacting", "example": "<PERSON>"}, "email": {"type": "string", "description": "Email address for response", "example": "<EMAIL>"}, "subject": {"type": "string", "description": "Subject of the inquiry", "example": "Question about premium features"}, "message": {"type": "string", "description": "Message content", "example": "I would like to know more about the premium features available in the app."}, "userId": {"type": "string", "description": "User ID if logged in", "example": "123e4567-e89b-12d3-a456-************"}, "category": {"type": "string", "description": "Category of the inquiry", "example": "technical", "enum": ["technical", "billing", "feature-request", "bug-report", "general"]}}, "required": ["name", "email", "subject", "message"]}, "FeedbackDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of feedback", "enum": ["bug", "feature-request", "improvement", "compliment", "complaint"], "example": "feature-request"}, "title": {"type": "string", "description": "Title of the feedback", "example": "Add dark mode support"}, "description": {"type": "string", "description": "Detailed description of the feedback", "example": "It would be great to have a dark mode option for better usability during night time."}, "priority": {"type": "string", "description": "Priority level of the feedback", "enum": ["low", "medium", "high", "critical"], "example": "medium"}, "email": {"type": "string", "description": "User email for follow-up", "example": "<EMAIL>"}, "userId": {"type": "string", "description": "User ID if authenticated", "example": "123e4567-e89b-12d3-a456-************"}, "deviceInfo": {"type": "string", "description": "Device information for bug reports", "example": "iOS 17.0, iPhone 14 Pro"}, "appVersion": {"type": "string", "description": "App version when feedback was submitted", "example": "1.2.3"}}, "required": ["type", "title", "description"]}, "HelpArticleResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Article ID"}, "title": {"type": "string", "description": "Article title"}, "description": {"type": "string", "description": "Article description"}, "content": {"type": "string", "description": "Article content"}, "category": {"type": "string", "description": "Article category", "enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"]}, "tags": {"description": "Article tags", "type": "array", "items": {"type": "string"}}, "published": {"type": "boolean", "description": "Whether the article is published"}, "sortOrder": {"type": "number", "description": "Display order"}, "viewCount": {"type": "number", "description": "View count"}, "helpfulCount": {"type": "number", "description": "Helpful count"}, "notHelpfulCount": {"type": "number", "description": "Not helpful count"}, "authorId": {"type": "string", "description": "Author ID"}, "createdAt": {"format": "date-time", "type": "string", "description": "Created date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Updated date"}}, "required": ["id", "title", "description", "content", "category", "tags", "published", "sortOrder", "viewCount", "helpfulCount", "notHelpfulCount", "authorId", "createdAt", "updatedAt"]}, "CreateHelpArticleDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Article title", "example": "Getting Started with Power Up"}, "description": {"type": "string", "description": "Article description", "example": "Learn how to get started with the app"}, "content": {"type": "string", "description": "Article content in HTML format"}, "category": {"type": "string", "description": "Article category", "enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"]}, "tags": {"description": "Article tags", "type": "array", "items": {"type": "string"}}, "published": {"type": "boolean", "description": "Whether the article is published", "default": true}, "sortOrder": {"type": "number", "description": "Display order (lower numbers appear first)", "default": 0}}, "required": ["title", "description", "content", "category"]}, "UpdateHelpArticleDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Article title"}, "description": {"type": "string", "description": "Article description"}, "content": {"type": "string", "description": "Article content in HTML format"}, "category": {"type": "string", "description": "Article category", "enum": ["getting-started", "account", "features", "billing", "troubleshooting", "privacy", "technical"]}, "tags": {"description": "Article tags", "type": "array", "items": {"type": "string"}}, "published": {"type": "boolean", "description": "Whether the article is published"}, "sortOrder": {"type": "number", "description": "Display order (lower numbers appear first)"}}}, "CreateBlogPostDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Blog post title", "example": "The Future of AI in Personal Wellness"}, "excerpt": {"type": "string", "description": "Short excerpt/summary", "example": "Discover how AI is revolutionizing personal wellness and habit formation."}, "content": {"type": "string", "description": "Blog post content in markdown", "example": "# Introduction\n\nThis is the content..."}, "slug": {"type": "string", "description": "URL slug (auto-generated if not provided)", "example": "future-ai-personal-wellness"}, "category": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "description": "Blog post category", "example": "ai-technology"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "description": "Blog post status", "example": "draft"}, "featuredImage": {"type": "string", "description": "Featured image URL", "example": "https://example.com/image.jpg"}, "metaDescription": {"type": "string", "description": "Meta description for SEO", "example": "Learn about AI wellness technology trends"}, "tags": {"description": "Tags array", "example": ["AI", "wellness", "technology"], "type": "array", "items": {"type": "string"}}, "isFeatured": {"type": "boolean", "description": "<PERSON> as featured post", "example": false}}, "required": ["title", "content", "category"]}, "UpdateBlogPostDto": {"type": "object", "properties": {"title": {"type": "string", "description": "Blog post title", "example": "The Future of AI in Personal Wellness"}, "excerpt": {"type": "string", "description": "Short excerpt/summary", "example": "Discover how AI is revolutionizing personal wellness and habit formation."}, "content": {"type": "string", "description": "Blog post content in markdown", "example": "# Introduction\n\nThis is the content..."}, "slug": {"type": "string", "description": "URL slug (auto-generated if not provided)", "example": "future-ai-personal-wellness"}, "category": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "description": "Blog post category", "example": "ai-technology"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "description": "Blog post status", "example": "draft"}, "featuredImage": {"type": "string", "description": "Featured image URL", "example": "https://example.com/image.jpg"}, "metaDescription": {"type": "string", "description": "Meta description for SEO", "example": "Learn about AI wellness technology trends"}, "tags": {"description": "Tags array", "example": ["AI", "wellness", "technology"], "type": "array", "items": {"type": "string"}}, "isFeatured": {"type": "boolean", "description": "<PERSON> as featured post", "example": false}}}, "BlogPostResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Blog post ID"}, "title": {"type": "string", "description": "Blog post title"}, "excerpt": {"type": "string", "description": "Short excerpt"}, "content": {"type": "string", "description": "Blog post content"}, "slug": {"type": "string", "description": "URL slug"}, "category": {"type": "string", "enum": ["wellness", "ai-technology", "habits", "productivity", "mental-health", "fitness", "nutrition", "company-news"], "description": "Blog post category"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "description": "Blog post status"}, "featuredImage": {"type": "string", "description": "Featured image URL"}, "metaDescription": {"type": "string", "description": "Meta description"}, "tags": {"description": "Tags array", "type": "array", "items": {"type": "string"}}, "views": {"type": "number", "description": "View count"}, "likes": {"type": "number", "description": "Like count"}, "isFeatured": {"type": "boolean", "description": "Is featured post"}, "publishedAt": {"format": "date-time", "type": "string", "description": "Published date"}, "author": {"type": "object", "description": "Author information"}, "createdAt": {"format": "date-time", "type": "string", "description": "Created date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Updated date"}}, "required": ["id", "title", "excerpt", "content", "slug", "category", "status", "featuredImage", "metaDescription", "tags", "views", "likes", "isFeatured", "publishedAt", "author", "createdAt", "updatedAt"]}, "CreateMobileFeedbackDto": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of feedback", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"], "example": "feature-request"}, "title": {"type": "string", "description": "Title/summary of the feedback", "example": "Add dark mode support", "maxLength": 200}, "description": {"type": "string", "description": "Detailed description of the feedback", "example": "It would be great to have a dark mode option for better usability during night time."}, "priority": {"type": "string", "description": "Priority level of the feedback", "enum": ["low", "medium", "high", "critical"], "example": "medium"}, "platform": {"type": "string", "description": "Mobile platform", "enum": ["ios", "android", "web"], "example": "ios"}, "appVersion": {"type": "string", "description": "App version", "example": "1.2.3"}, "deviceModel": {"type": "string", "description": "Device model", "example": "iPhone 14 Pro"}, "osVersion": {"type": "string", "description": "Operating system version", "example": "iOS 17.0"}, "screenResolution": {"type": "string", "description": "Screen resolution", "example": "1179x2556"}, "email": {"type": "string", "description": "User email for follow-up", "example": "<EMAIL>"}, "userName": {"type": "string", "description": "User name", "example": "<PERSON>"}, "contactBack": {"type": "boolean", "description": "Whether user wants to be contacted back", "example": true}, "anonymous": {"type": "boolean", "description": "Whether feedback should be anonymous", "example": false}, "rating": {"type": "number", "description": "Overall rating (1-5 stars)", "example": 4, "minimum": 1, "maximum": 5}, "featureContext": {"type": "string", "description": "Feature or screen context", "example": "Habits tracking screen"}, "reproductionSteps": {"type": "string", "description": "Steps to reproduce the issue (for bug reports)", "example": "1. Open habits screen\n2. Tap on add habit\n3. App crashes"}, "expectedBehavior": {"type": "string", "description": "Expected behavior (for bug reports)", "example": "App should open the add habit form"}, "actualBehavior": {"type": "string", "description": "Actual behavior (for bug reports)", "example": "App crashes and shows error message"}, "screenshotUrls": {"description": "Screenshot URLs", "example": ["https://example.com/screenshot1.jpg", "https://example.com/screenshot2.jpg"], "type": "array", "items": {"type": "string"}}, "videoUrl": {"type": "string", "description": "Video URL for demonstration", "example": "https://example.com/video.mp4"}, "tags": {"description": "Tags for categorization", "example": ["urgent", "accessibility", "mobile"], "type": "array", "items": {"type": "string"}}}, "required": ["type", "title", "description"]}, "MobileFeedbackResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Feedback ID", "example": "123e4567-e89b-12d3-a456-************"}, "type": {"type": "string", "description": "Type of feedback", "enum": ["bug", "feature-request", "improvement", "ui-ux", "performance", "content", "general"]}, "title": {"type": "string", "description": "Feedback title"}, "description": {"type": "string", "description": "Feedback description"}, "priority": {"type": "string", "description": "Priority level", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "description": "Current status", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}, "platform": {"type": "string", "description": "Mobile platform", "enum": ["ios", "android", "web"]}, "appVersion": {"type": "string", "description": "App version"}, "deviceModel": {"type": "string", "description": "Device model"}, "osVersion": {"type": "string", "description": "OS version"}, "userId": {"type": "string", "description": "User ID"}, "email": {"type": "string", "description": "User email"}, "userName": {"type": "string", "description": "User name"}, "contactBack": {"type": "boolean", "description": "Contact back preference"}, "anonymous": {"type": "boolean", "description": "Anonymous feedback"}, "rating": {"type": "number", "description": "Rating (1-5 stars)"}, "featureContext": {"type": "string", "description": "Feature context"}, "screenshotUrls": {"description": "Screenshot URLs", "type": "array", "items": {"type": "string"}}, "videoUrl": {"type": "string", "description": "Video URL"}, "adminNotes": {"type": "string", "description": "Admin notes"}, "assignedTo": {"type": "string", "description": "Assigned admin"}, "resolutionNotes": {"type": "string", "description": "Resolution notes"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation date"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update date"}, "resolvedAt": {"format": "date-time", "type": "string", "description": "Resolution date"}, "dueDate": {"format": "date-time", "type": "string", "description": "Due date"}}, "required": ["id", "type", "title", "description", "priority", "status", "contactBack", "anonymous", "createdAt", "updatedAt"]}, "UpdateMobileFeedbackDto": {"type": "object", "properties": {"status": {"type": "string", "description": "Feedback status", "enum": ["pending", "reviewing", "in-progress", "completed", "rejected", "duplicate"]}, "priority": {"type": "string", "description": "Priority level", "enum": ["low", "medium", "high", "critical"]}, "adminNotes": {"type": "string", "description": "Admin notes"}, "assignedTo": {"type": "string", "description": "Assigned admin user ID"}, "resolutionNotes": {"type": "string", "description": "Resolution notes"}, "estimatedHours": {"type": "number", "description": "Estimated hours for completion"}, "actualHours": {"type": "number", "description": "Actual hours spent"}, "dueDate": {"type": "string", "description": "Due date for resolution"}, "resolvedAt": {"type": "string", "description": "Resolution date"}}}}}}